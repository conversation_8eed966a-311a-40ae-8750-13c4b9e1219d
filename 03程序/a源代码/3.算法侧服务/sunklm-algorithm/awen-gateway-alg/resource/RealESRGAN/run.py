import subprocess
import os


class RealESRGAN:
    def __init__(self, script_path="/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/RealESRGAN/inference_realesrgan.py"):
        """
        初始化 RealESRGAN 类
        
        参数:
            script_path (str): RealESRGAN 推理脚本路径，默认为 'inference_realesrgan.py'
        """
        self.script_path = script_path

    def handle_request(self, req):
        """
        处理请求并调用相应的处理方法
        
        参数:
            req (dict): 请求字典，包含以下键:
                - input_path (str): 输入图像路径或目录
                - output_path (str): 输出目录
                - model_name (str, optional): 使用的模型名称，默认为 "RealESRGAN_x4plus"
                - outscale (float, optional): 最终的放大比例，默认为 4
                - face_enhance (bool, optional): 是否使用 GFPGAN 增强面部细节，默认为 False
                - tile (int, optional): 测试时的瓦片大小，0 表示不分瓦片，默认为 0
                - precision (str, optional): 精度选择，'fp16' 或 'fp32'，默认为 'fp16'
                - image_ext (str, optional): 输出图像的扩展名，默认为 'auto'
                
        返回:
            str: 命令执行的标准输出或错误信息
        """
        # 解析请求参数
        input_path = req.get("input_path")
        output_path = req.get("output_path")
        model_name = req.get("model_name", "RealESRGAN_x4plus")
        outscale = req.get("outscale", 4)
        face_enhance = req.get("face_enhance", False)
        tile = req.get("tile", 0)
        precision = req.get("precision", "fp16")
        image_ext = req.get("image_ext", "auto")
        
        # 验证参数
        if not input_path or not output_path:
            raise ValueError("请求中必须包含 input_path 和 output_path 参数")
        
        # 调用 run_realesrgan 方法
        return self.run_realesrgan(
            input_path=input_path,
            output_path=output_path,
            model_name=model_name,
            outscale=outscale,
            face_enhance=face_enhance,
            tile=tile,
            precision=precision,
            image_ext=image_ext
        )

    def run_realesrgan(self, input_path, output_path, model_name="RealESRGAN_x4plus", outscale=4, face_enhance=False, tile=0, precision='fp16', image_ext='auto'):
        """
        调用 RealESRGAN 的 inference 脚本，进行图像超分辨率处理。

        参数:
            input_path (str): 输入图像文件路径或文件夹路径
            output_path (str): 输出文件夹路径
            model_name (str): 使用的模型名称，默认为 'RealESRGAN_x4plus'
            outscale (float): 最终的放大比例，默认为 4
            face_enhance (bool): 是否使用 GFPGAN 增强面部细节，默认为 False
            tile (int): 测试时的瓦片大小，0 表示不分瓦片，默认为 0
            precision (str): 精度选择，'fp16' 或 'fp32'，默认为 'fp16'
            image_ext (str): 输出图像的扩展名，默认为 'auto'，可以是 'jpg' 或 'png'

        返回:
            str: 命令执行的标准输出或错误信息
        """
        # 构建命令
        command = [
            'python', self.script_path,
            '-n', model_name,
            '-i', input_path,
            '-o', output_path,
            '--outscale', str(outscale),
            '--ext', image_ext
        ]
        
        # 添加面部增强选项
        if face_enhance:
            command.append('--face_enhance')
        
        # 添加瓦片大小
        if tile != 0:
            command.extend(['--tile', str(tile)])
        
        # 设置精度
        if precision == 'fp32':
            command.append('--fp32')
        
        # 执行命令并捕获输出
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            return f"Error: {e.stderr}"


# 示例用法
if __name__ == "__main__":
    esrgan = RealESRGAN()
    
    # 单张图像处理示例
    req_single = {
        "input_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/Real-ESRGAN-master/inputs/0014.jpg",
        "output_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/Real-ESRGAN-master/results",
        "model_name": "RealESRGAN_x4plus",
        "outscale": 4,
        "face_enhance": True,
        "tile": 0,
        "precision": "fp16",
        "image_ext": "auto"
    }
    
    result = esrgan.handle_request(req_single)
    print("处理结果:", result)

    # 批量处理示例
    # req_batch = {
    #     "input_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/Real-ESRGAN-master/inputs",
    #     "output_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/Real-ESRGAN-master/results",
    #     "model_name": "RealESRGAN_x4plus",
    #     "outscale": 4,
    #     "face_enhance": False,
    #     "tile": 0,
    #     "precision": "fp16",
    #     "image_ext": "auto"
    # }
    
    # result = esrgan.handle_request(req_batch)
    # print("批量处理结果:", result)
