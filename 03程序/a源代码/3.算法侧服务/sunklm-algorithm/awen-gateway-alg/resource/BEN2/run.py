import torch
from PIL import Image, ImageDraw
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

import BEN2
import numpy as np
import os
from enum import Enum


class OutputFormat(Enum):
    """输出格式枚举"""
    PNG_TRANSPARENT = "png_transparent"  # PNG透明背景图
    PNG_MASK = "png_mask"               # PNG黑白蒙版
    SVG_MASK = "svg_mask"               # SVG蒙版矢量图
    BOTH = "both"                       # 同时输出PNG和SVG


class ImageMatting:
    def __init__(self, model_path="/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/BEN2/BEN2_Base.pth", use_cuda=True):
        """
        初始化图像抠图类
        
        参数:
            model_path (str): 模型权重文件路径
            use_cuda (bool): 是否使用GPU加速
        """
        self.device = torch.device('cuda' if use_cuda and torch.cuda.is_available() else 'cpu')
        self.model = BEN2.BEN_Base().to(self.device).eval()
        self.model.loadcheckpoints(model_path)

    def handle_request(self, req):
        """
        处理请求并调用相应的处理方法
        
        参数:
            req (dict): 请求字典，包含以下键:
                - input_path (str): 输入图像路径或目录
                - output_dir (str): 输出目录
                - output_format (str): 输出格式 ("png_transparent", "png_mask", "svg_mask", "both")
                - refine_foreground (bool, optional): 是否精细化前景边缘，默认为True
                - is_batch (bool, optional): 是否批量处理，默认为False
                
        返回:
            dict: 包含处理结果的字典
        """
        # 解析请求参数
        input_path = req.get("input_path")
        output_dir = req.get("output_dir")
        output_format_str = req.get("output_format", "png_transparent")
        refine_foreground = req.get("refine_foreground", True)
        is_batch = req.get("is_batch", False)
        
        # 验证参数
        if not input_path or not output_dir:
            raise ValueError("请求中必须包含 input_path 和 output_dir 参数")
        
        try:
            output_format = OutputFormat(output_format_str)
        except ValueError:
            raise ValueError(f"无效的输出格式: {output_format_str}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 根据是否批量处理选择调用方法
        if is_batch:
            return self.batch_process(
                input_dir=input_path,
                output_dir=output_dir,
                output_format=output_format,
                refine_foreground=refine_foreground
            )
        else:
            return self.process_image(
                image_path=input_path,
                output_dir=output_dir,
                output_format=output_format,
                refine_foreground=refine_foreground
            )

    def process_image(self, image_path, output_dir, output_format=OutputFormat.PNG_TRANSPARENT, refine_foreground=True):
        """
        处理单张图像
        
        参数:
            image_path (str): 输入图像路径
            output_dir (str): 输出目录路径
            output_format (OutputFormat): 输出格式
            refine_foreground (bool): 是否精细化前景边缘
            
        返回:
            dict: 包含输出文件路径和PIL.Image对象的字典
        """
        # 加载图像
        original_image = Image.open(image_path).convert("RGB")
        
        # 获取文件名（不带扩展名）
        filename = os.path.splitext(os.path.basename(image_path))[0]
        
        # 进行推理
        with torch.no_grad():
            result = self.model.inference(original_image, refine_foreground=refine_foreground)
        
        # 确保结果是PIL图像或转换为掩码
        if isinstance(result, torch.Tensor):
            mask = self._extract_mask_from_tensor(result)
        else:
            mask = self._extract_mask_from_pil(result)
        
        # 创建输出文件路径
        results = {}
        
        if output_format in [OutputFormat.PNG_TRANSPARENT, OutputFormat.BOTH]:
            png_path = os.path.join(output_dir, f"{filename}_transparent.png")
            transparent_img = self._create_transparent_png(original_image, mask)
            transparent_img.save(png_path)
            results['png_transparent'] = {'path': png_path, 'image': transparent_img}
            print(f"PNG透明背景图已保存至: {png_path}")
        
        if output_format in [OutputFormat.PNG_MASK, OutputFormat.BOTH]:
            mask_path = os.path.join(output_dir, f"{filename}_mask.png")
            mask_img = Image.fromarray(mask, mode='L')
            mask_img.save(mask_path)
            results['png_mask'] = {'path': mask_path, 'image': mask_img}
            print(f"PNG蒙版图已保存至: {mask_path}")
        
        if output_format in [OutputFormat.SVG_MASK, OutputFormat.BOTH]:
            svg_path = os.path.join(output_dir, f"{filename}_mask.svg")
            self._create_svg_mask(mask, svg_path)
            results['svg_mask'] = {'path': svg_path, 'image': None}
            print(f"SVG蒙版矢量图已保存至: {svg_path}")
        
        return results

    def batch_process(self, input_dir, output_dir, output_format=OutputFormat.PNG_TRANSPARENT, **kwargs):
        """
        批量处理图像
        
        参数:
            input_dir (str): 输入目录
            output_dir (str): 输出目录
            output_format (OutputFormat): 输出格式
            **kwargs: 其他参数传递给process_image函数
        """
        import glob
        os.makedirs(output_dir, exist_ok=True)
        
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        image_files = []
        
        for ext in extensions:
            image_files.extend(glob.glob(os.path.join(input_dir, ext)))
            image_files.extend(glob.glob(os.path.join(input_dir, ext.upper())))
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        for i, image_path in enumerate(image_files):
            try:
                print(f"处理 ({i+1}/{len(image_files)}): {image_path}")
                
                self.process_image(
                    image_path=image_path,
                    output_dir=output_dir,
                    output_format=output_format,
                    **kwargs
                )
                
                print(f"✓ 完成: {os.path.basename(image_path)}")
            except Exception as e:
                print(f"✗ 处理失败 {image_path}: {str(e)}")

    @staticmethod
    def _extract_mask_from_tensor(tensor):
        """从Tensor提取掩码"""
        if tensor.dim() == 4:  # BCHW
            tensor = tensor.squeeze(0)
        if tensor.dim() == 3 and tensor.shape[0] in [1, 3, 4]:  # CHW
            tensor = tensor.permute(1, 2, 0)
        
        if tensor.is_cuda:
            tensor = tensor.cpu()
        result_np = tensor.numpy()
        
        if result_np.shape[-1] == 1 or len(result_np.shape) == 2:
            mask = (result_np * 255).astype(np.uint8)
            if len(mask.shape) == 3:
                mask = mask.squeeze(-1)
        else:
            mask = (result_np[:, :, -1] * 255).astype(np.uint8)
        return mask

    @staticmethod
    def _extract_mask_from_pil(pil_image):
        """从PIL图像提取掩码"""
        if pil_image.mode == 'RGBA':
            mask = np.array(pil_image.split()[-1])
        elif pil_image.mode == 'L':
            mask = np.array(pil_image)
        else:
            mask = np.array(pil_image.convert('L'))
        return mask

    @staticmethod
    def _create_transparent_png(original_image, mask):
        """创建透明背景的PNG图像"""
        if original_image.size != (mask.shape[1], mask.shape[0]):
            original_image = original_image.resize((mask.shape[1], mask.shape[0]), Image.Resampling.LANCZOS)
        
        if original_image.mode != 'RGBA':
            original_image = original_image.convert('RGBA')
        
        img_array = np.array(original_image)
        img_array[:, :, 3] = mask
        return Image.fromarray(img_array, 'RGBA')

    @staticmethod
    def _create_svg_mask(mask, output_path, threshold=128):
        """创建SVG蒙版矢量图"""
        from PIL import Image, ImageFilter
        import base64
        from io import BytesIO
        
        height, width = mask.shape
        mask_img = Image.fromarray(mask, mode='L')
        mask_img = mask_img.filter(ImageFilter.GaussianBlur(radius=1))
        smoothed_mask = np.array(mask_img)
        binary_mask = (smoothed_mask > threshold).astype(np.uint8) * 255
        
        mask_pil = Image.fromarray(binary_mask, mode='L')
        buffer = BytesIO()
        mask_pil.save(buffer, format='PNG')
        mask_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <mask id="objectMask">
            <image width="{width}" height="{height}" href="data:image/png;base64,{mask_base64}"/>
        </mask>
        <pattern id="transparentPattern" patternUnits="userSpaceOnUse" width="10" height="10">
            <rect width="5" height="5" fill="#ffffff" opacity="0.8"/>
            <rect x="5" y="5" width="5" height="5" fill="#ffffff" opacity="0.8"/>
            <rect x="5" y="0" width="5" height="5" fill="#cccccc" opacity="0.8"/>
            <rect x="0" y="5" width="5" height="5" fill="#cccccc" opacity="0.8"/>
        </pattern>
    </defs>
    <rect width="{width}" height="{height}" fill="url(#transparentPattern)"/>
    <rect width="{width}" height="{height}" fill="black" mask="url(#objectMask)"/>
    <rect width="{width}" height="{height}" fill="none" stroke="#000000" stroke-width="1"/>
</svg>'''
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(svg_content)


# 示例用法
if __name__ == "__main__":
    matting = ImageMatting()
    
    # 单张图像处理示例
    req_single = {
        "input_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/BEN2-main/input_images/87c69a22273649a6845c7fccbee931c0.jpg",
        "output_dir": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/BEN2-main/output_results",
        "output_format": "both",
        "refine_foreground": True,
        "is_batch": False
    }
    
    results = matting.handle_request(req_single)
    print("处理结果:", results)
    
    # 批量处理示例
    req_batch = {
        "input_path": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/BEN2-main/input_images",
        "output_dir": "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/BEN2-main/output_results",
        "output_format": "svg_mask",
        "refine_foreground": True,
        "is_batch": True
    }
    
    matting.handle_request(req_batch)
