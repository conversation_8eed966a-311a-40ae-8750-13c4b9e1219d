#!/bin/bash
source ./config.cfg >/dev/null 2>&1
#这里可替换为你自己的执行程序，其他代码无需更改
SERVER="./GatewayMain.py"
Port=9080

# 检查第二个参数是否为端口号
if [ -n "$2" ] && [[ "$2" =~ ^[0-9]+$ ]] && [ "$2" -ge 1 ] && [ "$2" -le 65535 ]; then
    Port=$2
    echo "使用指定端口: $Port"
fi

#使用说明，用来提示输入参数
usage() {
    echo "Usage: sh 执行脚本.sh [start|stop|restart|status] [port]"
    echo "  port: 可选参数，指定服务端口 (1-65535)，默认为 9080"
    exit 1
}

#检查程序是否在运行
is_exist(){
  pid=`ps -ef|grep python|grep $Port|awk '{print $2}'`
  #pid=`lsof -i:$Port | grep 'python' | awk '{print $2}'`
  #如果不存在返回1，存在返回0
  if [ -z "$pid" ]; then
    return 1
  else
    echo 'pid: ' $pid 
    return 0
  fi
}

start(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "app is running, please stop it first!"
  else
    #source activate $CONDA_ENV
    #python com.py
    # 初始化conda环境
    eval "$(conda shell.bash hook)"
    conda activate mcp
    nohup python $SERVER $Port > ./logs/nohup.out 2>&1 &
    echo "started successfully, check log file"
    sleep 3
    is_exist
    echo "Pid is $pid"
  fi
}

#停止方法
stop(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "kill pid"
    kill -9 $pid
    sleep 3  
    is_exist
    if [ $? -eq "0" ]; then
      echo "stopped error, please check!"
      exit 2
    else
      echo "app has been stopped successfully"
    fi
  else
    echo "app is not running"
  fi  
}

#输出运行状态
status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "app is running. Pid is $pid"
  else
    echo "app is NOT running."
  fi
}

#重启
restart(){
  stop
  start
}

#根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
  "start")
    start
    exit
    ;;
  "stop")
    stop
    exit
    ;;
  "status")
    status
    exit
    ;;
  "restart")
    restart
    exit
    ;;
  *)
    usage
    ;;
esac
tail -f /dev/null
