"""
-----------------------------------------------------
   File Name：  PromptConstants.py
   Description : 预置的一些prompt模板
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)

   Author: kxj
   Data: 2025.7.1
   Change: add prompt for intent recognition
-----------------------------------------------------
"""
class PromptConstants:

    # 问题改写提示词
    REWRITER_PROMPT = """.Given a question and its context and a rewrite that decontextualizes the question, edit the 
    rewrite to create a revised version that fully addressescoreferences and omissions in the question without changing 
    the originalmeaning of the question but providing more information. The new rewriteshould not duplicate any 
    previously asked questions in the context, Ifthere is no need to edit the rewrite, return the rewrite as-is. Previous 
    Chat History: "{chat_history}" Current Query: "{current}" Only output the rewritten question! DON'T add any prefix. 
    Just answer the final result"""


    # 问题扩写提示词
    QUESTION_EXPAND_PROMPT = """
    # Role: 问题扩写助手

    ## Profile
    - language: 中文
    - description: 根据用户当前提出的问题和历史提问内容，对当前问题进行合理扩写，使其更加清晰、具体，并增强其上下文关联性。

    ## Skills
    1. 理解用户当前输入的问题意图。
    2. 提取并分析用户的历史提问或上下文内容。
    3. 基于上下文对当前问题进行语义扩展，增强清晰度与可操作性。
    4. 保持问题风格与语言一致，避免歧义和重复。

    ## Rules
    1. 扩写应围绕原始问题展开，不得偏离用户意图。
    2. 保持语言自然、通顺，逻辑清晰。
    3. 如果历史上下文不足，则基于当前问题合理补充可能的前提信息。
    4. 只需要返回扩写后的内容，不要有多余内容。

    ## Workflows
    1. 接收当前用户输入的问题。
    2. 提取最近的上下文/历史信息作为参考。
    3. 分析问题核心意图与上下文联系点。
    4. 扩写为更完整的问题描述，确保逻辑顺畅、具体明确。

    ## Init
    扩写用户问题，提升上下文相关性与清晰度，使其更易被AI理解并提供优质响应。

    ## 问题
    {current}

    ## 历史记录
    {chat_history}
    """

    # 意图识别提示词
    INTENT_RECOGNITION_PROMPT = """
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出仅包含一个ID表示意图的编号。
    {prompt}
    "意图列表": {intents}
    "输入": {current}
    "输出格式": {{
        "intentId": 数字
    }}
    """

    INTENT_RECOGNITION_PROMPT_PRO = """
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出包含一个ID表示意图的编号，一句简短的理由说明为什么是这个意图。
    {prompt}
    "意图列表": {intents}
    "输入": {current}
    "输出格式": {{
        "intentId": 数字,
        "reason": "判断理由"
    }}

    """
