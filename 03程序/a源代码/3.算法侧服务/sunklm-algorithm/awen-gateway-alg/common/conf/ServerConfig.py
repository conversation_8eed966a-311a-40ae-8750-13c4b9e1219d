import os
from enum import IntEnum
from typing import Dict

PWD = os.path.dirname(os.getcwd())

# 应用配置
class ServerConfig:
    # 服务名称
    SERVICE_NAME: str = "awen-gateway-alg"
    # 默认限流设置
    RATE_LIMIT: str = "10/minute"  # 每分钟10次
    THREAD_POOL_SIZE: int = 40
    TRUSTED_SERVICES: Dict[str, str] = {
        "agent": "http://***********:8012",
        "gateway": "http://127.0.0.1:9080",
        "order": "http://localhost:8003",
    }

class APIConfig:
    # 默认的API接口地址
    AGENT_URL = "http://***********:9081/agent/chatAgent"

# 任务类型
class TaskCode:
    """
    插件调用类型
    API_PLUG：使用api调用的插件
    """
    TEXT = "text"
    IMAGETOTEXT = "imgToText"
    TEXTTOIMAGE = "textToImg"
    
    RERANKLLM = "rerank"
    
    ASR = "asr"
    TTS = "tts"
    EMBEDDING = "embedding"


# 最大输出长度：
MAX_OUT_LEN = 2048
# 模型输出参数默认值
TEMPERATURE = 0.01
TOP_P = 0.01
TOP_K = 1



class ChatFormatCode():
    OPENAI_FORMAT = 'openai'
    GLM_FORMAT = 'glm'
    RAW_FORMAT = 'raw'

# 私有化部署的模型的标志，0 私有化部署，1 开源模型
class CompanyCode():
    PRIVATE = 0
    OPEN = 1

class ReturnCode(IntEnum):
    SUCESS = 0
    ERROR = 1

# 模型默认参数配置
class ModelDataDefault:
    # 默认使用的模型
    API_KEY = ''
    COMPANY = 'qwen'
    MODEL_TYPE = "qwen2.5-72b-instruct-gptq-int4"
    API_BASE = "http://***********:9020/v1/chat/completions"

    TOP_P = 0.01
    TOP_K = 1
    TEMPERATURE = 0.01
    # 文本最大输入长度
    MAX_SEQ_LEN = 8192
    # images最大输入
    MAX_IMAGES_LEN = 100000
    # 最大输出长度：
    MAX_OUT_LEN = 8192
    # 模型输出参数默认值
    QUERY = "你好"
    STREAM = False
    # 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
    QUERY_PER_SECOND = 1

    IS_DEEP_THINK = False

    DEPLOY_MODE=0
    RERANK_MODEL_NAME = "bge-m3"
    EMBEDDING_MODEL_NAME = "bge-reranker-v2-m3"

    REWRITE = False
    POSTPROCESS = False


class MysqlConf:
    # mysql配置
    MYSQL_HOST = "**********"
    MYSQL_PORT = 3306
    MYSQL_USER = "SUNKLM"
    MYSQL_PASSWORD = "Sunyard@6888"
    MYSQL_DATABASE = "AGENT_FLOW"


# 私有化部署的模型MAP
LLM_MAP = {
    "Qwen2.5-72B-Instruct": {
        "base_url": "http://**********:9997/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
    "qwen2-vl-7b-instruct-awq":{
        "base_url": "http://***********:9997/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
    "bge-m3": {
        "base_url": "http://***********:9002/v1/embeddings",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
    "bge-reranker-v2-m3":{
        "base_url": "http://***********:9002/v1/rerank",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
    "whisper-large-v3":{
        "base_url": "http://***********:9020/v1",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
    "cosyvoive-300-sft":{
        "base_url": "http://***********:9020/v1",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0},
}




# 工具测试
# the prompt to force LLM to generate response
FORCE_STOP_PROMPT_EN = """You should directly give results
 based on history information."""

# agent 最大轮次
MAX_TURN = 3

# mino配置
ENDPOINT = "172.1.0.172:9000"
# ACCESS_KEY = config['ACCESS_KEY']
# SECRET_KEY = config['SECRET_KEY']
ACCESS_KEY = 'minioadmin'
SECRET_KEY = 'minioadmin'
MINOBUCKET = "sunklm-dev"
parent_path = os.path.abspath('')
ASR_PATH = os.path.join(parent_path, "logs/asr_logs")

# # tts语音种类
VOICE = '中文女'
# 生成的tts语音存放的minio地址
MINIO_PATH = "agentToolFile/13/"

# 调用asr模型是否调用api接口，若False，直接通过函数调用
IS_ASR_API = False
# 调用tts模型是否调用api接口，若False，直接通过函数调用
IS_TTS_API =False