"""
JSON序列化和反序列化工具组件
提供安全、高效的JSON处理功能，支持自定义编码器和错误处理
"""

import json
import datetime
import decimal
import uuid
import enum
from typing import Any, Dict, List, Optional, Union, Callable, Type
from dataclasses import is_dataclass, asdict
import logging

# 设置日志
logger = logging.getLogger(__name__)


class JsonSerializationError(Exception):
    """JSON序列化异常"""
    pass


class JsonDeserializationError(Exception):
    """JSON反序列化异常"""
    pass


class CustomJsonEncoder(json.JSONEncoder):
    """自定义JSON编码器，支持更多Python数据类型"""
    
    def default(self, obj):
        """处理默认JSON编码器不支持的类型"""
        
        # 处理日期时间类型
        if isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif isinstance(obj, datetime.date):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        
        # 处理Decimal类型
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        
        # 处理UUID类型
        elif isinstance(obj, uuid.UUID):
            return str(obj)
        
        # 处理枚举类型
        elif isinstance(obj, enum.Enum):
            return obj.value
        
        # 处理数据类
        elif is_dataclass(obj):
            return asdict(obj)
        
        # 处理集合类型
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, frozenset):
            return list(obj)
        
        # 处理字节类型
        elif isinstance(obj, bytes):
            try:
                return obj.decode('utf-8')
            except UnicodeDecodeError:
                return obj.hex()
        
        # 处理复数
        elif isinstance(obj, complex):
            return {"real": obj.real, "imag": obj.imag, "__complex__": True}
        
        # 处理有__dict__属性的对象
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        
        # 处理可调用对象
        elif callable(obj):
            return f"<callable: {obj.__name__}>"
        
        # 其他情况转为字符串
        return str(obj)


class JsonUtils:
    """JSON工具类"""
    
    def __init__(self, 
                 ensure_ascii: bool = False,
                 indent: Optional[int] = None,
                 sort_keys: bool = False,
                 custom_encoder: Optional[Type[json.JSONEncoder]] = None,
                 custom_decoder: Optional[Callable] = None):
        """
        初始化JSON工具
        
        Args:
            ensure_ascii: 是否确保ASCII编码
            indent: 缩进空格数
            sort_keys: 是否排序键
            custom_encoder: 自定义编码器
            custom_decoder: 自定义解码器
        """
        self.ensure_ascii = ensure_ascii
        self.indent = indent
        self.sort_keys = sort_keys
        self.custom_encoder = custom_encoder or CustomJsonEncoder
        self.custom_decoder = custom_decoder
    
    def serialize(self, obj: Any, **kwargs) -> str:
        """
        序列化对象为JSON字符串
        
        Args:
            obj: 要序列化的对象
            **kwargs: 额外的json.dumps参数
            
        Returns:
            str: JSON字符串
            
        Raises:
            JsonSerializationError: 序列化失败时抛出
        """
        try:
            # 合并参数
            params = {
                'ensure_ascii': self.ensure_ascii,
                'indent': self.indent,
                'sort_keys': self.sort_keys,
                'cls': self.custom_encoder,
                **kwargs
            }
            
            result = json.dumps(obj, **params)
            logger.debug(f"JSON序列化成功，对象类型: {type(obj).__name__}")
            return result
            
        except Exception as e:
            error_msg = f"JSON序列化失败: {str(e)}"
            logger.error(error_msg)
            raise JsonSerializationError(error_msg) from e
    
    def deserialize(self, json_str: str, **kwargs) -> Any:
        """
        反序列化JSON字符串为Python对象
        
        Args:
            json_str: JSON字符串
            **kwargs: 额外的json.loads参数
            
        Returns:
            Any: Python对象
            
        Raises:
            JsonDeserializationError: 反序列化失败时抛出
        """
        try:
            if not isinstance(json_str, str):
                raise ValueError(f"输入必须是字符串，实际类型: {type(json_str).__name__}")
            
            if not json_str.strip():
                raise ValueError("输入的JSON字符串为空")
            
            # 合并参数
            params = {}
            if self.custom_decoder:
                params['object_hook'] = self.custom_decoder
            params.update(kwargs)
            
            result = json.loads(json_str, **params)
            logger.debug(f"JSON反序列化成功，结果类型: {type(result).__name__}")
            return result
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {str(e)}"
            logger.error(error_msg)
            raise JsonDeserializationError(error_msg) from e
        except Exception as e:
            error_msg = f"JSON反序列化失败: {str(e)}"
            logger.error(error_msg)
            raise JsonDeserializationError(error_msg) from e
    
    def serialize_to_file(self, obj: Any, file_path: str, encoding: str = 'utf-8', **kwargs) -> bool:
        """
        序列化对象并保存到文件
        
        Args:
            obj: 要序列化的对象
            file_path: 文件路径
            encoding: 文件编码
            **kwargs: 额外的序列化参数
            
        Returns:
            bool: 是否成功
        """
        try:
            json_str = self.serialize(obj, **kwargs)
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(json_str)
            logger.info(f"JSON序列化到文件成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"JSON序列化到文件失败: {e}")
            return False
    
    def deserialize_from_file(self, file_path: str, encoding: str = 'utf-8', **kwargs) -> Any:
        """
        从文件读取并反序列化JSON
        
        Args:
            file_path: 文件路径
            encoding: 文件编码
            **kwargs: 额外的反序列化参数
            
        Returns:
            Any: Python对象
            
        Raises:
            JsonDeserializationError: 反序列化失败时抛出
        """
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                json_str = f.read()
            result = self.deserialize(json_str, **kwargs)
            logger.info(f"从文件JSON反序列化成功: {file_path}")
            return result
        except FileNotFoundError:
            error_msg = f"文件不存在: {file_path}"
            logger.error(error_msg)
            raise JsonDeserializationError(error_msg)
        except Exception as e:
            error_msg = f"从文件JSON反序列化失败: {e}"
            logger.error(error_msg)
            raise JsonDeserializationError(error_msg) from e
    
    def is_valid_json(self, json_str: str) -> bool:
        """
        检查字符串是否为有效的JSON格式
        
        Args:
            json_str: 要检查的字符串
            
        Returns:
            bool: 是否为有效JSON
        """
        try:
            json.loads(json_str)
            return True
        except (json.JSONDecodeError, TypeError):
            return False
    
    def pretty_print(self, obj: Any, indent: int = 2) -> str:
        """
        美化打印JSON
        
        Args:
            obj: 要打印的对象
            indent: 缩进空格数
            
        Returns:
            str: 美化后的JSON字符串
        """
        try:
            return self.serialize(obj, indent=indent)
        except Exception as e:
            logger.error(f"JSON美化打印失败: {e}")
            return str(obj)
    
    def minify(self, json_str: str) -> str:
        """
        压缩JSON字符串（移除空白字符）
        
        Args:
            json_str: JSON字符串
            
        Returns:
            str: 压缩后的JSON字符串
        """
        try:
            obj = self.deserialize(json_str)
            return self.serialize(obj, separators=(',', ':'))
        except Exception as e:
            logger.error(f"JSON压缩失败: {e}")
            return json_str
    
    def merge_json_objects(self, *json_objects: Union[str, Dict]) -> Dict:
        """
        合并多个JSON对象
        
        Args:
            *json_objects: JSON字符串或字典对象
            
        Returns:
            Dict: 合并后的字典
        """
        result = {}
        
        for obj in json_objects:
            try:
                if isinstance(obj, str):
                    data = self.deserialize(obj)
                elif isinstance(obj, dict):
                    data = obj
                else:
                    logger.warning(f"跳过不支持的对象类型: {type(obj).__name__}")
                    continue
                
                if isinstance(data, dict):
                    result.update(data)
                else:
                    logger.warning(f"跳过非字典类型的数据: {type(data).__name__}")
                    
            except Exception as e:
                logger.error(f"合并JSON对象时出错: {e}")
                continue
        
        return result


# 创建默认实例
default_json_utils = JsonUtils(ensure_ascii=False, indent=2)

# 便捷函数
def to_json(obj: Any, **kwargs) -> str:
    """将对象序列化为JSON字符串"""
    return default_json_utils.serialize(obj, **kwargs)

def from_json(json_str: str, **kwargs) -> Any:
    """将JSON字符串反序列化为Python对象"""
    return default_json_utils.deserialize(json_str, **kwargs)

def is_valid_json(json_str: str) -> bool:
    """检查字符串是否为有效JSON"""
    return default_json_utils.is_valid_json(json_str)

def pretty_json(obj: Any, indent: int = 2) -> str:
    """美化打印JSON"""
    return default_json_utils.pretty_print(obj, indent)

def minify_json(json_str: str) -> str:
    """压缩JSON字符串"""
    return default_json_utils.minify(json_str)

def save_json(obj: Any, file_path: str, **kwargs) -> bool:
    """保存对象为JSON文件"""
    return default_json_utils.serialize_to_file(obj, file_path, **kwargs)

def load_json(file_path: str, **kwargs) -> Any:
    """从JSON文件加载对象"""
    return default_json_utils.deserialize_from_file(file_path, **kwargs)
