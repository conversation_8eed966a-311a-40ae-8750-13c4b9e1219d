"""
HTTP客户端组件
支持GET/POST/PUT/DELETE/PATCH/HEAD/OPTIONS等HTTP方法
提供同步和异步两种调用方式
"""

import json
import time
import asyncio
import uuid
from typing import Dict, Any, Optional, Union, Tuple
from enum import Enum
import requests
import aiohttp
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class HttpMethod(Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class HttpClient:
    """
    HTTP客户端组件
    支持同步和异步请求，提供重试、超时、日志等功能
    """
    
    def __init__(self, 
                 base_url: str = "",
                 timeout: int = 30,
                 max_retries: int = 3,
                 backoff_factor: float = 0.3,
                 default_headers: Optional[Dict[str, str]] = None,
                 service_name: str = "default",
                 auto_request_id: bool = True):
        """
        初始化HTTP客户端
        
        Args:
            base_url: 基础URL
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
            backoff_factor: 重试间隔因子
            default_headers: 默认请求头
            service_name: 服务名称，用于获取预定义配置
            auto_request_id: 是否自动生成请求ID
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.service_name = service_name
        self.auto_request_id = auto_request_id
        
        # 设置默认请求头
        self.default_headers = default_headers or {
            'Content-Type': 'application/json',
            'User-Agent': 'Awen-Gateway-HttpClient/1.0'
        }
        
        # 配置同步session
        self.session = requests.Session()
        self._setup_retry_strategy()
        
    def _setup_retry_strategy(self):
        """配置重试策略"""
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE", "PATCH"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        if endpoint.startswith(('http://', 'https://')):
            return endpoint
        return f"{self.base_url}/{endpoint.lstrip('/')}" if self.base_url else endpoint
    
    def _merge_headers(self, headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        """合并请求头"""
        merged_headers = self.default_headers.copy()
        if headers:
            merged_headers.update(headers)
        return merged_headers
    
    def _generate_request_id(self) -> str:
        """生成请求ID"""
        return str(uuid.uuid4())
    
    def _prepare_headers(self, headers: Optional[Dict[str, str]], request_id: str = None) -> Dict[str, str]:
        """准备请求头"""
        merged_headers = self._merge_headers(headers)
        
        # 自动添加请求ID
        if self.auto_request_id and request_id:
            merged_headers['X-Request-ID'] = request_id
        
        # 添加服务名称
        if self.service_name != "default":
            merged_headers['X-Service-Name'] = self.service_name
            
        return merged_headers
    
    def _log_request(self, method: str, url: str, headers: Dict, data: Any = None, request_id: str = None):
        """记录请求日志"""
        log_msg = f"HTTP请求 - {method} {url}"
        if request_id:
            log_msg += f" [RequestID: {request_id}]"
        print(f"INFO: {log_msg}")  # 简化日志，避免依赖LogUtils
        
    def _log_response(self, response, start_time: float, request_id: str = None):
        """记录响应日志"""
        duration = time.time() - start_time
        log_msg = f"HTTP响应 - 状态码: {response.status_code}, 耗时: {duration:.3f}s"
        if request_id:
            log_msg += f" [RequestID: {request_id}]"
        print(f"INFO: {log_msg}")
        
        # 记录错误响应
        if response.status_code >= 400:
            print(f"WARNING: HTTP错误响应 - {response.status_code}: {response.text[:500]}")
    
    def request(self, 
                method: Union[HttpMethod, str],
                endpoint: str,
                params: Optional[Dict[str, Any]] = None,
                data: Optional[Union[Dict, str, bytes]] = None,
                json_data: Optional[Dict[str, Any]] = None,
                headers: Optional[Dict[str, str]] = None,
                timeout: Optional[int] = None,
                stream: bool = False,
                request_id: Optional[str] = None,
                raise_for_status: bool = False,
                **kwargs) -> requests.Response:
        """
        发送HTTP请求 (同步)
        
        Args:
            method: HTTP方法
            endpoint: 请求端点
            params: URL参数
            data: 请求体数据
            json_data: JSON数据
            headers: 请求头
            timeout: 超时时间
            stream: 是否流式响应
            request_id: 请求ID，如果不提供且auto_request_id为True则自动生成
            raise_for_status: 是否在HTTP错误时抛出异常
            **kwargs: 其他requests参数
            
        Returns:
            requests.Response: 响应对象
        """
        method_str = method.value if isinstance(method, HttpMethod) else method.upper()
        url = self._build_url(endpoint)
        
        # 生成或使用提供的请求ID
        if not request_id and self.auto_request_id:
            request_id = self._generate_request_id()
        
        # 准备请求头
        merged_headers = self._prepare_headers(headers, request_id)
        timeout = timeout or self.timeout
        
        # 准备请求参数
        request_kwargs = {
            'params': params,
            'headers': merged_headers,
            'timeout': timeout,
            'stream': stream,
            **kwargs
        }
        
        # 处理请求体
        if json_data is not None:
            request_kwargs['json'] = json_data
        elif data is not None:
            request_kwargs['data'] = data
        
        start_time = time.time()
        self._log_request(method_str, url, merged_headers, json_data or data, request_id)
        
        try:
            response = self.session.request(method_str, url, **request_kwargs)
            self._log_response(response, start_time, request_id)
            
            # 可选的状态码检查
            if raise_for_status:
                response.raise_for_status()
                
            return response
        except Exception as e:
            error_msg = f"HTTP请求失败 - {method_str} {url}"
            if request_id:
                error_msg += f" [RequestID: {request_id}]"
            error_msg += f": {str(e)}"
            print(f"ERROR: {error_msg}")
            raise
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """GET请求"""
        return self.request(HttpMethod.GET, endpoint, params=params, **kwargs)
    
    def post(self, endpoint: str, data: Optional[Union[Dict, str]] = None, 
             json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """POST请求"""
        return self.request(HttpMethod.POST, endpoint, data=data, json_data=json_data, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Union[Dict, str]] = None,
            json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """PUT请求"""
        return self.request(HttpMethod.PUT, endpoint, data=data, json_data=json_data, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> requests.Response:
        """DELETE请求"""
        return self.request(HttpMethod.DELETE, endpoint, **kwargs)
    
    def patch(self, endpoint: str, data: Optional[Union[Dict, str]] = None,
              json_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """PATCH请求"""
        return self.request(HttpMethod.PATCH, endpoint, data=data, json_data=json_data, **kwargs)
    
    def head(self, endpoint: str, **kwargs) -> requests.Response:
        """HEAD请求"""
        return self.request(HttpMethod.HEAD, endpoint, **kwargs)
    
    def options(self, endpoint: str, **kwargs) -> requests.Response:
        """OPTIONS请求"""
        return self.request(HttpMethod.OPTIONS, endpoint, **kwargs)
    
    def is_success(self, response: requests.Response) -> bool:
        """检查响应是否成功"""
        return 200 <= response.status_code < 300
    
    def get_json(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET请求并返回JSON数据"""
        response = self.get(endpoint, **kwargs)
        response.raise_for_status()
        return response.json()
    
    def post_json(self, endpoint: str, json_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """POST JSON数据并返回JSON响应"""
        response = self.post(endpoint, json_data=json_data, **kwargs)
        response.raise_for_status()
        return response.json()
    
    def upload_file(self, endpoint: str, file_path: str, field_name: str = "file", 
                   additional_data: Optional[Dict[str, Any]] = None, **kwargs) -> requests.Response:
        """上传文件"""
        with open(file_path, 'rb') as f:
            files = {field_name: f}
            data = additional_data or {}
            # 移除Content-Type让requests自动设置
            headers = kwargs.get('headers', {}).copy()
            headers.pop('Content-Type', None)
            kwargs['headers'] = headers
            return self.post(endpoint, data=data, files=files, **kwargs)
    
    def download_file(self, endpoint: str, save_path: str, **kwargs) -> bool:
        """下载文件"""
        try:
            response = self.get(endpoint, stream=True, **kwargs)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            return True
        except Exception as e:
            print(f"ERROR: 文件下载失败: {e}")
            return False
    
    def close(self):
        """关闭session"""
        self.session.close()


# 便捷函数
def create_http_client(base_url: str = "", service_name: str = "default", **kwargs) -> HttpClient:
    """创建HTTP客户端实例"""
    return HttpClient(base_url=base_url, service_name=service_name, **kwargs)


def create_service_client(service_name: str, **kwargs) -> HttpClient:
    """根据服务名称创建HTTP客户端"""
    # 服务端点映射
    service_endpoints = {
        "agent": "http://172.1.0.154:9081",
        "mcp": "http://172.1.0.154:9082", 
        "knowledge": "http://172.1.0.154:8014",
        "retrieval": "http://172.1.0.154:8013",
        "ocr": "http://172.1.0.154:9011",
        "llm": "http://172.1.0.134:9020",
        "gateway": "http://127.0.0.1:9080"
    }
    
    service_url = service_endpoints.get(service_name.lower(), "")
    return create_http_client(base_url=service_url, service_name=service_name, **kwargs)


def create_llm_client(**kwargs) -> HttpClient:
    """创建LLM服务客户端"""
    return create_service_client("llm", **kwargs)


def create_agent_client(**kwargs) -> HttpClient:
    """创建Agent服务客户端"""
    return create_service_client("agent", **kwargs)


def create_mcp_client(**kwargs) -> HttpClient:
    """创建MCP服务客户端"""
    return create_service_client("mcp", **kwargs)
