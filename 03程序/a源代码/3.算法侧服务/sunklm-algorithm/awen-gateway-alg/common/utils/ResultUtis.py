import logging
from logging.handlers import TimedRotatingFileHandler
import os
import numpy
import json
from enum import IntEnum
from datetime import datetime, date

class ReturnCode(IntEnum):
    SUCESS = 0
    ERROR = 1


class NpEncoder(json.JSONEncoder):
    """
    该类为对多种数据类型格式化
    """
    def default(self, obj):
        if isinstance(obj, (numpy.int_, numpy.intc, numpy.intp, numpy.int8,
                            numpy.int16, numpy.int32, numpy.int64, numpy.uint8,
                            numpy.uint16, numpy.uint32, numpy.uint64)):
            return int(obj)
        elif isinstance(obj, (numpy.float16, numpy.float32,
                              numpy.float64)):
            return float(obj)
        elif isinstance(obj, numpy.ndarray):
            return obj.tolist()
            # Handle datetime objects
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def return_dict(code, data, message):
    """
    输出统一的算法服务格式
    :param code: 算法状态码
    :param data: 算法返回结果
    :param message: 算法报错信息，若无报错，为''
    :return:
    """
    return json.dumps({"retMsg": message, "retCode": code, "retMap": data},
                      cls=NpEncoder, indent=4)