import uvicorn
from fastapi import FastAPI,Request
from gateway.controller.GatewayController import item_router
from common.conf.ServerConfig import ServerConfig

settings = ServerConfig
# 初始化应用
app = FastAPI(title="API Gateway", description="统一接口网关")


#包含路由控制器 - 添加限流装饰器
app.include_router(
    item_router,
    prefix="/ai",
    tags=["AI网关"]
)

if __name__ == "__main__":
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=9080
    )

    server = uvicorn.Server(config)
    server.run()
