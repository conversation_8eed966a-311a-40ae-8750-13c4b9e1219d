import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))

import json
import asyncio
from asyncio import Lock
from typing import Optional
from mcp import ClientSession
from fastapi import FastAPI
from fastapi.responses import J<PERSON>NResponse

from common.utils.McpClientPool import MC<PERSON>lient<PERSON>ool
from gateway.dao.GatewayDao import GateWayDao
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict,ReturnCode


# 封装数据库初始化函数，方便管理和扩展
def get_gateway_dao():
    return GateWayDao()


gateWayDao = get_gateway_dao()

# 初始化mcp连接池及锁
mcp_pool_lock = Lock()
mcp_pool = MCPClientPool(max_connections_per_url=10, max_total_threads=30)


class MCPClient:
    def __init__(self, server_url: str):
        self.session: Optional[ClientSession] = None
        self.tool_mapping = {}  # 工具映射：prefixed_name -> (session, original_tool_name)
        self.server_url = server_url

    def getToolMap(self) -> dict:
        """获取工具映射关系"""
        data = gateWayDao.get_tools()
        for tool in data:
            toolId = tool[0]
            toolName = tool[1]
            mcpName = tool[2]
            self.tool_mapping[toolId] = {
                "mcpName": mcpName,
                "toolName": toolName,
                "isMcp": int(tool[3]) if len(tool) > 3 else 1
            }
        return self.tool_mapping

    async def call_tool(self, tool_id, tool_args):
        """
        调用工具。
        """
        try:
            try:
                self.session = await mcp_pool.get_session(self.server_url, server_id="default")
            except Exception as e:
                logger.error(f"[MCP ERROR] 获取连接失败: {e}")
                raise
            toolMap = self.getToolMap()
            tool = toolMap.get(tool_id)

            if tool is None:
                return return_dict(str(ReturnCode.ERROR.value), None, "未查询到对应工具")

            if tool['isMcp'] == 0:
                logger.info(f"【Service】【MCP调用】tool:http_function,输入:{tool_args}")
                # 参数处理
                input_data = {}
                input = {}
                for key, value in tool_args.items():
                    if key == "url" or key == "headers" or key == "method":
                        input_data[key] = value
                    else:
                        input[key] = value
                input_data['input'] = input
                logger.info(f"【Service】【MCP调用】tool:http_function,格式化:{input_data}")
                try:
                    result = await self.session.call_tool("http_function", input_data)
                except Exception as e:
                    logger.error(f"[MCP ERROR] http_function 调用失败: {e}")
                    raise
                if isinstance(result.content, list):
                    try:
                        json_list = [json.loads(item.text) for item in result.content if hasattr(item, "text")]
                        processed_result = json.dumps(json_list, ensure_ascii=False)
                    except Exception as e:
                        processed_result = result.content[0].text
                else:
                    processed_result = result.content
                return processed_result

            mcpName = tool['mcpName']
            logger.info(f"【Service】【MCP调用】tool:{mcpName},输入:{tool_args}")
            try:
                result = await self.session.call_tool(mcpName, tool_args)
            except Exception as e:
                logger.error(f"[MCP ERROR] {mcpName} 调用失败: {e}")
                raise
            if isinstance(result.content, list):
                try:
                    json_list = [json.loads(item.text) for item in result.content if hasattr(item, "text")]
                    processed_result = json.dumps(json_list, ensure_ascii=False)
                except Exception as e:
                    processed_result = result.content[0].text
            else:
                processed_result = result.content[0].text
            return processed_result
        except Exception as e:
            logger.error(f"【Service】【MCP调用】异常:{e}")
        finally:
            logger.info("call_tool 结束")


async def call_tool_id(serverUrl, toolId, toolArgs):
    mcp = MCPClient(serverUrl)
    # 异步初始化会话
    try:
        res = await mcp.call_tool(toolId, toolArgs)
        return res
    except Exception as e:
        return "调用异常"


def call_mcp_tool(request):
    try:
        res = asyncio.run(
            call_tool_id(
                request.get("serverUrl"),
                request.get("toolId"),
                request.get("toolArgs")
            )
        )
        if not res:
            return {"error": "调用返回为空"}
        return json.loads(res)
    except Exception as e:
        print(f"Error occurred: {e}")
        return None


def demo_test():
    serverUrl = "http://172.1.0.154:9085/sse"
    toolId = ""
    toolArgs = {
        "input": {
            "serviceYear": 2,
            "profession": "计算机"
        },
        "url": "http://172.1.1.217:8115/SunAPI/getPersonInfos",
        "headers": {
            "Content-Type": "application/json"
        },
        "method": "POST"
    }

    req = {
        "serverUrl": serverUrl,
        "toolId": toolId,
        "toolArgs": toolArgs
    }

    print(asyncio.run(call_tool_id(serverUrl, toolId, toolArgs)))
    print(call_mcp_tool(req))


if __name__ == "__main__":
    demo_test()

app = FastAPI()


@app.get("/monitor/connection_pool")
async def monitor_connection_pool():
    try:
        status = mcp_pool.status()
        return JSONResponse(content={"status": "ok", "details": status})
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})
