from mcp.types import ListToolsResult
import json

def convert(tool_list: ListToolsResult) -> dict:
    """
    将工具列表转换为可JSON序列化的字典
    
    Args:
        tool_list: MCP工具列表对象
        
    Returns:
        可JSON序列化的字典
    """
    result = {
        "tools": []
    }
    
    for tool in tool_list.tools:
        tool_dict = {
            "name": tool.name,
            "description": tool.description,
            "inputSchema": _convert_input_schema(tool.inputSchema)
        }
        result["tools"].append(tool_dict)

    result = json.dumps(result, ensure_ascii=False, indent=2)        
    return result

def _convert_input_schema(input_schema) -> list:
    """
    将inputSchema转换为可序列化的字典列表
    处理可能的嵌套结构和复杂类型
    """
    if not input_schema:
        return []
    
    # 处理列表形式的inputSchema
    if isinstance(input_schema, list):
        return [_convert_parameter(param) for param in input_schema]
    
    # 处理字典形式的inputSchema
    elif isinstance(input_schema, dict):
        # 假设这是一个对象类型的schema
        return [{"name": "input", "schema": _convert_dict_schema(input_schema)}]
    
    # 未知类型，尝试转换为字符串描述
    return [{"name": "unknown", "description": str(input_schema)}]

def _convert_parameter(param) -> dict:
    """将单个参数对象转换为字典"""
    # 处理普通对象
    if hasattr(param, '__dict__'):
        param_dict = {
            "name": getattr(param, "name", "unknown"),
            "type": str(getattr(param, "type", "unknown")),
            "description": getattr(param, "description", ""),
            "required": getattr(param, "required", False),
        }
        
        # 处理默认值
        if hasattr(param, "default"):
            param_dict["default"] = param.default
            
        # 处理可选值
        if hasattr(param, "enum"):
            param_dict["enum"] = list(param.enum)
            
        # 处理嵌套结构
        if hasattr(param, "properties"):
            param_dict["properties"] = [_convert_parameter(p) for p in param.properties]
            
        if hasattr(param, "items"):
            param_dict["items"] = _convert_parameter(param.items)
            
        return param_dict
    
    # 处理字典形式的参数
    elif isinstance(param, dict):
        return _convert_dict_schema(param)
    
    # 其他类型
    return {"name": "unknown", "value": str(param)}

def _convert_dict_schema(schema: dict) -> dict:
    """处理字典形式的schema定义"""
    converted = schema.copy()
    
    # 递归处理properties
    if "properties" in schema:
        converted["properties"] = {
            name: _convert_dict_schema(prop)
            for name, prop in schema["properties"].items()
        }
    
    # 递归处理items
    if "items" in schema:
        converted["items"] = _convert_dict_schema(schema["items"])
    
    return converted    
