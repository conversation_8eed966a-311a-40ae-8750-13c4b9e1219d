"""
文档处理服务
支持读取Word、PDF、TXT、PPT、Excel等文件，并将内容结构化为Markdown格式输出
"""

import os
import re
import requests
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import tempfile

from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict, ReturnCode

# 导入文档处理库
try:
    import docx
    from docx.document import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("python-docx未安装，无法处理Word文档")

try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logger.warning("PyPDF2或pdfplumber未安装，无法处理PDF文档")

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False
    logger.warning("python-pptx未安装，无法处理PowerPoint文档")

try:
    import pandas as pd
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("pandas未安装，无法处理Excel文档")


class DocumentProcessor:
    """文档处理服务类"""
    
    def __init__(self):
        """初始化文档处理服务"""
        self.supported_formats = {
            '.txt': self._process_txt,
            '.docx': self._process_docx,
            '.doc': self._process_docx,
            '.pdf': self._process_pdf,
            '.pptx': self._process_pptx,
            '.ppt': self._process_pptx,
            '.xlsx': self._process_excel,
            '.xls': self._process_excel,
            '.csv': self._process_csv
        }
        logger.info("【Service】【文档处理】服务初始化完成")
    
    def process_document(self, request: Dict[str, Any]) -> str:
        """
        处理文档的主要方法
        
        Args:
            request: 请求参数字典
                - file_path: 本地文件路径 (可选)
                - file_url: 文件URL (可选)
                - content: 直接传入的文本内容 (可选)
                - format_type: 文件格式类型 (可选，自动检测)
                - structure_level: 结构化级别 (可选，默认为'medium')
                    - 'basic': 基础结构化
                    - 'medium': 中等结构化
                    - 'advanced': 高级结构化
                
        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【文档处理】输入参数: {request}")
        
        try:
            # 获取输入参数
            file_path = request.get("file_path")
            file_url = request.get("file_url")
            content = request.get("content")
            format_type = request.get("format_type")
            structure_level = request.get("structure_level", "medium")
            
            # 参数验证
            if not any([file_path, file_url, content]):
                error_msg = "必须提供file_path、file_url或content中的一个参数"
                logger.error(f"【Service】【文档处理】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)
            
            # 处理文档内容
            if content:
                # 直接处理文本内容
                raw_content = content
                file_format = format_type or '.txt'
            elif file_url:
                # 从URL下载文件并处理
                raw_content, file_format = self._download_and_process(file_url, format_type)
            else:
                # 处理本地文件
                raw_content, file_format = self._process_local_file(file_path, format_type)
            
            # 结构化处理
            markdown_content = self._structure_content(raw_content, structure_level)
            
            # 构建返回结果
            result_data = {
                "markdown_content": markdown_content,
                "original_length": len(raw_content),
                "markdown_length": len(markdown_content),
                "file_format": file_format,
                "structure_level": structure_level,
                "processing_info": {
                    "sections_detected": len(re.findall(r'^#+\s', markdown_content, re.MULTILINE)),
                    "paragraphs_count": len(re.findall(r'\n\n', markdown_content)),
                    "lists_count": len(re.findall(r'^\s*[-*+]\s', markdown_content, re.MULTILINE))
                }
            }
            
            logger.info(f"【Service】【文档处理】成功，原文长度: {len(raw_content)}, Markdown长度: {len(markdown_content)}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "文档处理成功")
            
        except Exception as e:
            error_msg = f"文档处理失败: {str(e)}"
            logger.error(f"【Service】【文档处理】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)
    
    def _download_and_process(self, file_url: str, format_type: Optional[str] = None) -> tuple:
        """从URL下载文件并处理"""
        try:
            # 下载文件
            response = requests.get(file_url, timeout=30)
            response.raise_for_status()
            
            # 确定文件格式
            if not format_type:
                parsed_url = urlparse(file_url)
                file_ext = os.path.splitext(parsed_url.path)[1].lower()
                format_type = file_ext or '.txt'
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=format_type, delete=False) as temp_file:
                temp_file.write(response.content)
                temp_path = temp_file.name
            
            try:
                # 处理临时文件
                content, file_format = self._process_local_file(temp_path, format_type)
                return content, file_format
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            raise Exception(f"下载或处理URL文件失败: {e}")
    
    def _process_local_file(self, file_path: str, format_type: Optional[str] = None) -> tuple:
        """处理本地文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 确定文件格式
        if not format_type:
            file_ext = os.path.splitext(file_path)[1].lower()
            format_type = file_ext
        
        # 检查是否支持该格式
        if format_type not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {format_type}")
        
        # 调用相应的处理方法
        processor = self.supported_formats[format_type]
        content = processor(file_path)
        
        return content, format_type
    
    def _process_txt(self, file_path: str) -> str:
        """处理TXT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise Exception("无法解码文本文件")
    
    def _process_docx(self, file_path: str) -> str:
        """处理Word文档"""
        if not DOCX_AVAILABLE:
            raise Exception("python-docx未安装，无法处理Word文档")
        
        try:
            doc = docx.Document(file_path)
            content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text)
            
            # 处理表格
            for table in doc.tables:
                table_content = []
                for row in table.rows:
                    row_content = []
                    for cell in row.cells:
                        row_content.append(cell.text.strip())
                    table_content.append(" | ".join(row_content))
                content.append("\n".join(table_content))
            
            return "\n\n".join(content)
            
        except Exception as e:
            raise Exception(f"处理Word文档失败: {e}")
    
    def _process_pdf(self, file_path: str) -> str:
        """处理PDF文件"""
        if not PDF_AVAILABLE:
            raise Exception("PDF处理库未安装，无法处理PDF文档")
        
        try:
            content = []
            
            # 优先使用pdfplumber
            try:
                import pdfplumber
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        text = page.extract_text()
                        if text:
                            content.append(text)
            except:
                # 备用PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text = page.extract_text()
                        if text:
                            content.append(text)
            
            return "\n\n".join(content)
            
        except Exception as e:
            raise Exception(f"处理PDF文档失败: {e}")
    
    def _process_pptx(self, file_path: str) -> str:
        """处理PowerPoint文档"""
        if not PPTX_AVAILABLE:
            raise Exception("python-pptx未安装，无法处理PowerPoint文档")
        
        try:
            prs = Presentation(file_path)
            content = []
            
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_content = [f"幻灯片 {slide_num}"]
                
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content.append(shape.text)
                
                content.append("\n".join(slide_content))
            
            return "\n\n".join(content)
            
        except Exception as e:
            raise Exception(f"处理PowerPoint文档失败: {e}")
    
    def _process_excel(self, file_path: str) -> str:
        """处理Excel文件"""
        if not EXCEL_AVAILABLE:
            raise Exception("pandas未安装，无法处理Excel文档")
        
        try:
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            content = []
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                sheet_content = [f"工作表: {sheet_name}"]
                
                # 转换为文本格式
                if not df.empty:
                    # 添加列标题
                    headers = " | ".join(str(col) for col in df.columns)
                    sheet_content.append(headers)
                    sheet_content.append("-" * len(headers))
                    
                    # 添加数据行
                    for _, row in df.iterrows():
                        row_text = " | ".join(str(val) for val in row.values)
                        sheet_content.append(row_text)
                
                content.append("\n".join(sheet_content))
            
            return "\n\n".join(content)
            
        except Exception as e:
            raise Exception(f"处理Excel文档失败: {e}")
    
    def _process_csv(self, file_path: str) -> str:
        """处理CSV文件"""
        if not EXCEL_AVAILABLE:
            raise Exception("pandas未安装，无法处理CSV文档")
        
        try:
            df = pd.read_csv(file_path)
            content = []
            
            if not df.empty:
                # 添加列标题
                headers = " | ".join(str(col) for col in df.columns)
                content.append(headers)
                content.append("-" * len(headers))
                
                # 添加数据行
                for _, row in df.iterrows():
                    row_text = " | ".join(str(val) for val in row.values)
                    content.append(row_text)
            
            return "\n".join(content)
            
        except Exception as e:
            raise Exception(f"处理CSV文档失败: {e}")

    def _structure_content(self, content: str, level: str = "medium") -> str:
        """
        将文本内容结构化为Markdown格式

        Args:
            content: 原始文本内容
            level: 结构化级别 ('basic', 'medium', 'advanced')

        Returns:
            str: Markdown格式的内容
        """
        if not content or not content.strip():
            return "# 空文档\n\n文档内容为空。"

        # 清理内容
        content = self._clean_content(content)

        if level == "basic":
            return self._basic_structure(content)
        elif level == "medium":
            return self._medium_structure(content)
        elif level == "advanced":
            return self._advanced_structure(content)
        else:
            return self._medium_structure(content)

    def _clean_content(self, content: str) -> str:
        """清理文本内容"""
        # 移除多余的空白字符
        content = re.sub(r'\r\n', '\n', content)
        content = re.sub(r'\r', '\n', content)
        content = re.sub(r'\n{3,}', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)
        content = re.sub(r'[ \t]*\n[ \t]*', '\n', content)

        return content.strip()

    def _basic_structure(self, content: str) -> str:
        """基础结构化处理"""
        lines = content.split('\n')
        result = []

        # 添加文档标题
        result.append("# 文档内容\n")

        current_paragraph = []

        for line in lines:
            line = line.strip()

            if not line:
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')
            else:
                current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            result.append(' '.join(current_paragraph))

        return '\n'.join(result)

    def _medium_structure(self, content: str) -> str:
        """中等结构化处理"""
        lines = content.split('\n')
        result = []

        # 添加文档标题
        result.append("# 文档内容\n")

        current_paragraph = []

        for line in lines:
            line = line.strip()

            if not line:
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')
                continue

            # 检测标题
            if self._is_title(line):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                # 根据标题特征确定级别
                if self._is_main_title(line):
                    result.append(f"## {line}\n")
                else:
                    result.append(f"### {line}\n")

            # 检测列表项
            elif self._is_list_item(line):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                # 清理列表项格式
                clean_item = self._clean_list_item(line)
                result.append(f"- {clean_item}")

            # 检测数字编号
            elif self._is_numbered_item(line):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                # 提取编号和内容
                number, item_content = self._extract_numbered_item(line)
                result.append(f"{number}. {item_content}")

            # 普通文本
            else:
                current_paragraph.append(line)

        # 处理最后一个段落
        if current_paragraph:
            result.append(' '.join(current_paragraph))

        return '\n'.join(result)

    def _advanced_structure(self, content: str) -> str:
        """高级结构化处理"""
        lines = content.split('\n')
        result = []

        # 检测文档标题
        doc_title = self._detect_document_title(lines)
        result.append(f"# {doc_title}\n")

        # 检测目录结构
        sections = self._detect_sections(lines)

        if sections:
            result.append("## 目录\n")
            for section in sections:
                level = section.get('level', 1)
                title = section.get('title', '')
                indent = "  " * (level - 1)
                result.append(f"{indent}- [{title}](#{title.lower().replace(' ', '-')})")
            result.append('')

        # 处理内容
        current_paragraph = []
        in_code_block = False
        in_table = False
        table_rows = []

        for line in lines:
            line_stripped = line.strip()

            # 检测代码块
            if self._is_code_block(line_stripped):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                if not in_code_block:
                    result.append("```")
                    in_code_block = True
                else:
                    result.append("```\n")
                    in_code_block = False
                continue

            if in_code_block:
                result.append(line)
                continue

            # 检测表格
            if self._is_table_row(line_stripped):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                if not in_table:
                    in_table = True
                    table_rows = []

                table_rows.append(line_stripped)
                continue
            else:
                if in_table:
                    # 结束表格处理
                    result.extend(self._format_table(table_rows))
                    result.append('')
                    in_table = False
                    table_rows = []

            if not line_stripped:
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')
                continue

            # 其他处理逻辑与medium_structure类似
            if self._is_title(line_stripped):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                if self._is_main_title(line_stripped):
                    result.append(f"## {line_stripped}\n")
                else:
                    result.append(f"### {line_stripped}\n")

            elif self._is_list_item(line_stripped):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                clean_item = self._clean_list_item(line_stripped)
                result.append(f"- {clean_item}")

            elif self._is_numbered_item(line_stripped):
                if current_paragraph:
                    result.append(' '.join(current_paragraph))
                    current_paragraph = []
                    result.append('')

                number, item_content = self._extract_numbered_item(line_stripped)
                result.append(f"{number}. {item_content}")

            else:
                current_paragraph.append(line_stripped)

        # 处理最后的内容
        if in_table and table_rows:
            result.extend(self._format_table(table_rows))

        if current_paragraph:
            result.append(' '.join(current_paragraph))

        return '\n'.join(result)

    # 辅助方法
    def _is_title(self, line: str) -> bool:
        """检测是否为标题"""
        # 检测常见标题模式
        title_patterns = [
            r'^第[一二三四五六七八九十\d]+[章节部分]',  # 第一章、第二节等
            r'^\d+[\.\s]',  # 1. 或 1 开头
            r'^[一二三四五六七八九十]+[\.\s]',  # 一、二、等
            r'^[A-Z][A-Z\s]{2,}$',  # 全大写标题
            r'^.{1,50}[：:]$',  # 以冒号结尾的短行
        ]

        for pattern in title_patterns:
            if re.match(pattern, line):
                return True

        # 检测短行且包含关键词
        if len(line) < 50 and any(keyword in line for keyword in ['概述', '介绍', '总结', '结论', '背景', '目标', '方法']):
            return True

        return False

    def _is_main_title(self, line: str) -> bool:
        """检测是否为主标题"""
        main_patterns = [
            r'^第[一二三四五六七八九十\d]+章',
            r'^\d+\.',
            r'^[一二三四五六七八九十]+、',
        ]

        for pattern in main_patterns:
            if re.match(pattern, line):
                return True

        return False

    def _is_list_item(self, line: str) -> bool:
        """检测是否为列表项"""
        list_patterns = [
            r'^[-*+•]\s',  # - * + • 开头
            r'^[○●◦◉]\s',  # 圆点符号
            r'^\([a-zA-Z0-9]+\)\s',  # (1) (a) 等
            r'^[a-zA-Z]\)\s',  # a) b) 等
        ]

        for pattern in list_patterns:
            if re.match(pattern, line):
                return True

        return False

    def _is_numbered_item(self, line: str) -> bool:
        """检测是否为编号项"""
        return bool(re.match(r'^\d+[\.\)]\s', line))

    def _clean_list_item(self, line: str) -> str:
        """清理列表项格式"""
        # 移除列表标记
        cleaned = re.sub(r'^[-*+•○●◦◉]\s*', '', line)
        cleaned = re.sub(r'^\([a-zA-Z0-9]+\)\s*', '', cleaned)
        cleaned = re.sub(r'^[a-zA-Z]\)\s*', '', cleaned)
        return cleaned.strip()

    def _extract_numbered_item(self, line: str) -> tuple:
        """提取编号项的编号和内容"""
        match = re.match(r'^(\d+)[\.\)]\s*(.+)', line)
        if match:
            return match.group(1), match.group(2).strip()
        return "1", line.strip()

    def _detect_document_title(self, lines: List[str]) -> str:
        """检测文档标题"""
        # 查找前几行中的标题
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            if line and len(line) < 100:
                # 检查是否像标题
                if not self._is_list_item(line) and not self._is_numbered_item(line):
                    if len(line.split()) <= 10:  # 标题通常较短
                        return line

        return "文档"

    def _detect_sections(self, lines: List[str]) -> List[Dict[str, Any]]:
        """检测章节结构"""
        sections = []

        for line in lines:
            line = line.strip()
            if self._is_title(line):
                level = 1 if self._is_main_title(line) else 2
                sections.append({
                    'title': line,
                    'level': level
                })

        return sections[:10]  # 限制目录长度

    def _is_code_block(self, line: str) -> bool:
        """检测代码块标记"""
        return line.startswith('```') or line.startswith('~~~')

    def _is_table_row(self, line: str) -> bool:
        """检测表格行"""
        # 检测包含多个 | 分隔符的行
        return '|' in line and line.count('|') >= 2

    def _format_table(self, table_rows: List[str]) -> List[str]:
        """格式化表格"""
        if not table_rows:
            return []

        result = []

        # 添加表头
        if table_rows:
            result.append(table_rows[0])
            # 添加分隔行
            col_count = table_rows[0].count('|') + 1
            separator = '|' + '---|' * (col_count - 1)
            result.append(separator)

            # 添加数据行
            for row in table_rows[1:]:
                result.append(row)

        return result


# 创建全局实例
document_processor = DocumentProcessor()

# 便捷函数
def process_document(request: Dict[str, Any]) -> str:
    """便捷的文档处理函数"""
    return document_processor.process_document(request)
