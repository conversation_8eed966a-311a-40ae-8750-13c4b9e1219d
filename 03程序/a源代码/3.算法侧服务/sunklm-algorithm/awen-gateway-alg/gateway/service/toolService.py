import asyncio
import sys
import os
import json
from random import choices
from typing import Optional
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from anthropic import Anthropic
from dotenv import load_dotenv
from openai import OpenAI
from common.utils.LogUtils import logger
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))
from gateway.service.convert import convert
from mcp.types import ListToolsResult
from common.utils.ResultUtis import return_dict,ReturnCode

class McpService:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.messages = []

    async def connect_to_server(self, server_path: str) -> ListToolsResult:
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        # await self.session.initialize()
        # 创建 SSE 客户端连接上下文管理器
        self._streams_context = sse_client(url=server_path)
        # 异步初始化 SSE 连接，获取数据流对象
        streams = await self._streams_context.__aenter__()

        # 使用数据流创建 MCP 客户端会话上下文
        self._session_context = ClientSession(*streams)
        # 初始化客户端会话对象
        self.session: ClientSession = await self._session_context.__aenter__()

        # 执行 MCP 协议初始化握手
        await self.session.initialize()
        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        logger.info("\nConnected to server with tools:", [tool.name for tool in tools])
        return response

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

def get_tool_list(request) -> str:
    logger.info("get tool list")
    logger.info(request)
    logger.info(request.get("serverUrl"))
    res = asyncio.run(gettoollist(request.get("serverUrl")))
    return json.loads(res)

    # MCP工具处理
async def gettoollist(server_path):
    global res
    client = McpService()
    logger.info(f"gettoollist")
    try:
        logger.info(f"client connect to server")
        res = await client.connect_to_server(server_path)
        res = convert(res)
    except  Exception as e:
        logger.error(f"【Service】【MCP-工具处理】异常:{e}")
    finally:
        await client.cleanup()
    return res

    