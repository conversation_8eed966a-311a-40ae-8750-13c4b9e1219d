"""
-----------------------------------------------------
   File Name： llm_base_service.py
   Description : 文本改写
   Author :  mhxuan
   date： 2024/8/13 18:05
   Change Activity:  增加假设问题改写
-----------------------------------------------------
"""
from typing import Optional, Dict

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from common.constants import KnowledgePromptConstants
from common.constants.KnowledgeChat import ModelAPI
from common.conf.ServerConfig import LLM_NAME, LLM_MAP
REWRITER_PROMPT = ChatPromptTemplate.from_template(KnowledgePromptConstants.REWRITER_PROMPT)
HYDE_PROMPT = ChatPromptTemplate.from_template(KnowledgePromptConstants.HYDE_PROMPT)
CONTEXTUAL_PROMPT = ChatPromptTemplate.from_template(KnowledgePromptConstants.CONTEXTUAL_PROMPT1)
CONSISTENCY_PROMPT = ChatPromptTemplate.from_template(KnowledgePromptConstants.CONSISTENCY_PROMPT)
FAQ_PROMPT = ChatPromptTemplate.from_template(KnowledgePromptConstants.FAQ_PROMPT)

from common.utils.LogUtils import logger

# logger = logger.bind(name="llm_base_server")
# EVAL_PROMPT = ChatPromptTemplate.from_template()
class PromptOptimizerService:
    """
    提示词优化服务核心类
    """
    def __init__(
            self,
            model_type: str = LLM_NAME,
            base_url: str = LLM_MAP[LLM_NAME]["base_url"],
            api_key: str = LLM_MAP[LLM_NAME]["api_key"],
            company: str = LLM_MAP[LLM_NAME]["company"],
            deploy_mode: int = LLM_MAP[LLM_NAME]["deploy_mode"]
    ):
        self.model_type = model_type
        self.llm = ModelAPI(model_type=model_type,
                           api_key=api_key,
                           company=company,
                           deploy_mode=deploy_mode,
                           base_url=base_url)

    def calculate_similarity(self, prompt: str, template: str) -> float:
        """计算提示词与模板的相似度"""
        if not prompt or not template:
            return 0.0
        prompt_words = set(prompt.strip().split())
        template_words = set(template.strip().split())
        common = prompt_words & template_words
        union = prompt_words | template_words
        return len(common) / (len(union) + 1e-6)

    def match_best_template(self, user_prompt: str, templates: Dict[str, str]) -> Dict:
        """匹配最佳模板"""
        best_match = None
        highest_score = 0.0

        for name, template in templates.items():
            score = self.calculate_similarity(user_prompt, template)
            if score > highest_score:
                highest_score = score
                best_match = {
                    "template_name": name,
                    "template_content": template,
                    "similarity_score": round(score, 4)
                }
        return best_match or {
            "template_name": "DEFAULT_TEMPLATE",
            "template_content": "",
            "similarity_score": 0.0
        }

    def optimize_prompt(
            self,
            prompt: str,
            optimization_type: int = 1,
            feedback: str = "",
            expectation: str = "",
            requirements: str = "",
            templates: Optional[Dict[str, str]] = None
    ) -> Dict:
        """
        核心优化方法
        Args:
            prompt: 原始提示词
            optimization_type: 优化类型(1-自动优化/2-反馈优化/3-自定义优化)
            feedback: 用户反馈
            expectation: 期望改进方向
            requirements: 自定义优化需求
            templates: 可选模板字典
        Returns:
            优化后的提示词结果
        """
        # 1. 匹配模板（如果有提供）
        template_info = {}
        if templates:
            template_info = self.match_best_template(prompt, templates)

            # 2. 构造查询内容
            query_map = {
                1: f"优化以下提示词:\n{prompt}\n并根据优化结果生成3个建议问题",
                2: f"根据用户反馈优化提示词:\n原始提示词:{prompt}\n反馈:{feedback}\n期望:{expectation}\n并根据优化结果生成3个建议问题",
                3: f"根据自定义需求优化提示词:\n原始提示词:{prompt}\n需求:{requirements}\n并根据优化结果生成3个建议问题"
            }
            query = query_map.get(optimization_type, query_map[1])

            if template_info.get("template_content"):
                query = f"参考以下模板优化提示词:\n模板:{template_info['template_content']}\n{query}"
            logger.info(f"query:{query}")
            # 3. 调用模型
            result = self.llm.generate(query)

            return {
                "optimized_prompt": result.get("answer", ""),
                "template_info": template_info,
                "optimization_type": optimization_type
            }

class QueryLLMService:
    """
    Autor：mhxuan
    Description: 基于大模型实现的基础服务，目前实现问题改写、FAQ抽取、后处理服务

    Args:
        model_type (str): 模型类别
        base_url (str): 模型地址
        api_key (str): key
        company(str)：: 模型公司
    """

    def __init__(
            self,
            model_type: str = LLM_NAME,
            base_url: str = LLM_MAP[LLM_NAME]["base_url"],
            api_key: str = LLM_MAP[LLM_NAME]["api_key"],
            company: str = LLM_MAP[LLM_NAME]["company"],
            deploy_mode: int = LLM_MAP[LLM_NAME]["deploy_mode"]
    ):
        self.model_type = model_type
        self.base_url = base_url
        self.api_key = api_key
        self.company = company
        self.deploy_mode = deploy_mode
        self.llm = ModelAPI(model_type=self.model_type,
                            api_key=self.api_key,
                            company=self.company,
                            deploy_mode=self.deploy_mode,
                            base_url=self.base_url)
        self._output_parser = StrOutputParser()

    def rewrite(self, query: str, chat_history: list, prompt: str =REWRITER_PROMPT) -> str:
        if chat_history == "" or chat_history is None:
            chat_history = "Empty"
        format_query = prompt.invoke({"chat_history": chat_history, "current": query}).to_string()
        logger.info(f"问题改写prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite


    def hyde(self, query: str, prompt: str =HYDE_PROMPT) -> str:
        format_query = prompt.invoke({"query": query}).to_string()
        logger.info(f"假设问题改写prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite

    def contextual_retrieval(self, content: str, prompt: str =CONTEXTUAL_PROMPT) -> str:
        format_query = prompt.invoke({"chunk_content": content}).to_string()
        logger.info(f"上下文生成的prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite


    def extract_faq(self, num: int, context: str, prompt: str =FAQ_PROMPT) -> str:
        if context == "" or context is None:
            context = "Empty"
        format_query = prompt.invoke({"context": context, "num": num}).to_string()
        logger.info(f"FAQ抽取的prompt:\n\n{format_query}\n")
        faq_result = self.llm.generate(format_query)
        return faq_result

    def consistency(self, answer: str, chat_history: list, prompt: str =CONSISTENCY_PROMPT) -> str:
        if chat_history == "" or chat_history is None:
            chat_history = "Empty"
        format_query = prompt.invoke({"answer": answer, "chat_history": chat_history}).to_string()
        logger.info(f"一致性检测的prompt:\n\n{format_query}\n")
        faq_result = self.llm.generate(format_query)
        return faq_result



if __name__ == '__main__':
   QS = QueryLLMService()
   query = "手机银行操作知识要点\n\n一、手机银行下载 \n\n1.苹果/安卓市场搜索并下载“吉林银行—手机银行”。\n\n2.门户网站、微信公众号、营业网点扫描二维码，根据手机选择安卓或苹果版本。\n\n" \
           "注意：\n\n1.安卓手机扫码下载手机银行出现报错或乱码：\n\n（1）确认下载地址或扫描二维码是否正确且为最新地址或二维码。\n\n（2）确认使用浏览器支持扫描二维码，建议用手机自带浏览器或百度。\n\n" \
           "2.三星手机无法通过扫码和公众号安装手机银行：\n\n（1）确认系统版本，手机银行支持最低的系统版本为安卓5.0 苹果9.0系统版本，低于此版本则无法使用新版手机银行。\n\n" \
           "（2）如系统版本达标后，则下载时请使用QQ、百度浏览器打开下载地址。\n\n二、手机银行开通、注册（需年满16周岁（含）以上）"
   result = QS.extract_faq(10,query)
   print(result)
