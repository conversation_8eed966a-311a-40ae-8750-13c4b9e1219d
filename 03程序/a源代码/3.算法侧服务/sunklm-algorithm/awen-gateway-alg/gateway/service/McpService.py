import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))

import json
import asyncio
import nest_asyncio
from typing import Optional
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client
from gateway.dao.GatewayDao import Gate<PERSON>ayDao
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict,ReturnCode

nest_asyncio.apply()

# 初始化dao层
gateWayDao = GateWayDao()


class McpService:
    def __init__(self, server_url: str):
        self.session: Optional[ClientSession] = None
        self.sessions = {}  # 存储每个服务器的会话及其上下文：server_id -> (session, session_context, streams_context)
        self.tool_mapping = {}  # 工具映射：prefixed_name -> (session, original_tool_name)
        self.server_url = server_url
        self.exit_stack = AsyncExitStack()

    async def connect_to_server(self):
        """
        初始化与所有 SSE 服务器的连接，并获取可用工具列表。
        """
        try:
            # 创建 SSE 客户端连接上下文管理器
            self._streams_context = sse_client(url=self.server_url)
            # 异步初始化 SSE 连接，获取数据流对象
            streams = await self.exit_stack.enter_async_context(self._streams_context)

            # 使用数据流创建 MCP 客户端会话上下文
            self._session_context = ClientSession(*streams)
            # 初始化客户端会话对象
            self.session: ClientSession = await self.exit_stack.enter_async_context(self._session_context)

            # 执行 MCP 协议初始化握手
            await self.session.initialize()
        except Exception as e:
            logger.error(f"【Service】【MCP-连接】异常:{e}")
            return return_dict(str(ReturnCode.ERROR.value), None, f"【Service】【MCP-连接】异常:{e}")

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

    def getToolMap(self):
        data = gateWayDao.get_tools()
        for tool in data:
            toolId = tool[0]
            toolName = tool[1]
            mcpName = tool[2]
            isMcp = tool[3]
            self.tool_mapping[toolId] = {
                "mcpName": mcpName,
                "toolName": toolName,
                "isMcp": isMcp
            }
        return self.tool_mapping

    async def call_tool(self, tool_id, tool_args):
        """
        调用工具。
        """
        try:
            await self.connect_to_server()
            toolMap = self.getToolMap()
            tool = toolMap.get(tool_id)

            if tool is None:
                return return_dict(str(ReturnCode.ERROR.value), None, "未查询到对应工具")

            if tool['isMcp'] == 0:
                logger.info(f"【Service】【MCP调用】tool:http_function,输入:{tool_args}")
                # 参数处理
                input_data = {}
                input ={}
                for key, value in tool_args.items():
                    if key == "url" or key == "headers" or key == "method":
                        input_data[key] = value
                    else:
                        input[key] = value
                input_data['input'] = input
                logger.info(f"【Service】【MCP调用】tool:http_function,格式化:{input_data}")
                result = await self.session.call_tool("http_function", input_data)
                logger.info(f"【Service】【MCP调用】tool:http_function,结果:{result}")

                # 处理结果返回
                if len(result.content) > 1:
                    json_list = [json.loads(item.text) for item in result.content if hasattr(item, "text")]
                    processed_result = json.dumps(json_list, ensure_ascii=False)
                else:
                    try:
                        if isinstance(json.loads(result.content[0].text), dict):
                            processed_result = result.content[0].text
                        else:
                            processed_result = return_dict(str(ReturnCode.ERROR.value), None, result.content[0].text)
                    except Exception as e:
                        processed_result = return_dict(str(ReturnCode.ERROR.value), None, result.content[0].text)
                return processed_result

            mcpName = tool['mcpName']
            logger.info(f"【Service】【MCP调用】tool:{mcpName},输入:{tool_args}")
            result = await self.session.call_tool(mcpName, tool_args)
            logger.info(f"【Service】【MCP调用】结果:{result}")

            # 处理结果返回
            print(len(result.content))
            if len(result.content) > 1:
                json_list = [json.loads(item.text) for item in result.content if hasattr(item, "text")]
                processed_result = json.dumps(json_list, ensure_ascii=False)
            else:
                try:
                    if isinstance(json.loads(result.content[0].text), dict):
                        processed_result = result.content[0].text
                    else:
                        processed_result = return_dict(str(ReturnCode.ERROR.value), None, result.content[0].text)
                except Exception as e:
                    processed_result = return_dict(str(ReturnCode.ERROR.value), None, result.content[0].text)
            return processed_result
        except Exception as e:
            logger.error(f"【Service】【MCP调用】异常:{e}")
            return return_dict(str(ReturnCode.ERROR.value), None, f"【Service】【MCP调用】异常:{e}")


async def call_tool_id(serverUrl, toolId, toolArgs):
    mcp = McpService(serverUrl)
    # 异步初始化会话
    try:
        res = await mcp.call_tool(toolId, toolArgs)
        # 关闭连接上下文
        await mcp.cleanup()
        return res
    except Exception as e:
        return "调用异常"


def call_mcp_tool(request):
    try:
        # 获取当前事件循环
        loop = asyncio.get_event_loop()
        # 执行异步函数并阻塞等待结果
        res = loop.run_until_complete(
            call_tool_id(
                request.get("serverUrl"),
                request.get("toolId"),
                request.get("toolArgs")
            )
        )

        return json.loads(res)
    except Exception as e:
        print(f"Error occurred: {e}")
        return None


if __name__ == "__main__":
    serverUrl = "http://172.1.0.154:9085/sse"
    toolId = ""
    toolArgs = {
        "input": {
            "serviceYear": 2,
            "profession": "计算机"
        },
        "url": "http://172.1.1.217:8115/SunAPI/getPersonInfos",
        "headers": {
            "Content-Type": "application/json"
        },
        "method": "POST"
    }

    body = {
        "serverUrl": serverUrl,
        "toolId": toolId,
        "toolArgs": toolArgs
    }

    print(asyncio.run(call_tool_id(serverUrl, toolId, toolArgs)))

    print(call_mcp_tool(body))
