from fastapi.responses import StreamingResponse,JSONResponse,Response
from fastapi import HTTPException
import httpx
import json
from common.utils.LogUtils import logger
from common.conf.ServerConfig import APIConfig

class AgentService:
    def agent_stream_api(body):
        logger.info(f"【Service】【Agent-流式-执行】输入:{body}")

        async def generate_data():
            try:
                async with httpx.AsyncClient() as client:
                    async with client.stream(
                            method="POST",
                            url=APIConfig.AGENT_URL,
                            timeout=600,
                            json=body,
                    ) as response:
                        if response.headers.get("content-type", "").startswith("text/event-stream"):
                            async for chunk in response.aiter_bytes():
                                try:
                                    decoded_chunk = chunk.decode('utf-8')
                                    logger.debug(f"【Service】【Agent-流式-执行】流式消息:{decoded_chunk}")
                                    # 直接转发原始SSE数据
                                    yield decoded_chunk
                                except UnicodeDecodeError:
                                    logger.debug(f"【Service】【Agent-流式-执行】流式消息异常:{chunk}")
                        else:
                            content = await response.aread()
                            yield f"data: {content}\n\n"
            except Exception as e:
                logger.info(f"【Service】【Agent-流式-执行】异常:{e}")
                yield f"data: Error: {str(e)}\n\n"

        return StreamingResponse(generate_data(), media_type="text/event-stream")

    def agent_un_stream_api(body):
        logger.info(f"【Service】【Agent-非流式-执行】输入:{body}")
        try:
            with httpx.Client() as client:
                response = client.post(
                    url=APIConfig.AGENT_URL,
                    timeout=600,
                    json=body,
                )
                # 尝试解析 JSON
                try:
                    # 直接使用 response.json()
                    json_content = response.json()
                    logger.info(f"【Service】【Agent-非流式-执行】输出:{json_content}")
                    return JSONResponse(content=json_content, status_code=response.status_code)
                except json.JSONDecodeError:
                    # 如果不是 JSON，返回原始内容
                    res =  Response(
                        content=response.content,
                        status_code=response.status_code,
                        media_type=response.headers.get("content-type", "application/octet-stream")
                    )
                    logger.info(f"【Service】【Agent-非流式-执行】输出:{res}")
                    return res
        except Exception as e:
            logger.error(f"【Service】【Agent-非流式-执行】异常:{e}")
            raise HTTPException(status_code=500, detail=f"An error occurred: {e}")