import json
import traceback
from func_timeout import func_set_timeout, FunctionTimedOut
from common.utils.ResultUtis import *
from common.utils.LogUtils import logger
import subprocess

class CodeService:

    # 代码执行器，支持 Python 和 JavaScript
    def code_executor(body: dict, timeout: int = 20):
        '''
            代码执行器，执行python或javascript代码
            入参：
            - command: str - 代码源码
            - param: dict - 代码入参 必填
            - method: int - 语言类型：1=Python，2=JavaScript
            - timeout：int - 超时设置，非必需
        '''
        logger.info(f"【Service】【代码执行器】输入:{body}")
        try:
            command = body.get("command")
            param = body.get("param")
            method = body.get("method", 1)  # 默认为 Python

            if method == 1:
                result = CodeService._execute_python(command, param, timeout)
            elif method == 2:
                result = CodeService._execute_javascript(command, param, timeout)
            else:
                return json.loads(return_dict(str(ReturnCode.ERROR.value), None, "不支持的执行方法"))

            res = json.loads(return_dict(str(ReturnCode.SUCESS.value), result, "success"))
            logger.info(f"【Service】【代码执行器】输出:{res}")
            return res

        except FunctionTimedOut:
            error = f"代码执行超时（>{timeout}秒）"
            logger.error(f"【Service】【代码执行器】超时:{error}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, error))

        except Exception as e:
            logger.error(f"【Service】【代码执行器】异常:{e}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), str(e), traceback.format_exc()))

    @staticmethod
    def _execute_python(command, param, timeout):
        new_command = command + "\narg = " + repr(param)

        def _run_code():
            local_vars = {"__builtins__": __builtins__}
            exec(new_command, local_vars)
            return eval("solution(arg)", local_vars)

        return func_set_timeout(timeout)(_run_code)()

    @staticmethod
    def _execute_javascript(js_code, param, timeout):
        # 构造完整的 JS 脚本，参数以 JSON 形式传入
        js_script = f"""
const arg = {json.dumps(param)};

{js_code}

console.log(solution(arg));
"""

        # 写入临时文件执行
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.js', delete=False) as tmpfile:
            tmpfile.write(js_script.encode())
            tmpfile_path = tmpfile.name

        def _run_js():
            result = subprocess.run(['node', tmpfile_path], capture_output=True, text=True, timeout=timeout)
            if result.stderr:
                raise Exception(result.stderr)
            return result.stdout.strip()

        return func_set_timeout(timeout)(_run_js)()
