from common.utils.LogUtils import logger
from common.conf.ServerConfig import ModelDataDefault
from common.constants.PromptConstants import PromptConstants
from gateway.service.ModelService import ModelService
from langchain_core.prompts import ChatPromptTemplate
from common.utils.ResultUtis import *
import json
from fastapi import Request

"""
智能体工具
问题改写
问题扩写
"""
class AgentToolService:
    def __init__(self):
        # create default model
        self.llm = ModelService( 
            api_key=ModelDataDefault.API_KEY,
            company=ModelDataDefault.COMPANY,
            model_type=ModelDataDefault.MODEL_TYPE,
            api_base=ModelDataDefault.API_BASE,
            deploy_mode = ModelDataDefault.DEPLOY_MODE
        )

    # 问题改写
    def rewrite_predict(self,req):
        logger.info(f"【Service】【问题改写】输入:{req}")
        history = req.get("history",[])
        query = req.get("query")
        prompt = ChatPromptTemplate.from_template(PromptConstants.REWRITER_PROMPT)
        format_query = prompt.invoke({"chat_history": history, "current": query}).to_string()
        res = self.llm.un_stream_chat(format_query,history,None)
        logger.info(f"【Service】【问题改写】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))

    # 问题扩写
    def question_expand_predict(self,req):
        logger.info(f"【Service】【问题扩写】输入:{req}")
        history = req.get("history",[])
        query = req.get("query")
        prompt = ChatPromptTemplate.from_template(PromptConstants.QUESTION_EXPAND_PROMPT)
        format_query = prompt.invoke({"chat_history": history, "current": query}).to_string()
        res = self.llm.un_stream_chat(format_query,history,None)
        logger.info(f"【Service】【问题扩写】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))

    # 意图识别
    def intent_recognition(self, request, req:Request):
        logger.info(f"【Service】【意图识别】输入:{req}")
        history = request.get("history",[])
        prompt = request.get("prompt")
        query = request.get("query")
        mode = request.get("method")
        intents = self.add_intentId(request.get("intents"))
        print("formated intents:")
        print(intents)

        if(mode == 1): # pro mode
            prompt = ChatPromptTemplate.from_template(PromptConstants.INTENT_RECOGNITION_PROMPT_PRO)
            format_query = prompt.invoke({"prompt": prompt, "current": query, "intents": intents}).to_string()
            llm1 = self._create_user_model_service(request) # use user specified model
            res = llm1.un_stream_chat(format_query, history, None)
        else: # base mode
            prompt = ChatPromptTemplate.from_template(PromptConstants.INTENT_RECOGNITION_PROMPT)
            format_query = prompt.invoke({"prompt": prompt, "current": query, "intents": intents}).to_string()
            res = self.llm.un_stream_chat(format_query, history, None) # use default model
        
        logger.info(f"【Service】【意图识别】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))
    
    def add_intentId(self, intent_list):
        """
        将简单的意图字符串列表转换为包含intentId和description的对象列表

        Args:
            intent_list: 字符串列表，例如 ["画画", "写代码", "查询信息"]

        Returns:
            list: 对象列表，例如 [{"intentId": 1, "description": "画画"}, ...]
        """
        if not isinstance(intent_list, list):
            logger.warning(f"【Service】【意图识别】intent_list不是列表类型: {type(intent_list)}")
            return []

        result = []
        for i, intent in enumerate(intent_list, 1):
            if isinstance(intent, str):
                # 如果是字符串，转换为对象格式
                result.append({
                    "intentId": i,
                    "description": intent
                })
            elif isinstance(intent, dict):
                # 如果已经是字典，检查是否有必要的字段
                if "intentId" not in intent:
                    intent["intentId"] = i
                if "description" not in intent and "name" in intent:
                    intent["description"] = intent["name"]
                elif "description" not in intent:
                    intent["description"] = str(intent)
                result.append(intent)
            else:
                # 其他类型转换为字符串处理
                result.append({
                    "intentId": i,
                    "description": str(intent)
                })

        logger.info(f"【Service】【意图识别】转换意图列表: {len(intent_list)} -> {len(result)}")
        return result

    def _create_user_model_service(self, request):
        """
        根据用户请求参数创建模型服务实例
        """
        # 从请求中获取用户指定的模型参数，如果没有则使用默认值
        model = request.get('model')
        model_type = model.get('modelType', ModelDataDefault.MODEL_TYPE)
        api_base = model.get('baseUrl', ModelDataDefault.API_BASE)
        api_key = model.get('apiKey', ModelDataDefault.API_KEY)
        company = model.get('company', ModelDataDefault.COMPANY)
        deploy_mode = model.get('deployMode', ModelDataDefault.DEPLOY_MODE)
        temperature = model.get('temperature', ModelDataDefault.TEMPERATURE)
        top_p = model.get('topP', ModelDataDefault.TOP_P)
        top_k = model.get('topK', ModelDataDefault.TOP_K)
        max_seq_len = model.get('inputMaxToken', ModelDataDefault.MAX_SEQ_LEN)
        max_out_len = model.get('outputMaxToken', ModelDataDefault.MAX_OUT_LEN)

        logger.info(f"【Service】【创建用户模型】模型类型:{model_type}, API地址:{api_base}, 部署模式:{deploy_mode}")

        return ModelService(
            api_key=api_key,
            company=company,
            model_type=model_type,
            api_base=api_base,
            deploy_mode=deploy_mode,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            max_seq_len=max_seq_len,
            max_out_len=max_out_len
        )
    
