from common.utils.LogUtils import logger
from common.conf.ServerConfig import ModelDataDefault
from common.constants.PromptConstants import PromptConstants
from gateway.service.ModelService import ModelService
from langchain_core.prompts import ChatPromptTemplate
from common.utils.ResultUtis import *
from gateway.service.KnowledgeService import QueryLLMService, PromptOptimizerService
import json
import traceback
import time
from fastapi import Request
from common.constants.PromptTemplate import (
    DEFAULT_TEMPLET_CN, DEFAULT_TEMPLET_EN,
    INFORMATION_INTEGRATION, MULTI_QUERY_PROMPT,
    REWRITER_PROMPT, HYDE_PROMPT, EVAL_PROMPT,
    CONSISTENCY_PROMPT, FAQ_PROMPT, CONTEXTUAL_PROMPT,
    ABSTRACT_TEMPLET_CN, FILE_CLASSIFICATION_PROMPT,
    FILE_CLASSIFICATION_PROMPT2
)

QR = QueryLLMService()

"""
智能体工具
问题改写
问题扩写
"""


class AgentToolService:
    def __init__(self):
        # create default model
        self.llm = ModelService(
            api_key=ModelDataDefault.API_KEY,
            company=ModelDataDefault.COMPANY,
            model_type=ModelDataDefault.MODEL_TYPE,
            api_base=ModelDataDefault.API_BASE,
            deploy_mode=ModelDataDefault.DEPLOY_MODE
        )

    # 问题改写
    def rewrite_predict(self, req):
        logger.info(f"【Service】【问题改写】输入:{req}")
        history = req.get("history", [])
        query = req.get("query")
        prompt = ChatPromptTemplate.from_template(PromptConstants.REWRITER_PROMPT)
        format_query = prompt.invoke({"chat_history": history, "current": query}).to_string()
        res = self.llm.un_stream_chat(format_query, history, None)
        logger.info(f"【Service】【问题改写】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))

    # 问题扩写
    def question_expand_predict(self, req):
        logger.info(f"【Service】【问题扩写】输入:{req}")
        history = req.get("history", [])
        query = req.get("query")
        prompt = ChatPromptTemplate.from_template(PromptConstants.QUESTION_EXPAND_PROMPT)
        format_query = prompt.invoke({"chat_history": history, "current": query}).to_string()
        res = self.llm.un_stream_chat(format_query, history, None)
        logger.info(f"【Service】【问题扩写】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))

    # 意图识别
    def intent_recognition(self, request, req: Request):
        logger.info(f"【Service】【意图识别】输入:{req}")
        history = request.get("history", [])
        prompt = request.get("prompt")
        query = request.get("query")
        mode = request.get("method")
        intents = self.add_intentId(request.get("intents"))
        print("formated intents:")
        print(intents)

        if (mode == 1):  # pro mode
            prompt = ChatPromptTemplate.from_template(PromptConstants.INTENT_RECOGNITION_PROMPT_PRO)
            format_query = prompt.invoke({"prompt": prompt, "current": query, "intents": intents}).to_string()
            llm1 = self._create_user_model_service(request)  # use user specified model
            res = llm1.un_stream_chat(format_query, history, None)
        else:  # base mode
            prompt = ChatPromptTemplate.from_template(PromptConstants.INTENT_RECOGNITION_PROMPT)
            format_query = prompt.invoke({"prompt": prompt, "current": query, "intents": intents}).to_string()
            res = self.llm.un_stream_chat(format_query, history, None)  # use default model

        logger.info(f"【Service】【意图识别】输出:{res}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))

    def add_intentId(self, intent_list):
        """
        将简单的意图字符串列表转换为包含intentId和description的对象列表

        Args:
            intent_list: 字符串列表，例如 ["画画", "写代码", "查询信息"]

        Returns:
            list: 对象列表，例如 [{"intentId": 1, "description": "画画"}, ...]
        """
        if not isinstance(intent_list, list):
            logger.warning(f"【Service】【意图识别】intent_list不是列表类型: {type(intent_list)}")
            return []

        result = []
        for i, intent in enumerate(intent_list, 1):
            if isinstance(intent, str):
                # 如果是字符串，转换为对象格式
                result.append({
                    "intentId": i,
                    "description": intent
                })
            elif isinstance(intent, dict):
                # 如果已经是字典，检查是否有必要的字段
                if "intentId" not in intent:
                    intent["intentId"] = i
                if "description" not in intent and "name" in intent:
                    intent["description"] = intent["name"]
                elif "description" not in intent:
                    intent["description"] = str(intent)
                result.append(intent)
            else:
                # 其他类型转换为字符串处理
                result.append({
                    "intentId": i,
                    "description": str(intent)
                })

        logger.info(f"【Service】【意图识别】转换意图列表: {len(intent_list)} -> {len(result)}")
        return result

    def _create_user_model_service(self, request):
        """
        根据用户请求参数创建模型服务实例
        """
        # 从请求中获取用户指定的模型参数，如果没有则使用默认值
        model = request.get('model')
        model_type = model.get('modelType', ModelDataDefault.MODEL_TYPE)
        api_base = model.get('baseUrl', ModelDataDefault.API_BASE)
        api_key = model.get('apiKey', ModelDataDefault.API_KEY)
        company = model.get('company', ModelDataDefault.COMPANY)
        deploy_mode = model.get('deployMode', ModelDataDefault.DEPLOY_MODE)
        temperature = model.get('temperature', ModelDataDefault.TEMPERATURE)
        top_p = model.get('topP', ModelDataDefault.TOP_P)
        top_k = model.get('topK', ModelDataDefault.TOP_K)
        max_seq_len = model.get('inputMaxToken', ModelDataDefault.MAX_SEQ_LEN)
        max_out_len = model.get('outputMaxToken', ModelDataDefault.MAX_OUT_LEN)

        logger.info(f"【Service】【创建用户模型】模型类型:{model_type}, API地址:{api_base}, 部署模式:{deploy_mode}")

        return ModelService(
            api_key=api_key,
            company=company,
            model_type=model_type,
            api_base=api_base,
            deploy_mode=deploy_mode,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            max_seq_len=max_seq_len,
            max_out_len=max_out_len
        )

    # FAQ抽取
    def extract_faq_predict(self, request):
        try:
            data = request
            logger.info(f"corpus generate request data {str(data)}", )
            num = data.get("num", [])
            context = data.get("context", "")
            result = QR.extract_faq(num, context)
            return return_dict("0", result, "success")
        except Exception as e:
            logger.error(f"请求/generate/sentence接口出错：{str(traceback.format_exc())}")
            return return_dict("1", str(e), "error")

    # 知识检索
    def query_match_predict(self, request):
        time_start = time.time()
        logger.info("-" * 50)
        query_match = QueryMatch()
        try:
            params = json.loads(request.get_data(as_text=True))
            # 执行实际算法操作
            logger.info("[文本检索] ------query:{}------".format(params['query']))
            logger.info("[文本检索] 入参：" + str(params))
            result = query_match.query_match(params)
            result = json.loads(result)
            if result['retCode'] == ReturnCode.SUCESS:
                logger.info("[文本检索] 运行时间：{}----".format(time.time() - time_start))
                result = {'result': result['retMap']}
                logger.info("[文本检索] Rag result: {}".format(result))
                return return_dict(code=str(ReturnCode.SUCESS.value), data=result, message='success')
            else:
                return return_dict(code=str(ReturnCode.ERROR.value), data={}, message=result['retMsg'])
        except Exception as e:
            logger.error("[文本检索] 错误信息--------")
            logger.error(e)
            logger.error("{}".format(traceback.format_exc()))
            return return_dict(code=str(ReturnCode.ERROR.value), message='query match error.',
                               data=traceback.format_exc())

    # 提示词优化
    def prompt_optimize_predict(self, request):
        try:
            data = request
            logger.info(f"prompt optimize request data: {str(data)}")

            # 获取参数
            prompt = data.get("prompt", "")
            optimization_type = data.get("optimization_type", 1)  # 1-自动 2-反馈 3-自定义
            feedback = data.get("feedback", "")
            expectation = data.get("expectation", "")
            requirements = data.get("requirements", "")

            if not prompt:
                return return_dict("1", "prompt参数不能为空", "error")
            if optimization_type not in (1, 2, 3):
                return return_dict("1", "optimization_type必须是1/2/3", "error")
            if optimization_type == 2:
                if not feedback:
                    return return_dict("1", "反馈优化需提供feedback参数", "error")
                if not expectation:
                    return return_dict("1", "反馈优化需提供expectation参数", "error")
            if optimization_type == 3 and not requirements:
                return return_dict("1", "自定义优化需提供requirements参数", "error")
                # 构建模板字典

            prompt_templates = {
                "DEFAULT_TEMPLET_CN": DEFAULT_TEMPLET_CN,
                "DEFAULT_TEMPLET_EN": DEFAULT_TEMPLET_EN,
                "INFORMATION_INTEGRATION": INFORMATION_INTEGRATION,
                "MULTI_QUERY_PROMPT": MULTI_QUERY_PROMPT,
                "REWRITER_PROMPT": REWRITER_PROMPT,
                "HYDE_PROMPT": HYDE_PROMPT,
                "EVAL_PROMPT": EVAL_PROMPT,
                "CONSISTENCY_PROMPT": CONSISTENCY_PROMPT,
                "FAQ_PROMPT": FAQ_PROMPT,
                "CONTEXTUAL_PROMPT": CONTEXTUAL_PROMPT,
                "ABSTRACT_TEMPLET_CN": ABSTRACT_TEMPLET_CN,
                "FILE_CLASSIFICATION_PROMPT": FILE_CLASSIFICATION_PROMPT,
                "FILE_CLASSIFICATION_PROMPT2": FILE_CLASSIFICATION_PROMPT2
            }

            # 调用服务
            optimizer = PromptOptimizerService()
            result = optimizer.optimize_prompt(
                prompt=prompt,
                optimization_type=optimization_type,
                feedback=feedback,
                expectation=expectation,
                requirements=requirements,
                templates=prompt_templates
            )

            logger.info(f"{result}")

            return return_dict("0", result, "success")
        except Exception as e:
            logger.error(f"请求/prompt_optimize接口出错：{str(traceback.format_exc())}")
            return return_dict("1", str(e), "error")
