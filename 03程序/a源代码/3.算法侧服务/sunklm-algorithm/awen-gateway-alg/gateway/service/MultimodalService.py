import json
import requests
import openai
from gateway.service.MinioService import *
from common.conf.ServerConfig import *
from fastapi.responses import StreamingResponse

class MultiModalService:
    def __init__(self):
        self.headers = {"Content-Type": "application/json"}
        self.default_temperature = TEMPERATURE
        self.default_top_p = TOP_P
        self.default_top_k = TOP_K
        self.default_max_tokens = MAX_OUT_LEN
    
    def api_call(self, url, api_key, inputs, stream=False):
        # Add authorization header if api_key is provided
        headers = self.headers.copy()
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"
            
        if stream:
            response = requests.post(url, headers=headers, json=inputs, timeout=60, stream=True)
            return response
        else:
            response = requests.post(url, headers=headers, json=inputs)

            # print("Status Code:", response.status_code)
            # print("Response Text:", response.text)  # 查看服务端返回的具体信息
            return response.json()

    def _prepare_url(self, url, url_type):
        if url_type == "base64":
            mime_type = "image/png"
            data_uri_prefix = f'data:{mime_type};base64,'
            return data_uri_prefix + url
        elif url_type == "minio":
            return url
        else:
            raise KeyError(f"多模态模型url未提供{url_type}类型")

    def _prepare_inputs(self, model, query, encoded_url, content_type, temperature, top_p, top_k, max_tokens, stream=False):
        return {
            "model": model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": query},
                    {"type": content_type, content_type: {"url": encoded_url}}
                ]}],
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "max_tokens": max_tokens,
            "stream": stream
        }

    def multi_model(self, url, model, model_url, query, content_type,api_key=None, 
                   temperature=None, top_p=None, top_k=None, max_tokens=None, url_type="minio"):
        
        """多模态模型调用实现函数"""
        temperature = temperature or self.default_temperature
        top_p = top_p or self.default_top_p
        top_k = top_k or self.default_top_k
        max_tokens = max_tokens or self.default_max_tokens
        
        encoded_url = self._prepare_url(url, url_type)
        inputs = self._prepare_inputs(model, query, encoded_url, content_type, 
                                    temperature, top_p, top_k, max_tokens)
        
        result = self.api_call(model_url,api_key, inputs)["choices"][0]["message"]["content"]
        return result

    def multi_model_stream(self, url, model, model_url, query, content_type,api_key=None,
                          temperature=None, top_p=None, top_k=None, max_tokens=None, url_type="minio"):
        """多模态模型流式调用实现函数"""
        temperature = temperature or self.default_temperature
        top_p = top_p or self.default_top_p
        top_k = top_k or self.default_top_k
        max_tokens = max_tokens or self.default_max_tokens

        async def generate_data():
            encoded_url = self._prepare_url(url, url_type)
            inputs = self._prepare_inputs(model, query, encoded_url, content_type,
                                        temperature, top_p, top_k, max_tokens, stream=True)

            result = self.api_call(model_url, api_key,inputs, stream=True)

            if result.status_code == 200:
                for line in result.iter_lines():
                    if line:
                        try:
                            line = line.decode("utf-8")
                        except:
                            pass
                        line = line.strip("data").strip(": ")
                        if line == "[DONE]":
                            yield self._format_stream_response("Stream ended")
                            break
                        elif line and line[0] == "{" and line[-1] == "}":
                            line = json.loads(line)
                            res = line["choices"][0]["delta"].get("content")
                            if res:
                                yield self._format_stream_response(str(res))
            else:
                yield self._format_stream_response(f"Error: {result.status_code}", success=False)

        return StreamingResponse(generate_data(), media_type="text/event-stream")


    def asr_server(self, model, base_url, asr_path):
        """语音识别服务"""
        if IS_ASR_API:
            inputs = {"asr_path": asr_path}
            return self.api_call(base_url, inputs)
        else:
            local_path = download_from_minio(asr_path)
            client = openai.Client(api_key="not empty", base_url=base_url)
            with open(local_path, "rb") as audio_file:
                completion = client.audio.transcriptions.create(model=model, file=audio_file)
            return completion.text

    def tts_server(self, model, base_url, tts_path, voice, input):
        """语音合成服务"""
        file_name = tts_path.split("/")[-1]
        local_path = ASR_PATH + "/" + file_name
        
        if IS_TTS_API:
            inputs = {
                "query": input,
                "voice": voice,
                "tts_path": local_path
            }
            result = self.api_call(base_url, inputs)
        else:
            client = openai.Client(api_key="cannot be empty", base_url=base_url)
            response = client.audio.speech.create(
                model=model,
                input=input,
                voice=voice,
            )
            response.stream_to_file(local_path)
            result = ""
            
        upload_asr_to_minio(local_path, tts_path)
        return result

    def _format_stream_response(self, answer, success=True):
        """格式化流式响应"""
        data = {
            "retCode": str(ReturnCode.SUCESS.value) if success else str(ReturnCode.ERROR.value),
            "retMsg": "success" if success else "error",
            "retMap": {"answer": answer}
        }
        return f"data:{json.dumps(data, ensure_ascii=False)}\n\n"