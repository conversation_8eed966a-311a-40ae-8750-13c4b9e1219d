import os
from common.conf.ServerConfig import *
from common.utils.MinioConn import *

# MinIO连接 
minio = MinioClient()  # MinIO客户端
minio_client = minio.get_connect()  # MinIO连接

def upload_asr_to_minio(asr_path, minio_path):
    """
    将PDF文件中的图片上传到MinIO
    :param pdf_file_path: PDF文件路径
    """
    if os.path.exists(asr_path):
        with open(asr_path, 'rb') as asr_file:
            minio_client.put_object(MINOBUCKET, minio_path, asr_file,length=-1,part_size=100*1024*1024)
            # Delete the local image file after successful upload
            os.remove(asr_path)
    else:
        raise KeyError(f"Directory {asr_path} does not exist.")


def download_from_minio(object_path: str):
    """
    从MinIO对象存储中下载文件，并将其保存到本地指定目录
    :param bucket_name: MinIO桶的名称
    :param object_path: 桶内对象的路径
    :param local_dir: 本地要保存文件的目录
    :return: 一个元组，包含本地保存文件的完整路径和文件名
    """
    if not os.path.exists(ASR_PATH):
        os.makedirs(ASR_PATH)
    response = minio_client.get_object(MINOBUCKET, object_path)
    file_name = os.path.basename(object_path)
    save_path = os.path.join(ASR_PATH, file_name)
    with open(save_path, "wb") as file:
        for data in response:
            file.write(data)
    return save_path




