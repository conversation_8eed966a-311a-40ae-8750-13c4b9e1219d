"""
HTTP请求服务
提供统一的HTTP请求处理功能，集成自定义HttpClient组件
"""

import json
import requests
import base64
import hashlib
import hmac
import time
from typing import Dict, Any, Optional, Union
from enum import Enum
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict, ReturnCode

# 如果HttpClient模块不存在，创建基础的HTTP方法枚举
class HttpMethod(Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"

# 尝试导入HttpClient，如果失败则使用requests
try:
    from common.utils.HttpClient import create_http_client
    USE_HTTP_CLIENT = True
except ImportError:
    USE_HTTP_CLIENT = False
    logger.warning("HttpClient模块不可用，将使用requests库")

    # 创建简化的HTTP客户端
    class SimpleHttpClient:
        def __init__(self, timeout=30, auto_request_id=True, service_name="default"):
            self.timeout = timeout
            self.auto_request_id = auto_request_id
            self.service_name = service_name
            self.session = requests.Session()

        def get(self, url, **kwargs):
            return self.session.get(url, timeout=self.timeout, **kwargs)

        def post(self, url, json_data=None, **kwargs):
            return self.session.post(url, json=json_data, timeout=self.timeout, **kwargs)

        def put(self, url, json_data=None, **kwargs):
            return self.session.put(url, json=json_data, timeout=self.timeout, **kwargs)

        def delete(self, url, **kwargs):
            return self.session.delete(url, timeout=self.timeout, **kwargs)

        def patch(self, url, json_data=None, **kwargs):
            return self.session.patch(url, json=json_data, timeout=self.timeout, **kwargs)

        def head(self, url, **kwargs):
            return self.session.head(url, timeout=self.timeout, **kwargs)

        def options(self, url, **kwargs):
            return self.session.options(url, timeout=self.timeout, **kwargs)

        def is_success(self, response):
            return 200 <= response.status_code < 300

        def download_file(self, url, save_path, **kwargs):
            try:
                response = self.get(url, stream=True, **kwargs)
                response.raise_for_status()
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                return True
            except Exception:
                return False

        def upload_file(self, url, file_path, field_name="file", additional_data=None, **kwargs):
            with open(file_path, 'rb') as f:
                files = {field_name: f}
                data = additional_data or {}
                return self.session.post(url, files=files, data=data, timeout=self.timeout, **kwargs)

        def close(self):
            self.session.close()

    def create_http_client(timeout=30, auto_request_id=True, service_name="default"):
        return SimpleHttpClient(timeout, auto_request_id, service_name)


class AuthType(Enum):
    """鉴权类型枚举"""
    NONE = "none"
    BASIC = "basic"
    BEARER = "bearer"
    API_KEY = "api_key"
    DIGEST = "digest"
    OAUTH2 = "oauth2"
    CUSTOM = "custom"
    HMAC_SHA256 = "hmac_sha256"


class AuthConfig:
    """鉴权配置类"""

    def __init__(self, auth_type: AuthType = AuthType.NONE, **kwargs):
        self.auth_type = auth_type
        self.config = kwargs

    @classmethod
    def basic_auth(cls, username: str, password: str):
        """Basic认证"""
        return cls(AuthType.BASIC, username=username, password=password)

    @classmethod
    def bearer_token(cls, token: str):
        """Bearer Token认证"""
        return cls(AuthType.BEARER, token=token)

    @classmethod
    def api_key(cls, key: str, header_name: str = "X-API-Key"):
        """API Key认证"""
        return cls(AuthType.API_KEY, key=key, header_name=header_name)

    @classmethod
    def oauth2(cls, access_token: str, token_type: str = "Bearer"):
        """OAuth2认证"""
        return cls(AuthType.OAUTH2, access_token=access_token, token_type=token_type)

    @classmethod
    def hmac_sha256(cls, access_key: str, secret_key: str, timestamp_header: str = "X-Timestamp"):
        """HMAC-SHA256签名认证"""
        return cls(AuthType.HMAC_SHA256, access_key=access_key, secret_key=secret_key,
                  timestamp_header=timestamp_header)

    @classmethod
    def custom_headers(cls, headers: Dict[str, str]):
        """自定义请求头认证"""
        return cls(AuthType.CUSTOM, headers=headers)


class AuthHandler:
    """鉴权处理器"""

    @staticmethod
    def parse_auth_from_json(auth_data: Union[Dict[str, Any], AuthConfig]) -> Optional[AuthConfig]:
        """从JSON数据解析鉴权配置"""
        if isinstance(auth_data, AuthConfig):
            return auth_data

        if not isinstance(auth_data, dict):
            return None

        auth_type_str = auth_data.get("type", "none").lower()

        # 映射字符串到枚举
        type_mapping = {
            "none": AuthType.NONE,
            "basic": AuthType.BASIC,
            "bearer": AuthType.BEARER,
            "api_key": AuthType.API_KEY,
            "digest": AuthType.DIGEST,
            "oauth2": AuthType.OAUTH2,
            "custom": AuthType.CUSTOM,
            "hmac_sha256": AuthType.HMAC_SHA256
        }

        auth_type = type_mapping.get(auth_type_str, AuthType.NONE)

        if auth_type == AuthType.NONE:
            return None

        # 提取配置参数
        config = auth_data.copy()
        config.pop("type", None)  # 移除type字段

        return AuthConfig(auth_type, **config)

    @staticmethod
    def apply_auth(headers: Dict[str, str], auth_config: Union[AuthConfig, Dict[str, Any]],
                   method: str = "GET", url: str = "", body: str = "") -> Dict[str, str]:
        """应用鉴权到请求头，支持AuthConfig对象或JSON字典"""

        # 如果是字典，先解析为AuthConfig
        if isinstance(auth_config, dict):
            auth_config = AuthHandler.parse_auth_from_json(auth_config)

        if not auth_config or auth_config.auth_type == AuthType.NONE:
            return headers

        auth_headers = headers.copy()

        if auth_config.auth_type == AuthType.BASIC:
            username = auth_config.config.get("username", "")
            password = auth_config.config.get("password", "")
            credentials = base64.b64encode(f"{username}:{password}".encode()).decode()
            auth_headers["Authorization"] = f"Basic {credentials}"

        elif auth_config.auth_type == AuthType.BEARER:
            token = auth_config.config.get("token", "")
            auth_headers["Authorization"] = f"Bearer {token}"

        elif auth_config.auth_type == AuthType.API_KEY:
            key = auth_config.config.get("key", "")
            header_name = auth_config.config.get("header_name", "X-API-Key")
            auth_headers[header_name] = key

        elif auth_config.auth_type == AuthType.OAUTH2:
            access_token = auth_config.config.get("access_token", "")
            token_type = auth_config.config.get("token_type", "Bearer")
            auth_headers["Authorization"] = f"{token_type} {access_token}"

        elif auth_config.auth_type == AuthType.HMAC_SHA256:
            access_key = auth_config.config.get("access_key", "")
            secret_key = auth_config.config.get("secret_key", "")
            timestamp_header = auth_config.config.get("timestamp_header", "X-Timestamp")

            # 生成时间戳
            timestamp = str(int(time.time()))
            auth_headers[timestamp_header] = timestamp

            # 构造签名字符串
            sign_string = f"{method}\n{url}\n{timestamp}\n{body}"

            # 生成HMAC-SHA256签名
            signature = hmac.new(
                secret_key.encode(),
                sign_string.encode(),
                hashlib.sha256
            ).hexdigest()

            auth_headers["Authorization"] = f"HMAC-SHA256 AccessKey={access_key}, Signature={signature}"

        elif auth_config.auth_type == AuthType.CUSTOM:
            custom_headers = auth_config.config.get("headers", {})
            auth_headers.update(custom_headers)

        return auth_headers


class HttpRequestService:
    """HTTP请求服务类 - 支持多种鉴权方式"""

    def __init__(self, auth_config: Optional[Union[AuthConfig, Dict[str, Any]]] = None):
        """
        初始化HTTP请求服务

        Args:
            auth_config: 鉴权配置，可以是AuthConfig对象或JSON字典格式
                JSON格式示例:
                {
                    "type": "basic",
                    "username": "admin",
                    "password": "password123"
                }
        """
        self.default_timeout = 30
        self.default_headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Awen-Gateway-HttpService/1.0'
        }
        # 解析鉴权配置
        self.auth_config = AuthHandler.parse_auth_from_json(auth_config) if isinstance(auth_config, dict) else auth_config
        # 创建默认的HTTP客户端
        self.http_client = create_http_client(
            timeout=self.default_timeout,
            auto_request_id=True,
            service_name="http_service"
        )

    def http_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        HTTP请求方法 - 使用自定义HttpClient组件，支持鉴权

        Args:
            request: 请求参数字典
                - url: 请求URL
                - method: HTTP方法 (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
                - headers: 请求头 (可选)
                - data: 请求数据 (可选)
                - params: URL参数 (可选，用于GET请求)
                - timeout: 超时时间 (可选)
                - stream: 是否流式响应 (可选)
                - auth: 鉴权配置 (可选，会覆盖实例级别的鉴权配置)
                        可以是AuthConfig对象或JSON字典格式，例如:
                        {"type": "basic", "username": "admin", "password": "pass"}
                        {"type": "bearer", "token": "your-jwt-token"}
                        {"type": "api_key", "key": "your-api-key", "header_name": "X-API-Key"}
                        {"type": "hmac_sha256", "access_key": "key", "secret_key": "secret"}

        Returns:
            Dict: 响应结果
        """
        logger.info(f"【Service】【HTTP请求】输入: {request}")

        # 提取请求参数
        url = request.get("url")
        method = request.get("method", "GET").upper()
        headers = request.get("headers", {})
        data = request.get("data")
        params = request.get("params")
        timeout = request.get("timeout", self.default_timeout)
        stream = request.get("stream", False)
        auth_config = request.get("auth", self.auth_config)

        # 参数验证
        if not url:
            error_msg = "URL参数不能为空"
            logger.error(f"【Service】【HTTP请求】{error_msg}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, error_msg))

        # 合并请求头
        merged_headers = self.default_headers.copy()
        merged_headers.update(headers)

        # 应用鉴权
        if auth_config:
            body_str = json.dumps(data) if data else ""
            merged_headers = AuthHandler.apply_auth(
                merged_headers, auth_config, method, url, body_str
            )
            # 获取鉴权类型用于日志
            if isinstance(auth_config, dict):
                auth_type = auth_config.get("type", "unknown")
            else:
                auth_type = auth_config.auth_type.value if hasattr(auth_config, 'auth_type') else "unknown"
            logger.info(f"【Service】【HTTP请求】已应用{auth_type}鉴权")

        try:
            # 使用自定义HttpClient发送请求
            response = self._send_request_with_client(
                url=url,
                method=method,
                headers=merged_headers,
                data=data,
                params=params,
                timeout=timeout,
                stream=stream
            )

            # 处理响应
            return self._process_response(response, stream)

        except Exception as e:
            logger.error(f"【Service】【HTTP请求】异常: {e}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, str(e)))

    def _send_request_with_client(
        self, url: str, method: str, headers: Dict[str, str],
        data: Any = None, params: Dict[str, Any] = None,
        timeout: int = 30, stream: bool = False
    ):
        """使用HttpClient发送请求"""

        # 创建临时客户端（如果需要特殊配置）
        if timeout != self.default_timeout:
            client = create_http_client(timeout=timeout, auto_request_id=True)
        else:
            client = self.http_client

        try:
            # 根据HTTP方法调用相应的客户端方法
            if method == "GET":
                if USE_HTTP_CLIENT:
                    response = client.get(url, params=params, headers=headers,
                                        timeout=timeout, stream=stream)
                else:
                    response = client.get(url, params=params, headers=headers)
            elif method == "POST":
                if USE_HTTP_CLIENT:
                    response = client.post(url, json_data=data, headers=headers,
                                         timeout=timeout, stream=stream)
                else:
                    response = client.post(url, json_data=data, headers=headers)
            elif method == "PUT":
                if USE_HTTP_CLIENT:
                    response = client.put(url, json_data=data, headers=headers,
                                        timeout=timeout, stream=stream)
                else:
                    response = client.put(url, json_data=data, headers=headers)
            elif method == "DELETE":
                if USE_HTTP_CLIENT:
                    response = client.delete(url, params=params, headers=headers,
                                           timeout=timeout, stream=stream)
                else:
                    response = client.delete(url, headers=headers)
            elif method == "PATCH":
                if USE_HTTP_CLIENT:
                    response = client.patch(url, json_data=data, headers=headers,
                                          timeout=timeout, stream=stream)
                else:
                    response = client.patch(url, json_data=data, headers=headers)
            elif method == "HEAD":
                response = client.head(url, headers=headers)
            elif method == "OPTIONS":
                response = client.options(url, headers=headers)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            return response

        finally:
            # 如果创建了临时客户端，需要关闭
            if timeout != self.default_timeout and client != self.http_client:
                client.close()

    def _process_response(self, response, stream: bool = False) -> Dict[str, Any]:
        """处理HTTP响应"""

        try:
            # 检查响应状态
            if not self.http_client.is_success(response):
                error_msg = f"HTTP请求失败，状态码: {response.status_code}"
                logger.warning(f"【Service】【HTTP请求】{error_msg}")

                # 尝试获取错误信息
                try:
                    error_detail = response.text
                    if error_detail:
                        error_msg += f", 错误详情: {error_detail}"
                except:
                    pass

                return json.loads(return_dict(str(ReturnCode.ERROR.value), {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "error": error_detail if 'error_detail' in locals() else None
                }, error_msg))

            # 处理流式响应
            if stream:
                return json.loads(return_dict(str(ReturnCode.SUCESS.value), {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "stream": True,
                    "message": "流式响应，请使用iter_content()方法获取数据"
                }, "success"))

            # 处理普通响应
            result_data = {
                "status_code": response.status_code,
                "headers": dict(response.headers)
            }

            # 尝试解析JSON响应
            try:
                json_data = response.json()
                result_data["data"] = json_data
                logger.info(f"【Service】【HTTP请求】JSON响应成功")
            except:
                # 如果不是JSON，返回文本内容
                result_data["text"] = response.text
                logger.info(f"【Service】【HTTP请求】文本响应成功")

            return json.loads(return_dict(str(ReturnCode.SUCESS.value), result_data, "success"))

        except Exception as e:
            logger.error(f"【Service】【HTTP请求】响应处理异常: {e}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, f"响应处理异常: {e}"))

    def batch_request(self, requests_list: list) -> Dict[str, Any]:
        """
        批量HTTP请求

        Args:
            requests_list: 请求列表，每个元素是一个请求字典

        Returns:
            Dict: 批量请求结果
        """
        logger.info(f"【Service】【HTTP批量请求】开始处理{len(requests_list)}个请求")

        results = []
        success_count = 0
        error_count = 0

        for i, request in enumerate(requests_list):
            try:
                result = self.http_request(request)
                results.append({
                    "index": i,
                    "request": request.get("url", "unknown"),
                    "result": result
                })

                # 统计成功/失败数量
                if result.get("retCode") == str(ReturnCode.SUCESS.value):
                    success_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_result = json.loads(return_dict(str(ReturnCode.ERROR.value), None, str(e)))
                results.append({
                    "index": i,
                    "request": request.get("url", "unknown"),
                    "result": error_result
                })
                error_count += 1

        summary = {
            "total": len(requests_list),
            "success": success_count,
            "error": error_count,
            "results": results
        }

        logger.info(f"【Service】【HTTP批量请求】完成，成功: {success_count}, 失败: {error_count}")
        return json.loads(return_dict(str(ReturnCode.SUCESS.value), summary, "批量请求完成"))

    def download_file(self, url: str, save_path: str, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        下载文件

        Args:
            url: 文件URL
            save_path: 保存路径
            headers: 请求头

        Returns:
            Dict: 下载结果
        """
        logger.info(f"【Service】【HTTP文件下载】开始下载: {url} -> {save_path}")

        try:
            merged_headers = self.default_headers.copy()
            if headers:
                merged_headers.update(headers)

            success = self.http_client.download_file(url, save_path, headers=merged_headers)

            if success:
                logger.info(f"【Service】【HTTP文件下载】下载成功: {save_path}")
                return json.loads(return_dict(str(ReturnCode.SUCESS.value), {
                    "url": url,
                    "save_path": save_path,
                    "status": "downloaded"
                }, "文件下载成功"))
            else:
                error_msg = "文件下载失败"
                logger.error(f"【Service】【HTTP文件下载】{error_msg}")
                return json.loads(return_dict(str(ReturnCode.ERROR.value), None, error_msg))

        except Exception as e:
            logger.error(f"【Service】【HTTP文件下载】异常: {e}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, str(e)))

    def upload_file(
        self, url: str, file_path: str, field_name: str = "file",
        additional_data: Dict[str, Any] = None, headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        上传文件

        Args:
            url: 上传URL
            file_path: 文件路径
            field_name: 文件字段名
            additional_data: 额外数据
            headers: 请求头

        Returns:
            Dict: 上传结果
        """
        logger.info(f"【Service】【HTTP文件上传】开始上传: {file_path} -> {url}")

        try:
            merged_headers = self.default_headers.copy()
            if headers:
                merged_headers.update(headers)

            # 移除Content-Type，让requests自动设置
            merged_headers.pop('Content-Type', None)

            response = self.http_client.upload_file(
                url, file_path, field_name, additional_data, headers=merged_headers
            )

            return self._process_response(response)

        except Exception as e:
            logger.error(f"【Service】【HTTP文件上传】异常: {e}")
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, str(e)))

    def set_auth(self, auth_config: Union[AuthConfig, Dict[str, Any]]):
        """
        设置鉴权配置

        Args:
            auth_config: 鉴权配置，可以是AuthConfig对象或JSON字典
        """
        if isinstance(auth_config, dict):
            self.auth_config = AuthHandler.parse_auth_from_json(auth_config)
            auth_type = auth_config.get("type", "unknown")
        else:
            self.auth_config = auth_config
            auth_type = auth_config.auth_type.value if hasattr(auth_config, 'auth_type') else "unknown"

        logger.info(f"【Service】【HTTP请求】已设置{auth_type}鉴权")

    def clear_auth(self):
        """清除鉴权配置"""
        self.auth_config = None
        logger.info("【Service】【HTTP请求】已清除鉴权配置")

    def request_with_basic_auth(self, request: Dict[str, Any], username: str, password: str) -> Dict[str, Any]:
        """使用Basic认证发送请求"""
        auth_config = AuthConfig.basic_auth(username, password)
        request_with_auth = request.copy()
        request_with_auth["auth"] = auth_config
        return self.http_request(request_with_auth)

    def request_with_bearer_token(self, request: Dict[str, Any], token: str) -> Dict[str, Any]:
        """使用Bearer Token发送请求"""
        auth_config = AuthConfig.bearer_token(token)
        request_with_auth = request.copy()
        request_with_auth["auth"] = auth_config
        return self.http_request(request_with_auth)

    def request_with_api_key(self, request: Dict[str, Any], api_key: str, header_name: str = "X-API-Key") -> Dict[str, Any]:
        """使用API Key发送请求"""
        auth_config = AuthConfig.api_key(api_key, header_name)
        request_with_auth = request.copy()
        request_with_auth["auth"] = auth_config
        return self.http_request(request_with_auth)

    def request_with_hmac_signature(self, request: Dict[str, Any], access_key: str, secret_key: str) -> Dict[str, Any]:
        """使用HMAC-SHA256签名发送请求"""
        auth_config = AuthConfig.hmac_sha256(access_key, secret_key)
        request_with_auth = request.copy()
        request_with_auth["auth"] = auth_config
        return self.http_request(request_with_auth)

    def close(self):
        """关闭HTTP客户端"""
        if self.http_client:
            self.http_client.close()
            logger.info("【Service】【HTTP请求】客户端已关闭")


# 创建全局实例
http_service = HttpRequestService()

# 便捷函数
def make_http_request(request: Dict[str, Any]) -> Dict[str, Any]:
    """便捷的HTTP请求函数"""
    return http_service.http_request(request)

def make_batch_requests(requests_list: list) -> Dict[str, Any]:
    """便捷的批量HTTP请求函数"""
    return http_service.batch_request(requests_list)

def download_file(url: str, save_path: str, headers: Dict[str, str] = None) -> Dict[str, Any]:
    """便捷的文件下载函数"""
    return http_service.download_file(url, save_path, headers)

def upload_file(url: str, file_path: str, field_name: str = "file",
               additional_data: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
    """便捷的文件上传函数"""
    return http_service.upload_file(url, file_path, field_name, additional_data, headers)

# 鉴权相关便捷函数
def make_request_with_basic_auth(request: Dict[str, Any], username: str, password: str) -> Dict[str, Any]:
    """使用Basic认证的便捷请求函数"""
    return http_service.request_with_basic_auth(request, username, password)

def make_request_with_bearer_token(request: Dict[str, Any], token: str) -> Dict[str, Any]:
    """使用Bearer Token的便捷请求函数"""
    return http_service.request_with_bearer_token(request, token)

def make_request_with_api_key(request: Dict[str, Any], api_key: str, header_name: str = "X-API-Key") -> Dict[str, Any]:
    """使用API Key的便捷请求函数"""
    return http_service.request_with_api_key(request, api_key, header_name)

def make_request_with_hmac_signature(request: Dict[str, Any], access_key: str, secret_key: str) -> Dict[str, Any]:
    """使用HMAC签名的便捷请求函数"""
    return http_service.request_with_hmac_signature(request, access_key, secret_key)

def create_auth_service(auth_config: Union[AuthConfig, Dict[str, Any]]) -> HttpRequestService:
    """创建带鉴权的HTTP服务实例"""
    return HttpRequestService(auth_config)

def make_request_with_json_auth(request: Dict[str, Any], auth_config: Dict[str, Any]) -> Dict[str, Any]:
    """使用JSON格式鉴权配置的便捷请求函数"""
    request_with_auth = request.copy()
    request_with_auth["auth"] = auth_config
    return http_service.http_request(request_with_auth)
