import openai
import json
import requests
from typing import Optional, Union
from common.utils.LogUtils import logger
from common.conf.ServerConfig import *
from gradio_client import Client


class ImageModelService:
    def __init__(
        self, 
        api_key=ModelDataDefault.API_KEY,
        api_base=ModelDataDefault.API_BASE,
        model_type=ModelDataDefault.MODEL_TYPE,
        deploy_mode=ModelDataDefault.DEPLOY_MODE,
    ):
        
        self.api_key = api_key
        self.deploy_mode = deploy_mode
        self.model_type = model_type
        self.api_base = api_base

        if self.api_key is None:
            headers = {'Content-Type': 'application/json'}
        else:
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        self.headers = headers

    def generate_image(
        self,
        prompt: str = "Hello!",
        n: int = 1,
        size_width: int = 1024,
        size_height: int = 1024,
        response_format: str = "url",
        guidance_scale: float = 7.5,
        num_inference_steps: int = 50,
        negative_prompt: Optional[str] = None,
        sampler_name: Optional[str] = "default",
        init_image: Optional[Union[str, bytes]] = None, 
        **kwargs
    ) -> dict:
        """
        通用图像生成服务，仅支持私有部署
        """

        logger.info(f"【Service】【ImageModel 调用】Prompt: {prompt}, Model: {self.model_type}")

        # 处理私有部署
        if self.deploy_mode == CompanyCode.PRIVATE:         
            url = self.api_base + ("/images/variations" if init_image else "/images/generations")
            
            if init_image:
                
                # 图生图/图像变体 - 使用 multipart/form-data
                files = {
                    'image': open(init_image, 'rb')  # 直接上传文件
                }
                
                data = {
                    'model': self.model_type,
                    'prompt': prompt,
                    'n': str(n),
                    'size': f"{size_width}x{size_height}",
                    'response_format': response_format,
                    'guidance_scale': str(guidance_scale),
                    'num_inference_steps': str(num_inference_steps),
                }
                
                # 添加可选参数
                if negative_prompt:
                    data['negative_prompt'] = negative_prompt
                if sampler_name:
                    data['sampler_name'] = sampler_name

                logger.info(f"【Service】【ImageModel 私有部署】请求地址: {url}，数据: {data}")

                # 注意：使用 multipart/form-data 时不需要设置 Content-Type，requests 会自动设置
                headers = {'Authorization': f'Bearer {self.api_key}'} if self.api_key else {}

                try:
                    response = requests.post(
                        url=url,
                        headers=headers,
                        files=files,  # 使用 files 参数
                        data=data,    # 使用 data 参数（不是 json）
                        timeout=600
                    )
                    response.raise_for_status()
                    result = response.json()
                    logger.info(f"【Service】【ImageModel 私有部署】返回结果: {result}")
                except Exception as e:
                    logger.error(f"图像生成请求失败（私有部署）: {e}")
                    raise
                finally:
                    # 确保文件被关闭
                    if 'files' in locals() and files.get('image'):
                        files['image'].close()
            
            else:
                # 纯文本生图 - 使用 JSON 格式
                data = {
                    "model": self.model_type,
                    "prompt": prompt,
                    "n": n,
                    "size": f"{size_width}x{size_height}",
                    "response_format": response_format,
                    "guidance_scale": guidance_scale,
                    "num_inference_steps": num_inference_steps,
                    "negative_prompt": negative_prompt,
                    "sampler_name": sampler_name
                }

                logger.info(f"【Service】【ImageModel 私有部署】请求地址: {url}，数据: {json.dumps(data)}")

                headers = {'Content-Type': 'application/json'}
                if self.api_key:
                    headers['Authorization'] = f'Bearer {self.api_key}'

                try:
                    response = requests.post(
                        url=url,
                        headers=headers,
                        timeout=600,
                        json=data
                    )
                    response.raise_for_status()
                    result = response.json()
                    logger.info(f"【Service】【ImageModel 私有部署】返回结果: {result}")
                except Exception as e:
                    logger.error(f"图像生成请求失败（私有部署）: {e}")
                    raise

        else:
            error = f"部署方式 {self.deploy_mode} 不被支持"
            logger.error(error)
            raise KeyError(error)
        

        from minio import Minio
        from minio.error import S3Error
        import subprocess
        import os

        try:
            # 提取路径 
            image_results = result['data']
            logger.info(f"共生成了 {len(image_results)} 张图片.")

            # Docker & 本地路径参数 
            docker_container_name = "90ca6d2e6ca8"
            host_output_dir = "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output"

            # MinIO 参数 
            minio_client = Minio(
                ENDPOINT,              # MinIO 地址（注意端口）
                access_key=ACCESS_KEY,
                secret_key=SECRET_KEY,
                secure=False           # 使用 HTTP 而非 HTTPS
            )

            bucket_name = MINOBUCKET 
            object_prefix = "generated-images/"
            minio_base_url = ENDPOINT  # 用于构建外部 URL

            # 检查 Bucket 是否存在 
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
                logger.info(f"创建了 MinIO bucket: {bucket_name}")

            # 遍历生成图像，并更新URL为MinIO路径
            for i, image_info in enumerate(image_results):
                image_path = image_info['url']  # 这个应该是容器内路径
                image_name = os.path.basename(image_path)
                local_image_path = os.path.join(host_output_dir, image_name)

                logger.info(f"提取第 {i + 1} 张容器内路径: {image_path}")

                # docker cp 从容器中复制文件 
                docker_cp_cmd = [
                    "docker", "cp",
                    f"{docker_container_name}:{image_path}",
                    local_image_path
                ]
                subprocess.run(docker_cp_cmd, check=True)
                logger.info(f"第 {i + 1} 张图片成功复制到宿主机: {local_image_path}")

                # 上传到 MinIO 
                try:
                    minio_client.fput_object(
                        bucket_name=bucket_name,
                        object_name=object_prefix + image_name,
                        file_path=local_image_path,
                        content_type="image/png"
                    )
                    # 构建MinIO公共访问URL
                    public_url = f"http://{minio_base_url}/{bucket_name}/{object_prefix + image_name}"
                    
                    # 更新返回结果中的URL为MinIO路径
                    image_info['url'] = public_url
                    
                    logger.info(f"第 {i + 1} 张图片已上传到 MinIO: {public_url}")
                except S3Error as e:
                    logger.error(f"第 {i + 1} 张图片上传 MinIO 失败: {e}")
                    # 上传失败时保持原URL
                    continue
            
        except Exception as e:
            logger.error(f"处理图像失败: {e}")   

        return {
            "image_result": result
        }