import json
import asyncio
import requests
import time
import traceback
from enum import Enum
from openai import OpenAI
from typing import Dict, Tuple, Generator, List, Optional, Union
from json_repair import repair_json

from fastapi import Request
from fastapi.responses import StreamingResponse

from common.conf.ServerConfig import *
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict

from gateway.service.MultimodalService import MultiModalService
from gateway.service.ImageModelService import ImageModelService
class Format(Enum):
    normal = 0
    json_object = 1
    markdown = 2

def llm_predict_handle(request, req: Request):
    """大模型统一调用入口"""
    try:
         # 基础输入参数
        query = request.get('query')
        deploy_mode = request.get('deployMode', ModelDataDefault.DEPLOY_MODE)
        company = request.get('company')
        model_type = request.get('modelType', ModelDataDefault.MODEL_TYPE)
        api_base = request.get('baseUrl', ModelDataDefault.API_BASE)

        task_name = request.get('multiType', 'text_generate')

        temperature = request.get('temperature', ModelDataDefault.TEMPERATURE)
        top_p = request.get('topP', ModelDataDefault.TOP_P)
        top_k = request.get('topK', ModelDataDefault.TOP_K)
        isDeepThink = request.get('isDeepThink',ModelDataDefault.IS_DEEP_THINK)
        
        stream = request.get('stream', False)
        param = request.get('param')

        task_name = request.get('multiType', 'text_generate')

        api_key = request.get('apiKey', ModelDataDefault.API_KEY)
        max_seq_len = request.get('inputMaxToken', ModelDataDefault.MAX_SEQ_LEN)
        max_out_len = request.get('outputMaxToken', ModelDataDefault.MAX_OUT_LEN)

        if task_name == TaskCode.TEXT:
            # 文本大模型
            model = ModelService(
                model_type=model_type,
                api_base=api_base,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                deploy_mode=deploy_mode,
                api_key=api_key,
                max_seq_len=max_seq_len,
                max_out_len=max_out_len
            )
            stream = request.get('stream', False)
            history = request.get('history', [])
            tools = request.get('tools', [])
            output_format_raw = request.get('outputFormat', Format.normal.value)
            # 将整数转换为枚举对象
            if isinstance(output_format_raw, int):
                try:
                    output_format = Format(output_format_raw)
                except ValueError:
                    output_format = Format.normal
            else:
                output_format = output_format_raw

            json_format = request.get('jsonFormat', None)

            if (stream and output_format!=Format.json_object):
                response = model.stream_chat(req, query, history, tools, output_format, json_format)
            else:
                try:
                    res = model.un_stream_chat(query, history, tools, output_format, json_format)
                    return json.loads(return_dict(str(ReturnCode.SUCESS.value), res, "success"))
                except Exception as e:
                    logger.error(f"【Service】【Model-推理】异常:{e}")
                    return json.loads(return_dict(str(ReturnCode.ERROR.value), str(e), "error"))

        elif task_name == TaskCode.IMAGETOTEXT:
            # 图生文大模型
            multi_modal = MultiModalService()

            param = request.get("param", {})

            url = param.get("fileUrl", "")
            url_type = param.get("urlType", "minio")
            content_type = param.get('mediaType')


            if not all([url, content_type]):
                raise ValueError("多模态模型调用需要提供 fileUrl 和 mediaType 参数")
                
            if stream:
                response = multi_modal.multi_model_stream(
                    url=url,
                    model=model_type,
                    model_url=api_base,
                    query=query,
                    content_type=content_type,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    url_type=url_type,
                    api_key=api_key
                )
            else:
                response = multi_modal.multi_model(
                    url=url,
                    model=model_type,
                    model_url=api_base,
                    query=query,
                    content_type=content_type,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    url_type=url_type,
                    api_key=api_key
                )

        elif task_name == TaskCode.ASR:
            # 语音识别处理
            multi_modal = MultiModalService()
            asr_path = request.get('asrPath')
            if not asr_path:
                raise ValueError("语音识别需要提供 asrPath 参数")
            response = multi_modal.asr_server(model_type, api_base, asr_path)

        elif task_name == TaskCode.TTS:
            # 语音合成处理
            multi_modal = MultiModalService()
            voice = request.get('voice', 'default')
            query = request.get('query')
            tts_path = f"tts_{int(time.time())}.mp3"
            response = multi_modal.tts_server(model_type, api_base, tts_path, voice, query)
        
        elif task_name == TaskCode.TEXTTOIMAGE:
            
            service = ImageModelService(
                api_base=request.get('baseUrl'),
                model_type=request.get('modelType'),
                deploy_mode=deploy_mode                
            )

            param = request.get("param", {})

            n = param.get('n', 1)
            size_width = param.get('sizeWidth', 1024)
            size_height = param.get('sizeHeight', 1024)
            response_format = param.get('responseFormat', 'url')
            guidance_scale = param.get('guidanceScale', 7.5)
            num_inference_steps = param.get('numInferenceSteps', 50)
            negative_prompt = param.get('negativePrompt', '')  
            sampler_name = param.get('samplerName') 
            init_image = param.get('initImage')  

            response = service.generate_image(
                prompt=query,
                n=n,
                size_width=size_width,
                size_height=size_height,
                response_format=response_format,
                guidance_scale=guidance_scale,
                num_inference_steps=num_inference_steps,
                negative_prompt=negative_prompt,
                sampler_name=sampler_name,
                init_image=init_image
            )

            logger.info(f"生成结果: {response}") 

        else:
            raise ValueError(f"不支持的任务类型: {task_name}")

        return response

    except Exception as e:
        return return_dict(str(ReturnCode.ERROR.value), str(e), traceback.format_exc())



def repair_json_response(response_text: str, output_format: Format) -> str:
    """
    修复JSON格式的响应文本
    """
    if output_format == Format.json_object:
        try:
            # 尝试解析JSON，如果失败则使用json_repair修复
            json.loads(response_text)
            return response_text
        except json.JSONDecodeError:
            try:
                logger.info(f"【JSON修复】原始响应: {response_text}")
                repaired_json = repair_json(response_text)
                logger.info(f"【JSON修复】修复后响应: {repaired_json}")
                # 验证修复后的JSON是否有效
                json.loads(repaired_json)
                return repaired_json
            except Exception as e:
                logger.error(f"【JSON修复】修复失败: {e}")
                return response_text
    return response_text

class ModelService:
    def __init__(self, api_key=ModelDataDefault.API_KEY,
                 company=ModelDataDefault.COMPANY,
                 model_type=ModelDataDefault.MODEL_TYPE,
                 api_base=ModelDataDefault.API_BASE,
                 top_p=ModelDataDefault.TOP_P,
                 top_k=ModelDataDefault.TOP_K,
                 max_seq_len=ModelDataDefault.MAX_SEQ_LEN,
                 max_out_len=ModelDataDefault.MAX_OUT_LEN,
                 deploy_mode=ModelDataDefault.DEPLOY_MODE,
                 temperature=ModelDataDefault.TEMPERATURE):
        self.api_key = api_key
        self.company = company
        self.model_name = model_type
        self.model_type = model_type
        self.api_base = api_base
        self.top_p = top_p
        self.top_k = top_k
        self.max_seq_len = max_seq_len
        self.max_out_len = max_out_len
        self.deploy_mode = deploy_mode
        self.temperature = temperature
        if self.api_key is None:
            headers = {'Content-Type': 'application/json'}
        else:
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        self.headers = headers

    # 非流式输出
    def un_stream_chat(
        self,
        inputs: Union[List, str],
        history: Optional[list] = None,
        tools: Optional[list] = None,
        output_format = Format.normal,
        json_format = None,
        **kwargs
    ):
        """
        实现具体的API调用逻辑
        """
        logger.info(f"【Service】【Model-非流式-调用】inputs:{inputs},history:{history}")
        global messages
        max_out_len = min(
            self.max_out_len,
            self.max_seq_len - self.get_token_len(str(inputs)) - 100)
        if max_out_len <= 0:
            error = f"目前大模型仅支持{self.max_seq_len}长度输入,prompt输入长度为{len(str(inputs))}，导致模型可生成长度为0,请检查输入长度"
            logger.error(f"【Service】【Model-非流式-调用】异常:{error}")
            raise ValueError(error)

        # print(f"get format point2: {output_format}")
        # 根据输出格式构造prompt
        inputs = self.prompt_for_format(
            input = inputs, 
            format = output_format, 
            json_format = json_format
        )

        # 构造messages
        if not isinstance(inputs, list):
            messages = eval(self._make_content(inputs, history))

        if self.deploy_mode == CompanyCode.PRIVATE:

            # 构造私有化模型请求参数
            data = {
                'model': self.model_type,
                'messages': messages,
                'max_tokens': self.max_seq_len,
                'temperature': self.temperature,
                'top_p': self.top_p,
                'top_k': self.top_k,
                **kwargs
            }

            if tools is not None and len(tools) > 0:
                data['tools'] = tools

            logger.info(f"【Service】【Model-非流式-模型调用】输入:{data}")

            res = requests.post(
                f'{self.api_base}',
                headers=self.headers,
                timeout=600,
                json=data
            )
            # 获取返回结果
            response = json.loads(res.text)

            logger.info(f"【Service】【Model-非流式-模型调用】输出:{response}")
        elif self.deploy_mode == CompanyCode.OPEN:
            try:
                logger.info(f"【Service】【Model-非流式-模型调用-OpenAI方式】")
                # openAi 调用开源大模型
                client = OpenAI(api_key=self.api_key, base_url=self.api_base, timeout=600)

                if tools is not None and len(tools) > 0:
                    completion = client.chat.completions.create(model=self.model_type,
                                                                temperature=self.temperature,
                                                                top_p=self.top_p,
                                                                max_tokens=self.max_seq_len,
                                                                stream=False,
                                                                tools=tools,
                                                                messages=messages)
                else:
                    completion = client.chat.completions.create(model=self.model_type,
                                                                temperature=self.temperature,
                                                                top_p=self.top_p,
                                                                max_tokens=self.max_seq_len,
                                                                stream=False,
                                                                messages=messages)
                # 获取返回结果
                response = completion.model_dump()

                logger.info(f"【Service】【Model-非流式-模型调用-OpenAI方式】输出:{response}")
            except  Exception as e:
                print(f"发生错误: {e}")
        else:
            error = f"参数company：{self.company}的部署方式{self.deploy_mode}不符合规范，请检查"
            logger.error(f"【Service】【Model-非流式-模型调用】异常:{error}")
            raise KeyError(error)

        toolCalls = None
        
        if "done_reason" in response:
            answer = response['message']['content']
            if 'tool_calls' in response['message']:
                toolCalls = response['message']['tool_calls']
            promptToken = response['prompt_eval_count']
            completionTokens = response['eval_count']
            totalTokens = promptToken + completionTokens
        else:
            answer = response["choices"][0]["message"]["content"]
            if 'tool_calls' in response["choices"][0]['message']:
                toolCalls = response["choices"][0]['message']['tool_calls']
            promptToken = response["usage"]["prompt_tokens"]
            completionTokens = response["usage"]["completion_tokens"]
            totalTokens = response["usage"]["total_tokens"]

        if answer is None or answer == '':
            error = f"目前大模型仅支持{self.max_seq_len}长度输入,而输入的prompt长度为:{len(str(inputs))}，导致模型可生成长度为0,请检查输入长度"
            logger.error(f"【Service】【Model-非流式-模型调用】异常:{error}")
            raise KeyError(error)
        else:
            # if(output_format == Format.json_object):
            #     answer = repair_json_response(answer, output_format)
            raw_response = {"answer": answer,
                            "toolCalls": toolCalls,
                            "modelType": self.model_type,
                            "promptToken": promptToken,
                            "completionTokens": completionTokens,
                            "totalTokens": totalTokens}
        return raw_response

    # 流式输出
    def stream_chat(
        self,
        request: Request,
        inputs: Union[List, str],
        history: Optional[list] = None,
        tools: Optional[list] = None,
        output_format = Format.normal,
        json_format = None,
        **kwargs
    ):
        """
        如果需要支持流式聊天，可以实现这个方法
        """
        logger.info(f"【Service】【Model-流式-调用】inputs:{inputs},history:{history}")

        async def generate_data():
            answer_str = ""
            prompt_token = self.get_token_len(str(inputs)) if isinstance(inputs, str) else sum(
                self.get_token_len(str(item)) for item in inputs)
            token_info = {
                "promptToken": prompt_token,
                "completionTokens": 0,
                "totalTokens": 100
            }

            try:
                global messages
                original_inputs = inputs # 保存原始inputs，避免变量作用域问题
                accumulated_response = "" # 用于累积完整响应的变量（用于JSON修复）
                max_out_len = min(
                    self.max_out_len,
                    self.max_seq_len - self.get_token_len(str(inputs)) - 100)
                if max_out_len <= 0:
                    error = f"目前大模型仅支持{self.max_seq_len}长度输入,prompt输入长度为{len(str(inputs))}，导致模型可生成长度为0,请检查输入长度"
                    logger.error(f"【Service】【Model-流式-模型调用】异常:{error}")
                    raise ValueError(error)
                
                inputs = self.prompt_for_format(
                    input = original_inputs,
                    format = output_format,
                    json_format = json_format
                ) # 根据输出格式构造prompt

                if not isinstance(inputs, list):
                    messages = eval(self._make_content(inputs, history))

                if self.deploy_mode == CompanyCode.PRIVATE:
                    data = {
                        "model": self.model_type,
                        "messages": messages,
                        "temperature": self.temperature,
                        "top_p": self.top_p,
                        "max_tokens": max_out_len,
                        "stream": True
                    }
                    data.update(kwargs)

                    if tools and len(tools) > 0:
                        data['tools'] = tools

                    response = requests.post(
                        f'{self.api_base}',
                        headers=self.headers,
                        timeout=(600, 10),
                        json=data,
                        stream=True
                    )

                elif self.deploy_mode == CompanyCode.OPEN:
                    client = OpenAI(api_key=self.api_key, base_url=self.api_base, timeout=10)
                    completion = client.chat.completions.create(
                        model=self.model_type,
                        messages=messages,
                        temperature=self.temperature,
                        top_p=self.top_p,
                        max_tokens=max_out_len,
                        stream=True,
                        tools=tools if tools and len(tools) > 0 else None
                    )
                    response = completion.response
                else:
                    raise ValueError(f"不支持的部署模式: {self.deploy_mode}")

                if response.status_code != 200:
                    yield f"data:{json.dumps({'retCode': '1', 'retMsg': 'error', 'retMap': {'error': f'HTTP {response.status_code}'}}, ensure_ascii=False)}\n\n"
                    return

                for line in response.iter_lines():
                    if await request.is_disconnected():
                        logger.error("用户取消了请求")
                        return

                    if not line:
                        continue

                    try:
                        line = line.decode("utf-8").strip("data: ").strip()
                        if line == "[DONE]":
                            break

                        json_line = json.loads(line)
                        answer_fragment = json_line.get("choices", [{}])[0].get("delta", {}).get("content", "")

                        # 更新 token 计数（兼容不同模型响应格式）
                        if "usage" in json_line:
                            token_info["completionTokens"] = json_line["usage"].get("completion_tokens", 0)
                        elif "eval_count" in json_line:
                            token_info["completionTokens"] = json_line.get("eval_count", 0)
                        else:
                            token_info["completionTokens"] += 1  # 默认每帧 +1 token

                        token_info["totalTokens"] = token_info["promptToken"] + token_info["completionTokens"]

                        # 流式输出当前片段和 token 信息
                        data = {
                            "retCode": "0",
                            "retMsg": "success",
                            "retMap": {
                                "answer": answer_fragment,
                                "promptToken": token_info["promptToken"],
                                "completionTokens": token_info["completionTokens"],
                                "totalTokens": token_info["totalTokens"]
                            }
                        }

                        yield f"data:{json.dumps(data, ensure_ascii=False)}\n\n"
                        # 判断是否为结束帧
                        is_final = (
                                json_line.get("done", False) or
                                ("choices" in json_line and json_line["choices"][0].get("finish_reason"))
                        )
                        if is_final:
                            # 最终结束帧
                            yield f"data:{json.dumps({'retCode': '0', 'retMsg': 'success', 'retMap': {'answer': 'Stream ended', 'promptToken': token_info['promptToken'], 'completionTokens': token_info['completionTokens'], 'totalTokens': token_info['totalTokens']}}, ensure_ascii=False)}\n\n"
                            break

                    except Exception as e:
                        logger.error(f"流式数据解析失败: {str(e)}")
                        continue

            except Exception as e:
                logger.error(f"【Service】【Model-流式-模型调用】异常: {str(e)}")
                yield f"data:{json.dumps({'retCode': '1', 'retMsg': 'error', 'retMap': {'error': str(e)}}, ensure_ascii=False)}\n\n"

        return StreamingResponse(generate_data(), media_type="text/event-stream")
    
    # 根据输出格式要求，拼接提示词
    def prompt_for_format(self, input, format, json_format):
        """
            根据输出格式构造prompt
        """
        logger.info("开始根据输出格式改变prompt")
        # print(f"get format point3: {format}")
        # print(Format.json_object)

        if(format == Format.json_object):
            logger.info("using json format")
            #logger.info(json_format)
            modified_input = json.dumps({
                "restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。",
                "text": input,
                "format": json_format
            }, ensure_ascii=False)
            logger.info(modified_input)
            return modified_input
        
        elif(format == Format.markdown):
            logger.info("using markdown format")
            return json.dumps({  
                "restriction": "给出markdown格式的回答，注意确保markdown输出完全符合规范，以下markdown格式供你参考\n\n- **标题**：使用井号（#），根据层级调整数量（如一级标题`#`，二级标题`##`，依此类推）。\n- **列表项**：以短横线（-）开头，每项单独一行（无需额外缩进）。\n- **强调文本**：用单星号包裹（`*强调内容*`），显示为斜体；若需加粗，可使用双星号（`**加粗内容**`）。\n- **代码/命令**：单行代码用反引号包裹（`代码内容`）；多行代码建议使用三个反引号（```）开启代码块。\n- **引用文本**：以大于号（>）开头，支持多行引用（每行前加`>`）。\n- **链接**：格式为`[显示文本](链接地址)`（如`[示例链接](https://example.com)`）。\n- **图片**：格式为`![替代文本](图片地址)`（如`![示例图片](https://example.com/image.jpg)`）。\n\n> 注：若内容涉及多种格式嵌套（如列表中包含代码），请保持层级清晰；无特殊说明时，优先使用基础格式（如斜体用单星号，而非其他符号）",
                "text": input
            }, ensure_ascii=False)
        
        else: 
            logger.info("using normal format")
            return input
    
    # 获取输入token数
    def get_token_len(self, prompt: str) -> int:
        """
        Description:获取分词后字符串的长度。目前只计算英文和中文字符。如果需要更精确的长度，鼓励用户重写此方法。
        Args:
            prompt (str): 输入字符串。
        Return:
            int: 输入分词的长度
        """
        return len(prompt)

    # message信息处理
    def _make_content(
            self,
            query: str,
            history: List[Tuple[str, str]] = None,
            chat_format: str = ChatFormatCode.OPENAI_FORMAT,
    ) -> str:
        """
        Description:历史封装和格式化，将用户的问题和历史信息封装成openai接口需要的输入格式，messages格式
        Args:
            query：用户输入的问题
            history：用户输入的历史信息
            chat_format：大模型支持的输入格式，openai接口格式:[{"role":"user","content":"你好"},{"role":"assistant","content":"你好"}]
        Return
           str：openai接口输入的格式
        """
        max_window_size = self.max_seq_len
        if history is None:
            history = []
        if chat_format == ChatFormatCode.OPENAI_FORMAT:
            messages = []
            current_context_size = 0
            for item in history:
                item_list = item.split(":")
                query_text = dict(role=item_list[0], content=':'.join(item_list[1:]))
                current_context_size += len(':'.join(item_list[1:]))
                if current_context_size < max_window_size:
                    messages.append(query_text)
                else:
                    break
            messages.append(dict(role="user", content=query))
            raw_text = str(messages)
        elif chat_format == ChatFormatCode.RAW_FORMAT:
            raw_text = query
        elif chat_format == ChatFormatCode.GLM_FORMAT:
            im_start, im_end = "<|im_start|>", "<|im_end|>"

            def _tokenize_str(role, content):
                return f"{role}\n{content}"

            for turn_query, turn_response in reversed(history):
                query_text = _tokenize_str("user", turn_query)
                response_text = _tokenize_str("assistant", turn_response)
                prev_chat = (
                    f"\n{im_start}{query_text}{im_end}\n{im_start}{response_text}{im_end}")
                current_context_size = (len(prev_chat))
                if current_context_size < max_window_size:
                    raw_text = prev_chat + raw_text
                else:
                    break
            raw_text += f"\n{im_start}user\n{query}{im_end}\n{im_start}assistant\n"
        else:
            raise NotImplementedError(f"Unknown chat format {chat_format!r}")
        return raw_text

