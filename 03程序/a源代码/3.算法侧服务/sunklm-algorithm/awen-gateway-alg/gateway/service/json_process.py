import json
from typing import Dict, Any, Optional, Union

from common.utils.JsonUtils import JsonUtils
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict, ReturnCode


class JsonProcessService:
    """JSON处理服务类"""

    def __init__(self):
        """初始化JSON处理服务"""
        self.json_utils = JsonUtils()
        logger.info("【Service】【JSON处理】服务初始化完成")

    def json_serialize(self, request: Dict[str, Any]) -> str:
        """
        JSON序列化服务

        Args:
            request: 请求参数字典
                - data: 要序列化的数据 (必需)
                - ensure_ascii: 是否确保ASCII编码 (可选，默认False)
                - indent: 缩进空格数 (可选，默认None)
                - sort_keys: 是否排序键 (可选，默认False)

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON序列化】输入参数: {request}")

        try:
            # 获取必需参数
            data = request.get("data")
            if data is None:
                error_msg = "缺少必需参数: data"
                logger.error(f"【Service】【JSON序列化】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 获取可选参数
            ensure_ascii = request.get("ensure_ascii", False)
            indent = request.get("indent")
            sort_keys = request.get("sort_keys", False)

            # 执行序列化
            json_str = self.json_utils.serialize(
                data,
                ensure_ascii=ensure_ascii,
                indent=indent,
                sort_keys=sort_keys
            )

            result_data = {
                "json_string": json_str,
                "length": len(json_str),
                "type": "serialized"
            }

            logger.info(f"【Service】【JSON序列化】成功，长度: {len(json_str)}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "JSON序列化成功")

        except Exception as e:
            error_msg = f"JSON序列化失败: {str(e)}"
            logger.error(f"【Service】【JSON序列化】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_deserialize(self, request: Dict[str, Any]) -> str:
        """
        JSON反序列化服务

        Args:
            request: 请求参数字典
                - json_string: 要反序列化的JSON字符串 (必需)

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON反序列化】输入参数长度: {len(str(request))}")

        try:
            # 获取必需参数
            json_string = request.get("json_string")
            if not json_string:
                error_msg = "缺少必需参数: json_string"
                logger.error(f"【Service】【JSON反序列化】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 执行反序列化
            data = self.json_utils.deserialize(json_string)

            result_data = {
                "data": data,
                "type": type(data).__name__,
                "deserialized": True
            }

            logger.info(f"【Service】【JSON反序列化】成功，类型: {type(data).__name__}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "JSON反序列化成功")

        except Exception as e:
            error_msg = f"JSON反序列化失败: {str(e)}"
            logger.error(f"【Service】【JSON反序列化】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_validate(self, request: Dict[str, Any]) -> str:
        """
        JSON格式验证服务

        Args:
            request: 请求参数字典
                - json_string: 要验证的JSON字符串 (必需)

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON验证】输入参数长度: {len(str(request))}")

        try:
            # 获取必需参数
            json_string = request.get("json_string")
            if json_string is None:
                error_msg = "缺少必需参数: json_string"
                logger.error(f"【Service】【JSON验证】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 执行验证
            is_valid = self.json_utils.is_valid_json(json_string)

            result_data = {
                "is_valid": is_valid,
                "length": len(json_string),
                "message": "JSON格式有效" if is_valid else "JSON格式无效"
            }

            logger.info(f"【Service】【JSON验证】完成，结果: {is_valid}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "JSON验证完成")

        except Exception as e:
            error_msg = f"JSON验证失败: {str(e)}"
            logger.error(f"【Service】【JSON验证】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_format(self, request: Dict[str, Any]) -> str:
        """
        JSON格式化服务（美化或压缩）

        Args:
            request: 请求参数字典
                - json_string: 要格式化的JSON字符串 (必需)
                - action: 格式化动作 ("pretty" 或 "minify") (可选，默认"pretty")
                - indent: 美化时的缩进空格数 (可选，默认2)

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON格式化】输入参数长度: {len(str(request))}")

        try:
            # 获取必需参数
            json_string = request.get("json_string")
            if not json_string:
                error_msg = "缺少必需参数: json_string"
                logger.error(f"【Service】【JSON格式化】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 获取可选参数
            action = request.get("action", "pretty")
            indent = request.get("indent", 2)

            # 执行格式化
            if action == "pretty":
                formatted_json = self.json_utils.pretty_print(
                    self.json_utils.deserialize(json_string),
                    indent=indent
                )
                action_desc = "美化"
            elif action == "minify":
                formatted_json = self.json_utils.minify(json_string)
                action_desc = "压缩"
            else:
                error_msg = f"不支持的格式化动作: {action}"
                logger.error(f"【Service】【JSON格式化】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            result_data = {
                "formatted_json": formatted_json,
                "action": action,
                "original_length": len(json_string),
                "formatted_length": len(formatted_json),
                "compression_ratio": len(formatted_json) / len(json_string) if len(json_string) > 0 else 0
            }

            logger.info(f"【Service】【JSON格式化】{action_desc}完成，原长度: {len(json_string)}, 新长度: {len(formatted_json)}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, f"JSON{action_desc}完成")

        except Exception as e:
            error_msg = f"JSON格式化失败: {str(e)}"
            logger.error(f"【Service】【JSON格式化】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_merge(self, request: Dict[str, Any]) -> str:
        """
        JSON对象合并服务

        Args:
            request: 请求参数字典
                - json_objects: JSON对象列表 (必需)
                    可以是JSON字符串列表或字典对象列表

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON合并】输入参数: {request}")

        try:
            # 获取必需参数
            json_objects = request.get("json_objects")
            if not json_objects or not isinstance(json_objects, list):
                error_msg = "缺少必需参数: json_objects (必须是列表)"
                logger.error(f"【Service】【JSON合并】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 执行合并
            merged_data = self.json_utils.merge_json_objects(*json_objects)

            result_data = {
                "merged_data": merged_data,
                "source_count": len(json_objects),
                "merged_keys": list(merged_data.keys()) if isinstance(merged_data, dict) else [],
                "key_count": len(merged_data) if isinstance(merged_data, dict) else 0
            }

            logger.info(f"【Service】【JSON合并】成功，合并了{len(json_objects)}个对象")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "JSON合并成功")

        except Exception as e:
            error_msg = f"JSON合并失败: {str(e)}"
            logger.error(f"【Service】【JSON合并】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_extract(self, request: Dict[str, Any]) -> str:
        """
        JSON数据提取服务

        Args:
            request: 请求参数字典
                - json_string: JSON字符串 (必需)
                - path: 提取路径，使用点号分隔 (必需)
                    例如: "user.profile.name"

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON提取】输入参数: {request}")

        try:
            # 获取必需参数
            json_string = request.get("json_string")
            path = request.get("path")

            if not json_string:
                error_msg = "缺少必需参数: json_string"
                logger.error(f"【Service】【JSON提取】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            if not path:
                error_msg = "缺少必需参数: path"
                logger.error(f"【Service】【JSON提取】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 反序列化JSON
            data = self.json_utils.deserialize(json_string)

            # 按路径提取数据
            path_parts = path.split('.')
            current_data = data

            for part in path_parts:
                if isinstance(current_data, dict) and part in current_data:
                    current_data = current_data[part]
                elif isinstance(current_data, list) and part.isdigit():
                    index = int(part)
                    if 0 <= index < len(current_data):
                        current_data = current_data[index]
                    else:
                        raise IndexError(f"列表索引超出范围: {index}")
                else:
                    raise KeyError(f"路径不存在: {part}")

            result_data = {
                "extracted_data": current_data,
                "path": path,
                "data_type": type(current_data).__name__,
                "success": True
            }

            logger.info(f"【Service】【JSON提取】成功，路径: {path}")
            return return_dict(str(ReturnCode.SUCESS.value), result_data, "JSON数据提取成功")

        except Exception as e:
            error_msg = f"JSON数据提取失败: {str(e)}"
            logger.error(f"【Service】【JSON提取】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

    def json_transform(self, request: Dict[str, Any]) -> str:
        """
        JSON数据转换服务

        Args:
            request: 请求参数字典
                - json_string: JSON字符串 (必需)
                - transformations: 转换规则列表 (必需)
                    每个规则包含: {"from": "source_path", "to": "target_path"}

        Returns:
            str: 标准格式的响应结果
        """
        logger.info(f"【Service】【JSON转换】输入参数: {request}")

        try:
            # 获取必需参数
            json_string = request.get("json_string")
            transformations = request.get("transformations")

            if not json_string:
                error_msg = "缺少必需参数: json_string"
                logger.error(f"【Service】【JSON转换】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            if not transformations or not isinstance(transformations, list):
                error_msg = "缺少必需参数: transformations (必须是列表)"
                logger.error(f"【Service】【JSON转换】{error_msg}")
                return return_dict(str(ReturnCode.ERROR.value), None, error_msg)

            # 反序列化JSON
            source_data = self.json_utils.deserialize(json_string)
            result_data = {}

            # 应用转换规则
            for transformation in transformations:
                from_path = transformation.get("from")
                to_path = transformation.get("to")

                if not from_path or not to_path:
                    logger.warning(f"【Service】【JSON转换】跳过无效转换规则: {transformation}")
                    continue

                try:
                    # 提取源数据
                    current_data = source_data
                    for part in from_path.split('.'):
                        if isinstance(current_data, dict) and part in current_data:
                            current_data = current_data[part]
                        else:
                            raise KeyError(f"源路径不存在: {from_path}")

                    # 设置目标数据
                    target_parts = to_path.split('.')
                    target_container = result_data

                    for part in target_parts[:-1]:
                        if part not in target_container:
                            target_container[part] = {}
                        target_container = target_container[part]

                    target_container[target_parts[-1]] = current_data

                except Exception as e:
                    logger.warning(f"【Service】【JSON转换】转换规则失败: {transformation}, 错误: {e}")
                    continue

            response_data = {
                "transformed_data": result_data,
                "source_data": source_data,
                "transformations_applied": len(transformations),
                "success": True
            }

            logger.info(f"【Service】【JSON转换】成功，应用了{len(transformations)}个转换规则")
            return return_dict(str(ReturnCode.SUCESS.value), response_data, "JSON数据转换成功")

        except Exception as e:
            error_msg = f"JSON数据转换失败: {str(e)}"
            logger.error(f"【Service】【JSON转换】{error_msg}")
            return return_dict(str(ReturnCode.ERROR.value), None, error_msg)


# 创建全局实例
json_process_service = JsonProcessService()

# 便捷函数
def process_json_serialize(request: Dict[str, Any]) -> str:
    """便捷的JSON序列化函数"""
    return json_process_service.json_serialize(request)

def process_json_deserialize(request: Dict[str, Any]) -> str:
    """便捷的JSON反序列化函数"""
    return json_process_service.json_deserialize(request)

def process_json_validate(request: Dict[str, Any]) -> str:
    """便捷的JSON验证函数"""
    return json_process_service.json_validate(request)

def process_json_format(request: Dict[str, Any]) -> str:
    """便捷的JSON格式化函数"""
    return json_process_service.json_format(request)

def process_json_merge(request: Dict[str, Any]) -> str:
    """便捷的JSON合并函数"""
    return json_process_service.json_merge(request)

def process_json_extract(request: Dict[str, Any]) -> str:
    """便捷的JSON提取函数"""
    return json_process_service.json_extract(request)

def process_json_transform(request: Dict[str, Any]) -> str:
    """便捷的JSON转换函数"""
    return json_process_service.json_transform(request)