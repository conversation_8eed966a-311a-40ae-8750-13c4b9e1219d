import json
import threading
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor

from fastapi import APIRouter, HTTPException
from fastapi import Request

from common.conf.ServerConfig import ServerConfig
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict,ReturnCode

from gateway.service.ModelService import *
from gateway.service.AgentService import AgentService
from gateway.service.McpService import call_mcp_tool
from gateway.service.CodeService import CodeService
from gateway.service.AgentToolService import AgentToolService
from gateway.service.http_request import HttpRequestService
from gateway.service.json_process import JsonProcessService
from gateway.service.document_processor import DocumentProcessor

# from gateway.BEN2.run import ImageMatting
# from gateway.RealESRGAN.run import RealESRGAN

# 创建路由实例
item_router = APIRouter()

settings = ServerConfig

thread_pool_size = settings.THREAD_POOL_SIZE
executor = ThreadPoolExecutor(max_workers=thread_pool_size)

@item_router.api_route("/{service_name}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def process_task(
        request: Request,
        service_name: str,
        path: str):
    try:
        req = await request.json()
        handleName = path
        logger.info(f"【Controller】【AI网关】请求地址:{service_name}/{handleName}, 参数:{req}")
        # 检查服务是否存在
        if service_name not in settings.TRUSTED_SERVICES:
            raise HTTPException(status_code=404, detail="服务不存在")
        # 使用 asyncio.to_thread 将阻塞任务放到线程池中执行
        if service_name == "gateway":
            # result = await asyncio.to_thread(service_handle, path,req,request)
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(executor, service_handle, handleName, req, request)
            return result
        else:
            return json.loads(return_dict(str(ReturnCode.ERROR.value), None, "error"))
    except Exception as e:
        return json.loads(return_dict(str(ReturnCode.ERROR.value), None, "error"))




def service_handle(path,req,request:Request):
    """
    统一代理请求到各个服务，自动判断流式和非流式响应
    """

    try:
        if path == "callCode":
            logger.info(f"【Controller】【AI网关-callCode】当前线程名称:{threading.current_thread().name}")
            return CodeService.code_executor(req)
        if path == "callMcpTool":
            logger.info(f"【Controller】【AI网关-callMcpTool】当前线程名称:{threading.current_thread().name}")
            return call_mcp_tool(req)
        # 模型预测接口
        if path == "chatModel":
            logger.info(f"【Controller】【AI网关-chatModel】当前线程名称:{threading.current_thread().name}")
            return llm_predict_handle(req,request)
        if path == "chatAgent":
            logger.info(f"【Controller】【AI网关-chatAgent】当前线程名称:{threading.current_thread().name}")
            if req['stream']:
                return AgentService.agent_stream_api(req)
            else:
                res = AgentService.agent_un_stream_api(req)
                return res
        if path == "rewrite":
            agentToolService = AgentToolService()
            return agentToolService.rewrite_predict(req)
        if path == "questionExpand":
            agentToolService = AgentToolService()
            return agentToolService.question_expand_predict(req)
        if path == "intentRecognition":
            agentToolService = AgentToolService()
            return agentToolService.intent_recognition(req,request)
        if path == "httpRequest":
            http_service = HttpRequestService()
            return http_service.http_request(req)
        if path == "jsonSerialize":
            json_service = JsonProcessService()
            return json_service.json_serialize(req)
        if path == "jsonDeserialize":
            json_service = JsonProcessService()
            return json_service.json_deserialize(req)
        if path == "documentProcess":
            document_processor = DocumentProcessor()
            return document_processor.process_document(req)
        # if path == "imageMatting":
        #     matting = ImageMatting()
        #     return matting.handle_request(req)
        # if path == "RealESRGAN":
        #     esrgan = RealESRGAN()
        #     return esrgan.handle_request(req)

    
    except Exception as e:
        logger.error(f"【Controller】【AI网关-service处理】异常{e}")
        return return_dict(str(ReturnCode.ERROR.value), None, f"【Controller】【AI网关-service处理】异常{e}")
