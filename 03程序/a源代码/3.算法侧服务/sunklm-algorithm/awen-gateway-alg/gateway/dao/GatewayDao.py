from common.utils.DbUtils import POOL
from common.utils.LogUtils import logger

class GateWayDao:
    def get_tools(self):
        """
        获取可用工具列表。
        """
        logger.info(f"【Dao】【获取工具列表】")
        # 数据库连接
        conn = POOL.connection()
        with conn.cursor() as cursor:
            sql = """
                SELECT tool_id,tool_name,tool_mcp_name,is_mcp FROM xj_tool_info_tb
            """
            try:
                cursor.execute(sql)
                data = cursor.fetchall()
                logger.info(f"【Dao】【获取工具列表】结果:{data}")
                return data
            except Exception as e:
                logger.error(f"【Dao】【获取工具列表】异常:{e}")