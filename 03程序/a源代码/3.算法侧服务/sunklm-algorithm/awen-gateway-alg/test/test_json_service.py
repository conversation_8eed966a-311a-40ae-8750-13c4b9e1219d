"""
测试JSON处理服务
验证json_process.py的功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_json_serialize():
    """测试JSON序列化"""
    print("=== 测试JSON序列化 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试数据
        test_data = {
            "name": "张三",
            "age": 30,
            "city": "北京",
            "hobbies": ["编程", "阅读", "音乐"],
            "profile": {
                "email": "<EMAIL>",
                "active": True,
                "score": 95.5
            }
        }
        
        # 构建请求
        request = {
            "data": test_data,
            "indent": 2,
            "ensure_ascii": False
        }
        
        # 调用序列化服务
        result = service.json_serialize(request)
        print(f"序列化结果: {result}")
        
        # 解析结果
        result_dict = json.loads(result)
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            json_length = ret_map.get("length", 0)
            print(f"✅ 序列化成功，JSON长度: {json_length}")
            #print(ret_map)
            return True
        else:
            print(f"❌ 序列化失败: {result_dict.get('retMsg')}")
            return False
            
    except Exception as e:
        print(f"❌ 序列化测试异常: {e}")
        return False

def test_json_deserialize():
    """测试JSON反序列化"""
    print("\n=== 测试JSON反序列化 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试JSON字符串
        json_string = '''
        {
            "user": {
                "id": 1001,
                "name": "李四",
                "email": "<EMAIL>",
                "tags": ["developer", "python", "ai"]
            },
            "timestamp": "2024-01-01T12:00:00Z",
            "success": true
        }
        '''
        
        # 构建请求
        request = {
            "json_string": json_string
        }
        
        # 调用反序列化服务
        result = service.json_deserialize(request)
        print(f"反序列化结果: {result}")
        
        # 解析结果
        result_dict = json.loads(result)
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            data_type = ret_map.get("type", "unknown")
            data = ret_map.get("data", {})
            print(f"✅ 反序列化成功，数据类型: {data_type}")
            print(f"   用户名: {data.get('user', {}).get('name', 'N/A')}")
            return True
        else:
            print(f"❌ 反序列化失败: {result_dict.get('retMsg')}")
            return False
            
    except Exception as e:
        print(f"❌ 反序列化测试异常: {e}")
        return False

def test_json_validate():
    """测试JSON验证"""
    print("\n=== 测试JSON验证 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试有效JSON
        print("1. 测试有效JSON:")
        valid_json = '{"name": "测试", "value": 123, "active": true}'
        request = {"json_string": valid_json}
        
        result = service.json_validate(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            is_valid = ret_map.get("is_valid", False)
            print(f"   ✅ 有效JSON验证结果: {is_valid}")
        
        # 测试无效JSON
        print("\n2. 测试无效JSON:")
        invalid_json = '{"name": "测试", "value": 123,}'  # 多余的逗号
        request = {"json_string": invalid_json}
        
        result = service.json_validate(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            is_valid = ret_map.get("is_valid", True)
            print(f"   ✅ 无效JSON验证结果: {is_valid}")
            return True
        
        return False
            
    except Exception as e:
        print(f"❌ JSON验证测试异常: {e}")
        return False

def test_json_format():
    """测试JSON格式化"""
    print("\n=== 测试JSON格式化 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试美化格式化
        print("1. 测试美化格式化:")
        compact_json = '{"name":"测试","data":[1,2,3],"nested":{"key":"value"}}'
        request = {
            "json_string": compact_json,
            "action": "pretty",
            "indent": 4
        }
        
        result = service.json_format(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            original_length = ret_map.get("original_length", 0)
            formatted_length = ret_map.get("formatted_length", 0)
            print(f"   ✅ 美化成功: {original_length} -> {formatted_length} 字符")
        
        # 测试压缩格式化
        print("\n2. 测试压缩格式化:")
        pretty_json = '''
        {
            "name": "测试",
            "data": [
                1,
                2,
                3
            ],
            "nested": {
                "key": "value"
            }
        }
        '''
        request = {
            "json_string": pretty_json,
            "action": "minify"
        }
        
        result = service.json_format(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            original_length = ret_map.get("original_length", 0)
            formatted_length = ret_map.get("formatted_length", 0)
            compression_ratio = ret_map.get("compression_ratio", 0)
            print(f"   ✅ 压缩成功: {original_length} -> {formatted_length} 字符")
            print(f"   压缩比: {compression_ratio:.2f}")
            return True
        
        return False
            
    except Exception as e:
        print(f"❌ JSON格式化测试异常: {e}")
        return False

def test_json_merge():
    """测试JSON合并"""
    print("\n=== 测试JSON合并 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试合并多个JSON对象
        json_objects = [
            {"name": "张三", "age": 30},
            {"city": "北京", "country": "中国"},
            '{"department": "技术部", "position": "工程师"}',
            {"skills": ["Python", "JavaScript"], "experience": 5}
        ]
        
        request = {
            "json_objects": json_objects
        }
        
        result = service.json_merge(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            source_count = ret_map.get("source_count", 0)
            key_count = ret_map.get("key_count", 0)
            merged_keys = ret_map.get("merged_keys", [])
            print(f"   ✅ 合并成功: {source_count}个对象 -> {key_count}个键")
            print(f"   合并的键: {', '.join(merged_keys)}")
            return True
        else:
            print(f"❌ 合并失败: {result_dict.get('retMsg')}")
            return False
            
    except Exception as e:
        print(f"❌ JSON合并测试异常: {e}")
        return False

def test_json_extract():
    """测试JSON数据提取"""
    print("\n=== 测试JSON数据提取 ===")
    
    try:
        from gateway.service.json_process import JsonProcessService
        
        service = JsonProcessService()
        
        # 测试数据
        test_data = {
            "company": {
                "name": "科技公司",
                "departments": {
                    "tech": {
                        "name": "技术部",
                        "employees": [
                            {"name": "张三", "role": "工程师"},
                            {"name": "李四", "role": "架构师"}
                        ]
                    }
                }
            }
        }
        
        json_string = json.dumps(test_data, ensure_ascii=False)
        
        # 测试提取公司名称
        request = {
            "json_string": json_string,
            "path": "company.name"
        }
        
        result = service.json_extract(request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            ret_map = result_dict.get("retMap", {})
            extracted_data = ret_map.get("extracted_data")
            data_type = ret_map.get("data_type")
            print(f"   ✅ 提取成功: '{extracted_data}' (类型: {data_type})")
            return True
        else:
            print(f"❌ 提取失败: {result_dict.get('retMsg')}")
            return False
            
    except Exception as e:
        print(f"❌ JSON提取测试异常: {e}")
        return False

def test_gateway_integration():
    """测试网关集成"""
    print("\n=== 测试网关集成 ===")
    
    try:
        from gateway.controller.GatewayController import service_handle
        from starlette.requests import Request
        from starlette.datastructures import Headers
        
        # 创建模拟请求对象
        class MockRequest:
            def __init__(self):
                self.method = "POST"
                self.url = "/test"
                self.headers = Headers({})
        
        mock_request = MockRequest()
        
        # 测试JSON序列化
        serialize_req = {
            "data": {"message": "网关集成测试", "timestamp": "2024-01-01"}
        }
        
        result = service_handle("jsonSerialize", serialize_req, mock_request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            print("   ✅ 网关JSON序列化集成成功")
        
        # 测试JSON反序列化
        deserialize_req = {
            "json_string": '{"test": "网关反序列化", "success": true}'
        }
        
        result = service_handle("jsonDeserialize", deserialize_req, mock_request)
        result_dict = json.loads(result)
        
        if result_dict.get("retCode") == "0":
            print("   ✅ 网关JSON反序列化集成成功")
            return True
        
        return False
            
    except Exception as e:
        print(f"❌ 网关集成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始JSON处理服务测试...")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("JSON序列化", test_json_serialize),
        ("JSON反序列化", test_json_deserialize),
        ("JSON验证", test_json_validate),
        ("JSON格式化", test_json_format),
        ("JSON合并", test_json_merge),
        ("JSON提取", test_json_extract),
        ("网关集成", test_gateway_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print(f"🎉 JSON处理服务测试完成！")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，JSON处理服务工作正常！")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，JSON处理服务基本可用")
    else:
        print("❌ 多数测试失败，JSON处理服务需要修复")
    
    print("\n💡 JSON处理服务已集成到网关控制器:")
    print("   • jsonSerialize - JSON序列化服务")
    print("   • jsonDeserialize - JSON反序列化服务")
    print("   • 支持request.get()参数获取")
    print("   • 使用return_dict()标准输出格式")

if __name__ == "__main__":
    main()
