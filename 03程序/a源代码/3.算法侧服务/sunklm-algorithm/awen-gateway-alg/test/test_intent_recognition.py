from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import json
import sys
import os
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 尝试导入，如果失败则跳过数据库相关功能
try:
    from gateway.service.ModelService import llm_predict_handle
    from gateway.service.AgentToolService import AgentToolService
    print("✅ 成功导入服务模块")
    IMPORT_SUCCESS = True
except Exception as e:
    print(f"⚠️ 导入服务模块失败: {e}")
    # print("📝 将使用模拟服务进行测试")
    IMPORT_SUCCESS = False

    # # 创建模拟的AgentToolService
    # class MockAgentToolService:
    #     def intent_recognition(self, request, req):
    #         query = request.get('query', '')
    #         intent_data = json.loads(request.get('intent', '{}'))
    #         intents = intent_data.get('intents', [])

    #         # 简单的关键词匹配
    #         if '画' in query or '绘制' in query or '设计' in query:
    #             for intent in intents:
    #                 if '画' in intent.get('description', ''):
    #                     return {
    #                         "retCode": "0",
    #                         "retMsg": "success (模拟)",
    #                         "retMap": {
    #                             "intentId": intent['id'],
    #                             "description": intent['description'],
    #                             "confidence": 0.95
    #                         }
    #                     }

    #         # 默认返回第一个意图
    #         if intents:
    #             return {
    #                 "retCode": "0",
    #                 "retMsg": "success (模拟)",
    #                 "retMap": {
    #                     "intentId": intents[0]['id'],
    #                     "description": intents[0]['description'],
    #                     "confidence": 0.50
    #                 }
    #             }

    #         return {
    #             "retCode": "1",
    #             "retMsg": "No intents provided",
    #             "retMap": {}
    #         }

    # AgentToolService = MockAgentToolService

class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)


request_data = {
    'model': {
        'modelType': 'DeepSeek-R1-Distill-Llama-70B',
        'baseUrl': 'http://***********:8001/v1/chat/completions',
        'temperature': 0.01,
        'topP': 0.1,
        'topK': 1,
        'inputMaxToken': 30000,
        'outputMaxToken': 30000,
        'deployMode': 0,  # 使用私有化部署模式
    },
    'prompt': "",
    'query': '我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗',
    'method': 1, 
    'history': [],  # 添加历史记录
    "intents": ["画画", "写代码", "查询信息"]
}

# 运行函数
def main():
    """主测试函数"""
    print("🚀 开始意图识别测试...")
    print("=" * 50)

    req = MockRequest()

    # 统一的测试逻辑
    use_real_service = IMPORT_SUCCESS

    try:
        if use_real_service:
            print("📋 尝试使用真实服务进行测试")
            intent_tool = AgentToolService()

            # 模拟LLM响应，避免实际调用外部服务
            mock_llm_response = '{"intentId": 1}'

            # 尝试使用Mock来避免实际的LLM调用
            if hasattr(intent_tool, 'llm'):
                with patch.object(intent_tool.llm, 'un_stream_chat', return_value=mock_llm_response):
                    result = intent_tool.intent_recognition(request=request_data, req=req)
            else:
                # 如果没有llm属性，直接调用
                result = intent_tool.intent_recognition(request=request_data, req=req)

            print("✅ 真实服务测试成功")

        else:
            print("📋 使用模拟服务进行测试")
            intent_tool = AgentToolService()
            result = intent_tool.intent_recognition(request=request_data, req=req)
            print("✅ 模拟服务测试成功")

        print(f"📊 测试结果: {result}")

        # 验证结果
        if result.get('retCode') == '0':
            ret_map = result.get('retMap', {})
            if isinstance(ret_map, str):
                try:
                    intent_result = json.loads(ret_map)
                    intent_id = intent_result.get('intentId')
                    print(f"🎯 识别的意图ID: {intent_id}")
                except:
                    print(f"🎯 识别结果: {ret_map}")
            else:
                intent_id = ret_map.get('intentId')
                description = ret_map.get('description')
                confidence = ret_map.get('confidence')
                print(f"🎯 识别意图: {description} (ID: {intent_id}, 置信度: {confidence})")
        else:
            print(f"❌ 服务返回错误: {result.get('retMsg')}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        if use_real_service:
            print("📝 真实服务失败，尝试模拟服务")
            try:
                intent_tool = AgentToolService()
                result = intent_tool.intent_recognition(request=request_data, req=req)
                print("✅ 模拟服务测试成功")
                print(f"📊 测试结果: {result}")
            except Exception as e2:
                print(f"❌ 模拟服务也失败: {e2}")

    print("\n" + "=" * 50)
    print("🎉 意图识别测试完成！")

    # 测试不同的查询
    #test_additional_queries()


# def test_additional_queries():
#     """测试额外的查询案例"""
#     print("\n📝 测试额外的查询案例...")

#     test_cases = [
#         {
#             "query": "帮我写一个Python排序算法",
#             "expected": "写代码"
#         },
#         {
#             "query": "设计一个logo",
#             "expected": "画画"
#         },
#         {
#             "query": "今天天气怎么样",
#             "expected": "其他"
#         }
#     ]

#     intent_tool = AgentToolService()
#     req = MockRequest()

#     for i, test_case in enumerate(test_cases, 1):
#         print(f"\n   测试案例 {i}: {test_case['query']}")

#         test_request = request_data.copy()
#         test_request['query'] = test_case['query']

#         try:
#             if IMPORT_SUCCESS and hasattr(intent_tool, 'llm'):
#                 # 根据查询内容模拟不同的响应
#                 if '代码' in test_case['query'] or '算法' in test_case['query']:
#                     mock_response = '{"intentId": 2}'
#                 elif '画' in test_case['query'] or '设计' in test_case['query']:
#                     mock_response = '{"intentId": 1}'
#                 else:
#                     mock_response = '{"intentId": 1}'  # 默认

#                 with patch.object(intent_tool.llm, 'un_stream_chat', return_value=mock_response):
#                     result = intent_tool.intent_recognition(request=test_request, req=req)
#             else:
#                 result = intent_tool.intent_recognition(request=test_request, req=req)

#             if result.get('retCode') == '0':
#                 ret_map = result.get('retMap', {})
#                 if isinstance(ret_map, str):
#                     try:
#                         intent_result = json.loads(ret_map)
#                         intent_id = intent_result.get('intentId')
#                     except:
#                         intent_id = "解析失败"
#                 else:
#                     intent_id = ret_map.get('intentId', '未知')

#                 print(f"      ✅ 识别结果: 意图ID {intent_id}")
#             else:
#                 print(f"      ❌ 识别失败: {result.get('retMsg')}")

#         except Exception as e:
#             print(f"      ❌ 测试失败: {e}")


if __name__ == "__main__":
    main()


# 构造 request 参数（字典）
# request_data =  {
#     'modelId': '0353472512',
#     'modelType': 'qwen2-vl-7b-instruct-awq',
#     'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
#     'query': '张三才22岁，不想这么早就因为写代码而头秃',
#     'temperature': 0.01,
#     'topP': 0.1,
#     'topK': 1,
#     'inputMaxToken': 30000,
#     'outputMaxToken': 30000,
#     'multiType': '',
#     'stream': False,
#     'deployMode': 0,
#     'isDeepThink': False
#     ''
# }
#
# # 运行函数
# def main():
#     req = MockRequest()
#     result = llm_predict_handle(request_data, req)
#     print("测试结果:")
#     print(result)

# if __name__ == "__main__":
#     main()

