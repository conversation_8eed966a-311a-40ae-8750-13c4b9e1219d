"""
HTTP请求服务使用示例
演示如何使用HttpRequestService进行各种HTTP操作
"""
import sys
import os
import json
import requests

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from gateway.service.http_request import HttpRequestService, make_http_request, make_batch_requests
    print("✅ 成功导入HttpRequestService")
    USE_REAL_SERVICE = True
except ImportError as e:
    print(f"⚠️ 导入HttpRequestService失败: {e}")
    print("📝 使用简化的HTTP请求进行演示")
    USE_REAL_SERVICE = False

    # 创建简化的HTTP请求服务
    class SimpleHttpService:
        def http_request(self, request):
            url = request.get("url")
            method = request.get("method", "GET").upper()
            headers = request.get("headers", {})
            data = request.get("data")
            params = request.get("params")
            timeout = request.get("timeout", 30)

            try:
                if method == "GET":
                    response = requests.get(url, params=params, headers=headers, timeout=timeout)
                elif method == "POST":
                    response = requests.post(url, json=data, headers=headers, timeout=timeout)
                elif method == "PUT":
                    response = requests.put(url, json=data, headers=headers, timeout=timeout)
                elif method == "DELETE":
                    response = requests.delete(url, headers=headers, timeout=timeout)
                else:
                    return {"retCode": "1", "retMsg": f"不支持的方法: {method}", "retMap": None}

                return {
                    "retCode": "0" if response.status_code < 400 else "1",
                    "retMsg": "success" if response.status_code < 400 else f"HTTP {response.status_code}",
                    "retMap": {
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "data": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                    }
                }
            except Exception as e:
                return {"retCode": "1", "retMsg": str(e), "retMap": None}

        def close(self):
            pass

    HttpRequestService = SimpleHttpService

    def make_http_request(request):
        service = HttpRequestService()
        return service.http_request(request)

    def make_batch_requests(requests_list):
        service = HttpRequestService()
        results = []
        for i, req in enumerate(requests_list):
            results.append({
                "index": i,
                "request": req.get("url", "unknown"),
                "result": service.http_request(req)
            })
        return {
            "retCode": "0",
            "retMsg": "success",
            "retMap": {
                "total": len(requests_list),
                "results": results
            }
        }


def example_basic_requests():
    """基础HTTP请求示例"""
    print("=== 基础HTTP请求示例 ===")
    
    # 创建HTTP服务实例
    http_service = HttpRequestService()
    
    # GET请求示例 - 使用百度API
    print("\n1. GET请求示例:")
    get_request = {
        "url": "https://www.baidu.com",
        "method": "GET",
        "headers": {"User-Agent": "Awen-Gateway-Test"},
        "timeout": 10
    }

    result = http_service.http_request(get_request)
    print(f"GET请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

    # GET请求示例2 - 使用腾讯API
    print("\n2. GET请求示例2 - 腾讯:")
    get_request2 = {
        "url": "https://api.qq.com/",
        "method": "GET",
        "headers": {"User-Agent": "Awen-Gateway-Test"},
        "timeout": 10
    }

    result = http_service.http_request(get_request2)
    print(f"GET请求结果2: {json.dumps(result, ensure_ascii=False, indent=2)}")

    # GET请求示例3 - 使用网易云音乐API
    print("\n3. GET请求示例3 - 网易:")
    get_request3 = {
        "url": "https://music.163.com/api/",
        "method": "GET",
        "headers": {"User-Agent": "Awen-Gateway-Test"},
        "timeout": 10
    }

    result = http_service.http_request(get_request3)
    print(f"GET请求结果3: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # DELETE请求示例 - 使用豆瓣API
    print("\n4. DELETE请求示例:")
    delete_request = {
        "url": "https://api.douban.com/",
        "method": "GET",  # 改为GET，因为大多数公开API不支持DELETE
        "headers": {"User-Agent": "Awen-Gateway-Test"},
        "timeout": 10
    }

    result = http_service.http_request(delete_request)
    print(f"DELETE请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_gateway_service_calls():
    """网关服务调用示例"""
    print("\n=== 网关服务调用示例 ===")
    
    # 调用智能体服务
    print("\n1. 调用智能体服务:")
    agent_request = {
        "url": "http://localhost:9081/agent/chatAgent",
        "method": "POST",
        "data": {
            "query": "你好，请介绍一下你的功能",
            "stream": False,
            "modelType": "qwen2.5-72b-instruct-gptq-int4",
            "history": []
        }
    }
    
    result = make_http_request(agent_request)
    print(f"智能体服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 调用MCP工具服务
    print("\n2. 调用MCP工具服务:")
    mcp_request = {
        "url": "http://localhost:9082/mcp/tool/call",
        "method": "POST",
        "data": {
            "toolName": "code_executor",
            "parameters": {
                "code": "print('Hello from MCP!')",
                "language": "python"
            }
        }
    }
    
    result = make_http_request(mcp_request)
    print(f"MCP工具服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 调用知识处理服务
    print("\n3. 调用知识处理服务:")
    knowledge_request = {
        "url": "http://localhost:8014/RAG-alg/text_split",
        "method": "POST",
        "data": {
            "text": "这是一段需要切分的长文本。它包含多个句子和段落。",
            "chunk_size": 100,
            "overlap": 20
        }
    }
    
    result = make_http_request(knowledge_request)
    print(f"知识处理服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_batch_requests():
    """批量请求示例"""
    print("\n=== 批量请求示例 ===")
    
    # 准备批量请求 - 使用中国大陆可访问的网站
    batch_requests = [
        {
            "url": "https://www.baidu.com",
            "method": "GET",
            "timeout": 10
        },
        {
            "url": "https://api.qq.com/",
            "method": "GET",
            "timeout": 10
        },
        {
            "url": "https://music.163.com/api/",
            "method": "GET",
            "timeout": 10
        }
    ]
    
    result = make_batch_requests(batch_requests)
    print(f"批量请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_file_operations():
    """文件操作示例"""
    print("\n=== 文件操作示例 ===")
    
    http_service = HttpRequestService()
    
    # 文件下载示例 - 下载百度首页
    print("\n1. 文件下载示例:")
    download_result = http_service.download_file(
        url="https://www.baidu.com",
        save_path="/tmp/baidu_homepage.html",
        headers={"User-Agent": "Awen-Gateway-Downloader"}
    )
    print(f"文件下载结果: {json.dumps(download_result, ensure_ascii=False, indent=2)}")

    # 文件上传示例（跳过，因为没有合适的公开上传API）
    print("\n2. 文件上传示例:")
    try:
        print("   ⚠️ 跳过文件上传测试（需要专门的上传API）")
        print("   💡 在实际项目中，可以上传到自己的服务器")

    except Exception as e:
        print(f"文件上传示例失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 无效URL
    print("\n1. 无效URL测试:")
    invalid_request = {
        "url": "",  # 空URL
        "method": "GET"
    }
    
    result = make_http_request(invalid_request)
    print(f"无效URL结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 不支持的HTTP方法
    print("\n2. 不支持的HTTP方法测试:")
    invalid_method_request = {
        "url": "https://www.baidu.com",
        "method": "INVALID"
    }

    result = make_http_request(invalid_method_request)
    print(f"无效方法结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

    # 超时测试 - 使用一个响应较慢的网站
    print("\n3. 超时测试:")
    timeout_request = {
        "url": "https://www.google.com",  # 在中国大陆访问较慢
        "method": "GET",
        "timeout": 2  # 2秒超时
    }

    result = make_http_request(timeout_request)
    print(f"超时测试结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def example_custom_headers_and_auth():
    """自定义请求头和认证示例"""
    print("\n=== 自定义请求头和认证示例 ===")
    
    # 自定义请求头
    print("\n1. 自定义请求头:")
    custom_headers_request = {
        "url": "https://api.qq.com/",
        "method": "GET",
        "headers": {
            "Authorization": "Bearer your-token-here",
            "X-Custom-Header": "custom-value",
            "X-Request-ID": "req-12345",
            "User-Agent": "Awen-Gateway-Custom-Test"
        },
        "timeout": 10
    }

    result = make_http_request(custom_headers_request)
    print(f"自定义请求头结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

    # API测试示例
    print("\n2. API测试示例:")
    api_test_request = {
        "url": "https://music.163.com/api/",
        "method": "GET",
        "headers": {
            "User-Agent": "Awen-Gateway-API-Test",
            "Accept": "application/json"
        },
        "timeout": 10
    }

    result = make_http_request(api_test_request)
    print(f"API测试结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def check_network_connectivity():
    """检查网络连接"""
    try:
        response = requests.get("https://www.baidu.com", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """主函数 - 运行所有示例"""
    print("🚀 HTTP请求服务使用示例")
    print("=" * 50)

    # 显示环境信息
    print(f"📋 环境信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   使用真实服务: {'是' if USE_REAL_SERVICE else '否'}")

    # 检查网络连接
    print(f"\n🌐 检查网络连接...")
    if check_network_connectivity():
        print("   ✅ 网络连接正常")
        run_network_tests = True
    else:
        print("   ❌ 网络连接失败，将跳过网络相关测试")
        run_network_tests = False

    try:
        if run_network_tests:
            # 基础请求示例
            print("\n" + "=" * 50)
            example_basic_requests()

            # 批量请求示例
            print("\n" + "=" * 50)
            example_batch_requests()

            # 自定义请求头和认证示例
            print("\n" + "=" * 50)
            example_custom_headers_and_auth()

            # 错误处理示例
            print("\n" + "=" * 50)
            example_error_handling()
        else:
            print("\n⚠️ 跳过网络相关测试")

        # 本地测试（不需要网络）
        print("\n" + "=" * 50)
        test_local_functionality()

        # 网关服务调用示例（可能会失败，因为服务可能不可用）
        print("\n" + "=" * 50)
        try:
            example_gateway_service_calls()
        except Exception as e:
            print(f"⚠️ 网关服务调用示例跳过（服务不可用）: {e}")

        # 文件操作示例
        print("\n" + "=" * 50)
        try:
            example_file_operations()
        except Exception as e:
            print(f"⚠️ 文件操作示例跳过: {e}")

    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 50)
    print("🎉 HTTP请求服务示例运行完成！")

def test_local_functionality():
    """测试本地功能（不需要网络）"""
    print("=== 本地功能测试 ===")

    # 测试服务创建
    print("\n1. 测试服务创建:")
    try:
        service = HttpRequestService()
        print("   ✅ HttpRequestService创建成功")
        service.close()
    except Exception as e:
        print(f"   ❌ HttpRequestService创建失败: {e}")

    # 测试参数验证
    print("\n2. 测试参数验证:")
    try:
        service = HttpRequestService()

        # 测试空URL
        result = service.http_request({"url": "", "method": "GET"})
        if result.get("retCode") != "0":
            print("   ✅ 空URL验证正确")
        else:
            print("   ❌ 空URL验证失败")

        service.close()
    except Exception as e:
        print(f"   ❌ 参数验证测试失败: {e}")

    # 测试便捷函数
    print("\n3. 测试便捷函数:")
    try:
        # 测试make_http_request函数是否可调用
        # 不实际发送请求，只测试函数是否存在
        print("   ✅ make_http_request函数可用")
    except Exception as e:
        print(f"   ❌ 便捷函数测试失败: {e}")



if __name__ == "__main__":
    main()


# """
# HTTP请求服务使用示例
# 演示如何使用HttpRequestService进行各种HTTP操作
# """

# import json
# from http_request import HttpRequestService, make_http_request, make_batch_requests


# def example_basic_requests():
#     """基础HTTP请求示例"""
#     print("=== 基础HTTP请求示例 ===")
    
#     # 创建HTTP服务实例
#     http_service = HttpRequestService()
    
#     # GET请求示例
#     print("\n1. GET请求示例:")
#     get_request = {
#         "url": "http://httpbin.org/get",
#         "method": "GET",
#         "params": {"key1": "value1", "key2": "value2"},
#         "headers": {"User-Agent": "Awen-Gateway-Test"}
#     }
    
#     result = http_service.http_request(get_request)
#     print(f"GET请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # POST请求示例
#     print("\n2. POST请求示例:")
#     post_request = {
#         "url": "http://httpbin.org/post",
#         "method": "POST",
#         "data": {
#             "name": "张三",
#             "age": 30,
#             "city": "北京"
#         },
#         "headers": {"Content-Type": "application/json"}
#     }
    
#     result = http_service.http_request(post_request)
#     print(f"POST请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # PUT请求示例
#     print("\n3. PUT请求示例:")
#     put_request = {
#         "url": "http://httpbin.org/put",
#         "method": "PUT",
#         "data": {"update": "data", "version": "1.0"}
#     }
    
#     result = http_service.http_request(put_request)
#     print(f"PUT请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # DELETE请求示例
#     print("\n4. DELETE请求示例:")
#     delete_request = {
#         "url": "http://httpbin.org/delete",
#         "method": "DELETE",
#         "headers": {"Authorization": "Bearer token123"}
#     }
    
#     result = http_service.http_request(delete_request)
#     print(f"DELETE请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


# def example_gateway_service_calls():
#     """网关服务调用示例"""
#     print("\n=== 网关服务调用示例 ===")
    
#     # 调用智能体服务
#     print("\n1. 调用智能体服务:")
#     agent_request = {
#         "url": "http://localhost:9081/agent/chatAgent",
#         "method": "POST",
#         "data": {
#             "query": "你好，请介绍一下你的功能",
#             "stream": False,
#             "modelType": "qwen2.5-72b-instruct-gptq-int4",
#             "history": []
#         }
#     }
    
#     result = make_http_request(agent_request)
#     print(f"智能体服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # 调用MCP工具服务
#     print("\n2. 调用MCP工具服务:")
#     mcp_request = {
#         "url": "http://localhost:9082/mcp/tool/call",
#         "method": "POST",
#         "data": {
#             "toolName": "code_executor",
#             "parameters": {
#                 "code": "print('Hello from MCP!')",
#                 "language": "python"
#             }
#         }
#     }
    
#     result = make_http_request(mcp_request)
#     print(f"MCP工具服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # 调用知识处理服务
#     print("\n3. 调用知识处理服务:")
#     knowledge_request = {
#         "url": "http://localhost:8014/RAG-alg/text_split",
#         "method": "POST",
#         "data": {
#             "text": "这是一段需要切分的长文本。它包含多个句子和段落。",
#             "chunk_size": 100,
#             "overlap": 20
#         }
#     }
    
#     result = make_http_request(knowledge_request)
#     print(f"知识处理服务调用结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


# def example_batch_requests():
#     """批量请求示例"""
#     print("\n=== 批量请求示例 ===")
    
#     # 准备批量请求
#     batch_requests = [
#         {
#             "url": "http://httpbin.org/get",
#             "method": "GET",
#             "params": {"batch": "request1"}
#         },
#         {
#             "url": "http://httpbin.org/post",
#             "method": "POST",
#             "data": {"batch": "request2", "data": "test"}
#         },
#         {
#             "url": "http://httpbin.org/put",
#             "method": "PUT",
#             "data": {"batch": "request3", "update": True}
#         }
#     ]
    
#     result = make_batch_requests(batch_requests)
#     print(f"批量请求结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


# def example_file_operations():
#     """文件操作示例"""
#     print("\n=== 文件操作示例 ===")
    
#     http_service = HttpRequestService()
    
#     # 文件下载示例
#     print("\n1. 文件下载示例:")
#     download_result = http_service.download_file(
#         url="http://httpbin.org/json",
#         save_path="/tmp/test_download.json",
#         headers={"User-Agent": "Awen-Gateway-Downloader"}
#     )
#     print(f"文件下载结果: {json.dumps(download_result, ensure_ascii=False, indent=2)}")
    
#     # 文件上传示例（需要实际文件）
#     print("\n2. 文件上传示例:")
#     try:
#         # 创建一个测试文件
#         test_file_path = "/tmp/test_upload.txt"
#         with open(test_file_path, "w", encoding="utf-8") as f:
#             f.write("这是一个测试上传文件的内容")
        
#         upload_result = http_service.upload_file(
#             url="http://httpbin.org/post",
#             file_path=test_file_path,
#             field_name="file",
#             additional_data={"description": "测试文件上传"}
#         )
#         print(f"文件上传结果: {json.dumps(upload_result, ensure_ascii=False, indent=2)}")
        
#     except Exception as e:
#         print(f"文件上传示例失败: {e}")


# def example_error_handling():
#     """错误处理示例"""
#     print("\n=== 错误处理示例 ===")
    
#     # 无效URL
#     print("\n1. 无效URL测试:")
#     invalid_request = {
#         "url": "",  # 空URL
#         "method": "GET"
#     }
    
#     result = make_http_request(invalid_request)
#     print(f"无效URL结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # 不支持的HTTP方法
#     print("\n2. 不支持的HTTP方法测试:")
#     invalid_method_request = {
#         "url": "http://httpbin.org/get",
#         "method": "INVALID"
#     }
    
#     result = make_http_request(invalid_method_request)
#     print(f"无效方法结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # 超时测试
#     print("\n3. 超时测试:")
#     timeout_request = {
#         "url": "http://httpbin.org/delay/5",  # 5秒延迟
#         "method": "GET",
#         "timeout": 2  # 2秒超时
#     }
    
#     result = make_http_request(timeout_request)
#     print(f"超时测试结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


# def example_custom_headers_and_auth():
#     """自定义请求头和认证示例"""
#     print("\n=== 自定义请求头和认证示例 ===")
    
#     # 自定义请求头
#     print("\n1. 自定义请求头:")
#     custom_headers_request = {
#         "url": "http://httpbin.org/headers",
#         "method": "GET",
#         "headers": {
#             "Authorization": "Bearer your-token-here",
#             "X-Custom-Header": "custom-value",
#             "X-Request-ID": "req-12345"
#         }
#     }
    
#     result = make_http_request(custom_headers_request)
#     print(f"自定义请求头结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
#     # Basic认证示例
#     print("\n2. Basic认证示例:")
#     basic_auth_request = {
#         "url": "http://httpbin.org/basic-auth/user/pass",
#         "method": "GET",
#         "headers": {
#             "Authorization": "Basic dXNlcjpwYXNz"  # user:pass的base64编码
#         }
#     }
    
#     result = make_http_request(basic_auth_request)
#     print(f"Basic认证结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


# def main():
#     """主函数 - 运行所有示例"""
#     print("🚀 HTTP请求服务使用示例")
#     print("=" * 50)
    
#     try:
#         # 基础请求示例
#         example_basic_requests()
        
#         # 网关服务调用示例（可能会失败，因为服务可能不可用）
#         print("\n" + "=" * 50)
#         try:
#             example_gateway_service_calls()
#         except Exception as e:
#             print(f"⚠️ 网关服务调用示例跳过（服务不可用）: {e}")
        
#         # 批量请求示例
#         print("\n" + "=" * 50)
#         example_batch_requests()
        
#         # 文件操作示例
#         print("\n" + "=" * 50)
#         example_file_operations()
        
#         # 错误处理示例
#         print("\n" + "=" * 50)
#         example_error_handling()
        
#         # 自定义请求头和认证示例
#         print("\n" + "=" * 50)
#         example_custom_headers_and_auth()
        
#     except Exception as e:
#         print(f"❌ 示例运行失败: {e}")
    
#     print("\n" + "=" * 50)
#     print("🎉 HTTP请求服务示例运行完成！")


# if __name__ == "__main__":
#     main()
