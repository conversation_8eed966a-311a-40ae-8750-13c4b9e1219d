"""
使用bochaai.com测试HTTP组件功能
专门测试HTTP请求服务与博查AI API的集成
"""

import sys
import os
import json
import requests

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_direct_requests():
    """直接使用requests测试bochaai.com连通性"""
    print("=== 直接测试bochaai.com连通性 ===")
    
    try:
        # 测试基础连接
        print("1. 测试基础连接:")
        response = requests.get("https://api.bochaai.com", timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应内容: {response.text[:200]}...")
        
        # 测试API端点
        print("\n2. 测试API端点:")
        response = requests.post(
            "https://api.bochaai.com/v1/web-search",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-test"
            },
            json={"query": "测试查询"},
            timeout=15
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 直接请求失败: {e}")
        return False

def test_http_service_import():
    """测试HTTP服务导入"""
    print("\n=== 测试HTTP服务导入 ===")
    
    try:
        from gateway.service.http_request import HttpRequestService
        print("   ✅ 成功导入HttpRequestService")
        return True, HttpRequestService
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False, None

def test_bochaai_web_search(HttpRequestService):
    """测试博查AI Web搜索功能"""
    print("\n=== 测试博查AI Web搜索功能 ===")
    
    if not HttpRequestService:
        print("   ⚠️ 跳过测试（HttpRequestService未导入）")
        return False
    
    try:
        # 创建HTTP服务实例
        http_service = HttpRequestService()
        
        # 测试查询列表
        test_queries = [
            "Python编程",
            "人工智能",
            "机器学习"
        ]
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. 测试查询: {query}")
            
            # 构建请求
            request_data = {
                "url": "https://api.bochaai.com/v1/web-search",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa"
                },
                "data": {
                    "query": query,
                    "summary": True
                },
                "timeout": 30
            }
            
            try:
                # 调用HTTP服务
                result = http_service.http_request(request_data)
                
                # 解析结果
                if isinstance(result, str):
                    result_dict = json.loads(result)
                else:
                    result_dict = result
                
                if result_dict.get('retCode') == '0':
                    ret_map = result_dict.get('retMap', {})
                    status_code = ret_map.get('status_code', 'N/A')
                    print(f"   ✅ 请求成功，状态码: {status_code}")
                    
                    # 检查响应数据
                    response_data = ret_map.get('data')
                    if response_data:
                        if isinstance(response_data, dict):
                            if 'results' in response_data:
                                results_count = len(response_data.get('results', []))
                                print(f"   📝 搜索结果数量: {results_count}")
                            if 'summary' in response_data:
                                summary = response_data.get('summary', '')[:100]
                                print(f"   📄 摘要预览: {summary}...")
                        else:
                            preview = str(response_data)[:200]
                            print(f"   📄 响应预览: {preview}...")
                    
                    print(f" retMap: {ret_map}")
                    success_count += 1
                else:
                    print(f"   ❌ 请求失败: {result_dict.get('retMsg', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
        
        print(f"\n博查AI Web搜索测试: {success_count}/{len(test_queries)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False
    finally:
        try:
            http_service.close()
        except:
            pass

def test_bochaai_auth_methods(HttpRequestService):
    """测试不同的认证方法"""
    print("\n=== 测试不同认证方法 ===")
    
    if not HttpRequestService:
        print("   ⚠️ 跳过测试（HttpRequestService未导入）")
        return False
    
    try:
        # 测试1: 直接在headers中传递认证
        print("1. 测试直接headers认证:")
        http_service1 = HttpRequestService()
        
        request1 = {
            "url": "https://api.bochaai.com/v1/web-search",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa"
            },
            "data": {
                "query": "headers认证测试",
                "summary": True
            },
            "timeout": 20
        }
        
        result1 = http_service1.http_request(request1)
        result1_dict = json.loads(result1) if isinstance(result1, str) else result1
        
        if result1_dict.get('retCode') == '0':
            print("   ✅ headers认证成功")
        else:
            print(f"   ❌ headers认证失败: {result1_dict.get('retMsg')}")
        
        http_service1.close()
        
        # 测试2: 使用JSON格式认证配置
        print("\n2. 测试JSON格式认证:")
        auth_config = {
            "type": "bearer",
            "token": "sk-7a8dd800f6ee48f08ecccf77efa9fcaa"
        }
        
        http_service2 = HttpRequestService(auth_config)
        
        request2 = {
            "url": "https://api.bochaai.com/v1/web-search",
            "method": "POST",
            "data": {
                "query": "JSON认证测试",
                "summary": True
            },
            "timeout": 20
        }
        
        result2 = http_service2.http_request(request2)
        result2_dict = json.loads(result2) if isinstance(result2, str) else result2
        
        if result2_dict.get('retCode') == '0':
            print("   ✅ JSON认证成功")
        else:
            print(f"   ❌ JSON认证失败: {result2_dict.get('retMsg')}")
        
        http_service2.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 认证测试异常: {e}")
        return False

def test_error_handling(HttpRequestService):
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    if not HttpRequestService:
        print("   ⚠️ 跳过测试（HttpRequestService未导入）")
        return False
    
    try:
        http_service = HttpRequestService()
        
        # 测试1: 无效API Key
        print("1. 测试无效API Key:")
        invalid_request = {
            "url": "https://api.bochaai.com/v1/web-search",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer invalid-key"
            },
            "data": {
                "query": "测试查询",
                "summary": True
            },
            "timeout": 15
        }
        
        result = http_service.http_request(invalid_request)
        result_dict = json.loads(result) if isinstance(result, str) else result
        
        if result_dict.get('retCode') != '0':
            print("   ✅ 正确处理了无效API Key")
        else:
            ret_map = result_dict.get('retMap', {})
            status_code = ret_map.get('status_code', 200)
            if status_code >= 400:
                print("   ✅ 正确返回了错误状态码")
            else:
                print("   ⚠️ 无效API Key未被正确拒绝")
        
        # 测试2: 空查询
        print("\n2. 测试空查询:")
        empty_request = {
            "url": "https://api.bochaai.com/v1/web-search",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa"
            },
            "data": {
                "query": "",
                "summary": True
            },
            "timeout": 15
        }
        
        result = http_service.http_request(empty_request)
        result_dict = json.loads(result) if isinstance(result, str) else result
        
        if result_dict.get('retCode') == '0':
            ret_map = result_dict.get('retMap', {})
            status_code = ret_map.get('status_code', 200)
            print(f"   状态码: {status_code}")
            if status_code >= 400:
                print("   ✅ 正确处理了空查询")
            else:
                print("   ⚠️ 空查询被接受")
        else:
            print("   ✅ 正确拒绝了空查询")
        
        http_service.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 错误处理测试异常: {e}")
        return False

def test_gateway_integration():
    """测试网关集成"""
    print("\n=== 测试网关集成 ===")
    
    try:
        from gateway.controller.GatewayController import service_handle
        
        # 创建模拟请求对象
        class MockRequest:
            def __init__(self):
                self.method = "POST"
                self.url = "/httpRequest"
                self.headers = {}
        
        mock_request = MockRequest()
        
        # 测试通过网关调用HTTP服务
        gateway_request = {
            "url": "https://api.bochaai.com/v1/web-search",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa"
            },
            "data": {
                "query": "网关集成测试",
                "summary": True
            },
            "timeout": 20
        }
        
        result = service_handle("httpRequest", gateway_request, mock_request)
        
        if isinstance(result, str):
            result_dict = json.loads(result)
        else:
            result_dict = result
        
        if result_dict.get('retCode') == '0':
            print("   ✅ 网关集成测试成功")
            return True
        else:
            print(f"   ❌ 网关集成测试失败: {result_dict.get('retMsg')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 网关集成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 使用bochaai.com测试HTTP组件功能")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("直接请求测试", test_direct_requests),
        ("HTTP服务导入", lambda: test_http_service_import()[0]),
        ("博查AI Web搜索", lambda: test_bochaai_web_search(test_http_service_import()[1])),
        ("认证方法测试", lambda: test_bochaai_auth_methods(test_http_service_import()[1])),
        ("错误处理测试", lambda: test_error_handling(test_http_service_import()[1])),
        ("网关集成测试", test_gateway_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}通过")
            else:
                print(f"❌ {test_name}失败")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            import traceback
            traceback.print_exc()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print(f"🎉 HTTP组件测试完成！")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，HTTP组件与bochaai.com集成正常！")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，HTTP组件基本可用")
    else:
        print("❌ 多数测试失败，HTTP组件需要修复")
    
    print("\n💡 测试总结:")
    print("   • 验证了与bochaai.com的网络连通性")
    print("   • 测试了HTTP请求服务的基本功能")
    print("   • 验证了Bearer Token认证机制")
    print("   • 测试了JSON格式认证配置")
    print("   • 验证了错误处理机制")
    print("   • 测试了网关控制器集成")

if __name__ == "__main__":
    main()
