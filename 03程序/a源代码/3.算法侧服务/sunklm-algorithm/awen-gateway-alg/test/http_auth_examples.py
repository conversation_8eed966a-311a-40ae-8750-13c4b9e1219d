"""
Not working, use "http_bochaai.py" instead
HTTP服务鉴权功能使用示例
演示如何在实际项目中使用各种鉴权方式
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from gateway.service.http_request import (
    HttpRequestService, AuthConfig, AuthType,
    make_request_with_basic_auth, make_request_with_bearer_token,
    make_request_with_api_key, make_request_with_hmac_signature,
    create_auth_service
)

def example_basic_auth():
    """Basic认证示例"""
    print("=== Basic认证示例 ===")
    
    # 方式1: 使用便捷函数
    request = {
        "url": "https://www.baidu.com",
        "method": "GET",
        "timeout": 10
    }
    
    print("1. 使用便捷函数进行Basic认证:")
    result = make_request_with_basic_auth(request, "admin", "password123")
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 方式2: 创建带认证的服务实例
    print("\n2. 创建带Basic认证的服务实例:")
    auth_config = AuthConfig.basic_auth("admin", "password123")
    auth_service = HttpRequestService(auth_config)
    
    result = auth_service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    auth_service.close()
    
    # 方式3: 在请求中指定认证
    print("\n3. 在单个请求中指定Basic认证:")
    service = HttpRequestService()
    request_with_auth = request.copy()
    request_with_auth["auth"] = AuthConfig.basic_auth("admin", "password123")
    
    result = service.http_request(request_with_auth)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    service.close()

def example_bearer_token():
    """Bearer Token认证示例"""
    print("\n=== Bearer Token认证示例 ===")
    
    # JWT Token示例
    jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    
    request = {
        "url": "https://api.qq.com/",
        "method": "GET",
        "timeout": 10
    }
    
    print("1. 使用JWT Token进行Bearer认证:")
    result = make_request_with_bearer_token(request, jwt_token)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # OAuth2 Access Token示例
    print("\n2. 使用OAuth2 Access Token:")
    oauth_config = AuthConfig.oauth2("access_token_12345", "Bearer")
    auth_service = create_auth_service(oauth_config)
    
    result = auth_service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    auth_service.close()

def example_api_key():
    """API Key认证示例"""
    print("\n=== API Key认证示例 ===")
    
    request = {
        "url": "https://music.163.com/api/",
        "method": "GET",
        "timeout": 10
    }
    
    # 标准API Key
    print("1. 使用标准API Key (X-API-Key):")
    result = make_request_with_api_key(request, "sk-1234567890abcdef")
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 自定义请求头名称
    print("\n2. 使用自定义请求头名称:")
    result = make_request_with_api_key(request, "custom-key-12345", "X-Custom-API-Key")
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")

def example_hmac_signature():
    """HMAC签名认证示例"""
    print("\n=== HMAC-SHA256签名认证示例 ===")
    
    request = {
        "url": "https://api.douban.com/",
        "method": "POST",
        "data": {"message": "Hello API", "timestamp": "1234567890"},
        "timeout": 10
    }
    
    print("1. 使用HMAC-SHA256签名认证:")
    result = make_request_with_hmac_signature(request, "access_key_123", "secret_key_456")
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 查看生成的签名请求头
    if result.get('retCode') != '0':
        print("   💡 HMAC签名会自动生成时间戳和签名")

def example_custom_headers():
    """自定义请求头认证示例"""
    print("\n=== 自定义请求头认证示例 ===")
    
    # 微服务间认证
    custom_auth = AuthConfig.custom_headers({
        "X-Service-Token": "service-token-12345",
        "X-Client-ID": "gateway-service",
        "X-Request-Source": "awen-gateway"
    })
    
    request = {
        "url": "https://www.baidu.com",
        "method": "GET",
        "timeout": 10
    }
    
    print("1. 使用自定义请求头进行微服务认证:")
    service = HttpRequestService(custom_auth)
    result = service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    service.close()

def example_dynamic_auth():
    """动态切换认证示例"""
    print("\n=== 动态切换认证示例 ===")
    
    service = HttpRequestService()
    
    request = {
        "url": "https://www.baidu.com",
        "method": "GET",
        "timeout": 10
    }
    
    # 1. 无认证请求
    print("1. 发送无认证请求:")
    result = service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 2. 设置Basic认证
    print("\n2. 设置Basic认证后发送请求:")
    service.set_auth(AuthConfig.basic_auth("user", "pass"))
    result = service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 3. 切换到Bearer Token认证
    print("\n3. 切换到Bearer Token认证:")
    service.set_auth(AuthConfig.bearer_token("token-12345"))
    result = service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    # 4. 清除认证
    print("\n4. 清除认证后发送请求:")
    service.clear_auth()
    result = service.http_request(request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败'}")
    
    service.close()

def example_real_world_scenarios():
    """真实世界使用场景示例"""
    print("\n=== 真实世界使用场景示例 ===")
    
    # 场景1: 调用第三方API服务
    print("1. 调用第三方API服务 (模拟OpenAI API):")
    openai_request = {
        "url": "https://api.openai.com/v1/chat/completions",
        "method": "POST",
        "data": {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "Hello!"}]
        },
        "timeout": 30
    }
    
    # 使用Bearer Token认证
    api_key = "sk-your-openai-api-key-here"
    result = make_request_with_bearer_token(openai_request, api_key)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败 (预期，因为API Key无效)'}")
    
    # 场景2: 微服务间通信
    print("\n2. 微服务间通信:")
    microservice_auth = AuthConfig.custom_headers({
        "X-Service-Token": "internal-service-token",
        "X-Service-Name": "awen-gateway",
        "X-Service-Version": "1.0.0"
    })
    
    internal_request = {
        "url": "http://localhost:9081/internal/health",
        "method": "GET",
        "timeout": 5
    }
    
    service = HttpRequestService(microservice_auth)
    result = service.http_request(internal_request)
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败 (预期，服务可能不可用)'}")
    service.close()
    
    # 场景3: 云服务API调用 (模拟阿里云)
    print("\n3. 云服务API调用 (模拟阿里云):")
    cloud_request = {
        "url": "https://ecs.aliyuncs.com/",
        "method": "GET",
        "timeout": 10
    }
    
    # 使用HMAC签名认证
    result = make_request_with_hmac_signature(
        cloud_request, 
        "your-access-key", 
        "your-secret-key"
    )
    print(f"   状态: {'✅ 成功' if result.get('retCode') == '0' else '❌ 失败 (预期，因为密钥无效)'}")

def main():
    """主函数"""
    print("🔐 HTTP服务鉴权功能使用示例")
    print("=" * 50)
    
    try:
        # 基础认证示例
        example_basic_auth()
        
        # Bearer Token认证示例
        example_bearer_token()
        
        # API Key认证示例
        example_api_key()
        
        # HMAC签名认证示例
        example_hmac_signature()
        
        # 自定义请求头认证示例
        example_custom_headers()
        
        # 动态切换认证示例
        example_dynamic_auth()
        
        # 真实世界使用场景
        example_real_world_scenarios()
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎉 HTTP鉴权功能示例演示完成！")
    print("\n💡 使用建议:")
    print("   • 根据API提供商要求选择合适的鉴权方式")
    print("   • 敏感信息(密钥、密码)应从环境变量或配置文件读取")
    print("   • 生产环境中建议使用HTTPS确保传输安全")
    print("   • 定期轮换API密钥和访问令牌")
    print("   • 为不同服务使用不同的认证配置")

if __name__ == "__main__":
    main()
