import os
import sys
from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import asyncio
import anyio

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)

from gateway.service.ModelService import llm_predict_handle  



class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)

# 文本大模型
# request_data = {
#     "stream": False,
#     "query": '小面额人民币有哪些',
#     "company": 'qwen',
#     'modelType': 'qwen2-vl-7b-instruct-awq',
#     'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
#     "deployMode": 0,
#     "multiType": "text",
#     "temperature": 0.01,
#     "topK": 1,
#     "topP": 0.1,
#     "isDeepThink": False,
#     "param": {
#         "asrPath": "",
#         "voice": "",
#         "fileUrl": "",
#         "urlType": "",
#         "n": "",
#         "sizeWidth": "",
#         "sizeHeight": "",
#         "responseFormat": "",
#         "guidanceScale": "",
#         "numInferenceSteps": "",
#         "negativePrompt": "",
#         "samplerName": "",
#         "initImage": ""
#     }
# }


# 图生文大模型
request_data = {
    "stream": False,
    "query": '解释图片内容',
    "company": 'qwen',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    "deployMode": 0,
    "multiType": "imgToText",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png",
        "urlType": "minio",
        "mediaType": "image_url",
        "n": "",
        "sizeWidth": "",
        "sizeHeight": "",
        "responseFormat": "",
        "guidanceScale": "",
        "numInferenceSteps": "",
        "negativePrompt": "",
        "samplerName": "",
        "initImage": ""
    }
}


# 文生图大模型
request_data = {
    "stream": True,
    "query": "a dog sitting on the moon",
    "company": 'Stablility AI',
    'modelType': 'stable-diffusion-xl-base-1.0',
    'baseUrl': 'http://172.1.0.154:9020/v1',
    "deployMode": 0,
    "multiType": "textToImg",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "",
        "urlType": "",
        "mediaType": "",
        "n": "1",
        "sizeWidth": "512",
        "sizeHeight": "1024",
        "responseFormat": "url",
        "guidanceScale": "8.5",
        "numInferenceSteps": "40",
        "negativePrompt": "the dog is white",
        "samplerName": "DPM++ 2M Karras",
        "initImage": ""
    }
}

# # 运行函数
def main():
    req = MockRequest()
    result = llm_predict_handle(request_data, req)
    print("测试结果:")
    print(result)

if __name__ == "__main__":
    main()
