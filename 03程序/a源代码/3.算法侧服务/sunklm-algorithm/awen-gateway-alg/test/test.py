import os
import sys
from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import asyncio
import anyio

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)

from gateway.service.ModelService import llm_predict_handle  



class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)

<<<<<<< HEAD
request_data =  {
    'modelId': '0353472512',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    'query': '张三才22岁，不想这么早就因为写代码而头秃',
    'temperature': 0.01,
    'topP': 0.1,
    'topK': 1,
    'inputMaxToken': 2000,
    'outputMaxToken': 2000,
    'multiType': 'text_generate',
    'stream': True,
    'deployMode': 1,
    'isDeepThink': False
=======
# 文本大模型
# request_data = {
#     "stream": False,
#     "query": '小面额人民币有哪些',
#     "company": 'qwen',
#     'modelType': 'qwen2-vl-7b-instruct-awq',
#     'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
#     "deployMode": 0,
#     "multiType": "text",
#     "temperature": 0.01,
#     "topK": 1,
#     "topP": 0.1,
#     "isDeepThink": False,
#     "param": {
#         "asrPath": "",
#         "voice": "",
#         "fileUrl": "",
#         "urlType": "",
#         "n": "",
#         "sizeWidth": "",
#         "sizeHeight": "",
#         "responseFormat": "",
#         "guidanceScale": "",
#         "numInferenceSteps": "",
#         "negativePrompt": "",
#         "samplerName": "",
#         "initImage": ""
#     }
# }


# 图生文大模型
request_data = {
    "stream": False,
    "query": '解释图片内容',
    "company": 'qwen',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    "deployMode": 0,
    "multiType": "imgToText",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png",
        "urlType": "minio",
        "mediaType": "image_url",
        "n": "",
        "sizeWidth": "",
        "sizeHeight": "",
        "responseFormat": "",
        "guidanceScale": "",
        "numInferenceSteps": "",
        "negativePrompt": "",
        "samplerName": "",
        "initImage": ""
    }
>>>>>>> dev-kxj
}

# request_data = {
#     # 基本参数
#     'modelType': 'stable-diffusion-xl-base-1.0',
#     'baseUrl': 'http://172.1.0.154:9020/v1',
#     'query': 'a dog sitting on the moon',
#     'multiType': 'image_generate', # 图像生成任务
#     'deployMode': 1,  # 0为私有部署，1为OPENAI公有部署
    
#     'n': 1, # 注意n不能为0（否则会出现400 error），一次生成的图像数量，建议≤4
#     'size_width': 512, # 8的倍数即可
#     'size_height': 1024, # 8的倍数即可
#     'response_format': 'b64_json', # 响应格式，url为生成图像的存储地址，base64为直接返回编码数据
    

#     # 以下是 SDXL 支持的附加参数
#     # 'guidance_scale': 8.5, # 5-15 引导系数（CFG）：越高越严格匹配 prompt
#     # 'num_inference_steps': 40, # 推理步数（图像细节程度）
#     # 'negative_prompt': 'the dog is white', 
#     # 'sampler_name': 'DPM++ 2M Karras', # 采样器类型，default，Euler a（可能更创意），DDIM等

#     #  传入init_image就是图生图模式
#     # 'init_image':'/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/1aec328ee66a48ad8e1d68ba13297554.jpg'
# }


# 文生图大模型
request_data = {
    "stream": True,
    "query": "a dog sitting on the moon",
    "company": 'Stablility AI',
    'modelType': 'stable-diffusion-xl-base-1.0',
    'baseUrl': 'http://172.1.0.154:9020/v1',
    "deployMode": 0,
    "multiType": "textToImg",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "",
        "urlType": "",
        "mediaType": "",
        "n": "1",
        "sizeWidth": "512",
        "sizeHeight": "1024",
        "responseFormat": "url",
        "guidanceScale": "8.5",
        "numInferenceSteps": "40",
        "negativePrompt": "the dog is white",
        "samplerName": "DPM++ 2M Karras",
        "initImage": ""
    }
}

# # 运行函数
def main():
    req = MockRequest()
    result = llm_predict_handle(request_data, req)
    print("测试结果:")
    print(result)

if __name__ == "__main__":
    main()
