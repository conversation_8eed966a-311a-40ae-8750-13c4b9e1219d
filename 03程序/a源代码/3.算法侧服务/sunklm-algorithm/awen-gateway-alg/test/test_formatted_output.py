import os
import sys
from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import asyncio
import anyio

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)

from gateway.service.ModelService import llm_predict_handle  


class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)


request_data = {
    "stream": True,
    "query": '拜程序员工作所赐，张三年仅22就头顶强者发型',
    "outputFormat": 1, # 0:普通文本, 1:JSON, 2:Markdown
    "jsonFormat" : """{
        "data": {  
            "name": ,
            "age": ,
            "job": []
        }  
    }""",
    "company": 'qwen',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    "deployMode": 0,
    "multiType": "text",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "",
        "urlType": "",
        "n": "",
        "sizeWidth": "",
        "sizeHeight": "",
        "responseFormat": "",
        "guidanceScale": "",
        "numInferenceSteps": "",
        "negativePrompt": "",
        "samplerName": "",
        "initImage": ""
    }
}


# 运行函数
async def main():
    req = MockRequest()
    result = llm_predict_handle(request_data, req)

    print("测试结果:")
    print(f"返回类型: {type(result)}")

    # 如果是StreamingResponse，需要读取流式内容
    if hasattr(result, 'body_iterator'):
        print("检测到流式响应，开始读取内容:")
        try:
            content_parts = []
            async for chunk in result.body_iterator:
                if isinstance(chunk, bytes):
                    chunk_str = chunk.decode('utf-8')
                else:
                    chunk_str = str(chunk)
                content_parts.append(chunk_str)
                print(f"接收到数据块: {chunk_str}")

            full_content = ''.join(content_parts)
            print(f"\n完整响应内容:\n{full_content}")

        except Exception as e:
            print(f"读取流式响应时出错: {e}")
    else:
        # 如果不是流式响应，直接打印
        print(result)

def test_non_stream():
    """测试非流式输出"""
    print("\n=== 测试非流式输出 ===")

    # 复制请求数据并设置为非流式
    non_stream_data = request_data.copy()
    non_stream_data["stream"] = False

    req = MockRequest()
    result = llm_predict_handle(non_stream_data, req)

    print("非流式测试结果:")
    print(f"返回类型: {type(result)}")
    print(f"内容: {result}")

def test_different_formats():
    """测试不同的输出格式"""
    print("\n=== 测试不同输出格式 ===")

    formats = [
        (0, "普通文本格式"),
        (1, "JSON格式"),
        (2, "Markdown格式")
    ]

    for format_code, format_name in formats:
        print(f"\n--- 测试{format_name} (outputFormat={format_code}) ---")

        test_data = request_data.copy()
        test_data["stream"] = False  # 使用非流式便于查看结果
        test_data["outputFormat"] = format_code

        req = MockRequest()
        result = llm_predict_handle(test_data, req)

        print(f"格式: {format_name}")
        print(f"结果: {result}")

if __name__ == "__main__":
    print("🚀 开始测试格式化输出...")

    # 测试流式输出
    print("=== 测试流式输出 ===")
    asyncio.run(main())

    # 测试非流式输出
    test_non_stream()

    # 测试不同格式
    test_different_formats()

    print("\n🎉 测试完成！")
