import os
import sys
from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import asyncio
import anyio

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)

from gateway.service.ModelService import llm_predict_handle  


class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)


request_data = {
    "stream": True,
    "query": '拜程序员工作所赐，张三年仅22就头顶强者发型',
    "outputFormat": 1,
    "jsonFormat" : """{
        "data": {  
            "name": ,
            "age": ,
            "job": []
        }  
    }""",
    "company": 'qwen',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    "deployMode": 0,
    "multiType": "text",
    "temperature": 0.01,
    "topK": 1,
    "topP": 0.1,
    "isDeepThink": False,
    "param": {
        "asrPath": "",
        "voice": "",
        "fileUrl": "",
        "urlType": "",
        "n": "",
        "sizeWidth": "",
        "sizeHeight": "",
        "responseFormat": "",
        "guidanceScale": "",
        "numInferenceSteps": "",
        "negativePrompt": "",
        "samplerName": "",
        "initImage": ""
    }
}


# 运行函数
def main():
    req = MockRequest()
    result = llm_predict_handle(request_data, req)
    print("测试结果:")
    print(result)

if __name__ == "__main__":
    main()
