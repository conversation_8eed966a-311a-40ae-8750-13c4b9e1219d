from starlette.requests import Request
from starlette.datastructures import Headers
from starlette.types import Scope
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
from gateway.service.ModelService import llm_predict_handle  # 替换成你的模块路径
from gateway.service.AgentToolService import AgentToolService
# 尝试导入，如果失败则跳过数据库相关功能
try:
    from gateway.service.AgentToolService import AgentToolService
    print("成功导入 AgentToolService")
except Exception as e:
    print(f"导入 AgentToolService 失败: {e}")
    exit(1)

class MockRequest(Request):
    def __init__(self):
        scope: Scope = {
            "type": "http",
            "method": "POST",
            "path": "/test",
            "headers": Headers({}).raw,
        }
        super().__init__(scope)

# 测试用的请求数据
test_request_data = {
    'modelId': '0353472512',
    'modelType': 'qwen2-vl-7b-instruct-awq',
    'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions',
    'query': '我想做一幅画，内容是一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生',
    'temperature': 0.01,
    'topP': 0.1,
    'topK': 1,
    'inputMaxToken': 30000,
    'outputMaxToken': 30000,
    'multiType': 'text_generate',
    'stream': False,
    'deployMode': 0,
    'isDeepThink': False,
    'intent_mode': 0,
    'history': [],
    'intent': json.dumps({
        "intents": [
            {
                "id": 1,
                "description": "画画"
            },
            {
                "id": 2,
                "description": "写代码"
            },
            {
                "id": 3,
                "description": "写文章"
            }
        ]
    })
}

def test_intent_recognition():
    """测试意图识别功能"""
    print("\n=== 测试意图识别功能 ===")
    req = MockRequest()
    agent_tool = AgentToolService()
    
    result = agent_tool.intent_recognition(request=test_request_data, req=req)
    print("意图识别结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def test_rewrite_predict():
    """测试问题改写功能"""
    print("\n=== 测试问题改写功能 ===")
    agent_tool = AgentToolService()
    
    rewrite_data = test_request_data.copy()
    rewrite_data['query'] = '画画'
    rewrite_data['history'] = ['user:我想要创作一些艺术作品', 'assistant:好的，您想创作什么类型的艺术作品呢？']
    
    result = agent_tool.rewrite_predict(rewrite_data)
    print("问题改写结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def test_question_expand():
    """测试问题扩写功能"""
    print("\n=== 测试问题扩写功能 ===")
    agent_tool = AgentToolService()
    
    expand_data = test_request_data.copy()
    expand_data['query'] = '画一幅画'
    expand_data['history'] = ['user:我想要创作', 'assistant:您想创作什么？']
    
    result = agent_tool.question_expand_predict(expand_data)
    print("问题扩写结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def test_different_models():
    """测试不同模型参数"""
    print("\n=== 测试不同模型参数 ===")
    req = MockRequest()
    agent_tool = AgentToolService()
    
    # 测试不同的模型配置
    different_model_data = test_request_data.copy()
    different_model_data.update({
        'modelType': 'custom-model-test',
        'temperature': 0.8,
        'topP': 0.9,
        'topK': 50,
        'query': '帮我写代码'
    })
    
    result = agent_tool.intent_recognition(request=different_model_data, req=req)
    print("不同模型参数测试结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def main():
    """主测试函数"""
    print("开始测试 AgentToolService 的所有功能...")
    
    try:
        # 测试意图识别
        intent_result = test_intent_recognition()
        
        # 测试问题改写
        rewrite_result = test_rewrite_predict()
        
        # 测试问题扩写
        expand_result = test_question_expand()
        
        # 测试不同模型参数
        model_result = test_different_models()
        
        print("\n=== 所有测试完成 ===")
        print("✅ 意图识别功能正常")
        print("✅ 问题改写功能正常") 
        print("✅ 问题扩写功能正常")
        print("✅ 用户指定模型功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
