# Awen Gateway Algorithm Service
Generated by AI, be careful with the content

## 项目概述

Awen Gateway Algorithm Service (awen-gateway-alg) 是SUNKLM智能算法服务平台的统一API网关服务，负责请求路由、负载均衡、限流控制和服务代理。该服务基于FastAPI框架构建，提供高性能的异步处理能力。

## 功能特性

- 🚀 **统一接口网关**: 提供统一的API入口，简化客户端调用
- 🔄 **智能路由**: 自动路由请求到相应的后端服务
- 🛡️ **限流控制**: 内置请求限流机制，防止服务过载
- ⚡ **异步处理**: 基于FastAPI的高性能异步处理
- 🔧 **多模态支持**: 支持文本、图像、语音等多种模态处理
- 🤖 **智能体集成**: 集成Agent智能体服务
- 🛠️ **工具调用**: 支持MCP工具协议和代码执行

## 系统架构

```
awen-gateway-alg/
├── GatewayMain.py              # 主启动文件
├── run.sh                      # 启动脚本
├── common/                     # 公共模块
│   ├── conf/                   # 配置文件
│   │   └── ServerConfig.py     # 服务配置
│   ├── constants/              # 常量定义
│   └── utils/                  # 工具类
├── gateway/                    # 网关核心模块
│   ├── controller/             # 控制器层
│   │   └── GatewayController.py # 网关控制器
│   ├── service/                # 服务层
│   │   ├── AgentService.py     # 智能体服务
│   │   ├── ModelService.py     # 模型服务
│   │   ├── MultimodalService.py # 多模态服务
│   │   ├── CodeService.py      # 代码执行服务
│   │   ├── McpService.py       # MCP工具服务
│   │   └── MinioService.py     # 文件存储服务
│   └── dao/                    # 数据访问层
└── test/                       # 测试用例
```

## 核心服务

### 1. 模型服务 (ModelService)
- **文本生成**: 支持各种大语言模型的文本生成
- **多模态处理**: 图像理解、语音识别、语音合成
- **重排序**: 文本重排序和相关性评分
- **向量化**: 文本向量化和嵌入生成

### 2. 智能体服务 (AgentService)
- **流式对话**: 支持实时流式响应
- **非流式对话**: 标准请求-响应模式
- **工具调用**: 集成外部工具和API
- **知识检索**: 结合知识库进行智能问答

### 3. 代码执行服务 (CodeService)
- **Python代码执行**: 安全的代码执行环境
- **超时控制**: 防止代码执行时间过长
- **结果返回**: 标准化的执行结果格式

### 4. MCP工具服务 (McpService)
- **工具协议**: 支持Model Context Protocol
- **动态工具加载**: 运行时加载和卸载工具
- **工具调用**: 统一的工具调用接口

## API接口

### 基础路由格式
```
POST /ai/{service_name}/{path}
```

### 主要接口

#### 1. 模型对话接口
```http
POST /ai/gateway/chatModel
Content-Type: application/json

{
  "modelType": "qwen2.5-72b-instruct-gptq-int4",
  "baseUrl": "http://***********:9020/v1/chat/completions",
  "apiKey": "",
  "company": "qwen",
  "deployMode": 0,
  "query": "你好，请介绍一下你的功能",
  "history": [],
  "stream": false,
  "temperature": 0.01,
  "topP": 0.01,
  "topK": 1,
  "maxTokens": 8192
}
```

#### 2. 智能体对话接口
```http
POST /ai/gateway/chatAgent
Content-Type: application/json

{
  "query": "帮我分析一下这个文档",
  "stream": true,
  "modelType": "qwen2.5-72b-instruct-gptq-int4",
  "baseUrl": "http://***********:9020/v1/chat/completions",
  "company": "qwen",
  "deployMode": 0,
  "history": [],
  "toolList": [],
  "knowledgeInfo": {"status": 1, "fileIds": ["123"]},
  "faqInfo": null
}
```

#### 3. 代码执行接口
```http
POST /ai/gateway/callCode
Content-Type: application/json

{
  "command": "def solution(arg):\n    import json\n    data = json.loads(arg)\n    return data['a'] + data['b']",
  "param": {"a": 1, "b": 2},
  "timeout": 20
}
```

#### 4. MCP工具调用接口
```http
POST /ai/gateway/callMcpTool
Content-Type: application/json

{
  "toolName": "document_parser",
  "parameters": {
    "file_path": "/path/to/document.pdf"
  }
}
```

#### 5. 多模态处理接口
```http
POST /ai/gateway/chatModel
Content-Type: application/json

{
  "taskName": "multi_generate",
  "modelType": "qwen2-vl-7b-instruct-awq",
  "baseUrl": "http://***********:9997/v1/chat/completions",
  "query": "请描述这张图片",
  "url": "http://example.com/image.jpg",
  "contentType": "image"
}
```

## 配置说明

### 服务配置
```python
# 服务基本配置
SERVICE_NAME = "awen-gateway-alg"
RATE_LIMIT = "10/minute"  # 限流配置
THREAD_POOL_SIZE = 40     # 线程池大小

# 信任的服务列表
TRUSTED_SERVICES = {
    "agent": "http://***********:8012",
    "gateway": "http://127.0.0.1:9080",
    "order": "http://localhost:8003",
}
```

### 模型配置
```python
# 默认模型配置
MODEL_TYPE = "qwen2.5-72b-instruct-gptq-int4"
API_BASE = "http://***********:9020/v1/chat/completions"
COMPANY = "qwen"
TEMPERATURE = 0.01
MAX_SEQ_LEN = 8192
MAX_OUT_LEN = 8192
```

### 数据库配置
```python
# MySQL配置
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@123"
MYSQL_DATABASE = "AGENT_FLOW"
```

## 快速开始

### 1. 环境准备
```bash
# 创建conda环境
conda create -n mcp python=3.11
conda activate mcp

# 安装依赖
pip install fastapi uvicorn httpx requests
```

### 2. 配置修改
修改 `common/conf/ServerConfig.py` 中的配置项：
- 数据库连接信息
- 模型服务地址
- 其他服务地址

### 3. 启动服务

#### 方式一：使用启动脚本
```bash
chmod +x run.sh
./run.sh start [port]  # 可选指定端口，默认9080
```

#### 方式二：直接运行
```bash
python GatewayMain.py
```

### 4. 验证服务
```bash
# 检查服务状态
curl -X POST http://localhost:9080/ai/gateway/chatModel \
  -H "Content-Type: application/json" \
  -d '{"query": "你好", "modelType": "qwen2.5-72b-instruct-gptq-int4"}'
```

## 服务管理

### 启动服务
```bash
./run.sh start [port]
```

### 停止服务
```bash
./run.sh stop
```

### 重启服务
```bash
./run.sh restart
```

### 查看状态
```bash
./run.sh status
```

## 监控与日志

### 日志配置
- 日志文件位置: `./logs/nohup.out`
- 日志级别: INFO/DEBUG/ERROR
- 支持结构化日志输出

### 性能监控
- 线程池使用情况监控
- 请求响应时间统计
- 错误率统计
- 服务健康检查

## 开发指南

### 添加新的服务路由
1. 在 `GatewayController.py` 中添加新的路由处理逻辑
2. 在 `service_handle` 函数中添加对应的处理分支
3. 创建相应的服务类处理具体业务逻辑

### 集成新的模型
1. 在 `ServerConfig.py` 的 `LLM_MAP` 中添加模型配置
2. 在 `ModelService.py` 中添加模型适配逻辑
3. 更新API文档

### 添加新的工具
1. 实现工具的具体逻辑
2. 在 `McpService.py` 中注册工具
3. 更新工具调用接口

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i:9080
   
   # 检查日志
   tail -f logs/nohup.out
   ```

2. **模型调用失败**
   - 检查模型服务是否可用
   - 验证API_BASE地址
   - 确认模型类型配置

3. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 确认网络连通性

## 性能优化

### 线程池配置
```python
THREAD_POOL_SIZE = 40  # 根据服务器性能调整
```

### 限流配置
```python
RATE_LIMIT = "10/minute"  # 根据需求调整
```

### 缓存策略
- 模型响应缓存
- 静态资源缓存
- 数据库查询缓存

## 安全配置

- API密钥验证
- 请求限流
- 输入参数验证
- 代码执行沙箱

## 技术细节

### 异步处理架构
```python
# 使用线程池处理阻塞任务
executor = ThreadPoolExecutor(max_workers=thread_pool_size)

@item_router.api_route("/{service_name}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def process_task(request: Request, service_name: str, path: str):
    req = await request.json()
    loop = asyncio.get_running_loop()
    result = await loop.run_in_executor(executor, service_handle, path, req, request)
    return result
```

### 流式响应处理
```python
# 支持SSE流式响应
async def generate_data():
    async with httpx.AsyncClient() as client:
        async with client.stream(method="POST", url=url, json=body) as response:
            async for chunk in response.aiter_bytes():
                yield chunk.decode('utf-8')

return StreamingResponse(generate_data(), media_type="text/event-stream")
```

### 任务类型支持
- `text_generate`: 文本生成
- `multi_generate`: 多模态生成
- `rerank`: 重排序
- `embeddings`: 向量化
- `asr`: 语音识别
- `tts`: 语音合成

## 部署指南

### Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt

EXPOSE 9080

CMD ["python", "GatewayMain.py"]
```

### 环境变量配置
```bash
export MYSQL_HOST=**********
export MYSQL_PORT=3306
export MYSQL_USER=SUNKLM
export MYSQL_PASSWORD=Sunyard@123
export API_BASE=http://***********:9020/v1/chat/completions
```

### 负载均衡配置
```nginx
upstream gateway_backend {
    server 127.0.0.1:9080;
    server 127.0.0.1:9081;
    server 127.0.0.1:9082;
}

server {
    listen 80;
    location /ai/ {
        proxy_pass http://gateway_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 测试用例

### 单元测试
```python
# test/test_gateway.py
import pytest
from fastapi.testclient import TestClient
from GatewayMain import app

client = TestClient(app)

def test_chat_model():
    response = client.post("/ai/gateway/chatModel", json={
        "query": "你好",
        "modelType": "qwen2.5-72b-instruct-gptq-int4"
    })
    assert response.status_code == 200
```

### 集成测试
```bash
# 运行测试
python -m pytest test/ -v
```

## 扩展开发

### 自定义中间件
```python
from fastapi import Request
import time

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### 添加新的服务代理
```python
# 在 service_handle 函数中添加
if path == "newService":
    return NewService.handle_request(req)
```

## 监控指标

### 关键指标
- 请求QPS (每秒查询数)
- 响应时间 (P50, P95, P99)
- 错误率
- 线程池使用率
- 内存使用情况

### 监控接口
```http
GET /ai/gateway/metrics
GET /ai/gateway/health
```

## 更新日志

### v1.2.0 (2024-12-01)
- 添加流式响应支持
- 优化异步处理性能
- 增强错误处理机制
- 支持更多模型类型

### v1.1.0 (2024-11-15)
- 添加MCP工具协议支持
- 集成代码执行服务
- 优化多模态处理
- 增加监控指标

### v1.0.0 (2024-11-01)
- 初始版本发布
- 支持基础的网关功能
- 集成多模态处理
- 支持智能体服务代理
