2025-07-09 01:30:05,687 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-09 01:30:05,687 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M <PERSON>"} - [system]
2025-07-09 01:30:11,760 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1752024611, 'data': [{'url': '/data/x_inference/xinf_models/image/f028c4d3568c4be5bba7f785e8edcdb9.jpg', 'b64_json': None}]} - [system]
2025-07-09 01:30:11,761 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-09 01:30:11,766 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/f028c4d3568c4be5bba7f785e8edcdb9.jpg - [system]
2025-07-09 01:30:11,810 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/f028c4d3568c4be5bba7f785e8edcdb9.jpg - [system]
2025-07-09 01:30:11,843 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/f028c4d3568c4be5bba7f785e8edcdb9.jpg - [system]
2025-07-09 01:30:11,844 - app - INFO - 生成结果: {'image_result': {'created': 1752024611, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/f028c4d3568c4be5bba7f785e8edcdb9.jpg', 'b64_json': None}]}} - [system]
2025-07-09 01:35:51,973 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-09 01:35:51,973 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-09 01:35:57,444 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1752024957, 'data': [{'url': '/data/x_inference/xinf_models/image/f4324b41e5354cc4b53eb31affcc470a.jpg', 'b64_json': None}]} - [system]
2025-07-09 01:35:57,445 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-09 01:35:57,450 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/f4324b41e5354cc4b53eb31affcc470a.jpg - [system]
2025-07-09 01:35:57,493 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/f4324b41e5354cc4b53eb31affcc470a.jpg - [system]
2025-07-09 01:35:57,511 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/f4324b41e5354cc4b53eb31affcc470a.jpg - [system]
2025-07-09 01:35:57,511 - app - INFO - 生成结果: {'image_result': {'created': 1752024957, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/f4324b41e5354cc4b53eb31affcc470a.jpg', 'b64_json': None}]}} - [system]
2025-07-09 02:36:52,427 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-09 02:39:49,787 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:41:40,664 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:44:55,185 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:45,706 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:45,706 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:45,707 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:45:45,707 - app - INFO - using normal format - [system]
2025-07-09 02:45:45,707 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:45:46,315 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatceadf5d0-5c6e-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029143, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:45:46,316 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:46,316 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:45:46,316 - app - INFO - using normal format - [system]
2025-07-09 02:45:46,316 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:45:46,905 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatcf097d9c-5c6e-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029144, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:45:46,906 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:46,906 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:45:46,907 - app - INFO - using normal format - [system]
2025-07-09 02:45:46,907 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:45:47,524 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatcf63db98-5c6e-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029145, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:45:47,525 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:45:47,525 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:45:47,526 - app - INFO - using normal format - [system]
2025-07-09 02:45:47,526 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:45:48,127 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatcfc246f6-5c6e-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029145, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:48:58,369 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:48:58,370 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:48:58,370 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:48:58,370 - app - INFO - using normal format - [system]
2025-07-09 02:48:58,370 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:48:59,002 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat41836540-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029336, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:48:59,003 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:48:59,003 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:48:59,003 - app - INFO - using normal format - [system]
2025-07-09 02:48:59,003 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:48:59,609 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat41e35234-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029337, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:48:59,609 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:48:59,609 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:48:59,610 - app - INFO - using normal format - [system]
2025-07-09 02:48:59,610 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:49:00,220 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat423fb4ca-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029337, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:49:00,221 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:49:00,222 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:49:00,222 - app - INFO - using normal format - [system]
2025-07-09 02:49:00,222 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:49:00,834 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat429d6bba-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029338, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:52:12,345 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:52:12,345 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:52:12,345 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:52:12,346 - app - INFO - using normal format - [system]
2025-07-09 02:52:12,346 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:52:12,976 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb521c488-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029530, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:52:12,977 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:52:12,977 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:52:12,977 - app - INFO - using normal format - [system]
2025-07-09 02:52:12,977 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:52:13,585 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb5812734-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029531, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:52:13,586 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:52:13,586 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:52:13,587 - app - INFO - using normal format - [system]
2025-07-09 02:52:13,587 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:52:14,202 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb5de5c10-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029531, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:52:14,203 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:52:14,203 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:52:14,203 - app - INFO - using normal format - [system]
2025-07-09 02:52:14,203 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:52:14,801 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb63c1bd4-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029532, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:53:59,641 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:53:59,641 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:53:59,641 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:53:59,641 - app - INFO - using normal format - [system]
2025-07-09 02:53:59,641 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:54:00,258 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf515abc2-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029637, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:54:00,259 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:54:00,259 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:54:00,259 - app - INFO - using normal format - [system]
2025-07-09 02:54:00,259 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:54:00,858 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf572fbce-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029638, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:54:00,859 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:54:00,859 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:54:00,859 - app - INFO - using normal format - [system]
2025-07-09 02:54:00,859 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:54:01,447 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf5cf03d8-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029638, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:54:01,448 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:54:01,449 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:54:01,449 - app - INFO - using normal format - [system]
2025-07-09 02:54:01,449 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:54:02,049 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf62a0e22-5c6f-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029639, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:56:19,497 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:56:19,498 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:56:19,498 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:56:19,498 - app - INFO - using normal format - [system]
2025-07-09 02:56:19,498 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:56:20,144 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat487294ec-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029777, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:56:20,145 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:56:20,145 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:56:20,145 - app - INFO - using normal format - [system]
2025-07-09 02:56:20,145 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:56:20,790 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat48d3dff4-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029778, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:56:20,791 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:56:20,791 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:56:20,792 - app - INFO - using normal format - [system]
2025-07-09 02:56:20,792 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:56:21,452 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat493719fc-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029778, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:56:21,452 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:56:21,453 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:56:21,453 - app - INFO - using normal format - [system]
2025-07-09 02:56:21,453 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:56:22,081 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat499c1320-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029779, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:57:11,711 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:57:11,711 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:57:11,712 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:57:11,712 - app - INFO - using normal format - [system]
2025-07-09 02:57:11,712 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:57:12,333 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat679100b6-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029829, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:57:12,333 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:57:12,334 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:57:12,334 - app - INFO - using normal format - [system]
2025-07-09 02:57:12,334 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:57:12,945 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat67ef5e86-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029830, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:57:12,946 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:57:12,946 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:57:12,946 - app - INFO - using normal format - [system]
2025-07-09 02:57:12,946 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:57:13,554 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat684cbc8e-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029831, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:57:13,555 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:57:13,555 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:57:13,555 - app - INFO - using normal format - [system]
2025-07-09 02:57:13,555 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:57:14,179 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat68a98356-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029831, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:58:39,504 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:58:39,504 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:58:39,504 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:58:39,505 - app - INFO - using json format - [system]
2025-07-09 02:58:39,505 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 02:58:39,505 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:58:39,832 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat9be58ee0-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029917, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 02:58:39,832 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:58:39,832 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:58:39,832 - app - INFO - using normal format - [system]
2025-07-09 02:58:39,832 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:58:40,424 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat9c16a98a-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029917, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 02:58:40,424 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:58:40,425 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:58:40,425 - app - INFO - using json format - [system]
2025-07-09 02:58:40,425 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 02:58:40,425 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:58:40,740 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat9c70e81e-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029918, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 02:58:40,740 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 02:58:40,740 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 02:58:40,740 - app - INFO - using normal format - [system]
2025-07-09 02:58:40,740 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 02:58:41,330 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat9ca10ce2-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752029918, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:00:59,478 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:00:59,478 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:00:59,478 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:00:59,479 - app - INFO - using json format - [system]
2025-07-09 03:00:59,479 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:00:59,479 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:00:59,803 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatef537a74-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030057, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:00:59,804 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:00:59,804 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:00:59,804 - app - INFO - using normal format - [system]
2025-07-09 03:00:59,804 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:01:00,412 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatef846580-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030057, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:01:00,413 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:01:00,413 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:01:00,413 - app - INFO - using json format - [system]
2025-07-09 03:01:00,413 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:01:00,413 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:01:00,727 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatefe1473c-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030058, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:01:00,728 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:01:00,728 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:01:00,729 - app - INFO - using normal format - [system]
2025-07-09 03:01:00,729 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:01:01,342 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf0117f06-5c70-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030058, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:04:26,502 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:26,503 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:26,503 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:26,503 - app - INFO - using json format - [system]
2025-07-09 03:04:26,503 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:04:26,503 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:26,841 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6ab9e342-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030264, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:04:26,841 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:26,841 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:26,841 - app - INFO - using normal format - [system]
2025-07-09 03:04:26,841 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:27,458 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6aebb43a-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030264, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:04:27,459 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:27,459 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:27,460 - app - INFO - using json format - [system]
2025-07-09 03:04:27,460 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:04:27,460 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:27,777 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6b4a5120-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030265, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:04:27,778 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:27,778 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:27,779 - app - INFO - using normal format - [system]
2025-07-09 03:04:27,779 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:28,389 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6b7b3a24-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030265, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:04:48,971 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:48,971 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:48,971 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:48,972 - app - INFO - using json format - [system]
2025-07-09 03:04:48,972 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:04:48,972 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:49,289 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat781d2260-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030286, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:04:49,290 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:49,290 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:49,290 - app - INFO - using normal format - [system]
2025-07-09 03:04:49,290 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:49,896 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat784cf9ae-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030287, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:04:49,896 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:49,897 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:49,897 - app - INFO - using json format - [system]
2025-07-09 03:04:49,897 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:04:49,897 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:50,207 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat78a9b2f2-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030287, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:04:50,207 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:04:50,208 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:04:50,208 - app - INFO - using normal format - [system]
2025-07-09 03:04:50,208 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:04:50,817 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat78d911be-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030288, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:08:00,235 - app - INFO - 【Service】【Model-流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:00,236 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:00,236 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:00,236 - app - INFO - using normal format - [system]
2025-07-09 03:08:00,236 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:08:00,863 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatea1dec96-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030478, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:08:00,864 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:00,864 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:00,864 - app - INFO - using normal format - [system]
2025-07-09 03:08:00,864 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:08:01,499 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatea7cecbe-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030478, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:08:01,500 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:01,500 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:01,500 - app - INFO - using normal format - [system]
2025-07-09 03:08:01,500 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:08:02,110 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chateaddec44-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030479, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:08:02,111 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:02,111 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:02,111 - app - INFO - using normal format - [system]
2025-07-09 03:08:02,111 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:08:02,724 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chateb3b96e6-5c71-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030480, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:08:59,600 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:59,601 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:59,601 - app - INFO - using json format - [system]
2025-07-09 03:08:59,601 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:08:59,601 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:08:59,929 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0d80868a-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030537, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:08:59,929 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:08:59,930 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:08:59,930 - app - INFO - using json format - [system]
2025-07-09 03:08:59,930 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:08:59,930 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:09:00,242 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0db1a896-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030537, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:09:00,243 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:09:00,244 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:09:00,244 - app - INFO - using normal format - [system]
2025-07-09 03:09:00,244 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:09:00,853 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0de1a35c-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030538, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:09:00,854 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:09:00,854 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:09:00,854 - app - INFO - using json format - [system]
2025-07-09 03:09:00,855 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:09:00,855 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:09:01,178 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0e3f3e36-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030538, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:09:01,179 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:09:01,179 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:09:01,179 - app - INFO - using markdown format - [system]
2025-07-09 03:09:01,180 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出markdown格式的回答，注意确保markdown输出完全符合规范，以下markdown格式供你参考\\n\\n- **标题**：使用井号（#），根据层级调整数量（如一级标题`#`，二级标题`##`，依此类推）。\\n- **列表项**：以短横线（-）开头，每项单独一行（无需额外缩进）。\\n- **强调文本**：用单星号包裹（`*强调内容*`），显示为斜体；若需加粗，可使用双星号（`**加粗内容**`）。\\n- **代码/命令**：单行代码用反引号包裹（`代码内容`）；多行代码建议使用三个反引号（```）开启代码块。\\n- **引用文本**：以大于号（>）开头，支持多行引用（每行前加`>`）。\\n- **链接**：格式为`[显示文本](链接地址)`（如`[示例链接](https://example.com)`）。\\n- **图片**：格式为`![替代文本](图片地址)`（如`![示例图片](https://example.com/image.jpg)`）。\\n\\n> 注：若内容涉及多种格式嵌套（如列表中包含代码），请保持层级清晰；无特殊说明时，优先使用基础格式（如斜体用单星号，而非其他符号）", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:09:01,515 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0e70ad22-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030538, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三年仅22就头顶强者发型，这可能是因为他经常熬夜工作，导致头发稀疏。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 348, 'completion_tokens': 24, 'total_tokens': 372}} - [system]
2025-07-09 03:10:57,317 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:10:57,317 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:10:57,317 - app - INFO - using json format - [system]
2025-07-09 03:10:57,317 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:10:57,317 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:10:57,645 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat53aa74cc-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030655, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:10:57,646 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:10:57,646 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:10:57,646 - app - INFO - using json format - [system]
2025-07-09 03:10:57,646 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:10:57,647 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:10:57,963 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat53dbbe38-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030655, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:10:57,964 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:10:57,964 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:10:57,964 - app - INFO - using normal format - [system]
2025-07-09 03:10:57,964 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:10:58,575 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat540c4760-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030656, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:10:58,576 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:10:58,576 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:10:58,576 - app - INFO - using json format - [system]
2025-07-09 03:10:58,576 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:10:58,576 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:10:58,897 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat5469d27c-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030656, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:10:58,898 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:10:58,898 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:10:58,898 - app - INFO - using markdown format - [system]
2025-07-09 03:10:58,899 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出markdown格式的回答，注意确保markdown输出完全符合规范，以下markdown格式供你参考\\n\\n- **标题**：使用井号（#），根据层级调整数量（如一级标题`#`，二级标题`##`，依此类推）。\\n- **列表项**：以短横线（-）开头，每项单独一行（无需额外缩进）。\\n- **强调文本**：用单星号包裹（`*强调内容*`），显示为斜体；若需加粗，可使用双星号（`**加粗内容**`）。\\n- **代码/命令**：单行代码用反引号包裹（`代码内容`）；多行代码建议使用三个反引号（```）开启代码块。\\n- **引用文本**：以大于号（>）开头，支持多行引用（每行前加`>`）。\\n- **链接**：格式为`[显示文本](链接地址)`（如`[示例链接](https://example.com)`）。\\n- **图片**：格式为`![替代文本](图片地址)`（如`![示例图片](https://example.com/image.jpg)`）。\\n\\n> 注：若内容涉及多种格式嵌套（如列表中包含代码），请保持层级清晰；无特殊说明时，优先使用基础格式（如斜体用单星号，而非其他符号）", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:10:59,232 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat549b1490-5c72-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752030656, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三年仅22就头顶强者发型，这可能是因为他经常熬夜工作，导致头发稀疏。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 348, 'completion_tokens': 24, 'total_tokens': 372}} - [system]
2025-07-09 03:52:04,224 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:52:04,224 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:52:04,225 - app - INFO - using json format - [system]
2025-07-09 03:52:04,225 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:52:04,225 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:52:04,554 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat120dd404-5c78-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752033122, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:52:04,555 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:52:04,555 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:52:04,555 - app - INFO - using json format - [system]
2025-07-09 03:52:04,556 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:52:04,556 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:52:04,874 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat123fe25a-5c78-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752033122, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:52:04,875 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:52:04,876 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:52:04,876 - app - INFO - using normal format - [system]
2025-07-09 03:52:04,876 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '拜程序员工作所赐，张三年仅22就头顶强者发型'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:52:05,483 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat12708b4e-5c78-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752033122, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '"拜程序员工作所赐"在这里是指张三年仅22岁就因为长期坐在电脑前工作，导致头发稀疏，形成了"强者发型"。"强者发型"通常指的是头发稀疏，发际线较高，看起来像是秃顶。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 55, 'total_tokens': 89}} - [system]
2025-07-09 03:52:05,484 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:52:05,484 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:52:05,484 - app - INFO - using json format - [system]
2025-07-09 03:52:05,484 - app - INFO - {"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\n        \"data\": {  \n            \"name\": ,\n            \"age\": ,\n            \"job\": []\n        }  \n    }"} - [system]
2025-07-09 03:52:05,485 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出<format>格式的JSON回答，只输出确定的信息，不要编造内容，不要增加或减少变量，array内的元素数量没有限制。", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型", "format": "{\\n        \\"data\\": {  \\n            \\"name\\": ,\\n            \\"age\\": ,\\n            \\"job\\": []\\n        }  \\n    }"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:52:05,800 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat12cd7174-5c78-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752033123, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '{\n"data": {\n"name": "张三",\n"age": 22,\n"job": ["程序员"]\n}\n}'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 119, 'completion_tokens': 26, 'total_tokens': 145}} - [system]
2025-07-09 03:52:05,800 - app - INFO - 【Service】【Model-非流式-调用】inputs:拜程序员工作所赐，张三年仅22就头顶强者发型,history:[] - [system]
2025-07-09 03:52:05,801 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-09 03:52:05,801 - app - INFO - using markdown format - [system]
2025-07-09 03:52:05,801 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '{"restriction": "给出markdown格式的回答，注意确保markdown输出完全符合规范，以下markdown格式供你参考\\n\\n- **标题**：使用井号（#），根据层级调整数量（如一级标题`#`，二级标题`##`，依此类推）。\\n- **列表项**：以短横线（-）开头，每项单独一行（无需额外缩进）。\\n- **强调文本**：用单星号包裹（`*强调内容*`），显示为斜体；若需加粗，可使用双星号（`**加粗内容**`）。\\n- **代码/命令**：单行代码用反引号包裹（`代码内容`）；多行代码建议使用三个反引号（```）开启代码块。\\n- **引用文本**：以大于号（>）开头，支持多行引用（每行前加`>`）。\\n- **链接**：格式为`[显示文本](链接地址)`（如`[示例链接](https://example.com)`）。\\n- **图片**：格式为`![替代文本](图片地址)`（如`![示例图片](https://example.com/image.jpg)`）。\\n\\n> 注：若内容涉及多种格式嵌套（如列表中包含代码），请保持层级清晰；无特殊说明时，优先使用基础格式（如斜体用单星号，而非其他符号）", "text": "拜程序员工作所赐，张三年仅22就头顶强者发型"}'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-09 03:52:06,129 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat12fe0d5c-5c78-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752033123, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三年仅22就头顶强者发型，这可能是因为他经常熬夜工作，导致头发稀疏。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 348, 'completion_tokens': 24, 'total_tokens': 372}} - [system]
