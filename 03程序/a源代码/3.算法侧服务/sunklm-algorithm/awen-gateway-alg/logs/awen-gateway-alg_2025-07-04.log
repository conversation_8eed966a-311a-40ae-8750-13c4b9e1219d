2025-07-04 01:23:43,203 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON序列化】输入参数: {'data': {'name': '张三', 'age': 30, 'city': '北京', 'hobbies': ['编程', '阅读', '音乐'], 'profile': {'email': 'z<PERSON><PERSON>@example.com', 'active': True, 'score': 95.5}}, 'indent': 2, 'ensure_ascii': False} - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON序列化】成功，长度: 191 - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON反序列化】输入参数长度: 330 - [system]
2025-07-04 01:23:43,204 - app - INFO - 【Service】【JSON反序列化】成功，类型: dict - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON验证】输入参数长度: 63 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON验证】完成，结果: True - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON验证】输入参数长度: 48 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON验证】完成，结果: False - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON格式化】输入参数长度: 105 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON格式化】美化完成，原长度: 53, 新长度: 119 - [system]
2025-07-04 01:23:43,205 - app - INFO - 【Service】【JSON格式化】输入参数长度: 268 - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON格式化】压缩完成，原长度: 217, 新长度: 53 - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON合并】输入参数: {'json_objects': [{'name': '张三', 'age': 30}, {'city': '北京', 'country': '中国'}, '{"department": "技术部", "position": "工程师"}', {'skills': ['Python', 'JavaScript'], 'experience': 5}]} - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON合并】成功，合并了4个对象 - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON提取】输入参数: {'json_string': '{"company": {"name": "科技公司", "departments": {"tech": {"name": "技术部", "employees": [{"name": "张三", "role": "工程师"}, {"name": "李四", "role": "架构师"}]}}}}', 'path': 'company.name'} - [system]
2025-07-04 01:23:43,206 - app - INFO - 【Service】【JSON提取】成功，路径: company.name - [system]
2025-07-04 01:23:44,983 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:23:45,525 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:23:45,526 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:45,526 - app - INFO - 【Service】【JSON序列化】输入参数: {'data': {'message': '网关集成测试', 'timestamp': '2024-01-01'}} - [system]
2025-07-04 01:23:45,526 - app - INFO - 【Service】【JSON序列化】成功，长度: 48 - [system]
2025-07-04 01:23:45,527 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:23:45,527 - app - INFO - 【Service】【JSON反序列化】输入参数长度: 54 - [system]
2025-07-04 01:23:45,527 - app - INFO - 【Service】【JSON反序列化】成功，类型: dict - [system]
2025-07-04 01:30:24,088 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:30:24,213 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:30:24,687 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:30:24,689 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:30:24,981 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:32:22,839 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:32:22,988 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:32:23,486 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:32:23,488 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:32:23,713 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:34:21,023 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:34:21,298 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:34:21,299 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:34:21,433 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:34:21,435 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:34:21,559 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:34:21,562 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:34:21,563 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:34:21,781 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:34:21,783 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:34:21,784 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:34:21,974 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:34:21,975 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:34:22,136 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:34:22,137 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:34:23,685 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:34:23,841 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:34:24,337 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:34:24,339 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:34:24,566 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:13,594 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:43:13,857 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:13,859 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:43:13,983 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:13,985 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:43:14,125 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:14,128 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:43:14,129 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:43:14,343 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:14,345 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:43:14,345 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:43:14,346 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 01:43:14,545 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:43:14,548 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:43:14,549 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:43:14,741 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:43:14,742 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:43:14,945 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:43:14,946 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:43:16,501 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:43:16,662 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:43:17,204 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:43:17,206 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:43:17,414 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:48:27,144 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:48:27,689 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:27,689 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:27,689 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        项目介绍\n        \n        这是一个文档处理系统的介绍。\n        \n        主要功能：\n        - 支持多种文件格式\n        - 自动结构化处理\n        - 输出Markdown格式\n        \n        技术特点\n        \n        1. 支持Word文档\n        2. 支持PDF文件\n        3. 支持PowerPoint演示文稿\n        4. 支持Excel表格\n        \n        使用方法：\n        通过API调用即可使用本服务。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 01:48:27,692 - app - INFO - 【Service】【文档处理】成功，原文长度: 303, Markdown长度: 189 - [system]
2025-07-04 01:48:27,693 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:27,693 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/tmp/tmpxya3zmv7.txt', 'structure_level': 'advanced'} - [system]
2025-07-04 01:48:27,694 - app - INFO - 【Service】【文档处理】成功，原文长度: 200, Markdown长度: 547 - [system]
2025-07-04 01:48:27,694 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:27,694 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'basic'} - [system]
2025-07-04 01:48:27,694 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 147 - [system]
2025-07-04 01:48:27,694 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 01:48:27,695 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 177 - [system]
2025-07-04 01:48:27,695 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'advanced'} - [system]
2025-07-04 01:48:27,695 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 398 - [system]
2025-07-04 01:48:27,695 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:27,695 - app - INFO - 【Service】【文档处理】输入参数: {'file_url': 'https://example.com/test.txt', 'structure_level': 'medium'} - [system]
2025-07-04 01:48:33,740 - app - ERROR - 【Service】【文档处理】文档处理失败: 下载或处理URL文件失败: 404 Client Error: Not Found for url: https://example.com/test.txt - [system]
2025-07-04 01:48:33,745 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:33,745 - app - INFO - 【Service】【文档处理】输入参数: {} - [system]
2025-07-04 01:48:33,745 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 01:48:33,745 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/nonexistent/file.txt'} - [system]
2025-07-04 01:48:33,745 - app - ERROR - 【Service】【文档处理】文档处理失败: 文件不存在: /nonexistent/file.txt - [system]
2025-07-04 01:48:33,747 - app - INFO - 【Service】【文档处理】输入参数: {'content': ''} - [system]
2025-07-04 01:48:33,747 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 01:48:33,747 - app - INFO - 【Service】【文档处理】输入参数: {'content': '# 测试标题\n\n这是测试内容。\n\n- 列表项1\n- 列表项2', 'structure_level': 'basic'} - [system]
2025-07-04 01:48:33,747 - app - INFO - 【Service】【文档处理】成功，原文长度: 30, Markdown长度: 38 - [system]
2025-07-04 01:48:50,928 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,928 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,928 - app - INFO - 【Service】【JSON序列化】输入参数: {'data': {'name': '张三', 'age': 30, 'city': '北京', 'hobbies': ['编程', '阅读', '音乐'], 'profile': {'email': '<EMAIL>', 'active': True, 'score': 95.5}}, 'indent': 2, 'ensure_ascii': False} - [system]
2025-07-04 01:48:50,928 - app - INFO - 【Service】【JSON序列化】成功，长度: 191 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON反序列化】输入参数长度: 330 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON反序列化】成功，类型: dict - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON验证】输入参数长度: 63 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON验证】完成，结果: True - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON验证】输入参数长度: 48 - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON验证】完成，结果: False - [system]
2025-07-04 01:48:50,929 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON格式化】输入参数长度: 105 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON格式化】美化完成，原长度: 53, 新长度: 119 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON格式化】输入参数长度: 268 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON格式化】压缩完成，原长度: 217, 新长度: 53 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON合并】输入参数: {'json_objects': [{'name': '张三', 'age': 30}, {'city': '北京', 'country': '中国'}, '{"department": "技术部", "position": "工程师"}', {'skills': ['Python', 'JavaScript'], 'experience': 5}]} - [system]
2025-07-04 01:48:50,930 - app - INFO - 【Service】【JSON合并】成功，合并了4个对象 - [system]
2025-07-04 01:48:50,931 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:50,931 - app - INFO - 【Service】【JSON提取】输入参数: {'json_string': '{"company": {"name": "科技公司", "departments": {"tech": {"name": "技术部", "employees": [{"name": "张三", "role": "工程师"}, {"name": "李四", "role": "架构师"}]}}}}', 'path': 'company.name'} - [system]
2025-07-04 01:48:50,931 - app - INFO - 【Service】【JSON提取】成功，路径: company.name - [system]
2025-07-04 01:48:52,719 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:48:53,252 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:48:53,254 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:53,255 - app - INFO - 【Service】【JSON序列化】输入参数: {'data': {'message': '网关集成测试', 'timestamp': '2024-01-01'}} - [system]
2025-07-04 01:48:53,255 - app - INFO - 【Service】【JSON序列化】成功，长度: 48 - [system]
2025-07-04 01:48:53,255 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:48:53,255 - app - INFO - 【Service】【JSON反序列化】输入参数长度: 54 - [system]
2025-07-04 01:48:53,255 - app - INFO - 【Service】【JSON反序列化】成功，类型: dict - [system]
2025-07-04 01:51:37,147 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f1f54576110> - [system]
2025-07-04 01:51:37,148 - app - INFO - 【Service】【意图识别】转换意图列表: 3 -> 3 - [system]
2025-07-04 01:51:37,149 - app - INFO - 【Service】【创建用户模型】模型类型:DeepSeek-R1-Distill-Llama-70B, API地址:http://172.1.0.154:8001/v1/chat/completions, 部署模式:0 - [system]
2025-07-04 01:51:37,149 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出包含一个ID表示意图的编号，一句简短的理由说明为什么是这个意图。
    input_variables=['current', 'intents', 'prompt'] input_types={} partial_variables={} messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['current', 'intents', 'prompt'], input_types={}, partial_variables={}, template='\n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出包含一个ID表示意图的编号，一句简短的理由说明为什么是这个意图。\n    {prompt}\n    "意图列表": {intents}\n    "输入": {current}\n    "输出格式": {{\n        "intentId": 数字,\n        "reason": "判断理由"\n    }}\n\n    '), additional_kwargs={})]
    "意图列表": [{'intentId': 1, 'description': '画画'}, {'intentId': 2, 'description': '写代码'}, {'intentId': 3, 'description': '查询信息'}]
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-04 01:51:37,149 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 01:51:37,149 - app - INFO - using normal format - [system]
2025-07-04 01:51:37,150 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-04 01:51:37,150 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出包含一个ID表示意图的编号，一句简短的理由说明为什么是这个意图。\n    input_variables=[\'current\', \'intents\', \'prompt\'] input_types={} partial_variables={} messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=[\'current\', \'intents\', \'prompt\'], input_types={}, partial_variables={}, template=\'\\n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出。输出包含一个ID表示意图的编号，一句简短的理由说明为什么是这个意图。\\n    {prompt}\\n    "意图列表": {intents}\\n    "输入": {current}\\n    "输出格式": {{\\n        "intentId": 数字,\\n        "reason": "判断理由"\\n    }}\\n\\n    \'), additional_kwargs={})]\n    "意图列表": [{\'intentId\': 1, \'description\': \'画画\'}, {\'intentId\': 2, \'description\': \'写代码\'}, {\'intentId\': 3, \'description\': \'查询信息\'}]\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-04 01:51:44,998 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '<think>\n好，我现在需要分析用户的输入，确定它属于哪个意图。首先，用户说：“我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗。”这里的关键词是“画”和“建筑设计”。意图列表有三个：1是画画，2是写代码，3是查询信息。用户明确提到“帮我画出来”，所以最符合意图1，画画。其他意图如写代码或查询信息在这里不适用。因此，输出应该是intentId为1，理由是用户请求绘制建筑图画。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户请求绘制一座高耸巨大的建筑，属于画画意图。"\n}\n```', 'tool_calls': None}, 'finish_reason': 'stop', 'logprobs': None}], 'usage': {'prompt_tokens': 354, 'completion_tokens': 196, 'total_tokens': 550}, 'id': 'e4f0ec8769594394a4e4dddcdf40e899', 'object': 'chat.completion', 'created': 1751593904} - [system]
2025-07-04 01:51:44,998 - app - INFO - 【Service】【意图识别】输出:{'answer': '<think>\n好，我现在需要分析用户的输入，确定它属于哪个意图。首先，用户说：“我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗。”这里的关键词是“画”和“建筑设计”。意图列表有三个：1是画画，2是写代码，3是查询信息。用户明确提到“帮我画出来”，所以最符合意图1，画画。其他意图如写代码或查询信息在这里不适用。因此，输出应该是intentId为1，理由是用户请求绘制建筑图画。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户请求绘制一座高耸巨大的建筑，属于画画意图。"\n}\n```', 'toolCalls': None, 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'promptToken': 354, 'completionTokens': 196, 'totalTokens': 550} - [system]
2025-07-04 01:52:44,628 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:52:44,785 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:52:45,327 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:52,917 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:53:53,381 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:53,382 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:53,382 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        项目介绍\n        \n        这是一个文档处理系统的介绍。\n        \n        主要功能：\n        - 支持多种文件格式\n        - 自动结构化处理\n        - 输出Markdown格式\n        \n        技术特点\n        \n        1. 支持Word文档\n        2. 支持PDF文件\n        3. 支持PowerPoint演示文稿\n        4. 支持Excel表格\n        \n        使用方法：\n        通过API调用即可使用本服务。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 01:53:53,385 - app - INFO - 【Service】【文档处理】成功，原文长度: 303, Markdown长度: 189 - [system]
2025-07-04 01:53:53,385 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:53,386 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/tmp/tmpqolrpptf.txt', 'structure_level': 'advanced'} - [system]
2025-07-04 01:53:53,386 - app - INFO - 【Service】【文档处理】成功，原文长度: 200, Markdown长度: 547 - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'basic'} - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 147 - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 177 - [system]
2025-07-04 01:53:53,387 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'advanced'} - [system]
2025-07-04 01:53:53,388 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 398 - [system]
2025-07-04 01:53:53,388 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:53,388 - app - INFO - 【Service】【文档处理】输入参数: {'file_url': 'https://example.com/test.txt', 'structure_level': 'medium'} - [system]
2025-07-04 01:53:54,250 - app - ERROR - 【Service】【文档处理】文档处理失败: 下载或处理URL文件失败: 404 Client Error: Not Found for url: https://example.com/test.txt - [system]
2025-07-04 01:53:54,251 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:53:54,251 - app - INFO - 【Service】【文档处理】输入参数: {} - [system]
2025-07-04 01:53:54,252 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 01:53:54,252 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/nonexistent/file.txt'} - [system]
2025-07-04 01:53:54,252 - app - ERROR - 【Service】【文档处理】文档处理失败: 文件不存在: /nonexistent/file.txt - [system]
2025-07-04 01:53:54,252 - app - INFO - 【Service】【文档处理】输入参数: {'content': ''} - [system]
2025-07-04 01:53:54,252 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 01:53:54,252 - app - INFO - 【Service】【文档处理】输入参数: {'content': '# 测试标题\n\n这是测试内容。\n\n- 列表项1\n- 列表项2', 'structure_level': 'basic'} - [system]
2025-07-04 01:53:54,253 - app - INFO - 【Service】【文档处理】成功，原文长度: 30, Markdown长度: 38 - [system]
2025-07-04 01:56:00,104 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:56:00,307 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:56:00,309 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:56:00,442 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:56:00,445 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:56:00,571 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:56:00,574 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:56:00,575 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:56:00,812 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:56:00,814 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:56:00,815 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:56:00,815 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 01:56:01,091 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:56:01,093 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:56:01,094 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:56:01,221 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:56:01,222 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:56:01,371 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:56:01,372 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:56:02,901 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:56:03,057 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:56:03,571 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:56:03,573 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:56:03,837 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:16,422 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:16,702 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:16,704 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:16,837 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:16,840 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:16,959 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:16,962 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:16,963 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:17,161 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:17,163 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:17,164 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:17,164 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 01:58:17,401 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:17,403 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:17,404 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:58:17,545 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:58:17,546 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:58:17,689 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:58:17,690 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:19,225 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:58:19,379 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:58:19,877 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:58:19,879 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:20,120 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:49,722 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:49,982 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:49,984 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:50,124 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:50,126 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:58:50,263 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:50,266 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:50,266 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:50,524 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:50,527 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:50,527 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:50,527 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 01:58:50,765 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:58:50,767 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:50,768 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:58:50,907 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:58:50,908 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:58:51,062 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:58:51,063 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:58:52,598 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:58:52,752 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:58:53,249 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:58:53,251 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:58:53,471 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:21,909 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:59:22,142 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:22,144 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:59:22,270 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:22,273 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 01:59:22,412 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:22,422 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:59:22,426 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:59:22,642 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:22,645 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:59:22,645 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:59:22,645 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 01:59:22,875 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 01:59:22,877 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:59:22,878 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:59:23,020 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 01:59:23,021 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 01:59:23,144 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 01:59:23,145 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 01:59:24,696 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 01:59:24,891 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 01:59:25,395 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 01:59:25,397 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 01:59:25,637 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:02,328 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '3401558016', 'modelType': '/qwen32', 'baseUrl': 'http://***********:7782/v1/chat/completions', 'query': 'hi', 'apiKey': '', 'inputMaxToken': 4096, 'outputMaxToken': 4096, 'stream': True, 'deployMode': 0, 'multiType': 'text_generate'} - [system]
2025-07-04 02:06:02,329 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 02:06:02,330 - app - INFO - Received request: - [system]
2025-07-04 02:06:02,330 - app - INFO - 【Service】【Model-流式-调用】inputs:hi,history:[] - [system]
2025-07-04 02:06:02,342 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 02:06:02,343 - app - INFO - using normal format - [system]
2025-07-04 02:06:02,343 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': '/qwen32', 'messages': [{'role': 'user', 'content': 'hi'}], 'max_tokens': 4096, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1, 'stream': True} - [system]
2025-07-04 02:06:02,348 - app - ERROR - 【Service】【Model-流式-模型调用】异常:HTTPConnectionPool(host='***********', port=7782): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f81f96f49d0>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-04 02:06:54,268 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 02:06:54,471 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:54,474 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 02:06:54,600 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:54,604 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 02:06:54,730 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:54,735 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 02:06:54,735 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 02:06:54,976 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:54,979 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 02:06:54,979 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 02:06:54,980 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 02:06:55,260 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:06:55,263 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 02:06:55,264 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 02:06:55,463 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 02:06:55,464 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 02:06:55,626 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 02:06:55,627 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 02:06:57,124 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 02:06:57,278 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 02:06:57,784 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 02:06:57,786 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '网关集成测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 02:06:58,035 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 02:13:30,726 - app - INFO - Received request: - [system]
2025-07-04 02:13:30,727 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:13:30,727 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 1, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:13:37,285 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751595217, 'data': [{'url': '/data/x_inference/xinf_models/image/bd4c64f9293e4b32b289c06fa9684d61.jpg', 'b64_json': None}]} - [system]
2025-07-04 02:13:37,285 - app - INFO - 生成结果: {'image_result': {'created': 1751595217, 'data': [{'url': '/data/x_inference/xinf_models/image/bd4c64f9293e4b32b289c06fa9684d61.jpg', 'b64_json': None}]}} - [system]
2025-07-04 02:13:37,285 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-04 02:13:37,300 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/bd4c64f9293e4b32b289c06fa9684d61.jpg - [system]
2025-07-04 02:13:37,339 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/bd4c64f9293e4b32b289c06fa9684d61.jpg - [system]
2025-07-04 02:13:37,400 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/bd4c64f9293e4b32b289c06fa9684d61.jpg - [system]
2025-07-04 02:24:47,222 - app - INFO - Received request: - [system]
2025-07-04 02:24:47,222 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:24:47,223 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:24:47,680 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:25:00,792 - app - INFO - Received request: - [system]
2025-07-04 02:25:00,793 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:25:00,793 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:25:01,207 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:25:11,613 - app - INFO - Received request: - [system]
2025-07-04 02:25:11,614 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:25:11,614 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:25:11,970 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:26:09,094 - app - INFO - Received request: - [system]
2025-07-04 02:26:09,095 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:26:09,095 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:26:12,917 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:26:22,862 - app - INFO - Received request: - [system]
2025-07-04 02:26:22,863 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:26:22,863 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:26:23,249 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:28:58,815 - app - INFO - Received request: - [system]
2025-07-04 02:28:58,815 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:28:58,815 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:28:59,178 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:29:07,926 - app - INFO - Received request: - [system]
2025-07-04 02:29:07,926 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:29:07,926 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url"} - [system]
2025-07-04 02:29:08,281 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:29:51,936 - app - INFO - Received request: - [system]
2025-07-04 02:29:51,936 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:29:51,936 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": 0, "size": "512x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-04 02:29:52,260 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/generations - [system]
2025-07-04 02:30:14,939 - app - INFO - Received request: - [system]
2025-07-04 02:30:14,939 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:30:31,193 - app - INFO - Received request: - [system]
2025-07-04 02:30:31,194 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:30:31,194 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '0', 'size': '512x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:30:31,610 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/variations - [system]
2025-07-04 02:31:24,004 - app - INFO - Received request: - [system]
2025-07-04 02:31:24,004 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:31:24,004 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '0', 'size': '512x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:31:24,395 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/variations - [system]
2025-07-04 02:32:28,596 - app - INFO - Received request: - [system]
2025-07-04 02:32:28,596 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:32:28,596 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '0', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:32:29,002 - app - ERROR - 图像生成请求失败（私有部署）: 400 Client Error: Bad Request for url: http://172.1.0.154:9020/v1/images/variations - [system]
2025-07-04 02:36:10,671 - app - INFO - Received request: - [system]
2025-07-04 02:36:10,671 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:36:10,671 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '1', 'size': '512x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:36:12,899 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751596572, 'data': [{'url': '/data/x_inference/xinf_models/image/2e21bcadfcc143cabfd6100b9cbe5972.jpg', 'b64_json': None}]} - [system]
2025-07-04 02:36:12,900 - app - INFO - 生成结果: {'image_result': {'created': 1751596572, 'data': [{'url': '/data/x_inference/xinf_models/image/2e21bcadfcc143cabfd6100b9cbe5972.jpg', 'b64_json': None}]}} - [system]
2025-07-04 02:36:12,900 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-04 02:36:12,906 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/2e21bcadfcc143cabfd6100b9cbe5972.jpg - [system]
2025-07-04 02:36:12,945 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/2e21bcadfcc143cabfd6100b9cbe5972.jpg - [system]
2025-07-04 02:36:12,982 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/2e21bcadfcc143cabfd6100b9cbe5972.jpg - [system]
2025-07-04 02:50:40,786 - app - INFO - Received request: - [system]
2025-07-04 02:50:40,788 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:50:40,788 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '2', 'size': '512x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:50:44,290 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751597444, 'data': [{'url': '/data/x_inference/xinf_models/image/a91196e838ab47fcb4e7857f1904c41c.jpg', 'b64_json': None}, {'url': '/data/x_inference/xinf_models/image/470b63ccbbad4979872424c14ab12962.jpg', 'b64_json': None}]} - [system]
2025-07-04 02:50:44,291 - app - ERROR - 处理图像失败: 'image_result' - [system]
2025-07-04 02:50:44,292 - app - INFO - 生成结果: {'image_result': {'created': 1751597444, 'data': [{'url': '/data/x_inference/xinf_models/image/a91196e838ab47fcb4e7857f1904c41c.jpg', 'b64_json': None}, {'url': '/data/x_inference/xinf_models/image/470b63ccbbad4979872424c14ab12962.jpg', 'b64_json': None}]}} - [system]
2025-07-04 02:55:04,325 - app - INFO - Received request: - [system]
2025-07-04 02:55:04,326 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:55:04,326 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'a dog sitting on the moon', 'n': '2', 'size': '512x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'the dog is white', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-04 02:55:07,856 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751597707, 'data': [{'url': '/data/x_inference/xinf_models/image/320eacdf3cdb416688f0cd8434bfae0d.jpg', 'b64_json': None}, {'url': '/data/x_inference/xinf_models/image/e98d19676f92430ca7093fa1bf7f5f7b.jpg', 'b64_json': None}]} - [system]
2025-07-04 02:55:07,856 - app - INFO - 共生成了 2 张图片. - [system]
2025-07-04 02:55:07,863 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/320eacdf3cdb416688f0cd8434bfae0d.jpg - [system]
2025-07-04 02:55:07,913 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/320eacdf3cdb416688f0cd8434bfae0d.jpg - [system]
2025-07-04 02:55:08,163 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/320eacdf3cdb416688f0cd8434bfae0d.jpg - [system]
2025-07-04 02:55:08,164 - app - INFO - 提取第 2 张容器内路径: /data/x_inference/xinf_models/image/e98d19676f92430ca7093fa1bf7f5f7b.jpg - [system]
2025-07-04 02:55:08,218 - app - INFO - 第 2 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/e98d19676f92430ca7093fa1bf7f5f7b.jpg - [system]
2025-07-04 02:55:08,232 - app - INFO - 第 2 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/e98d19676f92430ca7093fa1bf7f5f7b.jpg - [system]
2025-07-04 02:55:08,233 - app - INFO - 生成结果: {'image_result': {'created': 1751597707, 'data': [{'url': '/data/x_inference/xinf_models/image/320eacdf3cdb416688f0cd8434bfae0d.jpg', 'b64_json': None}, {'url': '/data/x_inference/xinf_models/image/e98d19676f92430ca7093fa1bf7f5f7b.jpg', 'b64_json': None}]}} - [system]
2025-07-04 02:58:07,170 - app - INFO - Received request: - [system]
2025-07-04 02:58:07,170 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-04 02:58:07,170 - app - INFO - 【Service】【ImageModel 公有部署】请求地址: http://172.1.0.154:9020/v1 - [system]
2025-07-04 02:58:16,299 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9020/v1/images/generations "HTTP/1.1 200 OK" - [system]
2025-07-04 02:58:16,303 - app - INFO - 【Service】【ImageModel OpenAI】返回结果: {'created': 1751597896, 'data': [{'b64_json': None, 'revised_prompt': None, 'url': '/data/x_inference/xinf_models/image/c4f7ba202eeb4841abb71cfb60967d4e.jpg'}, {'b64_json': None, 'revised_prompt': None, 'url': '/data/x_inference/xinf_models/image/99d824e5c5084432b7cc6e376f6ba1e5.jpg'}], 'usage': None} - [system]
2025-07-04 02:58:16,303 - app - INFO - 共生成了 2 张图片. - [system]
2025-07-04 02:58:16,309 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/c4f7ba202eeb4841abb71cfb60967d4e.jpg - [system]
2025-07-04 02:58:16,348 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/c4f7ba202eeb4841abb71cfb60967d4e.jpg - [system]
2025-07-04 02:58:16,380 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/c4f7ba202eeb4841abb71cfb60967d4e.jpg - [system]
2025-07-04 02:58:16,380 - app - INFO - 提取第 2 张容器内路径: /data/x_inference/xinf_models/image/99d824e5c5084432b7cc6e376f6ba1e5.jpg - [system]
2025-07-04 02:58:16,417 - app - INFO - 第 2 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/99d824e5c5084432b7cc6e376f6ba1e5.jpg - [system]
2025-07-04 02:58:16,429 - app - INFO - 第 2 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/99d824e5c5084432b7cc6e376f6ba1e5.jpg - [system]
2025-07-04 02:58:16,429 - app - INFO - 生成结果: {'image_result': {'created': 1751597896, 'data': [{'b64_json': None, 'revised_prompt': None, 'url': '/data/x_inference/xinf_models/image/c4f7ba202eeb4841abb71cfb60967d4e.jpg'}, {'b64_json': None, 'revised_prompt': None, 'url': '/data/x_inference/xinf_models/image/99d824e5c5084432b7cc6e376f6ba1e5.jpg'}], 'usage': None}} - [system]
2025-07-04 03:09:31,050 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:09:31,051 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:09:31,052 - app - INFO - Received request: - [system]
2025-07-04 03:09:31,052 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:09:31,052 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:09:31,052 - app - INFO - using normal format - [system]
2025-07-04 03:09:31,053 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:09:31,053 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:12:49,537 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:12:49,537 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:12:49,538 - app - INFO - Received request: - [system]
2025-07-04 03:12:49,538 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:12:49,538 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:12:49,538 - app - INFO - using normal format - [system]
2025-07-04 03:12:49,538 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:12:49,539 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:20:50,861 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:20:50,862 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:20:50,863 - app - INFO - Received request: - [system]
2025-07-04 03:20:50,863 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:20:50,863 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:20:50,863 - app - INFO - using normal format - [system]
2025-07-04 03:20:50,864 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:20:50,864 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:21:15,832 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:21:15,833 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:21:15,833 - app - INFO - Received request: - [system]
2025-07-04 03:21:15,834 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:21:15,834 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:21:15,834 - app - INFO - using normal format - [system]
2025-07-04 03:21:15,835 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:21:15,835 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:22:19,043 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:22:19,044 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:22:19,045 - app - INFO - Received request: - [system]
2025-07-04 03:22:19,045 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:22:19,047 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:22:19,047 - app - INFO - using normal format - [system]
2025-07-04 03:22:19,047 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:22:19,048 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:22:37,428 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:22:37,429 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:22:37,430 - app - INFO - Received request: - [system]
2025-07-04 03:22:37,430 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:22:37,430 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:22:37,430 - app - INFO - using normal format - [system]
2025-07-04 03:22:37,431 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:22:37,431 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:23:57,736 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:23:57,737 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:23:57,737 - app - INFO - Received request: - [system]
2025-07-04 03:23:57,737 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:23:57,738 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:23:57,738 - app - INFO - using normal format - [system]
2025-07-04 03:23:57,738 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:23:57,739 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:27:19,641 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 03:27:19,647 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 03:27:19,648 - app - INFO - Received request: - [system]
2025-07-04 03:27:19,648 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 03:27:19,648 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 03:27:19,648 - app - INFO - using normal format - [system]
2025-07-04 03:27:19,649 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 03:27:19,649 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-04 03:34:02,352 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 03:34:02,819 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:02,820 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:02,820 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        项目介绍\n        \n        这是一个文档处理系统的介绍。\n        \n        主要功能：\n        - 支持多种文件格式\n        - 自动结构化处理\n        - 输出Markdown格式\n        \n        技术特点\n        \n        1. 支持Word文档\n        2. 支持PDF文件\n        3. 支持PowerPoint演示文稿\n        4. 支持Excel表格\n        \n        使用方法：\n        通过API调用即可使用本服务。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:34:02,823 - app - INFO - 【Service】【文档处理】成功，原文长度: 303, Markdown长度: 189 - [system]
2025-07-04 03:34:02,823 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:02,824 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/tmp/tmppxpdlw6r.txt', 'structure_level': 'advanced'} - [system]
2025-07-04 03:34:02,824 - app - INFO - 【Service】【文档处理】成功，原文长度: 200, Markdown长度: 547 - [system]
2025-07-04 03:34:02,825 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:02,825 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'basic'} - [system]
2025-07-04 03:34:02,825 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 147 - [system]
2025-07-04 03:34:02,825 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:34:02,825 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 177 - [system]
2025-07-04 03:34:02,826 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'advanced'} - [system]
2025-07-04 03:34:02,826 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 398 - [system]
2025-07-04 03:34:02,826 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:02,826 - app - INFO - 【Service】【文档处理】输入参数: {'file_url': 'https://example.com/test.txt', 'structure_level': 'medium'} - [system]
2025-07-04 03:34:04,047 - app - ERROR - 【Service】【文档处理】文档处理失败: 下载或处理URL文件失败: 404 Client Error: Not Found for url: https://example.com/test.txt - [system]
2025-07-04 03:34:04,048 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:34:04,048 - app - INFO - 【Service】【文档处理】输入参数: {} - [system]
2025-07-04 03:34:04,048 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:34:04,048 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/nonexistent/file.txt'} - [system]
2025-07-04 03:34:04,049 - app - ERROR - 【Service】【文档处理】文档处理失败: 文件不存在: /nonexistent/file.txt - [system]
2025-07-04 03:34:04,049 - app - INFO - 【Service】【文档处理】输入参数: {'content': ''} - [system]
2025-07-04 03:34:04,049 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:34:04,049 - app - INFO - 【Service】【文档处理】输入参数: {'content': '# 测试标题\n\n这是测试内容。\n\n- 列表项1\n- 列表项2', 'structure_level': 'basic'} - [system]
2025-07-04 03:34:04,050 - app - INFO - 【Service】【文档处理】成功，原文长度: 30, Markdown长度: 38 - [system]
2025-07-04 03:36:02,152 - app - WARNING - PyPDF2或pdfplumber未安装，无法处理PDF文档 - [system]
2025-07-04 03:36:02,525 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:02,526 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:02,526 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        项目介绍\n        \n        这是一个文档处理系统的介绍。\n        \n        主要功能：\n        - 支持多种文件格式\n        - 自动结构化处理\n        - 输出Markdown格式\n        \n        技术特点\n        \n        1. 支持Word文档\n        2. 支持PDF文件\n        3. 支持PowerPoint演示文稿\n        4. 支持Excel表格\n        \n        使用方法：\n        通过API调用即可使用本服务。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:36:02,528 - app - INFO - 【Service】【文档处理】成功，原文长度: 303, Markdown长度: 189 - [system]
2025-07-04 03:36:02,529 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:02,529 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/tmp/tmp0hzkmzbh.txt', 'structure_level': 'advanced'} - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】成功，原文长度: 200, Markdown长度: 547 - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'basic'} - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 147 - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:36:02,530 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 177 - [system]
2025-07-04 03:36:02,531 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'advanced'} - [system]
2025-07-04 03:36:02,531 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 398 - [system]
2025-07-04 03:36:02,531 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:02,531 - app - INFO - 【Service】【文档处理】输入参数: {'file_url': 'https://example.com/test.txt', 'structure_level': 'medium'} - [system]
2025-07-04 03:36:06,356 - app - ERROR - 【Service】【文档处理】文档处理失败: 下载或处理URL文件失败: 404 Client Error: Not Found for url: https://example.com/test.txt - [system]
2025-07-04 03:36:06,358 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:36:06,358 - app - INFO - 【Service】【文档处理】输入参数: {} - [system]
2025-07-04 03:36:06,358 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:36:06,358 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/nonexistent/file.txt'} - [system]
2025-07-04 03:36:06,359 - app - ERROR - 【Service】【文档处理】文档处理失败: 文件不存在: /nonexistent/file.txt - [system]
2025-07-04 03:36:06,359 - app - INFO - 【Service】【文档处理】输入参数: {'content': ''} - [system]
2025-07-04 03:36:06,359 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:36:06,359 - app - INFO - 【Service】【文档处理】输入参数: {'content': '# 测试标题\n\n这是测试内容。\n\n- 列表项1\n- 列表项2', 'structure_level': 'basic'} - [system]
2025-07-04 03:36:06,360 - app - INFO - 【Service】【文档处理】成功，原文长度: 30, Markdown长度: 38 - [system]
2025-07-04 03:37:51,264 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:51,264 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:51,264 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        项目介绍\n        \n        这是一个文档处理系统的介绍。\n        \n        主要功能：\n        - 支持多种文件格式\n        - 自动结构化处理\n        - 输出Markdown格式\n        \n        技术特点\n        \n        1. 支持Word文档\n        2. 支持PDF文件\n        3. 支持PowerPoint演示文稿\n        4. 支持Excel表格\n        \n        使用方法：\n        通过API调用即可使用本服务。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:37:51,269 - app - INFO - 【Service】【文档处理】成功，原文长度: 303, Markdown长度: 189 - [system]
2025-07-04 03:37:51,269 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:51,270 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/tmp/tmpjwoprnoy.txt', 'structure_level': 'advanced'} - [system]
2025-07-04 03:37:51,270 - app - INFO - 【Service】【文档处理】成功，原文长度: 200, Markdown长度: 547 - [system]
2025-07-04 03:37:51,270 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:51,270 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'basic'} - [system]
2025-07-04 03:37:51,271 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 147 - [system]
2025-07-04 03:37:51,271 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'medium'} - [system]
2025-07-04 03:37:51,271 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 177 - [system]
2025-07-04 03:37:51,271 - app - INFO - 【Service】【文档处理】输入参数: {'content': '\n        产品说明书\n        \n        第一部分：产品概述\n        本产品是一款智能设备。\n        \n        主要特点：\n        - 高性能处理器\n        - 大容量存储\n        - 智能算法\n        \n        第二部分：技术规格\n        1. CPU: 8核处理器\n        2. 内存: 16GB RAM\n        3. 存储: 512GB SSD\n        \n        使用注意事项：\n        请仔细阅读说明书后使用。\n        ', 'structure_level': 'advanced'} - [system]
2025-07-04 03:37:51,272 - app - INFO - 【Service】【文档处理】成功，原文长度: 285, Markdown长度: 398 - [system]
2025-07-04 03:37:51,272 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:51,272 - app - INFO - 【Service】【文档处理】输入参数: {'file_url': 'https://example.com/test.txt', 'structure_level': 'medium'} - [system]
2025-07-04 03:37:52,077 - app - ERROR - 【Service】【文档处理】文档处理失败: 下载或处理URL文件失败: 404 Client Error: Not Found for url: https://example.com/test.txt - [system]
2025-07-04 03:37:52,079 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 03:37:52,079 - app - INFO - 【Service】【文档处理】输入参数: {} - [system]
2025-07-04 03:37:52,079 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:37:52,079 - app - INFO - 【Service】【文档处理】输入参数: {'file_path': '/nonexistent/file.txt'} - [system]
2025-07-04 03:37:52,079 - app - ERROR - 【Service】【文档处理】文档处理失败: 文件不存在: /nonexistent/file.txt - [system]
2025-07-04 03:37:52,080 - app - INFO - 【Service】【文档处理】输入参数: {'content': ''} - [system]
2025-07-04 03:37:52,080 - app - ERROR - 【Service】【文档处理】必须提供file_path、file_url或content中的一个参数 - [system]
2025-07-04 03:37:52,080 - app - INFO - 【Service】【文档处理】输入参数: {'content': '# 测试标题\n\n这是测试内容。\n\n- 列表项1\n- 列表项2', 'structure_level': 'basic'} - [system]
2025-07-04 03:37:52,080 - app - INFO - 【Service】【文档处理】成功，原文长度: 30, Markdown长度: 38 - [system]
2025-07-04 06:16:11,524 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'Python编程', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 06:16:11,782 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 06:16:11,783 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '人工智能', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 06:16:11,909 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 06:16:11,911 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '机器学习', 'summary': True}, 'timeout': 30} - [system]
2025-07-04 06:16:12,050 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 06:16:12,051 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:12,051 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': 'headers认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 06:16:12,307 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 06:16:12,308 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:12,308 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'data': {'query': 'JSON认证测试', 'summary': True}, 'timeout': 20} - [system]
2025-07-04 06:16:12,308 - app - INFO - 【Service】【HTTP请求】已应用bearer鉴权 - [system]
2025-07-04 06:16:12,528 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-04 06:16:12,528 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:12,529 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer invalid-key'}, 'data': {'query': '测试查询', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 06:16:12,658 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 401 - [system]
2025-07-04 06:16:12,659 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'https://api.bochaai.com/v1/web-search', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer sk-7a8dd800f6ee48f08ecccf77efa9fcaa'}, 'data': {'query': '', 'summary': True}, 'timeout': 15} - [system]
2025-07-04 06:16:12,793 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 400 - [system]
2025-07-04 06:16:12,794 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:14,180 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 06:16:14,789 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 06:16:32,180 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'params': {'test': 'value'}} - [system]
2025-07-04 06:16:35,911 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get?test=value (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:35,912 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/post', 'method': 'POST', 'data': {'name': '测试', 'value': 123}} - [system]
2025-07-04 06:16:38,561 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /post (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:38,561 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/put', 'method': 'PUT', 'data': {'update': True}} - [system]
2025-07-04 06:16:41,247 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /put (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:41,248 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/delete', 'method': 'DELETE'} - [system]
2025-07-04 06:16:43,923 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /delete (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:43,924 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:43,925 - app - INFO - 【Service】【HTTP请求】输入: {'url': '', 'method': 'GET'} - [system]
2025-07-04 06:16:43,925 - app - ERROR - 【Service】【HTTP请求】URL参数不能为空 - [system]
2025-07-04 06:16:43,925 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'INVALID_METHOD'} - [system]
2025-07-04 06:16:43,925 - app - ERROR - 【Service】【HTTP请求】异常: 不支持的HTTP方法: INVALID_METHOD - [system]
2025-07-04 06:16:43,926 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:43,926 - app - INFO - 【Service】【HTTP批量请求】开始处理3个请求 - [system]
2025-07-04 06:16:43,926 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'params': {'batch': '1'}} - [system]
2025-07-04 06:16:46,590 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get?batch=1 (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:46,591 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/post', 'method': 'POST', 'data': {'batch': '2'}} - [system]
2025-07-04 06:16:49,284 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /post (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:49,285 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/put', 'method': 'PUT', 'data': {'batch': '3'}} - [system]
2025-07-04 06:16:54,047 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /put (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:54,048 - app - INFO - 【Service】【HTTP批量请求】完成，成功: 0, 失败: 3 - [system]
2025-07-04 06:16:54,048 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:54,048 - app - INFO - 【Service】【HTTP文件下载】开始下载: http://httpbin.org/json -> /tmp/test_download.json - [system]
2025-07-04 06:16:56,720 - app - ERROR - 【Service】【HTTP文件下载】文件下载失败 - [system]
2025-07-04 06:16:56,721 - app - INFO - 【Service】【HTTP文件上传】开始上传: /tmp/test_upload.txt -> http://httpbin.org/post - [system]
2025-07-04 06:16:59,380 - app - ERROR - 【Service】【HTTP文件上传】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /post (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:16:59,380 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:16:59,381 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'params': {'test': 'convenience'}} - [system]
2025-07-04 06:17:02,071 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get?test=convenience (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:17:02,072 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://nonexistent-domain-12345.com', 'method': 'GET', 'timeout': 5} - [system]
2025-07-04 06:17:02,685 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7fc0a3401850>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-04 06:17:03,288 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7fc0a3403d90>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-04 06:17:04,493 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7fc0a3403f50>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-04 06:17:04,501 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='nonexistent-domain-12345.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7fc0a340c0d0>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")) - [system]
2025-07-04 06:17:04,501 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/delay/10', 'method': 'GET', 'timeout': 2} - [system]
2025-07-04 06:17:07,244 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /delay/10 (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-04 06:17:07,244 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-04 06:20:49,043 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '3401558016', 'modelType': '/qwen32', 'baseUrl': 'http://***********:7782/v1/chat/completions', 'query': 'nihao ', 'apiKey': '', 'inputMaxToken': 4096, 'outputMaxToken': 4096, 'stream': True, 'deployMode': 0, 'multiType': 'text_generate'} - [system]
2025-07-04 06:20:49,044 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 06:20:49,045 - app - INFO - Received request: - [system]
2025-07-04 06:20:49,045 - app - INFO - 【Service】【Model-流式-调用】inputs:nihao ,history:[] - [system]
2025-07-04 06:20:49,047 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 06:20:49,047 - app - INFO - using normal format - [system]
2025-07-04 06:20:49,048 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': '/qwen32', 'messages': [{'role': 'user', 'content': 'nihao '}], 'max_tokens': 4096, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1, 'stream': True} - [system]
2025-07-04 06:20:49,053 - app - ERROR - 【Service】【Model-流式-模型调用】异常:HTTPConnectionPool(host='***********', port=7782): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f81f96f7d50>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-04 06:21:31,477 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 06:21:32,096 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 06:28:28,149 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 06:28:28,791 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 06:32:46,563 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-04 06:32:47,171 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-04 06:33:17,067 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '3401558016', 'modelType': '/qwen32', 'baseUrl': 'http://***********:7782/v1/chat/completions', 'query': 'hi', 'apiKey': '', 'inputMaxToken': 4096, 'outputMaxToken': 4096, 'stream': True, 'deployMode': 0, 'multiType': 'text_generate'} - [system]
2025-07-04 06:33:17,068 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 06:33:17,068 - app - INFO - Received request: - [system]
2025-07-04 06:33:17,069 - app - INFO - 【Service】【Model-流式-调用】inputs:hi,history:[] - [system]
2025-07-04 06:33:17,078 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 06:33:17,078 - app - INFO - using normal format - [system]
2025-07-04 06:33:17,078 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': '/qwen32', 'messages': [{'role': 'user', 'content': 'hi'}], 'max_tokens': 4096, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1, 'stream': True} - [system]
2025-07-04 06:33:17,083 - app - ERROR - 【Service】【Model-流式-模型调用】异常:HTTPConnectionPool(host='***********', port=7782): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f6e82eb9d50>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-04 07:35:41,633 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1032489984', 'modelType': '音频理解', 'baseUrl': 'test', 'query': '你是谁', 'apiKey': '私有化部署', 'inputMaxToken': 1, 'outputMaxToken': 128, 'stream': False} - [system]
2025-07-04 07:35:41,634 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 07:35:41,635 - app - INFO - Received request: - [system]
2025-07-04 07:35:41,635 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-04 07:35:41,635 - app - ERROR - 【Service】【Model-非流式-调用】异常:目前大模型仅支持1长度输入,prompt输入长度为3，导致模型可生成长度为0,请检查输入长度 - [system]
2025-07-04 07:35:41,635 - app - ERROR - 【Service】【Model-推理】异常:目前大模型仅支持1长度输入,prompt输入长度为3，导致模型可生成长度为0,请检查输入长度 - [system]
2025-07-04 07:41:43,398 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:7782/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 4096, 'outputMaxToken': 4096, 'stream': False} - [system]
2025-07-04 07:41:43,399 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 07:41:43,400 - app - INFO - Received request: - [system]
2025-07-04 07:41:43,400 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-04 07:41:43,400 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 07:41:43,401 - app - INFO - using normal format - [system]
2025-07-04 07:41:43,401 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-04 07:41:43,401 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 4096, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-04 07:41:43,407 - app - ERROR - 【Service】【Model-推理】异常:HTTPConnectionPool(host='***********', port=7782): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f6e82ec9910>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-04 08:11:08,465 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '*********', 'baseUrl': 'test3', 'query': '你是谁', 'apiKey': '私有化部署', 'inputMaxToken': 1, 'outputMaxToken': 128, 'stream': False} - [system]
2025-07-04 08:11:08,466 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 08:11:08,466 - app - INFO - Received request: - [system]
2025-07-04 08:11:08,467 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-04 08:11:08,467 - app - ERROR - 【Service】【Model-非流式-调用】异常:目前大模型仅支持1长度输入,prompt输入长度为3，导致模型可生成长度为0,请检查输入长度 - [system]
2025-07-04 08:11:08,467 - app - ERROR - 【Service】【Model-推理】异常:目前大模型仅支持1长度输入,prompt输入长度为3，导致模型可生成长度为0,请检查输入长度 - [system]
2025-07-04 08:34:26,189 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-04 08:34:26,190 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-04 08:34:26,190 - app - INFO - Received request: - [system]
2025-07-04 08:34:26,191 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-04 08:34:26,191 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-04 08:34:26,191 - app - INFO - using normal format - [system]
2025-07-04 08:34:26,192 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-04 08:34:26,192 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 13:14:33,353 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 13:14:33,356 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 13:14:33,356 - app - INFO - Received request: - [system]
2025-07-05 13:14:33,357 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 13:14:33,357 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 13:14:33,357 - app - INFO - using normal format - [system]
2025-07-05 13:14:33,357 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 13:14:33,357 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 13:39:26,566 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 13:39:26,567 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 13:39:26,568 - app - INFO - Received request: - [system]
2025-07-05 13:39:26,568 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 13:39:26,568 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 13:39:26,568 - app - INFO - using normal format - [system]
2025-07-05 13:39:26,569 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 13:39:26,569 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 13:53:48,037 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 13:53:48,037 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 13:53:48,038 - app - INFO - Received request: - [system]
2025-07-05 13:53:48,038 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 13:53:48,038 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 13:53:48,039 - app - INFO - using normal format - [system]
2025-07-05 13:53:48,039 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 13:53:48,040 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:17:05,424 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 14:17:05,425 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:17:05,425 - app - INFO - Received request: - [system]
2025-07-05 14:17:05,426 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 14:17:05,426 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 14:17:05,426 - app - INFO - using normal format - [system]
2025-07-05 14:17:05,427 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 14:17:05,427 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:19:05,680 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 14:19:05,681 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:19:05,681 - app - INFO - Received request: - [system]
2025-07-05 14:19:05,681 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 14:19:05,682 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 14:19:05,682 - app - INFO - using normal format - [system]
2025-07-05 14:19:05,682 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 14:19:05,682 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:20:54,247 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 14:20:54,247 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:20:54,248 - app - INFO - Received request: - [system]
2025-07-05 14:20:54,248 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 14:20:54,248 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 14:20:54,248 - app - INFO - using normal format - [system]
2025-07-05 14:20:54,249 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 14:20:54,249 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:22:19,989 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 14:22:19,990 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:22:19,990 - app - INFO - Received request: - [system]
2025-07-05 14:22:19,991 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 14:22:19,991 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 14:22:19,991 - app - INFO - using normal format - [system]
2025-07-05 14:22:19,992 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 14:22:19,992 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:25:22,760 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 14:25:22,761 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:25:22,761 - app - INFO - Received request: - [system]
2025-07-05 14:25:22,762 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 14:25:22,762 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 14:25:22,762 - app - INFO - using normal format - [system]
2025-07-05 14:25:22,763 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 14:25:22,763 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 14:38:52,978 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:38:52,979 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:38:52,979 - app - ERROR - 【Controller】【AI网关-service处理】异常'stream' - [system]
2025-07-05 14:42:48,371 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:42:48,372 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:42:48,372 - app - ERROR - 【Controller】【AI网关-service处理】异常'stream' - [system]
2025-07-05 14:42:57,112 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:42:57,112 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:42:57,113 - app - ERROR - 【Controller】【AI网关-service处理】异常'stream' - [system]
2025-07-05 14:54:45,959 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}], 'stream': True} - [system]
2025-07-05 14:54:45,959 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:54:45,960 - app - INFO - 【Service】【Agent-流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}], 'stream': True} - [system]
2025-07-05 14:54:46,048 - app - INFO - 【Service】【Agent-流式-执行】异常:All connection attempts failed - [system]
2025-07-05 14:55:38,414 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': True, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:55:38,415 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:55:38,416 - app - INFO - 【Service】【Agent-流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': True, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:55:38,438 - app - INFO - 【Service】【Agent-流式-执行】异常:All connection attempts failed - [system]
2025-07-05 14:56:38,189 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': True, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:56:38,190 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:56:38,190 - app - INFO - 【Service】【Agent-流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': True, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:56:38,212 - app - INFO - 【Service】【Agent-流式-执行】异常:All connection attempts failed - [system]
2025-07-05 14:57:22,953 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:57:22,954 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:57:22,954 - app - ERROR - 【Controller】【AI网关-service处理】异常'stream' - [system]
2025-07-05 14:59:18,518 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': 1, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:59:18,519 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:59:18,519 - app - INFO - 【Service】【Agent-流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': 1, 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:59:18,542 - app - INFO - 【Service】【Agent-流式-执行】异常:All connection attempts failed - [system]
2025-07-05 14:59:29,753 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:59:29,754 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 14:59:29,754 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 14:59:29,774 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-05 14:59:29,775 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-05 15:00:02,051 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:00:02,052 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:00:02,054 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:00:02,073 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-05 15:00:02,073 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-05 15:01:01,674 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:01:01,675 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:01:01,676 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:01:01,696 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-05 15:01:01,696 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-05 15:01:23,966 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:01:23,967 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:01:23,968 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:01:23,988 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-05 15:01:23,989 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-05 15:04:20,917 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:04:20,919 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:04:20,920 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'faqInfo': None, 'knowledgeInfo': None, 'query': '', 'apiKey': '', 'company': '', 'modelType': '', 'baseUrl': '', 'deployMode': '', 'stream': '', 'toolList': [{'toolName': '1'}, {'toolName': '2'}]} - [system]
2025-07-05 15:04:20,941 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-05 15:04:20,941 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-05 15:14:03,871 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 15:14:03,872 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:14:03,873 - app - INFO - Received request: - [system]
2025-07-05 15:14:03,873 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 15:14:03,873 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 15:14:03,874 - app - INFO - using normal format - [system]
2025-07-05 15:14:03,874 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 15:14:03,874 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 15:22:53,653 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 15:22:53,654 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:22:53,655 - app - INFO - Received request: - [system]
2025-07-05 15:22:53,655 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 15:22:53,655 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 15:22:53,655 - app - INFO - using normal format - [system]
2025-07-05 15:22:53,656 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 15:22:53,656 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-05 15:31:58,715 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        ', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-05 15:31:58,716 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-05 15:31:58,716 - app - INFO - Received request: - [system]
2025-07-05 15:31:58,717 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        ,history:None - [system]
2025-07-05 15:31:58,717 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-05 15:31:58,717 - app - INFO - using normal format - [system]
2025-07-05 15:31:58,718 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-05 15:31:58,718 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 01:38:03,174 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2257274880', 'modelType': 'deepseek-chat', 'baseUrl': 'https://api.deepseek.com/v1', 'query': '你好请问你会什么', 'apiKey': '***********************************', 'inputMaxToken': 8192, 'outputMaxToken': 8192, 'stream': True, 'deployMode': 1, 'multiType': 'text_generate'} - [system]
2025-07-07 01:38:03,176 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 01:38:03,176 - app - INFO - Received request: - [system]
2025-07-07 01:38:03,176 - app - INFO - 【Service】【Model-流式-调用】inputs:你好请问你会什么,history:[] - [system]
2025-07-07 01:38:03,178 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 01:38:03,178 - app - INFO - using normal format - [system]
2025-07-07 01:38:03,178 - app - INFO - 【Service】【Model-流式-模型调用-OpenAI方式】 - [system]
2025-07-07 01:38:03,721 - openai._base_client - INFO - Retrying request to /chat/completions in 0.442124 seconds - [system]
2025-07-07 01:38:04,271 - openai._base_client - INFO - Retrying request to /chat/completions in 0.906672 seconds - [system]
2025-07-07 01:38:05,268 - app - ERROR - 【Service】【Model-流式-模型调用】异常:cannot access local variable 'response' where it is not associated with a value - [system]
2025-07-07 05:23:51,808 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:23:51,809 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:23:51,810 - app - INFO - Received request: - [system]
2025-07-07 05:23:51,810 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:23:51,810 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:23:51,810 - app - INFO - using normal format - [system]
2025-07-07 05:23:51,810 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:23:51,810 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:24:06,046 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:24:06,047 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:24:06,047 - app - INFO - Received request: - [system]
2025-07-07 05:24:06,047 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:24:06,047 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:24:06,047 - app - INFO - using normal format - [system]
2025-07-07 05:24:06,047 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:24:06,047 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:24:54,997 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:24:54,998 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:24:54,998 - app - INFO - Received request: - [system]
2025-07-07 05:24:54,999 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:24:54,999 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:24:54,999 - app - INFO - using normal format - [system]
2025-07-07 05:24:54,999 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:24:55,000 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:29:23,108 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:29:23,109 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:29:23,109 - app - INFO - Received request: - [system]
2025-07-07 05:29:23,109 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:29:23,110 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:29:23,110 - app - INFO - using normal format - [system]
2025-07-07 05:29:23,110 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:29:23,111 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:31:21,643 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:31:21,644 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:31:21,644 - app - INFO - Received request: - [system]
2025-07-07 05:31:21,644 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:31:21,644 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:31:21,645 - app - INFO - using normal format - [system]
2025-07-07 05:31:21,645 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:31:21,645 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:32:25,837 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:32:25,837 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:32:25,838 - app - INFO - Received request: - [system]
2025-07-07 05:32:25,838 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:32:25,839 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:32:25,839 - app - INFO - using normal format - [system]
2025-07-07 05:32:25,839 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:32:25,840 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:36:25,937 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:36:25,938 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:36:25,938 - app - INFO - Received request: - [system]
2025-07-07 05:36:25,939 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:36:25,939 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:36:25,939 - app - INFO - using normal format - [system]
2025-07-07 05:36:25,939 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:36:25,940 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:39:11,507 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:39:11,507 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:39:11,508 - app - INFO - Received request: - [system]
2025-07-07 05:39:11,508 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:39:11,508 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:39:11,509 - app - INFO - using normal format - [system]
2025-07-07 05:39:11,509 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:39:11,509 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:40:42,650 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:40:42,651 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:40:42,651 - app - INFO - Received request: - [system]
2025-07-07 05:40:42,652 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 05:40:42,652 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:40:42,652 - app - INFO - using normal format - [system]
2025-07-07 05:40:42,653 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 05:40:42,653 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 05:45:20,851 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:45:20,852 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:45:20,852 - app - INFO - Received request: - [system]
2025-07-07 05:45:20,852 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:45:20,853 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:45:20,853 - app - INFO - using normal format - [system]
2025-07-07 05:45:20,853 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:45:20,854 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1, 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}]} - [system]
2025-07-07 05:45:20,879 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:33471, pid=938] Object of type list_iterator is not JSON serializable'} - [system]
2025-07-07 05:45:20,879 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 05:45:44,316 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:45:44,317 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:45:44,317 - app - INFO - Received request: - [system]
2025-07-07 05:45:44,318 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:45:44,318 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:45:44,318 - app - INFO - using normal format - [system]
2025-07-07 05:45:44,319 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:45:44,319 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1, 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}]} - [system]
2025-07-07 05:45:44,342 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:33471, pid=938] Object of type list_iterator is not JSON serializable'} - [system]
2025-07-07 05:45:44,342 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 05:45:47,605 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:45:47,606 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:45:47,606 - app - INFO - Received request: - [system]
2025-07-07 05:45:47,606 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:45:47,607 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:45:47,607 - app - INFO - using normal format - [system]
2025-07-07 05:45:47,608 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:45:47,608 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1, 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}]} - [system]
2025-07-07 05:45:47,632 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:33471, pid=938] Object of type list_iterator is not JSON serializable'} - [system]
2025-07-07 05:45:47,633 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 05:46:05,749 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:46:05,750 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:46:05,750 - app - INFO - Received request: - [system]
2025-07-07 05:46:05,751 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:46:05,751 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:46:05,751 - app - INFO - using normal format - [system]
2025-07-07 05:46:05,752 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:46:05,752 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1, 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}]} - [system]
2025-07-07 05:46:05,772 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:33471, pid=938] Object of type list_iterator is not JSON serializable'} - [system]
2025-07-07 05:46:05,772 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 05:51:18,130 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:51:18,131 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:51:18,132 - app - INFO - Received request: - [system]
2025-07-07 05:51:18,132 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:51:18,132 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:51:18,132 - app - INFO - using normal format - [system]
2025-07-07 05:51:18,133 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:51:18,133 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1, 'tools': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}]} - [system]
2025-07-07 05:51:18,159 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:33471, pid=938] Object of type list_iterator is not JSON serializable'} - [system]
2025-07-07 05:51:18,159 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 05:57:17,241 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 05:57:17,242 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 05:57:17,242 - app - INFO - Received request: - [system]
2025-07-07 05:57:17,243 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 05:57:17,243 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 05:57:17,243 - app - INFO - using normal format - [system]
2025-07-07 05:57:17,244 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 05:57:17,244 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 05:57:19,147 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3b83e8d2-5af7-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751867836, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '你好，看起来你可能还没有提供具体的任务描述。请告诉我你希望我完成的复杂任务，包括任务的目标、期望的结果以及任何相关的背景信息。这样我才能更好地为你服务，自动调度合适的子智能体来完成每个子任务，并最终返回汇总的结果。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 391, 'completion_tokens': 58, 'total_tokens': 449}} - [system]
2025-07-07 06:03:33,943 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 06:03:33,944 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 06:03:33,944 - app - INFO - Received request: - [system]
2025-07-07 06:03:33,945 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 06:03:33,945 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 06:03:33,945 - app - INFO - using normal format - [system]
2025-07-07 06:03:33,946 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 06:03:33,946 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 06:04:25,600 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 06:04:25,601 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 06:04:25,601 - app - INFO - Received request: - [system]
2025-07-07 06:04:25,602 - app - INFO - 【Service】【Model-非流式-调用】inputs:
    # Role: MCP 控制智能体调度器
    
    ## Profile
    - language: 中文
    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
    
    ## Skills
    - 理解用户的高层任务目标并进行任务分解
    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
    - 协调多个子智能体的任务顺序和依赖关系
    - 实时反馈任务进度并汇总结果输出
    
    ## OutputFormat(可选项):
    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
    
    ## Rules
    - 所有任务必须以结构化形式注册至 MCP 平台
    - 子任务的定义必须明确，可度量
    - 避免资源冲突和任务重复分配
    - 汇总输出需具备可读性与可验证性
    
    ## Workflows
    1. 读取用户输入的任务目标
    2. 拆分为若干子任务，定义每个任务所需能力
    3. 为每个子任务选择合适的子智能体并注册至 MCP
    4. 监控任务状态并在任务完成后收集结果
    5. 汇总结果并以所需格式输出
    
    ## Init
    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
    
    ## 用户问题
    测试问题

,history:None - [system]
2025-07-07 06:04:25,602 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 06:04:25,602 - app - INFO - using normal format - [system]
2025-07-07 06:04:25,603 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 06:04:25,603 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n    # Role: MCP 控制智能体调度器\n    \n    ## Profile\n    - language: 中文\n    - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n    \n    ## Skills\n    - 理解用户的高层任务目标并进行任务分解\n    - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n    - 协调多个子智能体的任务顺序和依赖关系\n    - 实时反馈任务进度并汇总结果输出\n    \n    ## OutputFormat(可选项):\n    输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n    \n    ## Rules\n    - 所有任务必须以结构化形式注册至 MCP 平台\n    - 子任务的定义必须明确，可度量\n    - 避免资源冲突和任务重复分配\n    - 汇总输出需具备可读性与可验证性\n    \n    ## Workflows\n    1. 读取用户输入的任务目标\n    2. 拆分为若干子任务，定义每个任务所需能力\n    3. 为每个子任务选择合适的子智能体并注册至 MCP\n    4. 监控任务状态并在任务完成后收集结果\n    5. 汇总结果并以所需格式输出\n    \n    ## Init\n    你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n    \n    ## 用户问题\n    测试问题\n\n'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 06:04:26,995 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3ad61b98-5af8-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751868264, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '你好，看起来你可能还没有提供具体的任务描述。请告诉我你希望我完成的复杂任务，包括任务的目标、期望的结果以及任何相关的背景信息。这样我才能更好地为你服务，自动调度合适的子智能体来完成每个子任务，并最终返回汇总的结果。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 391, 'completion_tokens': 58, 'total_tokens': 449}} - [system]
2025-07-07 06:06:35,612 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 06:06:35,613 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 06:06:35,613 - app - INFO - Received request: - [system]
2025-07-07 06:06:35,613 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 06:06:35,614 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 06:06:35,614 - app - INFO - using normal format - [system]
2025-07-07 06:06:35,614 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 06:06:35,614 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 06:12:06,752 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-07 06:12:06,753 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 06:12:06,753 - app - INFO - Received request: - [system]
2025-07-07 06:12:06,754 - app - INFO - 【Service】【Model-非流式-调用】inputs:
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        测试问题,history:None - [system]
2025-07-07 06:12:06,754 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 06:12:06,754 - app - INFO - using normal format - [system]
2025-07-07 06:12:06,754 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 06:12:06,755 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题'}], 'max_tokens': 100000, 'temperature': 0.95, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 06:12:08,347 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat4db44fe0-5af9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751868726, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '你好，看起来你可能还没有提供具体的任务描述。请告诉我你希望我完成的复杂任务，包括任务的目标、期望的结果以及任何相关的背景信息。这样我才能更好地为你服务，自动调度合适的子智能体来完成任务。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 390, 'completion_tokens': 51, 'total_tokens': 441}} - [system]
2025-07-07 06:12:08,368 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n                    # 你是智能体问答专家\n\n                    # 用户问题:\n                    测试问题\n\n                    # 文件知识\n                    None\n\n                    # faq知识\n                    None\n\n                    # 工具信息\n                    你好，看起来你可能还没有提供具体的任务描述。请告诉我你希望我完成的复杂任务，包括任务的目标、期望的结果以及任何相关的背景信息。这样我才能更好地为你服务，自动调度合适的子智能体来完成任务。            \n                ', 'tool': None, 'history': None, 'stream': False} - [system]
2025-07-07 06:12:08,369 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 06:12:08,369 - app - INFO - Received request: - [system]
2025-07-07 06:12:08,369 - app - INFO - 【Service】【Model-非流式-调用】inputs:
                    # 你是智能体问答专家

                    # 用户问题:
                    测试问题

                    # 文件知识
                    None

                    # faq知识
                    None

                    # 工具信息
                    你好，看起来你可能还没有提供具体的任务描述。请告诉我你希望我完成的复杂任务，包括任务的目标、期望的结果以及任何相关的背景信息。这样我才能更好地为你服务，自动调度合适的子智能体来完成任务。            
                ,history:None - [system]
2025-07-07 06:12:08,369 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 06:12:08,370 - app - INFO - using normal format - [system]
2025-07-07 06:12:08,370 - app - ERROR - 【Service】【Model-非流式-模型调用】异常:参数company：qwen的部署方式不符合规范，请检查 - [system]
2025-07-07 06:12:08,370 - app - ERROR - 【Service】【Model-推理】异常:'参数company：qwen的部署方式不符合规范，请检查' - [system]
2025-07-07 07:05:56,928 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1887694976785866752', 'baseUrl': 'https://api.deepseek.com/v1', 'query': '你是谁', 'apiKey': '***********************************', 'inputMaxToken': 8192, 'outputMaxToken': 8192, 'stream': False} - [system]
2025-07-07 07:05:56,929 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 07:05:56,929 - app - INFO - Received request: - [system]
2025-07-07 07:05:56,929 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-07 07:05:56,929 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 07:05:56,929 - app - INFO - using normal format - [system]
2025-07-07 07:05:56,929 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 07:05:56,930 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 07:05:57,577 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'event_id': '30-clou-68-20250707150554-3350166b', 'error_msg': 'Not Found. Please check the configuration.'} - [system]
2025-07-07 07:05:57,577 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 07:06:03,980 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1887694976785866752', 'baseUrl': 'https://api.deepseek.com/v1', 'query': '你是谁', 'apiKey': '***********************************', 'inputMaxToken': 8192, 'outputMaxToken': 8192, 'stream': False} - [system]
2025-07-07 07:06:03,980 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 07:06:03,981 - app - INFO - Received request: - [system]
2025-07-07 07:06:03,981 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-07 07:06:03,981 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 07:06:03,981 - app - INFO - using normal format - [system]
2025-07-07 07:06:03,982 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 07:06:03,982 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 07:06:04,601 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'event_id': '30-clou-68-20250707150601-c75d8d48', 'error_msg': 'Not Found. Please check the configuration.'} - [system]
2025-07-07 07:06:04,601 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-07 07:27:31,363 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1887694976785866752', 'baseUrl': 'https://api.deepseek.com/v1', 'query': '你是谁', 'apiKey': '***********************************', 'inputMaxToken': 8192, 'outputMaxToken': 8192, 'stream': False} - [system]
2025-07-07 07:27:31,364 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-07 07:27:31,365 - app - INFO - Received request: - [system]
2025-07-07 07:27:31,365 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-07 07:27:31,365 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-07 07:27:31,365 - app - INFO - using normal format - [system]
2025-07-07 07:27:31,366 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-07 07:27:31,366 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-07 07:27:31,994 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'event_id': '30-clou-68-20250707152728-79896cc2', 'error_msg': 'Not Found. Please check the configuration.'} - [system]
2025-07-07 07:27:31,995 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
