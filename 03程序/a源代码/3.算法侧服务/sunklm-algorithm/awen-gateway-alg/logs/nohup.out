2025-07-08 10:05:00,569 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 10:05:01,153 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
INFO:     Started server process [1713211]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9080 (Press CTRL+C to quit)
2025-07-08 10:52:03,556 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:52:03,558 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:53037 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 10:54:59,380 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:54:59,381 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,382 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 10:54:59,384 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:54330 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 10:54:59,385 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
INFO:     ************:54329 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:47,545 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:47,546 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     **********:45550 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     **********:45550 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:56,404 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:56,405 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     **********:45588 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:32:11,712 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:32:11,713 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,714 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
INFO:     **********:45652 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     **********:46212 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:35:11,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/6843d4ea-a6b5-48cf-a473-a0f1c10a383e/ceff59a3-6f96-4ab3-b1f9-97cc14660b0f.png', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-08 11:35:11,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     **********:46356 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://***********:8001/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,108 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
INFO:     **********:47478 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:14:29,406 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:14:29,407 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:63571 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:63872 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:16:07,455 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-09 01:16:07,456 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:64387 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:16:44,387 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:16:44,388 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:64689 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:17:09,169 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:17:09,173 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:17:09,185 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:17:09,185 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:64845 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:23,800 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:23,800 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:49504 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:19:53,883 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:53,885 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:53,896 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:53,897 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:49746 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:21:55,390 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:21:55,392 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:21:55,406 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:21:55,407 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:50721 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'awen', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'awen', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:22:22,397 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: awen'} - [system]
2025-07-09 01:22:22,397 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:50943 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:23:18,855 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:23:18,857 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:23:20,396 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat4a217018-5c63-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752024197, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '我是Qwen，由阿里云开发的超大规模语言模型。我被设计用于生成各种文本，如文章、故事、诗歌、故事等，并能根据不同的场景和需求进行对话、提供信息查询、创作等任务。很高兴为您服务！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 31, 'completion_tokens': 54, 'total_tokens': 85}} - [system]
INFO:     ************:51426 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44058 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:34:07,656 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {}} - [system]
2025-07-09 01:34:07,657 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:56580 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:34:59,358 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '图片内容', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:34:59,359 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:56939 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:35:07,729 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:35:07,730 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:57002 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:36:23,789 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:36:23,790 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44060 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:56:40,208 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:56:40,209 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44062 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:59:43,049 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:59:43,050 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44064 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44066 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:59120 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:26:05,899 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:26:05,900 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:59941 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:31:10,480 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:10,481 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:60820 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:31:31,041 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:31,042 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:60884 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:34:08,331 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n# Role: MCP 控制智能体调度器\n\n## Profile\n- language: 中文\n- description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n\n## Skills\n- 理解用户的高层任务目标并进行任务分解\n- 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n- 协调多个子智能体的任务顺序和依赖关系\n- 实时反馈任务进度并汇总结果输出\n\n## OutputFormat(可选项):\n输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n\n## Rules\n- 所有任务必须以结构化形式注册至 MCP 平台\n- 子任务的定义必须明确，可度量\n- 避免资源冲突和任务重复分配\n- 汇总输出需具备可读性与可验证性\n\n## Workflows\n1. 读取用户输入的任务目标\n2. 拆分为若干子任务，定义每个任务所需能力\n3. 为每个子任务选择合适的子智能体并注册至 MCP\n4. 监控任务状态并在任务完成后收集结果\n5. 汇总结果并以所需格式输出\n\n## Init\n你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n\n## 用户问题\n测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:34:08,332 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:61366 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
INFO:     ************:62754 - "GET /ai/gateway/chatModel HTTP/1.1" 200 OK
INFO:     ************:62754 - "GET /favicon.ico HTTP/1.1" 404 Not Found
2025-07-09 02:53:41,755 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:53:41,756 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:64782 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:65080 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:59:32,952 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
INFO:     ***********:49435 - "POST /ai/gateway/chatAgent HTTP/1.1" 200 OK
2025-07-09 02:59:40,513 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:40,514 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:49457 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:49546 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 03:19:04,631 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:19:04,631 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:19:04,632 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:19:04,633 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:19:04,645 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:19:04,646 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ***********:52860 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 03:20:26,784 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'embedding', 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:20:26,784 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:53099 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 03:22:48,671 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'tool': [{'toolName': 'qichacha_bidding_details'}], 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:22:48,688 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:22:48,688 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ***********:53519 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 03:22:55,425 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'tool': [{'toolName': 'qichacha_bidding_details'}], 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:22:55,425 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:22:55,426 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:22:55,426 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:22:55,438 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:22:55,439 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ***********:53539 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 05:52:13,186 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 05:52:13,186 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44068 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 05:53:19,518 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 05:53:19,518 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44070 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 06:52:21,875 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://***********:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': 'http://127.0.0.1:9082/mcpTool', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '0076761088'} - [system]
2025-07-09 06:52:21,877 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 06:52:21,879 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://***********:9082/sse - [system]
2025-07-09 06:52:21,923 - httpx - INFO - HTTP Request: GET http://***********:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 06:52:21,925 - mcp.client.sse - INFO - Received endpoint URL: http://***********:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 - [system]
2025-07-09 06:52:21,925 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://***********:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 - [system]
2025-07-09 06:52:21,935 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,939 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 06:52:21,977 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 06:52:21,978 - app - INFO - 【Service】【MCP调用】tool:file_format_tool,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': 'http://127.0.0.1:9082/mcpTool', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 06:52:21,984 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,989 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,994 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='Error executing tool file_format_tool: 2 validation errors for file_format_toolArguments\nfilePaths\n  Field required [type=missing, input_value={\'code\': 0, \'data\': \'## 4.../json; charset=UTF-8"}\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\ncleanType\n  Field required [type=missing, input_value={\'code\': 0, \'data\': \'## 4.../json; charset=UTF-8"}\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing', annotations=None)] isError=True - [system]
1
INFO:     **********:35344 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
2025-07-09 06:55:17,684 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 06:55:17,684 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44072 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 07:03:40,890 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://***********:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:03:40,891 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:03:40,891 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://***********:9082/sse - [system]
2025-07-09 07:03:40,925 - httpx - INFO - HTTP Request: GET http://***********:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:03:40,926 - mcp.client.sse - INFO - Received endpoint URL: http://***********:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad - [system]
2025-07-09 07:03:40,927 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://***********:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad - [system]
2025-07-09 07:03:40,934 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:40,936 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:03:40,993 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:03:41,001 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:03:41,006 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:41,010 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:41,013 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
1
INFO:     **********:37996 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
2025-07-09 07:04:10,127 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://***********:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:04:10,128 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:04:10,128 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://***********:9082/sse - [system]
2025-07-09 07:04:10,148 - httpx - INFO - HTTP Request: GET http://***********:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:04:10,148 - mcp.client.sse - INFO - Received endpoint URL: http://***********:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 - [system]
2025-07-09 07:04:10,149 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://***********:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 - [system]
2025-07-09 07:04:10,153 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,155 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:04:10,158 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:04:10,159 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:04:10,162 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,165 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,168 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
1
INFO:     **********:38110 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
2025-07-09 07:15:03,728 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://***********:9082/sse', 'toolArgs': {'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:15:03,728 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:15:03,729 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://***********:9082/sse - [system]
2025-07-09 07:15:03,756 - httpx - INFO - HTTP Request: GET http://***********:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:15:03,757 - mcp.client.sse - INFO - Received endpoint URL: http://***********:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 - [system]
2025-07-09 07:15:03,757 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://***********:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 - [system]
2025-07-09 07:15:03,763 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,765 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:15:03,769 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:15:03,770 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:15:03,775 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,780 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,782 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
1
INFO:     **********:40694 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
2025-07-09 07:15:04,100 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://***********:9082/sse', 'toolArgs': {'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:15:04,101 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:15:04,101 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://***********:9082/sse - [system]
2025-07-09 07:15:04,127 - httpx - INFO - HTTP Request: GET http://***********:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:15:04,128 - mcp.client.sse - INFO - Received endpoint URL: http://***********:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e - [system]
2025-07-09 07:15:04,129 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://***********:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e - [system]
2025-07-09 07:15:04,134 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,136 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:15:04,140 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:15:04,141 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:15:04,146 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,150 - httpx - INFO - HTTP Request: POST http://***********:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,152 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
1
INFO:     **********:40694 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
