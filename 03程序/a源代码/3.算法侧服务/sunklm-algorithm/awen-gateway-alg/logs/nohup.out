2025-07-11 01:52:59,817 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-11 01:53:00,578 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
INFO:     Started server process [3572436]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9080 (Press CTRL+C to quit)
2025-07-11 02:01:48,006 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/d9680d6b-f24a-4ee8-bc7d-5d47bebcc6ac/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://172.1.0.134:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-11 02:01:48,008 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-0_0 - [system]
Error occurred: There is no current event loop in thread 'ThreadPoolExecutor-0_0'.
INFO:     172.1.0.88:43090 - "POST /ai/gateway/callMcpTool HTTP/1.1" 200 OK
2025-07-11 02:14:37,373 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 2048, 'outputMaxToken': 2048, 'multiType': 'text'} - [system]
2025-07-11 02:14:37,374 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
2025-07-11 02:14:37,374 - app - INFO - 【Service】【Model-流式-调用】inputs:,history:[] - [system]
INFO:     172.1.0.88:45130 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 263, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 74, in app
    |     await response(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 262, in __call__
    |     with collapse_excgroups():
    |   File "/home/<USER>/envs/mcp/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/asyncio/tasks.py", line 277, in __step
    |     result = coro.send(None)
    |              ^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 266, in wrap
    |     await func()
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 246, in stream_response
    |     async for chunk in self.body_iterator:
    |   File "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/service/ModelService.py", line 386, in generate_data
    |     prompt_token = self.get_token_len(str(inputs)) if isinstance(inputs, str) else sum(
    |                                                                  ^^^^^^
    | UnboundLocalError: cannot access local variable 'inputs' where it is not associated with a value
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 74, in app
    await response(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 262, in __call__
    with collapse_excgroups():
  File "/home/<USER>/envs/mcp/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/asyncio/tasks.py", line 277, in __step
    result = coro.send(None)
             ^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 266, in wrap
    await func()
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 246, in stream_response
    async for chunk in self.body_iterator:
  File "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/service/ModelService.py", line 386, in generate_data
    prompt_token = self.get_token_len(str(inputs)) if isinstance(inputs, str) else sum(
                                                                 ^^^^^^
UnboundLocalError: cannot access local variable 'inputs' where it is not associated with a value
2025-07-11 02:15:24,907 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 2048, 'outputMaxToken': 2048, 'multiType': 'text'} - [system]
2025-07-11 02:15:24,908 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
2025-07-11 02:15:24,908 - app - INFO - 【Service】【Model-流式-调用】inputs:,history:[] - [system]
INFO:     172.1.0.88:45254 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 263, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 74, in app
    |     await response(scope, receive, send)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 262, in __call__
    |     with collapse_excgroups():
    |   File "/home/<USER>/envs/mcp/lib/python3.11/contextlib.py", line 158, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/envs/mcp/lib/python3.11/asyncio/tasks.py", line 277, in __step
    |     result = coro.send(None)
    |              ^^^^^^^^^^^^^^^
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 266, in wrap
    |     await func()
    |   File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 246, in stream_response
    |     async for chunk in self.body_iterator:
    |   File "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/service/ModelService.py", line 386, in generate_data
    |     prompt_token = self.get_token_len(str(inputs)) if isinstance(inputs, str) else sum(
    |                                                                  ^^^^^^
    | UnboundLocalError: cannot access local variable 'inputs' where it is not associated with a value
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/routing.py", line 74, in app
    await response(scope, receive, send)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 262, in __call__
    with collapse_excgroups():
  File "/home/<USER>/envs/mcp/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/home/<USER>/envs/mcp/lib/python3.11/asyncio/tasks.py", line 277, in __step
    result = coro.send(None)
             ^^^^^^^^^^^^^^^
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 266, in wrap
    await func()
  File "/home/<USER>/envs/mcp/lib/python3.11/site-packages/starlette/responses.py", line 246, in stream_response
    async for chunk in self.body_iterator:
  File "/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/gateway/service/ModelService.py", line 386, in generate_data
    prompt_token = self.get_token_len(str(inputs)) if isinstance(inputs, str) else sum(
                                                                 ^^^^^^
UnboundLocalError: cannot access local variable 'inputs' where it is not associated with a value
2025-07-11 02:27:09,035 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 02:27:09,036 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:47126 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 02:38:59,374 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 02:38:59,375 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:49178 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 02:40:08,342 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 02:40:08,342 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:49354 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 02:41:17,136 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 02:41:17,143 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:49536 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:18:42,914 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:18:42,916 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:55916 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:19:48,845 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:19:48,845 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:56090 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:24:21,733 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:24:21,733 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:56824 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:25:32,461 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:25:32,462 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:57022 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:28:12,209 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:28:12,210 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:57442 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:30:16,138 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:30:16,139 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:57772 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-11 06:31:48,212 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://172.1.0.134:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText'} - [system]
2025-07-11 06:31:48,213 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-0_0 - [system]
INFO:     172.1.0.88:58014 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
