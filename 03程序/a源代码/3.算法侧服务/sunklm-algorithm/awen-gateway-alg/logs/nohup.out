2025-07-08 10:05:00,569 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 10:05:01,153 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
INFO:     Started server process [1713211]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:9080 (Press CTRL+C to quit)
2025-07-08 10:52:03,556 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:52:03,558 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:53037 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 10:54:59,380 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:54:59,381 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,382 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 10:54:59,384 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:54330 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 10:54:59,385 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
INFO:     ************:54329 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:47,545 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:47,546 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     172.1.0.88:45550 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     172.1.0.88:45550 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:31:56,404 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:56,405 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     172.1.0.88:45588 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:32:11,712 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:32:11,713 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,714 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
INFO:     172.1.0.88:45652 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     172.1.0.88:46212 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:35:11,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/6843d4ea-a6b5-48cf-a473-a0f1c10a383e/ceff59a3-6f96-4ab3-b1f9-97cc14660b0f.png', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-08 11:35:11,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     172.1.0.88:46356 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://***********:8001/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,108 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
INFO:     172.1.0.88:47478 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:14:29,406 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:14:29,407 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:63571 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:63872 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:16:07,455 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-09 01:16:07,456 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:64387 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:16:44,387 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:16:44,388 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:64689 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:17:09,169 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:17:09,173 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:17:09,185 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:17:09,185 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:64845 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:23,800 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:23,800 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:49504 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:19:53,883 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:53,885 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:53,896 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:53,897 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:49746 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:21:55,390 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:21:55,392 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:21:55,406 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:21:55,407 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:50721 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'awen', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'awen', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:22:22,397 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: awen'} - [system]
2025-07-09 01:22:22,397 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
INFO:     ************:50943 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:23:18,855 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:23:18,857 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:23:20,396 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat4a217018-5c63-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752024197, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '我是Qwen，由阿里云开发的超大规模语言模型。我被设计用于生成各种文本，如文章、故事、诗歌、故事等，并能根据不同的场景和需求进行对话、提供信息查询、创作等任务。很高兴为您服务！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 31, 'completion_tokens': 54, 'total_tokens': 85}} - [system]
INFO:     ************:51426 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44058 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:34:07,656 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {}} - [system]
2025-07-09 01:34:07,657 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:56580 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:34:59,358 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '图片内容', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:34:59,359 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:56939 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:35:07,729 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:35:07,730 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ************:57002 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:36:23,789 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:36:23,790 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44060 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:56:40,208 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:56:40,209 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44062 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 01:59:43,049 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:59:43,050 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44064 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:44066 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:59120 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:26:05,899 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:26:05,900 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:59941 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:31:10,480 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:10,481 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:60820 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:31:31,041 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:31,042 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:60884 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
2025-07-09 02:34:08,331 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n# Role: MCP 控制智能体调度器\n\n## Profile\n- language: 中文\n- description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n\n## Skills\n- 理解用户的高层任务目标并进行任务分解\n- 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n- 协调多个子智能体的任务顺序和依赖关系\n- 实时反馈任务进度并汇总结果输出\n\n## OutputFormat(可选项):\n输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n\n## Rules\n- 所有任务必须以结构化形式注册至 MCP 平台\n- 子任务的定义必须明确，可度量\n- 避免资源冲突和任务重复分配\n- 汇总输出需具备可读性与可验证性\n\n## Workflows\n1. 读取用户输入的任务目标\n2. 拆分为若干子任务，定义每个任务所需能力\n3. 为每个子任务选择合适的子智能体并注册至 MCP\n4. 监控任务状态并在任务完成后收集结果\n5. 汇总结果并以所需格式输出\n\n## Init\n你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n\n## 用户问题\n测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:34:08,332 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
INFO:     ***********:61366 - "POST /ai/gateway/chatModel HTTP/1.1" 200 OK
