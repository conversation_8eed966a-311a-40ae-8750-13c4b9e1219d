2025-07-02 02:08:57,165 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f9cca582410> - [system]
2025-07-02 02:08:57,167 - app - INFO - 【Service】【意图识别】输出:{"intentId": 1} - [system]
2025-07-02 02:08:57,168 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f9c2517ba50> - [system]
2025-07-02 02:08:57,169 - app - INFO - 【Service】【意图识别】输出:{"intentId": 2} - [system]
2025-07-02 02:08:57,169 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f9c2517ba50> - [system]
2025-07-02 02:08:57,170 - app - INFO - 【Service】【意图识别】输出:{"intentId": 1} - [system]
2025-07-02 02:08:57,171 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f9c2517ba50> - [system]
2025-07-02 02:08:57,171 - app - INFO - 【Service】【意图识别】输出:{"intentId": 1} - [system]
2025-07-02 02:09:14,998 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f99141e6650> - [system]
2025-07-02 02:09:15,000 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:09:15,000 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:09:15,000 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:09:15,000 - app - INFO - using normal format - [system]
2025-07-02 02:09:15,000 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:09:15,000 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:09:15,012 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:09:15,012 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f99141e6650> - [system]
2025-07-02 02:09:15,013 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:09:15,013 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:09:15,013 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:09:15,013 - app - INFO - using normal format - [system]
2025-07-02 02:09:15,013 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:09:15,014 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:09:15,020 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:09:15,021 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f986e1b2c90> - [system]
2025-07-02 02:09:15,022 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:09:15,022 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 帮我写一个Python排序算法
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:09:15,022 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:09:15,022 - app - INFO - using normal format - [system]
2025-07-02 02:09:15,022 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:09:15,022 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 帮我写一个Python排序算法\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:09:15,029 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:09:15,029 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f986e1b2c90> - [system]
2025-07-02 02:09:15,030 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:09:15,030 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 设计一个logo
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:09:15,030 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:09:15,030 - app - INFO - using normal format - [system]
2025-07-02 02:09:15,030 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:09:15,031 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 设计一个logo\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:09:15,036 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:09:15,037 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f986e1b2c90> - [system]
2025-07-02 02:09:15,038 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:09:15,038 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 今天天气怎么样
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:09:15,038 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:09:15,038 - app - INFO - using normal format - [system]
2025-07-02 02:09:15,038 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:09:15,038 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 今天天气怎么样\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:09:15,044 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:11:45,672 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f35f32393d0> - [system]
2025-07-02 02:11:45,674 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:11:45,674 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:11:45,674 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:11:45,674 - app - INFO - using normal format - [system]
2025-07-02 02:11:45,675 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:11:45,675 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:11:45,688 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:11:45,689 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f35f32393d0> - [system]
2025-07-02 02:11:45,690 - app - INFO - 【Service】【创建用户模型】模型类型:qwen2.5-72b-instruct-gptq-int4, API地址:http://***********:9020/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:11:45,690 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:11:45,690 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:11:45,690 - app - INFO - using normal format - [system]
2025-07-02 02:11:45,690 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:11:45,690 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:11:45,697 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-02 02:12:06,734 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7fb601e192d0> - [system]
2025-07-02 02:12:06,735 - app - INFO - 【Service】【意图识别】输出:{"intentId": 1} - [system]
2025-07-02 02:14:51,918 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f302ed5d610> - [system]
2025-07-02 02:14:51,919 - app - INFO - 【Service】【创建用户模型】模型类型:DeepSeek-R1-Distill-Llama-70B, API地址:http://***********:8001/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 02:14:51,919 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 02:14:51,919 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 02:14:51,920 - app - INFO - using normal format - [system]
2025-07-02 02:14:51,920 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 02:14:51,920 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 02:15:28,635 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '<think>\n好，我现在需要分析用户的输入，并将其映射到给定的意图列表中。首先，用户提到有一个想法，关于建造一座高耸巨大的建筑，将城市的功能集中，人们可以在楼里过完一生，并请求帮助绘制这个想法。意图列表中有多个选项，但这里没有提供具体的意图描述，所以我需要根据输入内容推测可能的意图。\n\n用户的主要需求是设计或绘制一个建筑概念，这涉及到建筑设计或城市规划的相关意图。因此，最可能的意图ID应该是与建筑设计或城市规划相关的。判断理由是用户明确提到了建筑设计的需求，并请求绘制帮助，这直接指向了设计相关的意图。\n\n如果意图列表中有一个ID对应“建筑设计”或“城市规划”，那么应该选择那个ID。假设意图列表中的ID为1对应“建筑设计”，那么输出应该是intentId为1，并给出相应的理由。\n\n总结来说，用户的需求是关于设计高耸建筑的，因此映射到与建筑设计相关的意图是合理的。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户提出了一个关于设计高耸建筑的想法，并请求帮助绘制，这表明用户的意图是请求建筑设计。"\n}\n```', 'tool_calls': None}, 'finish_reason': 'stop', 'logprobs': None}], 'usage': {'prompt_tokens': 134, 'completion_tokens': 316, 'total_tokens': 450}, 'id': '972f13c7f39a4fbb9b15732cd53e2c79', 'object': 'chat.completion', 'created': 1751422528} - [system]
2025-07-02 02:15:28,636 - app - INFO - 【Service】【意图识别】输出:{'answer': '<think>\n好，我现在需要分析用户的输入，并将其映射到给定的意图列表中。首先，用户提到有一个想法，关于建造一座高耸巨大的建筑，将城市的功能集中，人们可以在楼里过完一生，并请求帮助绘制这个想法。意图列表中有多个选项，但这里没有提供具体的意图描述，所以我需要根据输入内容推测可能的意图。\n\n用户的主要需求是设计或绘制一个建筑概念，这涉及到建筑设计或城市规划的相关意图。因此，最可能的意图ID应该是与建筑设计或城市规划相关的。判断理由是用户明确提到了建筑设计的需求，并请求绘制帮助，这直接指向了设计相关的意图。\n\n如果意图列表中有一个ID对应“建筑设计”或“城市规划”，那么应该选择那个ID。假设意图列表中的ID为1对应“建筑设计”，那么输出应该是intentId为1，并给出相应的理由。\n\n总结来说，用户的需求是关于设计高耸建筑的，因此映射到与建筑设计相关的意图是合理的。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户提出了一个关于设计高耸建筑的想法，并请求帮助绘制，这表明用户的意图是请求建筑设计。"\n}\n```', 'toolCalls': None, 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'promptToken': 134, 'completionTokens': 316, 'totalTokens': 450} - [system]
2025-07-02 03:21:56,907 - app - INFO - 【Service】【意图识别】输入:<__main__.MockRequest object at 0x7f86a2fd1390> - [system]
2025-07-02 03:21:56,909 - app - INFO - 【Service】【创建用户模型】模型类型:DeepSeek-R1-Distill-Llama-70B, API地址:http://***********:8001/v1/chat/completions, 部署模式:0 - [system]
2025-07-02 03:21:56,909 - app - INFO - 【Service】【Model-非流式-调用】inputs:Human: 
    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。
    "意图列表": None
    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗
    "输出格式": {
        "intentId": 数字,
        "reason": "判断理由"
    }

    ,history:[] - [system]
2025-07-02 03:21:56,909 - app - INFO - 开始根据输出格式改变prompt - [system]
2025-07-02 03:21:56,909 - app - INFO - using normal format - [system]
2025-07-02 03:21:56,910 - app - INFO - 【Service】【Model-非流式-模型调用-PRIVATE方式】 - [system]
2025-07-02 03:21:56,910 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'messages': [{'role': 'user', 'content': 'Human: \n    你是一个NLP专家，请根据以下多个意图的描述，确保将<输入>映射到其中一个意图上。请以json格式输出，输出包含一个ID表示意图的编号，以及做出判断的理由。\n    "意图列表": None\n    "输入": 我有一个想法，一座高耸巨大的建筑将城市的功能全都集中，人们可以在楼中过完一生，可以帮我画出来吗\n    "输出格式": {\n        "intentId": 数字,\n        "reason": "判断理由"\n    }\n\n    '}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-02 03:22:08,882 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '<think>\n好，我现在需要分析用户的输入，并将其映射到给定的意图列表中。首先，用户提到有一个想法，是关于建造一座高耸巨大的建筑，将城市的功能集中在一起，人们可以在楼里过完一生，并请求帮助绘制这个想法。意图列表中有多个选项，但这里没有提供具体的意图描述，所以我需要根据输入内容推测可能的意图。\n\n用户的主要意图似乎是请求帮助绘制或设计一个建筑概念。因此，最合适的意图应该是与设计或绘图相关的。假设意图列表中有一个ID为1，描述为“请求设计或绘图帮助”，那么这个输入应该映射到ID 1。判断的理由是用户明确提出了设计请求，并希望得到帮助来绘制他们的建筑概念。\n\n如果意图列表中的ID 1确实对应“请求设计或绘图帮助”，那么输出应该是{"intentId": 1, "reason": "用户请求帮助绘制高耸建筑的设计"}。如果没有这个意图，可能需要选择最接近的意图，但根据当前信息，ID 1是合适的。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户请求帮助绘制高耸建筑的设计"\n}\n```', 'tool_calls': None}, 'finish_reason': 'stop', 'logprobs': None}], 'usage': {'prompt_tokens': 134, 'completion_tokens': 310, 'total_tokens': 444}, 'id': '0f5d319cb2314e43937a4d96842625f7', 'object': 'chat.completion', 'created': 1751426528} - [system]
2025-07-02 03:22:08,883 - app - INFO - 【Service】【意图识别】输出:{'answer': '<think>\n好，我现在需要分析用户的输入，并将其映射到给定的意图列表中。首先，用户提到有一个想法，是关于建造一座高耸巨大的建筑，将城市的功能集中在一起，人们可以在楼里过完一生，并请求帮助绘制这个想法。意图列表中有多个选项，但这里没有提供具体的意图描述，所以我需要根据输入内容推测可能的意图。\n\n用户的主要意图似乎是请求帮助绘制或设计一个建筑概念。因此，最合适的意图应该是与设计或绘图相关的。假设意图列表中有一个ID为1，描述为“请求设计或绘图帮助”，那么这个输入应该映射到ID 1。判断的理由是用户明确提出了设计请求，并希望得到帮助来绘制他们的建筑概念。\n\n如果意图列表中的ID 1确实对应“请求设计或绘图帮助”，那么输出应该是{"intentId": 1, "reason": "用户请求帮助绘制高耸建筑的设计"}。如果没有这个意图，可能需要选择最接近的意图，但根据当前信息，ID 1是合适的。\n</think>\n\n```json\n{\n    "intentId": 1,\n    "reason": "用户请求帮助绘制高耸建筑的设计"\n}\n```', 'toolCalls': None, 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'promptToken': 134, 'completionTokens': 310, 'totalTokens': 444} - [system]
2025-07-02 03:22:40,905 - app - INFO - Received request: - [system]
2025-07-02 03:22:40,905 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a second dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:45:04,383 - app - INFO - Received request: - [system]
2025-07-02 05:45:04,383 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a second dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:45:52,224 - app - INFO - Received request: - [system]
2025-07-02 05:45:52,224 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a second dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:46:15,077 - app - INFO - Received request: - [system]
2025-07-02 05:46:15,077 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a second dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:46:15,077 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://***********:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "add a second dog", "n": 1, "size": "1024x1024", "response_format": "url", "guidance_scale": 8.5, "num_inference_steps": 40, "negative_prompt": "blurry, low quality, ugly", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-02 05:46:24,020 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751435184, 'data': [{'url': '/data/x_inference/xinf_models/image/9b7893bc49b94d56b480f434a73c67b0.jpg', 'b64_json': None}]} - [system]
2025-07-02 05:46:24,021 - app - INFO - 生成结果: {'image_result': {'created': 1751435184, 'data': [{'url': '/data/x_inference/xinf_models/image/9b7893bc49b94d56b480f434a73c67b0.jpg', 'b64_json': None}]}} - [system]
2025-07-02 05:46:24,021 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-02 05:46:24,026 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/9b7893bc49b94d56b480f434a73c67b0.jpg - [system]
2025-07-02 05:46:24,062 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/9b7893bc49b94d56b480f434a73c67b0.jpg - [system]
2025-07-02 05:46:24,091 - app - INFO - 第 1 张图片已上传到 MinIO: ***********:9000/sunklm-dev/generated-images/9b7893bc49b94d56b480f434a73c67b0.jpg - [system]
2025-07-02 05:47:50,658 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 05:47:50,658 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'params': {'test': 'value'}, 'timeout': 10} - [system]
2025-07-02 05:47:53,385 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get?test=value (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-02 05:47:53,386 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/post', 'method': 'POST', 'data': {'name': '测试', 'value': 123}, 'timeout': 10} - [system]
2025-07-02 05:47:56,039 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /post (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-02 05:47:56,040 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 05:47:56,040 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9080/health', 'method': 'GET', 'timeout': 3} - [system]
2025-07-02 05:47:56,049 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 404 - [system]
2025-07-02 05:47:56,050 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9081/health', 'method': 'GET', 'timeout': 3} - [system]
2025-07-02 05:47:56,062 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 404 - [system]
2025-07-02 05:47:56,063 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9082/health', 'method': 'GET', 'timeout': 3} - [system]
2025-07-02 05:47:56,076 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 404 - [system]
2025-07-02 05:47:56,077 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:8014/health', 'method': 'GET', 'timeout': 3} - [system]
2025-07-02 05:47:56,080 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2e7fbfdfd0>: Failed to establish a new connection: [Errno 111] Connection refused')': /health - [system]
2025-07-02 05:47:56,681 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2e7fbfe8d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /health - [system]
2025-07-02 05:47:57,883 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2e7fd04a90>: Failed to establish a new connection: [Errno 111] Connection refused')': /health - [system]
2025-07-02 05:47:57,885 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='localhost', port=8014): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f2e7fc00c10>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-02 05:47:57,885 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 05:47:59,263 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'timeout': 10} - [system]
2025-07-02 05:48:01,949 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-02 05:48:01,950 - app - INFO - 【Service】【HTTP请求】输入: {'url': '', 'method': 'GET'} - [system]
2025-07-02 05:48:01,950 - app - ERROR - 【Service】【HTTP请求】URL参数不能为空 - [system]
2025-07-02 05:48:01,950 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'INVALID_METHOD'} - [system]
2025-07-02 05:48:01,950 - app - ERROR - 【Service】【HTTP请求】异常: 不支持的HTTP方法: INVALID_METHOD - [system]
2025-07-02 05:48:01,950 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://nonexistent-domain-12345.com', 'method': 'GET', 'timeout': 3} - [system]
2025-07-02 05:48:02,976 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f2e7d06f710>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-02 05:48:03,580 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f2e7d06fe90>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-02 05:48:04,784 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f2e7d07cd10>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")': / - [system]
2025-07-02 05:48:04,788 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='nonexistent-domain-12345.com', port=80): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x7f2e7d07d610>: Failed to resolve 'nonexistent-domain-12345.com' ([Errno -2] Name or service not known)")) - [system]
2025-07-02 05:48:04,788 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 05:48:04,789 - app - INFO - 【Service】【HTTP批量请求】开始处理2个请求 - [system]
2025-07-02 05:48:04,789 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/get', 'method': 'GET', 'timeout': 10} - [system]
2025-07-02 05:48:07,490 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /get (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-02 05:48:07,491 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://httpbin.org/post', 'method': 'POST', 'data': {'test': 'data'}, 'timeout': 10} - [system]
2025-07-02 05:48:10,141 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='httpbin.org', port=80): Max retries exceeded with url: /post (Caused by ResponseError('too many 503 error responses')) - [system]
2025-07-02 05:48:10,142 - app - INFO - 【Service】【HTTP批量请求】完成，成功: 0, 失败: 2 - [system]
2025-07-02 05:48:10,142 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 05:48:10,165 - app - INFO - Received request: - [system]
2025-07-02 05:48:10,165 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a third dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:48:20,845 - app - INFO - Received request: - [system]
2025-07-02 05:48:20,845 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a third dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:48:41,960 - app - INFO - Received request: - [system]
2025-07-02 05:48:41,960 - app - INFO - 【Service】【ImageModel 调用】Prompt: add a third dog, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-02 05:48:41,961 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://***********:9020/v1/images/variations，数据: {'model': 'stable-diffusion-xl-base-1.0', 'prompt': 'add a third dog', 'n': '1', 'size': '1024x1024', 'response_format': 'url', 'guidance_scale': '8.5', 'num_inference_steps': '40', 'negative_prompt': 'blurry, low quality, ugly', 'sampler_name': 'DPM++ 2M Karras'} - [system]
2025-07-02 05:48:45,456 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751435325, 'data': [{'url': '/data/x_inference/xinf_models/image/eed1108cd6aa4d1aa69dc33794fba646.jpg', 'b64_json': None}]} - [system]
2025-07-02 05:48:45,457 - app - INFO - 生成结果: {'image_result': {'created': 1751435325, 'data': [{'url': '/data/x_inference/xinf_models/image/eed1108cd6aa4d1aa69dc33794fba646.jpg', 'b64_json': None}]}} - [system]
2025-07-02 05:48:45,457 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-02 05:48:45,463 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/eed1108cd6aa4d1aa69dc33794fba646.jpg - [system]
2025-07-02 05:48:45,516 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/eed1108cd6aa4d1aa69dc33794fba646.jpg - [system]
2025-07-02 05:48:45,542 - app - INFO - 第 1 张图片已上传到 MinIO: ***********:9000/sunklm-dev/generated-images/eed1108cd6aa4d1aa69dc33794fba646.jpg - [system]
2025-07-02 06:00:46,751 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 06:00:46,751 - app - INFO - 【Service】【HTTP请求】输入: {'url': '', 'method': 'GET'} - [system]
2025-07-02 06:00:46,751 - app - ERROR - 【Service】【HTTP请求】URL参数不能为空 - [system]
2025-07-02 06:00:46,752 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 06:00:46,752 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9081/agent/chatAgent', 'method': 'POST', 'data': {'query': '你好，请介绍一下你的功能', 'stream': False, 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'history': []}} - [system]
2025-07-02 06:00:46,759 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-02 06:00:46,759 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9082/mcp/tool/call', 'method': 'POST', 'data': {'toolName': 'code_executor', 'parameters': {'code': "print('Hello from MCP!')", 'language': 'python'}}} - [system]
2025-07-02 06:00:46,763 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 404 - [system]
2025-07-02 06:00:46,763 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:8014/RAG-alg/text_split', 'method': 'POST', 'data': {'text': '这是一段需要切分的长文本。它包含多个句子和段落。', 'chunk_size': 100, 'overlap': 20}} - [system]
2025-07-02 06:00:46,764 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f24f59046d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 06:00:47,365 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f24f59052d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 06:00:48,566 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f259a053d10>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 06:00:48,567 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='localhost', port=8014): Max retries exceeded with url: /RAG-alg/text_split (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f24f58f83d0>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-02 06:00:48,568 - app - INFO - 【Service】【HTTP文件下载】开始下载: https://www.baidu.com -> /tmp/baidu_homepage.html - [system]
2025-07-02 06:00:48,581 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 06:00:49,197 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 06:00:50,410 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 06:00:50,426 - app - ERROR - 【Service】【HTTP文件下载】文件下载失败 - [system]
2025-07-02 09:10:25,254 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 09:10:25,255 - app - INFO - 【Service】【HTTP请求】输入: {'url': '', 'method': 'GET'} - [system]
2025-07-02 09:10:25,255 - app - ERROR - 【Service】【HTTP请求】URL参数不能为空 - [system]
2025-07-02 09:10:25,255 - app - INFO - 【Service】【HTTP请求】客户端已关闭 - [system]
2025-07-02 09:10:25,255 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9081/agent/chatAgent', 'method': 'POST', 'data': {'query': '你好，请介绍一下你的功能', 'stream': False, 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'history': []}} - [system]
2025-07-02 09:10:25,269 - app - INFO - 【Service】【HTTP请求】JSON响应成功 - [system]
2025-07-02 09:10:25,270 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:9082/mcp/tool/call', 'method': 'POST', 'data': {'toolName': 'code_executor', 'parameters': {'code': "print('Hello from MCP!')", 'language': 'python'}}} - [system]
2025-07-02 09:10:25,277 - app - WARNING - 【Service】【HTTP请求】HTTP请求失败，状态码: 404 - [system]
2025-07-02 09:10:25,277 - app - INFO - 【Service】【HTTP请求】输入: {'url': 'http://localhost:8014/RAG-alg/text_split', 'method': 'POST', 'data': {'text': '这是一段需要切分的长文本。它包含多个句子和段落。', 'chunk_size': 100, 'overlap': 20}} - [system]
2025-07-02 09:10:25,279 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1b02cc3250>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 09:10:25,880 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1b02cc2350>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 09:10:27,081 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1b02cd27d0>: Failed to establish a new connection: [Errno 111] Connection refused')': /RAG-alg/text_split - [system]
2025-07-02 09:10:27,083 - app - ERROR - 【Service】【HTTP请求】异常: HTTPConnectionPool(host='localhost', port=8014): Max retries exceeded with url: /RAG-alg/text_split (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f1b02cd3d90>: Failed to establish a new connection: [Errno 111] Connection refused')) - [system]
2025-07-02 09:10:27,083 - app - INFO - 【Service】【HTTP文件下载】开始下载: https://www.baidu.com -> /tmp/baidu_homepage.html - [system]
2025-07-02 09:10:27,102 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 09:10:27,725 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 09:10:28,949 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(104, 'Connection reset by peer')': / - [system]
2025-07-02 09:10:28,963 - app - ERROR - 【Service】【HTTP文件下载】文件下载失败 - [system]
