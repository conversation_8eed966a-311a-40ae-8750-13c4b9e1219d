2025-07-08 02:06:09,329 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:06:09,329 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 02:06:12,295 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat1bed8a42-5ba0-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751940369, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 02:06:40,130 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:06:48,975 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:07:12,295 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:09:27,463 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:36:04,314 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:36:04,314 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:36:04,322 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:39:19,738 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:39:19,738 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:39:22,765 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat455ac25c-5bc6-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751956760, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:39:57,762 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:39:57,762 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:39:57,769 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:40:22,334 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:40:22,334 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:40:22,340 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:40:59,225 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:40:59,226 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:41:02,228 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat80a7b342-5bc6-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751956859, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:41:43,086 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:41:43,086 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:41:43,096 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:42:03,913 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:42:03,913 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:42:03,922 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:43:11,206 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:43:11,206 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:43:11,214 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:43:32,175 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:43:32,175 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:43:32,184 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:44:20,898 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:20,898 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '0.01', 'top_p': '1', 'top_k': '0.1'} - [system]
2025-07-08 06:44:20,906 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:44:41,558 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:41,558 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:44:41,586 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:44:41,586 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:44:59,090 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:59,090 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:44:59,118 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:44:59,118 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:45:10,539 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:45:10,539 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:45:10,556 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:45:10,556 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:45:36,432 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:45:36,432 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:45:36,452 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:45:36,452 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:46:14,723 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:46:14,723 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:46:14,743 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:46:14,743 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:46:54,123 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:46:54,123 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:46:57,231 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat5430bd80-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957214, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:47:56,322 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:47:56,322 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:47:56,340 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:47:56,340 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:48:51,066 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:48:51,066 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:48:51,088 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:48:51,089 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:49:38,869 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:49:38,870 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:49:39,367 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb662e4d8-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957376, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:49:55,589 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:49:55,589 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:49:56,079 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatc05a23e8-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957393, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:50:06,962 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:50:06,962 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:50:07,454 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatc721af34-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957405, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:50:30,888 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:06,257 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:06,258 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:06,276 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-08 06:51:06,276 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:51:15,093 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:15,093 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:15,107 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-08 06:51:15,107 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:51:26,097 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:26,097 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:26,580 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf64c5c32-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957484, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:51:32,433 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:32,433 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:32,923 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatfa136568-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957490, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:51:50,531 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:50,532 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:51,029 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat04dd300a-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957508, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:52:11,426 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:52:11,426 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:52:11,913 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat115134f8-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957529, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:52:46,867 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:52:47,470 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:53:22,471 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:53:22,471 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:53:22,923 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3ba9655e-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957600, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:53:32,434 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:53:33,018 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:58:42,344 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:58:42,927 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:58:47,935 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:58:47,936 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 06:58:49,845 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:58:49,845 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 06:59:50,223 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:59:50,826 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:59:56,002 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:59:56,003 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:02:17,634 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 07:02:18,079 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 07:02:21,593 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:02:21,594 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:02:24,625 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat7d00d072-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958142, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:02:58,271 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:02:58,271 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int41', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:02:58,282 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: Qwen2.5-72B-Instruct-GPTQ-Int41'} - [system]
2025-07-08 07:02:58,282 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 07:03:28,224 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:03:41,553 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:03:41,554 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:03:44,842 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chataca9ece6-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958222, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:04:08,888 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 07:04:08,889 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:04:22,537 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatbcf4dac0-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958260, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:06:10,802 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:06:10,803 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:10:29,959 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': 'nihao', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 07:10:29,959 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:10:46,317 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/b0866f9a-100c-4c1f-8419-416b68d94442/dbd5bb78-f1f6-49e5-af8a-8dac0bb81e2f.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 07:10:46,318 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:08,232 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:12:08,235 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:14,865 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:12:14,865 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:17,355 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 07:12:17,355 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:13:04,715 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:13:04,716 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:13:46,172 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:13:46,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:16:45,923 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:16:45,924 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:22:49,086 - app - INFO - 【图生文】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:22:49,087 - app - ERROR - 【LLM统一入口】异常: 不支持的URL类型:  - [system]
2025-07-08 07:23:05,737 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'multiType': 'text', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:23:05,739 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:23:05,739 - app - INFO - 【Service】【Model-流式-调用】inputs:122,history:[] - [system]
2025-07-08 07:23:05,751 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '122'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 30000, 'stream': True} - [system]
2025-07-08 07:23:16,814 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text - [system]
2025-07-08 07:23:42,235 - app - INFO - 【非流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:23:42,235 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8071} - [system]
2025-07-08 07:23:50,421 - app - INFO - 【私有化API响应】输出: {'id': 'chat78533f8a-5bcc-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959428, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:24:02,526 - app - INFO - 【流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:24:56,035 - app - ERROR - 【LLM统一入口】异常: name 'DeployMode' is not defined - [system]
2025-07-08 07:26:13,035 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text_generate - [system]
2025-07-08 07:26:46,847 - app - INFO - 【流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:27:35,911 - app - INFO - 【图生文流式】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:27:53,083 - app - INFO - 【图生文】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:27:53,083 - app - ERROR - 【LLM统一入口】异常: 不支持的URL类型:  - [system]
2025-07-08 07:31:55,392 - app - INFO - 【图生文流式】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:32:07,211 - app - INFO - 【非流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:32:07,211 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8071} - [system]
2025-07-08 07:32:16,081 - app - INFO - 【私有化API响应】输出: {'id': 'chata550310e-5bcd-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959933, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:32:16,081 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:32:42,726 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:32:42,726 - app - INFO - 【私有化API请求】输入: {'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:32:43,196 - app - INFO - 【私有化API响应】输出: {'id': 'chatba7b9280-5bcd-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959960, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 07:32:43,197 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:33:03,880 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:33:03,880 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:33:07,311 - app - INFO - 【私有化API响应】输出: {'id': 'chatc71766b8-5bcd-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959984, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:33:07,312 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:33:36,352 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:33:36,352 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:33:49,703 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2570079232', 'modelType': '123', 'baseUrl': '123', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 44, 'outputMaxToken': 444, 'param': {}} - [system]
2025-07-08 07:33:49,704 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:34:04,165 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 07:34:04,166 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:34:48,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'text', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 07:34:48,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:35:29,025 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:35:29,025 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:35:29,026 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:35:29,027 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:37:03,612 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:37:03,613 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:37:45,012 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:37:45,013 - app - ERROR - 【Service】【Model-推理】异常:name 'CompanyCode' is not defined - [system]
2025-07-08 07:38:53,737 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:38:53,738 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:38:53,739 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:38:53,740 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:40:31,050 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:40:31,051 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:46:47,705 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:46:47,706 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:46:52,316 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb220b0aa-5bcf-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751960809, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:47:01,334 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:48:45,763 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text - [system]
2025-07-08 07:49:28,806 - app - INFO - 【流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:49:40,574 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:49:40,574 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:49:44,066 - app - INFO - 【私有化API响应】输出: {'id': 'chat192a768c-5bd0-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751960981, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:50:40,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:50:40,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:51:24,228 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:51:24,229 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:51:24,230 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:51:24,231 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:52:54,651 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:52:54,652 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:52:57,850 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat8cd89fc8-5bd0-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751961175, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:08:39,280 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:08:39,280 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:08:42,501 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatbfe2dc74-5bd2-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962120, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:09:51,547 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:09:51,547 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:09:54,777 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chateaf53a4c-5bd2-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962192, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:14:18,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:14:18,884 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:14:22,673 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat8a4eab78-5bd3-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962460, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:14:26,184 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:15:40,981 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:15:40,982 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:16:22,646 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:16:22,646 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:17:08,547 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:17:08,548 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:17:53,104 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:04,180 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:04,180 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:19:17,328 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:17,329 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:20:17,703 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:17,710 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:20:38,533 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:38,533 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:20:41,844 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6c97d0a4-5bd4-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962839, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:20:49,757 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:49,761 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:22:09,917 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 08:22:10,504 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 08:26:14,811 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:26:14,815 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:26:25,488 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:26:25,489 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:26:28,664 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3b6525bc-5bd5-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751963186, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:26:37,479 - app - INFO - 【Service】【Model-非流式-调用】inputs:,history:[] - [system]
2025-07-08 08:26:37,479 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': ''}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:26:38,464 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat428a91d8-5bd5-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751963196, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': 'Hello! It seems like your message was cut off. How can I assist you today? Feel free to ask me any questions or let me know if you need help with anything specific.'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 29, 'completion_tokens': 38, 'total_tokens': 67}} - [system]
2025-07-08 08:30:53,149 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 08:30:53,151 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 08:52:13,045 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 08:52:13,046 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:52:14,037 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatd5ceb49e-5bd8-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751964731, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 08:56:27,510 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '11', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 08:56:27,511 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:00:53,139 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:00:53,139 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:00:54,162 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0bce7574-5bda-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965251, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 09:08:09,764 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:08:09,765 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:08:10,760 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat100efc70-5bdb-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965688, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 09:09:10,787 - app - INFO - 【Service】【Model-流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:09:10,886 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:10:11,172 - app - INFO - 【Service】【Model-流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:10:11,412 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:11:07,805 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:07,805 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:11:08,305 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat7a2ddce8-5bdb-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965865, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:11:14,270 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:14,359 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:11:58,128 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:58,132 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:12:36,534 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:12:36,537 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:13:00,842 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:00,846 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:13:32,031 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:32,031 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:13:32,447 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatd023df8a-5bdb-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751966010, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:13:41,780 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:41,784 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:14:23,630 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:14:23,633 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:14:55,757 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:16:50,380 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:17:09,225 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:17:09,791 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:17:30,222 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:17:30,806 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:18:02,533 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:18:14,142 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:18:14,142 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:18:14,626 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat784b6098-5bdc-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751966292, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:18:33,884 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:18:34,475 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:19:23,856 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:19:24,452 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:25:31,763 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你好', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 09:25:31,764 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:26:05,591 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977265254510841', 'baseUrl': 'http://172.1.0.154:8003/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 131072, 'outputMaxToken': 131072, 'stream': False} - [system]
2025-07-08 09:26:05,592 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:26:19,394 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你好', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 09:26:19,395 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:43:38,316 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:43:38,896 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:44:16,185 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:44:16,757 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:46:43,257 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:46:43,843 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 10:01:11,394 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:01:11,394 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:01:17,323 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751968877, 'data': [{'url': '/data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:01:17,323 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:01:17,329 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,373 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,392 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,393 - app - INFO - 生成结果: {'image_result': {'created': 1751968877, 'data': [{'url': '/data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:03:34,034 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:03:34,035 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:03:40,107 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751969020, 'data': [{'url': '/data/x_inference/xinf_models/image/372d16a793cd42ac92fabf29572ff1ec.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:03:40,107 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:03:40,113 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,156 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,183 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,184 - app - INFO - 生成结果: {'image_result': {'created': 1751969020, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/372d16a793cd42ac92fabf29572ff1ec.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:04:35,160 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:04:35,160 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:04:40,887 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751969080, 'data': [{'url': '/data/x_inference/xinf_models/image/84052824d6db46e8aac9dd0ae5c5cf96.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:04:40,888 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:04:40,896 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,939 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,984 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,984 - app - INFO - 生成结果: {'image_result': {'created': 1751969080, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/84052824d6db46e8aac9dd0ae5c5cf96.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:05:00,569 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 10:05:01,153 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 10:52:03,556 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:52:03,558 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,380 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:54:59,381 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,382 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 10:54:59,384 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,385 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 11:31:47,545 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:47,546 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:31:56,404 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:56,405 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,712 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:32:11,713 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,714 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:35:11,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/6843d4ea-a6b5-48cf-a473-a0f1c10a383e/ceff59a3-6f96-4ab3-b1f9-97cc14660b0f.png', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-08 11:35:11,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://172.1.0.154:8001/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,108 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
2025-07-09 01:14:29,406 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:14:29,407 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:07,455 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-09 01:16:07,456 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:16:44,387 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:16:44,388 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:17:09,169 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:17:09,173 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:17:09,185 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:17:09,185 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:23,800 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:23,800 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:19:53,883 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:53,885 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:53,896 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:53,897 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:21:55,390 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:21:55,392 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:21:55,406 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:21:55,407 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'awen', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'awen', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:22:22,397 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: awen'} - [system]
2025-07-09 01:22:22,397 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:23:18,855 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:23:18,857 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:23:20,396 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat4a217018-5c63-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752024197, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '我是Qwen，由阿里云开发的超大规模语言模型。我被设计用于生成各种文本，如文章、故事、诗歌、故事等，并能根据不同的场景和需求进行对话、提供信息查询、创作等任务。很高兴为您服务！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 31, 'completion_tokens': 54, 'total_tokens': 85}} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:34:07,656 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {}} - [system]
2025-07-09 01:34:07,657 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:34:59,358 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '图片内容', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:34:59,359 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:35:07,729 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:35:07,730 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:36:23,789 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:36:23,790 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:56:40,208 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:56:40,209 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:59:43,049 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:59:43,050 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:26:05,899 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:26:05,900 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:31:10,480 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:10,481 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:31:31,041 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:31,042 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:34:08,331 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n# Role: MCP 控制智能体调度器\n\n## Profile\n- language: 中文\n- description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n\n## Skills\n- 理解用户的高层任务目标并进行任务分解\n- 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n- 协调多个子智能体的任务顺序和依赖关系\n- 实时反馈任务进度并汇总结果输出\n\n## OutputFormat(可选项):\n输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n\n## Rules\n- 所有任务必须以结构化形式注册至 MCP 平台\n- 子任务的定义必须明确，可度量\n- 避免资源冲突和任务重复分配\n- 汇总输出需具备可读性与可验证性\n\n## Workflows\n1. 读取用户输入的任务目标\n2. 拆分为若干子任务，定义每个任务所需能力\n3. 为每个子任务选择合适的子智能体并注册至 MCP\n4. 监控任务状态并在任务完成后收集结果\n5. 汇总结果并以所需格式输出\n\n## Init\n你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n\n## 用户问题\n测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:34:08,332 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:53:41,755 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:53:41,756 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:59:32,952 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-09 02:59:40,513 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:40,514 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
