2025-07-08 02:06:09,329 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:06:09,329 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 02:06:12,295 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat1bed8a42-5ba0-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751940369, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 02:06:40,130 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:06:48,975 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:07:12,295 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 02:09:27,463 - app - INFO - 【Service】【Model-流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:36:04,314 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:36:04,314 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:36:04,322 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:39:19,738 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:39:19,738 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:39:22,765 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat455ac25c-5bc6-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751956760, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:39:57,762 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:39:57,762 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:39:57,769 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:40:22,334 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:40:22,334 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:40:22,340 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:40:59,225 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:40:59,226 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:41:02,228 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat80a7b342-5bc6-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751956859, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:41:43,086 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:41:43,086 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:41:43,096 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:42:03,913 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:42:03,913 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:42:03,922 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:43:11,206 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:43:11,206 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:43:11,214 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:43:32,175 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:43:32,175 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '', 'top_p': '', 'top_k': ''} - [system]
2025-07-08 06:43:32,184 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:44:20,898 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:20,898 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': '0.01', 'top_p': '1', 'top_k': '0.1'} - [system]
2025-07-08 06:44:20,906 - app - ERROR - 【Service】【Model-推理】异常:Expecting value: line 1 column 1 (char 0) - [system]
2025-07-08 06:44:41,558 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:41,558 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:44:41,586 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:44:41,586 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:44:59,090 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:44:59,090 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:44:59,118 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:44:59,118 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:45:10,539 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:45:10,539 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:45:10,556 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:45:10,556 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:45:36,432 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:45:36,432 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:45:36,452 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:45:36,452 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:46:14,723 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-08 06:46:14,723 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:46:14,743 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:46:14,743 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:46:54,123 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 06:46:54,123 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:46:57,231 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat5430bd80-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957214, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三可以采取以下措施来预防或缓解因长时间写代码而引起的头发问题：\n\n1. **保持良好的生活习惯**：保证充足的睡眠，避免熬夜。熬夜会导致身体内分泌失调，从而影响头发的健康。\n\n2. **合理安排工作和休息时间**：长时间连续工作，不给身体足够的休息时间，会导致身体疲劳，进而影响头发的健康。建议每工作45-60分钟，休息10-15分钟。\n\n3. **保持良好的饮食习惯**：多吃富含维生素和矿物质的食物，如新鲜水果、蔬菜、全谷物、坚果和种子等，这些食物有助于头发的健康。\n\n4. **适当运动**：定期进行适量的体育锻炼，如散步、慢跑、瑜伽等，可以促进血液循环，有助于头发的健康。\n\n5. **保持良好的心理状态**：避免过度焦虑和压力，可以通过冥想、深呼吸、瑜伽等方式来缓解压力。\n\n6. **使用护发产品**：使用含有天然成分的洗发水和护发素，避免使用含有化学成分的洗发水和护发素，这些产品可能会对头发造成伤害。\n\n7. **定期检查头发健康状况**：定期检查头发的健康状况，如果发现头发问题，及时就医。\n\n通过以上措施，张三可以有效预防或缓解因长时间写代码而引起的头发问题。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 35, 'completion_tokens': 284, 'total_tokens': 319}} - [system]
2025-07-08 06:47:56,322 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:47:56,322 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:47:56,340 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:47:56,340 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:48:51,066 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:48:51,066 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 1, 'top_k': 0.1} - [system]
2025-07-08 06:48:51,088 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:35667, pid=158] top_k must be -1 (disable), or at least 1, got 0.'} - [system]
2025-07-08 06:48:51,089 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:49:38,869 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:49:38,870 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:49:39,367 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb662e4d8-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957376, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:49:55,589 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:49:55,589 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 30000, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:49:56,079 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatc05a23e8-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957393, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:50:06,962 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:50:06,962 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:50:07,454 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatc721af34-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957405, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:50:30,888 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:06,257 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:06,258 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:06,276 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-08 06:51:06,276 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:51:15,093 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:15,093 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:15,107 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-08 06:51:15,107 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 06:51:26,097 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:26,097 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:26,580 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatf64c5c32-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957484, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:51:32,433 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:32,433 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:32,923 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatfa136568-5bc7-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957490, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:51:50,531 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:51:50,532 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:51:51,029 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat04dd300a-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957508, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:52:11,426 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:52:11,426 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:52:11,913 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat115134f8-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957529, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:52:46,867 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:52:47,470 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:53:22,471 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 06:53:22,471 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 06:53:22,923 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3ba9655e-5bc8-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751957600, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 06:53:32,434 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:53:33,018 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:58:42,344 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:58:42,927 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:58:47,935 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:58:47,936 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 06:58:49,845 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:58:49,845 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 06:59:50,223 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 06:59:50,826 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 06:59:56,002 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': False} - [system]
2025-07-08 06:59:56,003 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:02:17,634 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 07:02:18,079 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 07:02:21,593 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:02:21,594 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:02:24,625 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat7d00d072-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958142, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:02:58,271 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:02:58,271 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int41', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:02:58,282 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: Qwen2.5-72B-Instruct-GPTQ-Int41'} - [system]
2025-07-08 07:02:58,282 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-08 07:03:28,224 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:03:41,553 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:03:41,554 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:03:44,842 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chataca9ece6-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958222, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:04:08,888 - app - INFO - 【Service】【Model-非流式-调用】inputs:张三才22岁，不想这么早就因为写代码而头秃,history:[] - [system]
2025-07-08 07:04:08,889 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:04:22,537 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatbcf4dac0-5bc9-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751958260, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:06:10,802 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:06:10,803 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:10:29,959 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': 'nihao', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 07:10:29,959 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:10:46,317 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/b0866f9a-100c-4c1f-8419-416b68d94442/dbd5bb78-f1f6-49e5-af8a-8dac0bb81e2f.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 07:10:46,318 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:08,232 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:12:08,235 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:14,865 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:12:14,865 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:12:17,355 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 07:12:17,355 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:13:04,715 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:13:04,716 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:13:46,172 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:13:46,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:16:45,923 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:16:45,924 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:22:49,086 - app - INFO - 【图生文】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:22:49,087 - app - ERROR - 【LLM统一入口】异常: 不支持的URL类型:  - [system]
2025-07-08 07:23:05,737 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '122', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'multiType': 'text', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 07:23:05,739 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:23:05,739 - app - INFO - 【Service】【Model-流式-调用】inputs:122,history:[] - [system]
2025-07-08 07:23:05,751 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '122'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 30000, 'stream': True} - [system]
2025-07-08 07:23:16,814 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text - [system]
2025-07-08 07:23:42,235 - app - INFO - 【非流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:23:42,235 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8071} - [system]
2025-07-08 07:23:50,421 - app - INFO - 【私有化API响应】输出: {'id': 'chat78533f8a-5bcc-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959428, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:24:02,526 - app - INFO - 【流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:24:56,035 - app - ERROR - 【LLM统一入口】异常: name 'DeployMode' is not defined - [system]
2025-07-08 07:26:13,035 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text_generate - [system]
2025-07-08 07:26:46,847 - app - INFO - 【流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:27:35,911 - app - INFO - 【图生文流式】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:27:53,083 - app - INFO - 【图生文】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:27:53,083 - app - ERROR - 【LLM统一入口】异常: 不支持的URL类型:  - [system]
2025-07-08 07:31:55,392 - app - INFO - 【图生文流式】文件: , 查询: 张三才22岁，不想这么早就因为写代码而头秃 - [system]
2025-07-08 07:32:07,211 - app - INFO - 【非流式对话】输入: 张三才22岁，不想这么早就因为写代码而头秃, 历史: [] - [system]
2025-07-08 07:32:07,211 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '张三才22岁，不想这么早就因为写代码而头秃'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8071} - [system]
2025-07-08 07:32:16,081 - app - INFO - 【私有化API响应】输出: {'id': 'chata550310e-5bcd-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959933, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '张三，22岁正是青春洋溢的年纪，确实不应该因为过度工作而影响健康，包括头发的健康。写代码是一项需要长时间集中注意力的工作，但通过合理安排时间和采取一些健康的生活习惯，可以有效避免“头秃”的问题。这里有一些建议，希望能帮到你：\n\n1. **合理安排工作时间**：避免长时间连续工作，每工作45-60分钟，起身活动5-10分钟，让眼睛和大脑都得到休息。\n\n2. **保持良好的坐姿**：长时间保持不良坐姿不仅对颈椎、腰椎有害，也可能间接影响血液循环，从而影响头发健康。\n\n3. **健康饮食**：均衡饮食，多吃富含蛋白质、维生素和矿物质的食物，如鱼、肉、蛋、奶、豆制品、新鲜蔬菜和水果等，有助于头发的健康生长。\n\n4. **适量运动**：定期进行适量的体育锻炼，如快走、慢跑、游泳等，可以促进血液循环，增强身体素质，对头发健康也有好处。\n\n5. **保持良好的睡眠**：保证充足的睡眠时间，尽量避免熬夜，良好的睡眠有助于身体和大脑的恢复，对预防脱发也有积极作用。\n\n6. **减压**：学会适当放松，可以通过听音乐、阅读、旅行等方式来缓解工作和生活中的压力，减少因压力过大导致的脱发。\n\n7. **正确护理头发**：使用适合自己发质的洗发水和护发素，避免使用过热的水洗头，减少使用吹风机、烫发棒等高温电器对头发的伤害。\n\n希望这些建议能帮助你保持健康，同时也能享受编程带来的乐趣。记住，健康永远是第一位的！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 45, 'completion_tokens': 359, 'total_tokens': 404}} - [system]
2025-07-08 07:32:16,081 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:32:42,726 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:32:42,726 - app - INFO - 【私有化API请求】输入: {'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:32:43,196 - app - INFO - 【私有化API响应】输出: {'id': 'chatba7b9280-5bcd-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959960, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 07:32:43,197 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:33:03,880 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:33:03,880 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:33:07,311 - app - INFO - 【私有化API响应】输出: {'id': 'chatc71766b8-5bcd-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751959984, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:33:07,312 - app - ERROR - 【文本生成】异常: SUCCESS - [system]
2025-07-08 07:33:36,352 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:33:36,352 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:33:49,703 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2570079232', 'modelType': '123', 'baseUrl': '123', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 44, 'outputMaxToken': 444, 'param': {}} - [system]
2025-07-08 07:33:49,704 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:34:04,165 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 07:34:04,166 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:34:48,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'text', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 07:34:48,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:35:29,025 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:35:29,025 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:35:29,026 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:35:29,027 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:37:03,612 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:37:03,613 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:37:45,012 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:37:45,013 - app - ERROR - 【Service】【Model-推理】异常:name 'CompanyCode' is not defined - [system]
2025-07-08 07:38:53,737 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:38:53,738 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:38:53,739 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:38:53,740 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:40:31,050 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:40:31,051 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:46:47,705 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:46:47,706 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:46:52,316 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb220b0aa-5bcf-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751960809, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:47:01,334 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:48:45,763 - app - ERROR - 【LLM统一入口】异常: 不支持的任务类型: text - [system]
2025-07-08 07:49:28,806 - app - INFO - 【流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:49:40,574 - app - INFO - 【非流式对话】输入: 小面额人民币有哪些, 历史: [] - [system]
2025-07-08 07:49:40,574 - app - INFO - 【私有化API请求】输入: {'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1, 'max_tokens': 8083} - [system]
2025-07-08 07:49:44,066 - app - INFO - 【私有化API响应】输出: {'id': 'chat192a768c-5bd0-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751960981, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 07:50:40,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-08 07:50:40,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:51:24,228 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 07:51:24,229 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 07:51:24,230 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 07:51:24,231 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '123'}], 'temperature': '0.01', 'top_p': '0.1', 'max_tokens': 200, 'stream': True} - [system]
2025-07-08 07:52:54,651 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 07:52:54,652 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 07:52:57,850 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat8cd89fc8-5bd0-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751961175, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:08:39,280 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:08:39,280 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:08:42,501 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatbfe2dc74-5bd2-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962120, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:09:51,547 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:09:51,547 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:09:54,777 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chateaf53a4c-5bd2-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962192, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:14:18,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:14:18,884 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:14:22,673 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat8a4eab78-5bd3-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962460, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:14:26,184 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:15:40,981 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:15:40,982 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:16:22,646 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:16:22,646 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:17:08,547 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:17:08,548 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:17:53,104 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:04,180 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:04,180 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:19:17,328 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:19:17,329 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:20:17,703 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:17,710 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:20:38,533 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:38,533 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:20:41,844 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6c97d0a4-5bd4-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751962839, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:20:49,757 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:20:49,761 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:22:09,917 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 08:22:10,504 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 08:26:14,811 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:26:14,815 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 08:26:25,488 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 08:26:25,489 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:26:28,664 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat3b6525bc-5bd5-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751963186, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币通常指的是面值较小的人民币纸币和硬币。在中国现行的人民币体系中，小面额人民币主要包括以下几种：\n\n1. **1角（纸币和硬币）**\n2. **5角（纸币和硬币）**\n3. **1元（纸币和硬币）**\n4. **5元（纸币）**\n5. **10元（纸币）**\n\n这些小面额的人民币在日常交易中非常常见，尤其是在小额交易或找零时。随着电子支付的普及，虽然现金交易的频率有所下降，但小面额人民币仍然是不可或缺的。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 137, 'total_tokens': 171}} - [system]
2025-07-08 08:26:37,479 - app - INFO - 【Service】【Model-非流式-调用】inputs:,history:[] - [system]
2025-07-08 08:26:37,479 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': ''}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:26:38,464 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat428a91d8-5bd5-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751963196, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': 'Hello! It seems like your message was cut off. How can I assist you today? Feel free to ask me any questions or let me know if you need help with anything specific.'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 29, 'completion_tokens': 38, 'total_tokens': 67}} - [system]
2025-07-08 08:30:53,149 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0, 'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/16220b39-4219-44eb-bd60-d22786663b92/5ff2a578-3a01-469f-bcef-dc64fcc74698.png', 'mediaType': 'image_url', 'urlType': 'minio'} - [system]
2025-07-08 08:30:53,151 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 08:52:13,045 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 08:52:13,046 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 08:52:14,037 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatd5ceb49e-5bd8-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751964731, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 08:56:27,510 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '11', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 08:56:27,511 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:00:53,139 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:00:53,139 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:00:54,162 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0bce7574-5bda-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965251, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 09:08:09,764 - app - INFO - 【Service】【Model-非流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:08:09,765 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '解释图片内容'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:08:10,760 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat100efc70-5bdb-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965688, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '当然，我很乐意帮助您解释图片内容。不过，我需要您先上传或提供图片的链接，这样我才能看到图片并进行描述。您可以如何提供图片呢？'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 32, 'completion_tokens': 39, 'total_tokens': 71}} - [system]
2025-07-08 09:09:10,787 - app - INFO - 【Service】【Model-流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:09:10,886 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:10:11,172 - app - INFO - 【Service】【Model-流式-调用】inputs:解释图片内容,history:[] - [system]
2025-07-08 09:10:11,412 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:11:07,805 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:07,805 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:11:08,305 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat7a2ddce8-5bdb-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751965865, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:11:14,270 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:14,359 - app - ERROR - 【Service】【Model-流式-模型调用】异常: Receive channel has not been made available - [system]
2025-07-08 09:11:58,128 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:11:58,132 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:12:36,534 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:12:36,537 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:13:00,842 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:00,846 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:13:32,031 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:32,031 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:13:32,447 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatd023df8a-5bdb-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751966010, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:13:41,780 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:13:41,784 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:14:23,630 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:14:23,633 - app - INFO - 【Service】【Model-流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'temperature': 0.01, 'top_p': 0.1, 'max_tokens': 8192, 'stream': True} - [system]
2025-07-08 09:14:55,757 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:16:50,380 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:17:09,225 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:17:09,791 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:17:30,222 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:17:30,806 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:18:02,533 - app - INFO - 【Service】【Model-流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:18:14,142 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民币有哪些,history:[] - [system]
2025-07-08 09:18:14,142 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2-vl-7b-instruct-awq', 'messages': [{'role': 'user', 'content': '小面额人民币有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.1, 'top_k': 1} - [system]
2025-07-08 09:18:14,626 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat784b6098-5bdc-11f0-ab2b-0425c59bcdbb', 'object': 'chat.completion', 'created': 1751966292, 'model': 'qwen2-vl-7b-instruct-awq', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '小面额人民币包括1角、5角、1元、5元、10元、20元、50元、100元等面额的人民币。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 24, 'completion_tokens': 40, 'total_tokens': 64}} - [system]
2025-07-08 09:18:33,884 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:18:34,475 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:19:23,856 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:19:24,452 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:25:31,763 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你好', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 09:25:31,764 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:26:05,591 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977265254510841', 'baseUrl': 'http://172.1.0.154:8003/v1/chat/completions', 'query': '你是谁', 'apiKey': '', 'inputMaxToken': 131072, 'outputMaxToken': 131072, 'stream': False} - [system]
2025-07-08 09:26:05,592 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:26:19,394 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0353472512', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你好', 'apiKey': '', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'stream': True, 'deployMode': 0} - [system]
2025-07-08 09:26:19,395 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 09:43:38,316 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:43:38,896 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:44:16,185 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:44:16,757 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 09:46:43,257 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 09:46:43,843 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 10:01:11,394 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:01:11,394 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:01:17,323 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751968877, 'data': [{'url': '/data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:01:17,323 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:01:17,329 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,373 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,392 - app - INFO - 第 1 张图片已上传到 MinIO: 172.1.0.172:9000/sunklm-dev/generated-images/b80cc21ff5d848d8b50523a7364b6fba.jpg - [system]
2025-07-08 10:01:17,393 - app - INFO - 生成结果: {'image_result': {'created': 1751968877, 'data': [{'url': '/data/x_inference/xinf_models/image/b80cc21ff5d848d8b50523a7364b6fba.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:03:34,034 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:03:34,035 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:03:40,107 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751969020, 'data': [{'url': '/data/x_inference/xinf_models/image/372d16a793cd42ac92fabf29572ff1ec.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:03:40,107 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:03:40,113 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,156 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,183 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/372d16a793cd42ac92fabf29572ff1ec.jpg - [system]
2025-07-08 10:03:40,184 - app - INFO - 生成结果: {'image_result': {'created': 1751969020, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/372d16a793cd42ac92fabf29572ff1ec.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:04:35,160 - app - INFO - 【Service】【ImageModel 调用】Prompt: a dog sitting on the moon, Model: stable-diffusion-xl-base-1.0 - [system]
2025-07-08 10:04:35,160 - app - INFO - 【Service】【ImageModel 私有部署】请求地址: http://172.1.0.154:9020/v1/images/generations，数据: {"model": "stable-diffusion-xl-base-1.0", "prompt": "a dog sitting on the moon", "n": "1", "size": "512x1024", "response_format": "url", "guidance_scale": "8.5", "num_inference_steps": "40", "negative_prompt": "the dog is white", "sampler_name": "DPM++ 2M Karras"} - [system]
2025-07-08 10:04:40,887 - app - INFO - 【Service】【ImageModel 私有部署】返回结果: {'created': 1751969080, 'data': [{'url': '/data/x_inference/xinf_models/image/84052824d6db46e8aac9dd0ae5c5cf96.jpg', 'b64_json': None}]} - [system]
2025-07-08 10:04:40,888 - app - INFO - 共生成了 1 张图片. - [system]
2025-07-08 10:04:40,896 - app - INFO - 提取第 1 张容器内路径: /data/x_inference/xinf_models/image/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,939 - app - INFO - 第 1 张图片成功复制到宿主机: /home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-gateway-alg/image_output/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,984 - app - INFO - 第 1 张图片已上传到 MinIO: http://172.1.0.172:9000/sunklm-dev/generated-images/84052824d6db46e8aac9dd0ae5c5cf96.jpg - [system]
2025-07-08 10:04:40,984 - app - INFO - 生成结果: {'image_result': {'created': 1751969080, 'data': [{'url': 'http://172.1.0.172:9000/sunklm-dev/generated-images/84052824d6db46e8aac9dd0ae5c5cf96.jpg', 'b64_json': None}]}} - [system]
2025-07-08 10:05:00,569 - app - INFO - 【Service】【JSON处理】服务初始化完成 - [system]
2025-07-08 10:05:01,153 - app - INFO - 【Service】【文档处理】服务初始化完成 - [system]
2025-07-08 10:52:03,556 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:52:03,558 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,380 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'param': {}} - [system]
2025-07-08 10:54:59,381 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,382 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '6950321152', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '123', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 200, 'outputMaxToken': 200, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 10:54:59,384 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 10:54:59,385 - app - INFO - 【Service】【Model-流式-调用】inputs:123,history:[] - [system]
2025-07-08 11:31:47,545 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:47,546 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:50,958 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:31:56,404 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-08 11:31:56,405 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,712 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:32:11,713 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:32:11,714 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-08 11:34:34,325 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:35:11,128 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/6843d4ea-a6b5-48cf-a473-a0f1c10a383e/ceff59a3-6f96-4ab3-b1f9-97cc14660b0f.png', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-08 11:35:11,129 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://172.1.0.154:8001/v1/chat/completions', 'query': '哈哈哈', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-08 11:39:58,107 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-08 11:39:58,108 - app - INFO - 【Service】【Model-流式-调用】inputs:哈哈哈,history:[] - [system]
2025-07-09 01:14:29,406 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:14:29,407 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'param': {}} - [system]
2025-07-09 01:15:14,681 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:07,455 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': '5043991552', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'param': {}} - [system]
2025-07-09 01:16:07,456 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:16:44,365 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:16:44,366 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:16:44,387 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:16:44,388 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:17:09,169 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:17:09,172 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:17:09,173 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:17:09,185 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:17:09,185 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:23,785 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:23,786 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:23,800 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:23,800 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:19:53,883 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:19:53,884 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:19:53,885 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:19:53,896 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:19:53,897 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:21:55,390 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:21:55,391 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:21:55,392 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:21:55,406 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 01:21:55,407 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'awen', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:22:22,383 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:22:22,384 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'awen', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:22:22,397 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: awen'} - [system]
2025-07-09 01:22:22,397 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 01:23:18,855 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text', 'param': {}} - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:23:18,856 - app - INFO - 【Service】【Model-非流式-调用】inputs:你是谁,history:[] - [system]
2025-07-09 01:23:18,857 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '你是谁'}], 'max_tokens': 32769, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 01:23:20,396 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat4a217018-5c63-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752024197, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '我是Qwen，由阿里云开发的超大规模语言模型。我被设计用于生成各种文本，如文章、故事、诗歌、故事等，并能根据不同的场景和需求进行对话、提供信息查询、创作等任务。很高兴为您服务！'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 31, 'completion_tokens': 54, 'total_tokens': 85}} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:26:38,312 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:34:07,656 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '1832977373136203777', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '你是谁', 'stream': False, 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {}} - [system]
2025-07-09 01:34:07,657 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:34:59,358 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '图片内容', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:34:59,359 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:35:07,729 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/2df8fe3b-240f-42f4-8bb3-007889d7c183/a55963a9-1164-4174-9f8a-5a1c6c17b452.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 01:35:07,730 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:36:23,789 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:36:23,790 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:56:40,208 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:56:40,209 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 01:59:43,049 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 01:59:43,050 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 02:02:30,159 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:21:30,247 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:26:05,899 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 0, 'apiKey': 'not used actually', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:26:05,900 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:31:10,480 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:10,481 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:31:31,041 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:31:31,042 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:34:08,331 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': '', 'modelType': '', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': '', 'apiKey': '', 'query': '\n# Role: MCP 控制智能体调度器\n\n## Profile\n- language: 中文\n- description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n\n## Skills\n- 理解用户的高层任务目标并进行任务分解\n- 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n- 协调多个子智能体的任务顺序和依赖关系\n- 实时反馈任务进度并汇总结果输出\n\n## OutputFormat(可选项):\n输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n\n## Rules\n- 所有任务必须以结构化形式注册至 MCP 平台\n- 子任务的定义必须明确，可度量\n- 避免资源冲突和任务重复分配\n- 汇总输出需具备可读性与可验证性\n\n## Workflows\n1. 读取用户输入的任务目标\n2. 拆分为若干子任务，定义每个任务所需能力\n3. 为每个子任务选择合适的子智能体并注册至 MCP\n4. 监控任务状态并在任务完成后收集结果\n5. 汇总结果并以所需格式输出\n\n## Init\n你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n\n## 用户问题\n测试问题', 'tool': [{'type': 'function', 'function': {'name': 'qichacha_bidding_details', 'description': '\n        企查查招投标详情查询\n        入参：\n        - bidding_id: str - 列表内提供的bidding_id\n        ', 'parameters': {'properties': {'bidding_id': {'title': 'Bidding Id', 'type': 'string'}, 'url': {'title': 'Url', 'type': 'string'}, 'headers': {'default': None, 'title': 'headers', 'type': 'string'}}, 'required': ['bidding_id', 'url'], 'title': 'qichacha_bidding_detailsArguments', 'type': 'object'}}}], 'history': None, 'stream': False} - [system]
2025-07-09 02:34:08,332 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:53:41,755 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:53:41,756 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:55:23,269 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:59:32,952 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatAgent, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Controller】【AI网关-chatAgent】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 02:59:32,953 - app - INFO - 【Service】【Agent-非流式-执行】输入:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Service】【Agent-非流式-执行】异常:[Errno 111] Connection refused - [system]
2025-07-09 02:59:33,033 - app - ERROR - 【Controller】【AI网关-service处理】异常500: An error occurred: [Errno 111] Connection refused - [system]
2025-07-09 02:59:40,513 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 02:59:40,514 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:00:09,963 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:19:04,631 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:19:04,631 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:19:04,632 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:19:04,633 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:19:04,645 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:19:04,646 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 03:20:26,784 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'embedding', 'query': '小面额人民有哪些', 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:20:26,784 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:22:48,671 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'tool': [{'toolName': 'qichacha_bidding_details'}], 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:22:48,672 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:22:48,688 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:22:48,688 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 03:22:55,425 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'stream': False, 'multiType': 'text', 'query': '小面额人民有哪些', 'tool': [{'toolName': 'qichacha_bidding_details'}], 'company': 'qwen', 'modelType': 'qwen2.5-72b-instruct-gptq-int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'deplpyMode': 0} - [system]
2025-07-09 03:22:55,425 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 03:22:55,426 - app - INFO - 【Service】【Model-非流式-调用】inputs:小面额人民有哪些,history:[] - [system]
2025-07-09 03:22:55,426 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'qwen2.5-72b-instruct-gptq-int4', 'messages': [{'role': 'user', 'content': '小面额人民有哪些'}], 'max_tokens': 8192, 'temperature': 0.01, 'top_p': 0.01, 'top_k': 1} - [system]
2025-07-09 03:22:55,438 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'detail': '[address=***********:31322, pid=140] Model not found in the model list, uid: qwen2.5-72b-instruct-gptq-int4'} - [system]
2025-07-09 03:22:55,439 - app - ERROR - 【Service】【Model-推理】异常:'choices' - [system]
2025-07-09 05:52:13,186 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 05:52:13,186 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 05:53:19,518 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 05:53:19,518 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 06:52:21,875 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': 'http://127.0.0.1:9082/mcpTool', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '0076761088'} - [system]
2025-07-09 06:52:21,877 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 06:52:21,879 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 06:52:21,923 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 06:52:21,925 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 - [system]
2025-07-09 06:52:21,925 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 - [system]
2025-07-09 06:52:21,935 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,939 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 06:52:21,977 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 06:52:21,978 - app - INFO - 【Service】【MCP调用】tool:file_format_tool,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': 'http://127.0.0.1:9082/mcpTool', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 06:52:21,984 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,989 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b03e211e81e1431abac42633f801eca5 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 06:52:21,994 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='Error executing tool file_format_tool: 2 validation errors for file_format_toolArguments\nfilePaths\n  Field required [type=missing, input_value={\'code\': 0, \'data\': \'## 4.../json; charset=UTF-8"}\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\ncleanType\n  Field required [type=missing, input_value={\'code\': 0, \'data\': \'## 4.../json; charset=UTF-8"}\'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing', annotations=None)] isError=True - [system]
2025-07-09 06:55:17,684 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'company': 'tencent', 'modelType': 'qwen', 'baseUrl': '', 'temperature': 0.95, 'topP': 0.01, 'topK': 1, 'inputMaxToken': 100000, 'outputMaxToken': 2048, 'deployMode': 'cloud', 'apiKey': '', 'query': '\n        # Role: MCP 控制智能体调度器\n\n        ## Profile\n        - language: 中文\n        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。\n        \n        ## Skills\n        - 理解用户的高层任务目标并进行任务分解\n        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度\n        - 协调多个子智能体的任务顺序和依赖关系\n        - 实时反馈任务进度并汇总结果输出\n        \n        ## OutputFormat(可选项):\n        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。\n        \n        ## Rules\n        - 所有任务必须以结构化形式注册至 MCP 平台\n        - 子任务的定义必须明确，可度量\n        - 避免资源冲突和任务重复分配\n        - 汇总输出需具备可读性与可验证性\n        \n        ## Workflows\n        1. 读取用户输入的任务目标\n        2. 拆分为若干子任务，定义每个任务所需能力\n        3. 为每个子任务选择合适的子智能体并注册至 MCP\n        4. 监控任务状态并在任务完成后收集结果\n        5. 汇总结果并以所需格式输出\n        \n        ## Init\n        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。\n        \n        ## 用户问题\n        1', 'tools': [], 'history': None, 'stream': False} - [system]
2025-07-09 06:55:17,684 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:03:40,890 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:03:40,891 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:03:40,891 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 07:03:40,925 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:03:40,926 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad - [system]
2025-07-09 07:03:40,927 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad - [system]
2025-07-09 07:03:40,934 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:40,936 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:03:40,993 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:03:41,001 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:03:41,006 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:41,010 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e55de450d9254d55a2e56cfef46278ad "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:03:41,013 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
2025-07-09 07:04:10,127 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolArgs': {'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:04:10,128 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:04:10,128 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 07:04:10,148 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:04:10,148 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 - [system]
2025-07-09 07:04:10,149 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 - [system]
2025-07-09 07:04:10,153 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,155 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:04:10,158 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:04:10,159 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'code': 0, 'data': '## 4.1角色游戏中小班幼儿同伴互动主要类型的分析\n\n表2 幼儿角色游戏同伴互动分布表\n\n|      互动类型 |           次数|            百分比||\n|---|---|---|---|\n|        合作|             46|            42.6% ||\n|        冲突|             37|           34.3% ||\n|        协商|             25|           23.1% ||\n\n\n\n通过观察，发现小班幼儿角色游戏同伴互动的类型中，合作、冲突和协商出现的频率最高，所以将这三个维度作为研究的重点。由表2可知，幼儿的合作行为最多，共46次，占42.6% ；冲突行为其次，共37次，占34.3% ，协商行为最少，共25次，占23.1% 。\n\n### 4.1.1 合作行为最多\n\n案例1：我们一起烤串吧\n\n      ![img](福\n-\n♠)\n\n                   图1 我们一起烤串吧场景图\n\n希希一个人在摆摊烤串，依依看到之后走了过来说：“我们一起烤串可以吗希希？”希希说:“好啊，我们一起烤。”不一会儿，烧烤摊旁边的小朋友越来越多，他们一起围在桌子旁边做起了烧烤。但是烤炉只有一个，好几个小朋友都抢着要烤串。老师走过来对他们说：“烧烤店是不是要有收银员和客人呀？”于是幼儿们开始合作开烧烤店，有的幼儿负责烤，有的负责卖，有的幼儿当顾客，他们在一起玩的非常开心。\n\n\n\n', 'log_id': '20241227172728FE828D29AFD8C77811CD', 'msg': 'success', 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:04:10,162 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,165 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ea57aa3e645a40e283d08b76133ab857 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:04:10,168 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'code': 0, 'data': '## 4...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
2025-07-09 07:15:03,728 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolArgs': {'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:15:03,728 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:15:03,729 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 07:15:03,756 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:15:03,757 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 - [system]
2025-07-09 07:15:03,757 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 - [system]
2025-07-09 07:15:03,763 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,765 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:15:03,769 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:15:03,770 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:15:03,775 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,780 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c5411e289f044e2acd4103c27266b57 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:03,782 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
2025-07-09 07:15:04,100 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolArgs': {'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'}, 'toolId': '1857247232'} - [system]
2025-07-09 07:15:04,101 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 07:15:04,101 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 07:15:04,127 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 07:15:04,128 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e - [system]
2025-07-09 07:15:04,129 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e - [system]
2025-07-09 07:15:04,134 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,136 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 07:15:04,140 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 07:15:04,141 - app - INFO - 【Service】【MCP调用】tool:tts_function,输入:{'user_name': '小明', 'last_question': '你们支持货到付款吗？', 'agent_response': '你好，{{user_name}}！我们目前不支持货到付款，推荐使用微信或支付宝支付方式。如需帮助，请随时联系人工客服。', 'order_info': {'order_id': 'ORD123456', 'order_status': '已发货', 'delivery_date': '2025-07-10'}, 'plugin_response': {'success': True, 'data': {'product_name': '无线蓝牙耳机', 'price': '¥299', 'stock': 15}}, 'url': ' http://***********:9020/v1', 'method': 'POST', 'headers': '{"contentType":"application/json; charset=UTF-8"}'} - [system]
2025-07-09 07:15:04,146 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,150 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d8077df3cc864e04a108eb57a7e1d78e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 07:15:04,152 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text="Error executing tool tts_function: 2 validation errors for tts_functionArguments\nmodelType\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nquery\n  Field required [type=missing, input_value={'user_name': '小明', '...n/json; charset=UTF-8'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", annotations=None)] isError=True - [system]
2025-07-09 09:18:17,541 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '5203266560', 'modelType': 'qwen2-vl-7b-instruct-awq', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'inputMaxToken': 30000, 'outputMaxToken': 30000, 'multiType': 'imgToText', 'param': {'fileUrl': 'http://172.1.6.188:9000/sunklm-dev/model_upload_file/046e72a1-089f-4cd7-b3ca-1791e38a36a9/c2557fbe-9fb4-42d3-84a7-122d3e79a59f.jpg', 'mediaType': 'image_url', 'urlType': 'minio'}} - [system]
2025-07-09 09:18:17,542 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:09,368 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:09,370 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:09,372 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:09,419 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:09,421 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=b6b4882613c845f78287903942e076e8 - [system]
2025-07-09 11:53:09,422 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=b6b4882613c845f78287903942e076e8 - [system]
2025-07-09 11:53:09,430 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6b4882613c845f78287903942e076e8 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:09,434 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:09,442 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:09,445 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:09,449 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6b4882613c845f78287903942e076e8 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:09,454 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6b4882613c845f78287903942e076e8 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:17,907 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:20,110 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:20,111 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:20,112 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:20,140 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:20,142 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=5de2ea395a3f45be8ebb95238252fe27 - [system]
2025-07-09 11:53:20,142 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=5de2ea395a3f45be8ebb95238252fe27 - [system]
2025-07-09 11:53:20,148 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5de2ea395a3f45be8ebb95238252fe27 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:20,151 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:20,154 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:20,155 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:20,160 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5de2ea395a3f45be8ebb95238252fe27 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:20,164 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5de2ea395a3f45be8ebb95238252fe27 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:22,940 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:25,531 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:25,532 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:25,533 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:25,559 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:25,560 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=25ff03fb44fa490b9a0eb3471478534f - [system]
2025-07-09 11:53:25,561 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=25ff03fb44fa490b9a0eb3471478534f - [system]
2025-07-09 11:53:25,566 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=25ff03fb44fa490b9a0eb3471478534f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:25,569 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:25,572 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:25,581 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:25,604 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=25ff03fb44fa490b9a0eb3471478534f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:25,608 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=25ff03fb44fa490b9a0eb3471478534f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:28,754 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:30,974 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:30,975 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:30,975 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:31,001 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:31,002 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=23da0e79e70a49369d046e536c166fde - [system]
2025-07-09 11:53:31,003 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=23da0e79e70a49369d046e536c166fde - [system]
2025-07-09 11:53:31,008 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=23da0e79e70a49369d046e536c166fde "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:31,013 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:31,016 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:31,017 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:31,020 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=23da0e79e70a49369d046e536c166fde "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:31,025 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=23da0e79e70a49369d046e536c166fde "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:34,489 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:38,059 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:38,060 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:38,061 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:38,088 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:38,089 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=f1f7e1e81e7e4ca08a91bed6c32eb1a1 - [system]
2025-07-09 11:53:38,090 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=f1f7e1e81e7e4ca08a91bed6c32eb1a1 - [system]
2025-07-09 11:53:38,096 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f1f7e1e81e7e4ca08a91bed6c32eb1a1 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:38,098 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:38,102 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:38,103 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:38,108 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f1f7e1e81e7e4ca08a91bed6c32eb1a1 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:38,114 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f1f7e1e81e7e4ca08a91bed6c32eb1a1 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:41,679 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:44,442 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:44,442 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:44,443 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:44,470 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:44,472 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=5c7ba33c070a47dba3b897491d8a1ed2 - [system]
2025-07-09 11:53:44,472 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=5c7ba33c070a47dba3b897491d8a1ed2 - [system]
2025-07-09 11:53:44,478 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c7ba33c070a47dba3b897491d8a1ed2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:44,481 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:44,483 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:44,484 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:44,488 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c7ba33c070a47dba3b897491d8a1ed2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:44,494 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5c7ba33c070a47dba3b897491d8a1ed2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:48,150 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:50,819 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:50,820 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:50,821 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:50,848 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:50,849 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=10de27293b59493084f513e7b5355f00 - [system]
2025-07-09 11:53:50,850 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=10de27293b59493084f513e7b5355f00 - [system]
2025-07-09 11:53:50,856 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=10de27293b59493084f513e7b5355f00 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:50,858 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:50,861 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:50,862 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:50,868 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=10de27293b59493084f513e7b5355f00 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:50,874 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=10de27293b59493084f513e7b5355f00 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:54,488 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:53:57,326 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:53:57,327 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:53:57,328 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:53:57,355 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:53:57,357 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=b46c22d3e1fc45e49dc28345b7ce2278 - [system]
2025-07-09 11:53:57,357 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=b46c22d3e1fc45e49dc28345b7ce2278 - [system]
2025-07-09 11:53:57,363 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b46c22d3e1fc45e49dc28345b7ce2278 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:57,366 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:53:57,369 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:53:57,371 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:53:57,375 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b46c22d3e1fc45e49dc28345b7ce2278 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:53:57,381 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b46c22d3e1fc45e49dc28345b7ce2278 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:01,045 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:04,058 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:04,059 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:04,060 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:04,087 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:04,088 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=8621e186dd9c4baa8a203db63e6cd8f2 - [system]
2025-07-09 11:54:04,089 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=8621e186dd9c4baa8a203db63e6cd8f2 - [system]
2025-07-09 11:54:04,095 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=8621e186dd9c4baa8a203db63e6cd8f2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:04,097 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:04,100 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:04,101 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:04,107 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=8621e186dd9c4baa8a203db63e6cd8f2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:04,112 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=8621e186dd9c4baa8a203db63e6cd8f2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:07,714 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:10,928 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:10,929 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:10,929 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:10,955 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:10,956 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=0d093a16cbea4664b893962dffa8bc2e - [system]
2025-07-09 11:54:10,957 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=0d093a16cbea4664b893962dffa8bc2e - [system]
2025-07-09 11:54:10,962 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0d093a16cbea4664b893962dffa8bc2e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:10,965 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:10,972 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:10,973 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:10,978 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0d093a16cbea4664b893962dffa8bc2e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:10,983 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0d093a16cbea4664b893962dffa8bc2e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:19,207 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:22,627 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:22,627 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:22,628 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:22,656 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:22,658 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=70d2f62a61a445a682aabcb115084ee3 - [system]
2025-07-09 11:54:22,658 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=70d2f62a61a445a682aabcb115084ee3 - [system]
2025-07-09 11:54:22,664 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=70d2f62a61a445a682aabcb115084ee3 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:22,667 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:22,671 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:22,672 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:22,677 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=70d2f62a61a445a682aabcb115084ee3 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:22,682 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=70d2f62a61a445a682aabcb115084ee3 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:26,309 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:29,635 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:29,635 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:29,636 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:29,664 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:29,665 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=0e7722748a16488b83e1f9d32094019a - [system]
2025-07-09 11:54:29,666 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=0e7722748a16488b83e1f9d32094019a - [system]
2025-07-09 11:54:29,672 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0e7722748a16488b83e1f9d32094019a "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:29,675 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:29,685 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:29,686 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:29,691 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0e7722748a16488b83e1f9d32094019a "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:29,696 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0e7722748a16488b83e1f9d32094019a "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:33,271 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:36,434 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:36,435 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:36,436 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:36,464 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:36,465 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=c867f0b75e1246b6a557b8ec57cf27d2 - [system]
2025-07-09 11:54:36,466 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=c867f0b75e1246b6a557b8ec57cf27d2 - [system]
2025-07-09 11:54:36,472 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=c867f0b75e1246b6a557b8ec57cf27d2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:36,474 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:36,477 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:36,478 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:36,484 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=c867f0b75e1246b6a557b8ec57cf27d2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:36,489 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=c867f0b75e1246b6a557b8ec57cf27d2 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:40,118 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:42,533 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:42,534 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:42,535 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:42,562 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:42,564 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=6f5cb53096c84876acf2a46e2c2d436f - [system]
2025-07-09 11:54:42,564 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=6f5cb53096c84876acf2a46e2c2d436f - [system]
2025-07-09 11:54:42,571 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6f5cb53096c84876acf2a46e2c2d436f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:42,573 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:42,577 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:42,579 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:42,584 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6f5cb53096c84876acf2a46e2c2d436f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:42,588 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6f5cb53096c84876acf2a46e2c2d436f "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:51,216 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:54:54,436 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:54:54,437 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:54:54,438 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:54:54,464 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:54:54,465 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=53744d4cc68148c0a6c1a455488fb56e - [system]
2025-07-09 11:54:54,465 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=53744d4cc68148c0a6c1a455488fb56e - [system]
2025-07-09 11:54:54,471 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=53744d4cc68148c0a6c1a455488fb56e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:54,473 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:54:54,478 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:54:54,479 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:54:54,511 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=53744d4cc68148c0a6c1a455488fb56e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:54,516 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=53744d4cc68148c0a6c1a455488fb56e "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:54:58,035 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:55:02,281 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:55:02,281 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:55:02,282 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:55:02,310 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:55:02,311 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=bd60a26e0c3c42bb8e36c1102e9ead01 - [system]
2025-07-09 11:55:02,312 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=bd60a26e0c3c42bb8e36c1102e9ead01 - [system]
2025-07-09 11:55:02,318 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=bd60a26e0c3c42bb8e36c1102e9ead01 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:02,321 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:55:02,324 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:55:02,326 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:55:02,329 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=bd60a26e0c3c42bb8e36c1102e9ead01 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:02,335 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=bd60a26e0c3c42bb8e36c1102e9ead01 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:05,523 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 11:55:07,976 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-09 11:55:07,976 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 11:55:07,977 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-09 11:55:08,005 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-09 11:55:08,006 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=f98be98d6bee4c12ad0fd1fce9797c22 - [system]
2025-07-09 11:55:08,007 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=f98be98d6bee4c12ad0fd1fce9797c22 - [system]
2025-07-09 11:55:08,013 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f98be98d6bee4c12ad0fd1fce9797c22 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:08,015 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-09 11:55:08,018 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-09 11:55:08,019 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/3ff61997-f2d7-41dc-aece-68050d15184e/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-09 11:55:08,023 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f98be98d6bee4c12ad0fd1fce9797c22 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:08,029 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=f98be98d6bee4c12ad0fd1fce9797c22 "HTTP/1.1 202 Accepted" - [system]
2025-07-09 11:55:11,219 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": "\\u4f60\\u662f\\u8ab0"\n    }\n}', annotations=None)] isError=False - [system]
2025-07-09 12:06:07,414 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768} - [system]
2025-07-09 12:06:07,415 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:06:37,389 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768} - [system]
2025-07-09 12:06:37,390 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:09:26,709 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:09:26,711 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:09:26,711 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:10:14,590 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:10:14,590 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:10:14,591 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:11:51,157 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:11:51,158 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:11:51,159 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:13:38,721 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:13:38,722 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:13:38,722 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:14:52,472 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:14:52,473 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:14:52,473 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:16:08,806 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:16:08,807 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:16:08,807 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:18:02,451 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:18:02,452 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:18:02,453 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:18:58,478 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:18:58,479 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:18:58,479 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:20:02,242 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:20:02,242 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:20:02,242 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:20:25,655 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:20:25,656 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:20:25,657 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:22:05,265 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:22:05,266 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:22:05,267 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:22:33,879 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:22:33,879 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:22:33,880 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:23:32,723 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:23:32,724 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:23:32,725 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:23:58,439 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:23:58,440 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:23:58,441 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:24:55,346 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:24:55,347 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:24:55,347 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:25:15,964 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:25:15,965 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:25:15,965 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:25:56,423 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:25:56,424 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:25:56,424 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:28:48,361 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:28:48,362 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:28:48,363 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:28:48,363 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-09 12:28:52,357 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat41ecdeca-5cc0-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752064129, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-09 12:34:08,351 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:34:08,351 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:34:08,352 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:34:08,352 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-09 12:34:11,998 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat00a8f132-5cc1-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752064449, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-09 12:34:33,121 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:34:33,122 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:34:33,122 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:34:33,123 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-09 12:34:36,671 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat0f6b7ac8-5cc1-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752064474, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-09 12:38:04,803 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-09 12:38:04,804 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-09 12:38:04,804 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-09 12:38:04,805 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-09 12:38:08,691 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat8d97921a-5cc1-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752064686, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 03:17:50,396 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:17:50,397 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:17:50,397 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:17:50,398 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 03:17:54,163 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat7431d83c-5d3c-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752117471, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 03:18:05,187 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:18:05,187 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:18:05,188 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:28:55,726 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:28:55,727 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:28:55,728 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:37:33,504 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:37:33,504 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:37:33,505 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:40:52,541 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:40:52,541 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:40:52,542 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:45:10,166 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:45:10,166 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:45:10,167 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:53:27,621 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 03:53:27,622 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 03:53:27,623 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 03:53:27,623 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 03:53:31,219 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6e135822-5d41-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752119608, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 05:38:32,206 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 05:38:32,208 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 05:38:32,209 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 05:38:32,209 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 05:38:36,056 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat1be4fd44-5d50-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752125913, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 05:39:18,956 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 05:39:18,957 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 05:39:18,957 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 05:39:18,958 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 05:39:22,512 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat37c175ba-5d50-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752125959, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 05:47:46,451 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 05:47:46,452 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 05:47:46,452 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 05:47:46,452 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 05:47:50,307 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat663f805c-5d51-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752126467, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 05:49:59,525 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 05:49:59,526 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 05:49:59,526 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 05:49:59,526 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 05:50:03,005 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chatb5905b4a-5d51-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752126600, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 05:50:17,703 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 05:50:17,704 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 05:50:17,704 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:03:18,460 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:03:18,461 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:03:18,461 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:03:54,972 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:03:54,973 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:03:54,973 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:05:11,265 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:05:11,266 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:05:11,266 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:05:35,789 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:05:35,790 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:05:35,791 - app - INFO - 【Service】【Model-流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:08:38,279 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:08:38,279 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:08:38,280 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:08:38,280 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 06:08:41,971 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat5063e2fc-5d54-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752127719, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 06:08:42,253 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://172.1.0.154:8001/v1/chat/completions', 'query': '根据用户问题回答\n{{}inputi}', 'stream': True, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:08:42,254 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:08:42,254 - app - INFO - 【Service】【Model-流式-调用】inputs:根据用户问题回答
{{}inputi},history:[] - [system]
2025-07-10 06:09:24,179 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '0287083520', 'modelType': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'baseUrl': 'http://***********:9020/v1/chat/completions', 'query': '输出{{}input}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32769, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:09:24,180 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:09:24,181 - app - INFO - 【Service】【Model-非流式-调用】inputs:输出{{}input},history:[] - [system]
2025-07-10 06:09:24,181 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'messages': [{'role': 'user', 'content': '输出{{}input}'}], 'max_tokens': 32769, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 06:09:27,733 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'id': 'chat6bc01d5e-5d54-11f0-8743-0425c59bcdbb', 'object': 'chat.completion', 'created': 1752127765, 'model': 'Qwen2.5-72B-Instruct-GPTQ-Int4', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '看起来你可能是在尝试使用某种模板或格式化字符串的语法，但你的输入中似乎缺少了实际的内容。在许多编程语言或模板引擎中，`{{}}` 通常用于插入变量或表达式的值。例如，在某些模板引擎中，`{{ name }}` 会被替换为变量 `name` 的值。\n\n如果你想要输出一个特定的值或变量，你需要提供该值或变量。例如：\n\n- 如果你想输出一个字符串 "Hello, World!"，你可以写成 `{{ "Hello, World!" }}`。\n- 如果你想输出一个变量 `name`，你可以写成 `{{ name }}`。\n\n请提供你想要输出的具体内容或变量，这样我可以更准确地帮助你。'}, 'finish_reason': None}], 'usage': {'prompt_tokens': 34, 'completion_tokens': 153, 'total_tokens': 187}} - [system]
2025-07-10 06:09:27,894 - app - INFO - 【Controller】【AI网关】请求地址:gateway/chatModel, 参数:{'modelId': '2937540608', 'modelType': 'DeepSeek-R1-Distill-Llama-70B', 'baseUrl': 'http://172.1.0.154:8001/v1/chat/completions', 'query': '根据用户问题回答\n{{}inputi}', 'stream': False, 'deployMode': 0, 'temperature': '0.01', 'topP': '0.1', 'topK': '1', 'inputMaxToken': 32768, 'outputMaxToken': 32768, 'multiType': 'text'} - [system]
2025-07-10 06:09:27,894 - app - INFO - 【Controller】【AI网关-chatModel】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:09:27,895 - app - INFO - 【Service】【Model-非流式-调用】inputs:根据用户问题回答
{{}inputi},history:[] - [system]
2025-07-10 06:09:27,895 - app - INFO - 【Service】【Model-非流式-模型调用】输入:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'messages': [{'role': 'user', 'content': '根据用户问题回答\n{{}inputi}'}], 'max_tokens': 32768, 'temperature': '0.01', 'top_p': '0.1', 'top_k': '1'} - [system]
2025-07-10 06:09:46,094 - app - INFO - 【Service】【Model-非流式-模型调用】输出:{'model': 'DeepSeek-R1-Distill-Llama-70B', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '<think>\n嗯，用户发来的信息看起来有点问题。他们写的是“{{}inputi}”，这好像不是一个正常的问题或者指令。首先，我得确认这是不是输入错误，或者是某种特定的格式。可能用户是想输入一些变量或者占位符，但写错了。比如，他们可能想写“{{input}}”或者其他类似的东西，但结果变成了“{{}inputi}”。\n\n接下来，我应该考虑用户的意图。他们可能是在尝试使用某种模板或者占位符，但由于语法错误，导致内容无法正确显示。也有可能是他们想输入一些特定的代码或者配置，但因为格式问题，无法被正确解析。\n\n另外，用户可能是想测试我的反应，或者他们可能遇到了某种技术问题，导致输入出现了错误。比如，他们可能在使用某种编程语言或者脚本时，变量名写错了，导致程序无法运行。也有可能是在使用某种模板引擎时，语法错误导致内容无法正确渲染。\n\n考虑到这些可能性，我应该如何回应呢？首先，我需要礼貌地指出用户的输入可能有问题，并询问他们是否需要帮助解决某个特定的问题。这样既不会让用户感到被忽视，又能引导他们提供更多的信息，以便我能更好地帮助他们。\n\n此外，我还应该考虑到用户可能的技术背景。如果他们是在处理技术问题，比如编程、脚本或者模板引擎，我应该准备好提供相关的帮助，比如调试代码、解释语法错误，或者提供正确的使用方法。\n\n总的来说，我需要做的是：\n1. 确认用户的输入是否有误。\n2. 礼貌地询问用户是否需要帮助解决某个问题。\n3. 准备好根据用户的进一步信息提供相应的帮助。\n\n这样不仅能帮助用户解决问题，还能展示我的专业性和友好性，让用户感到被重视和支持。\n</think>\n\n您好！看起来您的输入可能有误，或者您可能在尝试表达某个特定的问题。如果您有任何问题或需要帮助，请随时告诉我，我会尽力为您提供帮助。', 'tool_calls': None}, 'finish_reason': 'stop', 'logprobs': None}], 'usage': {'prompt_tokens': 13, 'completion_tokens': 490, 'total_tokens': 503}, 'id': 'e7fac57a14f644c1a396a887584cf3ee', 'object': 'chat.completion', 'created': 1752127786} - [system]
2025-07-10 06:51:14,686 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:51:14,687 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:51:14,689 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:51:14,736 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:51:14,737 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=2a64a1bbd27d4d4fa7832bb0a00f914f - [system]
2025-07-10 06:51:14,738 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=2a64a1bbd27d4d4fa7832bb0a00f914f - [system]
2025-07-10 06:51:14,745 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=2a64a1bbd27d4d4fa7832bb0a00f914f "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:14,748 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:51:14,794 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:51:14,796 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:51:14,800 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=2a64a1bbd27d4d4fa7832bb0a00f914f "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:14,805 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=2a64a1bbd27d4d4fa7832bb0a00f914f "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:29,028 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:51:31,747 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:51:31,747 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:51:31,748 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:51:31,774 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:51:31,775 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=ab225407c07b40748125b8a395736825 - [system]
2025-07-10 06:51:31,776 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=ab225407c07b40748125b8a395736825 - [system]
2025-07-10 06:51:31,782 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ab225407c07b40748125b8a395736825 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:31,784 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:51:31,821 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:51:31,822 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:51:31,826 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ab225407c07b40748125b8a395736825 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:31,830 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=ab225407c07b40748125b8a395736825 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:41,453 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:51:43,591 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:51:43,592 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:51:43,593 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:51:43,619 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:51:43,620 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=944754ae1d574aa3ab059b2329bb5c73 - [system]
2025-07-10 06:51:43,620 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=944754ae1d574aa3ab059b2329bb5c73 - [system]
2025-07-10 06:51:43,626 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=944754ae1d574aa3ab059b2329bb5c73 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:43,628 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:51:43,631 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:51:43,632 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:51:43,636 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=944754ae1d574aa3ab059b2329bb5c73 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:43,640 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=944754ae1d574aa3ab059b2329bb5c73 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:53,965 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:51:57,117 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:51:57,118 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:51:57,119 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:51:57,144 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:51:57,146 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=4fac9eadd5584e8c9e7f0e2195e62445 - [system]
2025-07-10 06:51:57,146 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=4fac9eadd5584e8c9e7f0e2195e62445 - [system]
2025-07-10 06:51:57,152 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4fac9eadd5584e8c9e7f0e2195e62445 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:57,154 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:51:57,159 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:51:57,160 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:51:57,164 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4fac9eadd5584e8c9e7f0e2195e62445 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:51:57,169 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4fac9eadd5584e8c9e7f0e2195e62445 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:08,483 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:52:11,378 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:52:11,379 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:52:11,379 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:52:11,405 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:52:11,406 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=0318b16c612540c7a4103f435fc91d1b - [system]
2025-07-10 06:52:11,407 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=0318b16c612540c7a4103f435fc91d1b - [system]
2025-07-10 06:52:11,412 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0318b16c612540c7a4103f435fc91d1b "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:11,414 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:52:11,417 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:52:11,418 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:52:11,423 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0318b16c612540c7a4103f435fc91d1b "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:11,428 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=0318b16c612540c7a4103f435fc91d1b "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:21,720 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:52:24,939 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:52:24,940 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:52:24,940 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:52:24,966 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:52:24,967 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=d80102837e184f81be876ece4b65ecf8 - [system]
2025-07-10 06:52:24,968 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=d80102837e184f81be876ece4b65ecf8 - [system]
2025-07-10 06:52:24,974 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d80102837e184f81be876ece4b65ecf8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:24,977 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:52:24,981 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:52:24,982 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:52:24,987 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d80102837e184f81be876ece4b65ecf8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:24,992 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=d80102837e184f81be876ece4b65ecf8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:35,268 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:52:37,914 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:52:37,914 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:52:37,915 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:52:37,941 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:52:37,942 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=e6ddd60072a04e618d159f3c5722ab30 - [system]
2025-07-10 06:52:37,942 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=e6ddd60072a04e618d159f3c5722ab30 - [system]
2025-07-10 06:52:37,948 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e6ddd60072a04e618d159f3c5722ab30 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:37,950 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:52:37,953 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:52:37,954 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:52:37,959 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e6ddd60072a04e618d159f3c5722ab30 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:37,964 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=e6ddd60072a04e618d159f3c5722ab30 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:48,903 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:52:52,142 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:52:52,142 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:52:52,143 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:52:52,157 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:52:52,158 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=6c2dad649803485cb52b403b6c71444a - [system]
2025-07-10 06:52:52,158 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=6c2dad649803485cb52b403b6c71444a - [system]
2025-07-10 06:52:52,161 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6c2dad649803485cb52b403b6c71444a "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:52,162 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:52:52,166 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:52:52,167 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:52:52,169 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6c2dad649803485cb52b403b6c71444a "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:52:52,172 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6c2dad649803485cb52b403b6c71444a "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:03,862 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:53:07,096 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:53:07,097 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:53:07,098 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:53:07,124 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:53:07,125 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=6557ab7855d747699042518f5a202ad8 - [system]
2025-07-10 06:53:07,125 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=6557ab7855d747699042518f5a202ad8 - [system]
2025-07-10 06:53:07,131 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6557ab7855d747699042518f5a202ad8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:07,133 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:53:07,137 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:53:07,137 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:53:07,142 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6557ab7855d747699042518f5a202ad8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:07,146 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=6557ab7855d747699042518f5a202ad8 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:17,569 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:53:20,074 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:53:20,075 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:53:20,076 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:53:20,101 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:53:20,103 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=b6c6ea8c658e4df18551d9320b815bb5 - [system]
2025-07-10 06:53:20,103 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=b6c6ea8c658e4df18551d9320b815bb5 - [system]
2025-07-10 06:53:20,109 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6c6ea8c658e4df18551d9320b815bb5 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:20,111 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:53:20,113 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:53:20,121 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:53:20,124 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6c6ea8c658e4df18551d9320b815bb5 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:20,129 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=b6c6ea8c658e4df18551d9320b815bb5 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:30,800 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:53:34,051 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:53:34,051 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:53:34,052 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:53:34,078 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:53:34,079 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=1b4525e706144c41bde5715e93cc90ff - [system]
2025-07-10 06:53:34,079 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=1b4525e706144c41bde5715e93cc90ff - [system]
2025-07-10 06:53:34,085 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=1b4525e706144c41bde5715e93cc90ff "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:34,087 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:53:34,089 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:53:34,090 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:53:34,095 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=1b4525e706144c41bde5715e93cc90ff "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:34,100 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=1b4525e706144c41bde5715e93cc90ff "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:44,034 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:53:47,001 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:53:47,002 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:53:47,002 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:53:47,021 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:53:47,022 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=3c5d5422fe7a47e69f7f8446cdb0d4f0 - [system]
2025-07-10 06:53:47,023 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=3c5d5422fe7a47e69f7f8446cdb0d4f0 - [system]
2025-07-10 06:53:47,027 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=3c5d5422fe7a47e69f7f8446cdb0d4f0 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:47,029 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:53:47,031 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:53:47,032 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:53:47,055 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=3c5d5422fe7a47e69f7f8446cdb0d4f0 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:47,059 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=3c5d5422fe7a47e69f7f8446cdb0d4f0 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:53:59,765 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:54:03,137 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:54:03,137 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:54:03,138 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:54:03,164 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:54:03,165 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=50b9fb05c24945559a5a6f50ab9a657c - [system]
2025-07-10 06:54:03,166 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=50b9fb05c24945559a5a6f50ab9a657c - [system]
2025-07-10 06:54:03,172 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=50b9fb05c24945559a5a6f50ab9a657c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:03,174 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:54:03,177 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:54:03,178 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:54:03,183 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=50b9fb05c24945559a5a6f50ab9a657c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:03,188 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=50b9fb05c24945559a5a6f50ab9a657c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:13,359 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:54:15,887 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:54:15,888 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:54:15,889 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:54:15,922 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:54:15,924 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=4e2047e8b69840b8a45fa9353fb9d327 - [system]
2025-07-10 06:54:15,924 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=4e2047e8b69840b8a45fa9353fb9d327 - [system]
2025-07-10 06:54:15,931 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4e2047e8b69840b8a45fa9353fb9d327 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:15,933 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:54:15,937 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:54:15,938 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:54:15,942 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4e2047e8b69840b8a45fa9353fb9d327 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:15,948 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=4e2047e8b69840b8a45fa9353fb9d327 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:26,180 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:54:28,912 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:54:28,912 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:54:28,915 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:54:28,932 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:54:28,932 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=5164bff1d7fa45fdaec91d381a03d9e1 - [system]
2025-07-10 06:54:28,933 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=5164bff1d7fa45fdaec91d381a03d9e1 - [system]
2025-07-10 06:54:28,936 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5164bff1d7fa45fdaec91d381a03d9e1 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:28,938 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:54:28,940 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:54:28,941 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:54:28,944 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5164bff1d7fa45fdaec91d381a03d9e1 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:28,948 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5164bff1d7fa45fdaec91d381a03d9e1 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:38,432 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:54:40,900 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:54:40,900 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:54:40,901 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:54:40,927 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:54:40,928 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=59af3ecf77a84111bf3aa18b31e0a0c6 - [system]
2025-07-10 06:54:40,929 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=59af3ecf77a84111bf3aa18b31e0a0c6 - [system]
2025-07-10 06:54:40,934 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=59af3ecf77a84111bf3aa18b31e0a0c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:40,936 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:54:40,940 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:54:40,944 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:54:40,947 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=59af3ecf77a84111bf3aa18b31e0a0c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:40,952 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=59af3ecf77a84111bf3aa18b31e0a0c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:51,181 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:54:53,982 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:54:53,983 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:54:53,983 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:54:54,009 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:54:54,010 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=5e78cdbf26954139bab4836cde5a6a3d - [system]
2025-07-10 06:54:54,010 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=5e78cdbf26954139bab4836cde5a6a3d - [system]
2025-07-10 06:54:54,016 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5e78cdbf26954139bab4836cde5a6a3d "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:54,018 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:54:54,021 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:54:54,027 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:54:54,031 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5e78cdbf26954139bab4836cde5a6a3d "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:54:54,036 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=5e78cdbf26954139bab4836cde5a6a3d "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:03,885 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:55:07,046 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:55:07,047 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:55:07,048 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:55:07,092 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:55:07,093 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=93fb99d9fed04fc0b503dfa47927b53c - [system]
2025-07-10 06:55:07,093 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=93fb99d9fed04fc0b503dfa47927b53c - [system]
2025-07-10 06:55:07,099 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=93fb99d9fed04fc0b503dfa47927b53c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:07,101 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:55:07,103 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:55:07,104 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:55:07,108 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=93fb99d9fed04fc0b503dfa47927b53c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:07,113 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=93fb99d9fed04fc0b503dfa47927b53c "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:17,341 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
2025-07-10 06:55:19,979 - app - INFO - 【Controller】【AI网关】请求地址:gateway/callMcpTool, 参数:{'serverUrl': 'http://172.1.0.154:9082/sse', 'toolId': '1690572800', 'toolArgs': {'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}}} - [system]
2025-07-10 06:55:19,979 - app - INFO - 【Controller】【AI网关-callMcpTool】当前线程名称:ThreadPoolExecutor-1_0 - [system]
2025-07-10 06:55:19,980 - mcp.client.sse - INFO - Connecting to SSE endpoint: http://172.1.0.154:9082/sse - [system]
2025-07-10 06:55:20,006 - httpx - INFO - HTTP Request: GET http://172.1.0.154:9082/sse "HTTP/1.1 200 OK" - [system]
2025-07-10 06:55:20,007 - mcp.client.sse - INFO - Received endpoint URL: http://172.1.0.154:9082/messages/?session_id=44ba86ecc61740b397f96610a2ee66c6 - [system]
2025-07-10 06:55:20,007 - mcp.client.sse - INFO - Starting post writer with endpoint URL: http://172.1.0.154:9082/messages/?session_id=44ba86ecc61740b397f96610a2ee66c6 - [system]
2025-07-10 06:55:20,014 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=44ba86ecc61740b397f96610a2ee66c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:20,020 - app - INFO - 【Dao】【获取工具列表】 - [system]
2025-07-10 06:55:20,028 - app - INFO - 【Dao】【获取工具列表】结果:[('0076761088', 'file_format_tool', 'file_format_tool', '1'), ('0228690944', 'know_file_sql_get', 'know_file_sql_get', '1'), ('0992138240', 'file_rec_handle', 'file_rec_handle', '1'), ('1384469504', 'know_file_parse', 'know_file_parse', '1'), ('1690572800', 'ASR', 'asr_function', '1'), ('1857247232', 'TTS', 'tts_function', '0'), ('1880028160', 'searchbcinternet', 'search_bc_internet', '0'), ('1960825856', 'know_pending_file', 'know_pending_file', '1'), ('2677584896', 'know_sftp_tool', 'know_sftp_tool', '1'), ('3584392192', 'file_layout_handle', 'file_layout_handle', '1'), ('3937511424', 'Textify', 'parse_file', '1'), ('4346774528', 'know_file_split', 'know_file_split', '1'), ('5641848832', 'sql_executor', 'sql_executor', '1'), ('5732357120', 'codeexecutor', 'code_executor', '1'), ('6189350912', 'searchbinginternet', 'search_bing_internet', '0'), ('6402688000', 'extractfaq', 'extract_faq', '1'), ('6800135168', 'know_file_sql_exec', 'know_file_sql_exec', '1'), ('7080353792', 'readminiofiledata', 'read_minio_file_data', '1'), ('7163485184', 'productmatrix', 'product_matrix', '1'), ('7751298048', 'know_space_info', 'know_space_info', '1'), ('8137337856', 'OCR', 'ocr_parse_file', '0'), ('8559431680', 'questionrewrite', 'question_rewrite', '1'), ('8657878016', 'file_clean_tool', 'file_clean_tool', '1')] - [system]
2025-07-10 06:55:20,031 - app - INFO - 【Service】【MCP调用】tool:asr_function,输入:{'asrPath': 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1e256544-17a0-4a78-99c7-5c235ff0ed6f/recording.wav', 'modelType': 'whisper-large-v3', 'url': ' http://***********:9020/v1', 'headers': {'contentType': 'application/json; charset=UTF-8', 'Authorization': ''}} - [system]
2025-07-10 06:55:20,041 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=44ba86ecc61740b397f96610a2ee66c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:20,046 - httpx - INFO - HTTP Request: POST http://172.1.0.154:9082/messages/?session_id=44ba86ecc61740b397f96610a2ee66c6 "HTTP/1.1 202 Accepted" - [system]
2025-07-10 06:55:30,278 - app - INFO - 【Service】【MCP调用】结果:meta=None content=[TextContent(type='text', text='{\n    "retMsg": "success",\n    "retCode": 0,\n    "retMap": {\n        "text": " 1, 2, 3, 4, 5, 6, 7, 8, 9, 10."\n    }\n}', annotations=None)] isError=False - [system]
