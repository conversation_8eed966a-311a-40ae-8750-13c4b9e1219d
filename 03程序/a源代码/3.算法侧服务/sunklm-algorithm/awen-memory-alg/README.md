# MemoryService_V1.0 - 记忆服务系统

## 项目概述

MemoryService_V1.0 是一个基于多数据库的智能记忆服务系统，支持问答数据的存储、检索和管理。系统采用混合检索架构，结合 Elasticsearch 的文本检索能力和 Milvus 的向量检索能力，为用户提供高效、准确的记忆检索服务。

### 核心特性

- **混合检索架构**: 结合 ES 文本检索和 Milvus 向量检索
- **多数据库支持**: MySQL、Elasticsearch、Milvus
- **智能记忆管理**: 支持记忆存储、检索、评分和统计
- **RESTful API**: 提供标准化的 HTTP 接口
- **Docker 部署**: 支持容器化部署
- **完整的初始化流程**: 自动创建数据库表结构和索引

## 系统架构

```
MemoryService_V1.0/
├── common/                 # 公共模块
│   ├── conf/              # 配置文件
│   │   ├── db_config.py   # 数据库配置
│   │   └── server_config.py # 服务器配置
│   └── utils/             # 工具类
├── mcpProtocol/           # MCP协议实现
│   └── service/           # 核心服务
│       └── MemoryService.py # 记忆服务主类
├── init/                  # 初始化脚本
│   └── init_all.py        # 数据库初始化
├── logs/                  # 日志目录
├── model/                 # 模型文件
├── resources/             # 资源文件
├── run_api.py             # API服务入口
├── run.sh                 # 服务管理脚本
├── test_memory_api.py     # API测试脚本
├── requirements.txt       # Python依赖
├── Dockerfile             # Docker镜像配置
└── .dockerignore          # Docker忽略文件
```

## 技术栈

- **后端框架**: Flask 2.0.1
- **数据库**: MySQL 8.0+, Elasticsearch 7.17.0, Milvus 2.2.12
- **向量化**: sentence-transformers 2.2.2
- **机器学习**: scikit-learn 1.0.2, torch 1.13.1
- **日志**: loguru 0.6.0
- **容器化**: Docker

## 快速开始

### 环境要求

- Python 3.10+
- Docker & Docker Compose
- MySQL 8.0+
- Elasticsearch 7.17.0+
- Milvus 2.2.12+

### 1. 本地开发环境部署

#### 1.1 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

#### 1.2 安装依赖

```bash
pip install -r requirements.txt
```

#### 1.3 配置数据库连接

编辑 `common/conf/db_config.py` 文件，修改数据库连接配置：

```python
MYSQL_CONFIG = {
    "host": "your_mysql_host",
    "port": "3306",
    "user": "your_username",
    "password": "your_password",
    "db": "your_database",
    "charset": "utf8mb4"
}

ES_CONFIG = {
    "host": "your_es_host",
    "port": "9200",
    "retry_on_timeout": True,
    "request_timeout": 30,
    "max_retries": 3,
    "index_name": "awen_memory"
}

MILVUS_CONFIG = {
    "host": "your_milvus_host",
    "port": "19530",
    "collection_name": "awen_memory"
}
```

#### 1.4 初始化数据库

```bash
python init/init_all.py
```

#### 1.5 启动服务

```bash
# 使用管理脚本
./run.sh start

# 或直接启动
python run_api.py
```

### 2. Docker 部署

#### 2.1 构建 Docker 镜像

```bash
# 构建镜像
docker build -t memory-service:v1.0 .

# 查看构建的镜像
docker images | grep memory-service
```

#### 2.2 运行容器

```bash
# 基本运行
docker run -d --name memory-service -p 8848:8848 -v $(pwd)/logs:/app/logs memory-service:v1.0

# 带环境变量的运行
docker run -d \
  --name memory-service \
  -p 8848:8848 \
  -v $(pwd)/logs:/app/logs \
  -e MYSQL_HOST=your_mysql_host \
  -e ES_HOST=your_es_host \
  -e MILVUS_HOST=your_milvus_host \
  memory-service:v1.0
```

```bash
# start container
docker run -d --rm -p 8848:8848 --name memory-service-live memory-service:fixed
# use shell inside this container
docker exec -it memory-service-live /bin/bash
# run the debug with shell
docker run -it --rm -p 8848:8848 --name memory-service-debug memory-service:fixed /bin/bash
```

#### 2.3 使用 Docker Compose（推荐）

创建 `docker-compose.yml` 文件：

```yaml
version: "3.8"

services:
  memory-service:
    build: .
    container_name: memory-service
    ports:
      - "8848:8848"
    volumes:
      - ./logs:/app/logs
      - ./resources:/app/resources
    environment:
      - MYSQL_HOST=mysql
      - ES_HOST=elasticsearch
      - MILVUS_HOST=milvus-standalone
    depends_on:
      - mysql
      - elasticsearch
      - milvus-standalone
    restart: unless-stopped
    networks:
      - memory-network

  mysql:
    image: mysql:8.0
    container_name: memory-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: AWEN_PRIMARY
      MYSQL_USER: SUNKLM
      MYSQL_PASSWORD: Sunyard@123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - memory-network

  elasticsearch:
    image: elasticsearch:7.17.0
    container_name: memory-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - memory-network

  milvus-standalone:
    image: milvusdb/milvus:v2.2.12
    container_name: memory-milvus
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_USE_EMBED: "true"
      ETCD_DATA_DIR: "/var/lib/milvus/etcd"
      ETCD_CONFIG_PATH: "/milvus/configs/etcd.yaml"
      MINIO_ADDRESS: "localhost:9000"
    ports:
      - "19530:19530"
      - "9091:9091"
    volumes:
      - milvus_data:/var/lib/milvus
    networks:
      - memory-network

volumes:
  mysql_data:
  es_data:
  milvus_data:

networks:
  memory-network:
    driver: bridge
```

启动服务：

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f memory-service

# 停止服务
docker-compose down
```

## API 接口文档

### 1. 记忆存储接口

**接口地址**: `POST /api/memory/save`

**请求参数**:

```json
{
  "question": "用户问题",
  "answer": "AI回答",
  "user_id": "用户ID",
  "agent_id": "智能体ID",
  "session_id": "会话ID"
}
```

**响应示例**:

```json
{
  "status": "success",
  "data": "memory_id_12345"
}
```

### 2. 记忆检索接口

**接口地址**: `POST /api/memory/recall`

**请求参数**:

```json
{
  "query": "检索查询",
  "model_id": "MEMO_21918",
  "user_id": "用户ID",
  "agent_id": "智能体ID",
  "session_id": "会话ID",
  "ast_user_id": ["关联用户ID"],
  "ast_agent_id": ["关联智能体ID"],
  "ast_session_id": ["关联会话ID"]
}
```

**响应示例**:

```json
{
  "status": "success",
  "data": {
    "memory_id": "memory_id_12345",
    "question": "原始问题",
    "answer": "原始回答",
    "score": 0.95,
    "source": "hybrid"
  }
}
```

## 服务管理

### 使用管理脚本

```bash
# 启动服务
./run.sh start

# 停止服务
./run.sh stop

# 重启服务
./run.sh restart

# 查看服务状态
./run.sh status

# 运行API测试
./run.sh test

# 初始化数据库
./run.sh init

# 查看实时日志
./run.sh logs
```

### Docker 容器管理

```bash
# 启动容器
docker start memory-service

# 停止容器
docker stop memory-service

# 重启容器
docker restart memory-service

# 查看容器状态
docker ps -a | grep memory-service

# 查看容器日志
docker logs -f memory-service

# 进入容器
docker exec -it memory-service bash
```

## 配置说明

### 数据库配置

在 `common/conf/db_config.py` 中配置数据库连接：

- **MySQL**: 存储记忆统计信息和模型参数
- **Elasticsearch**: 存储文本索引，支持全文检索
- **Milvus**: 存储向量索引，支持语义检索

### 服务配置

在 `common/conf/server_config.py` 中配置服务参数：

- 服务端口
- 日志级别
- 超时设置
- 缓存配置

## 监控和日志

### 日志文件

- 应用日志: `logs/nohup.out`
- 错误日志: `logs/error.log`
- 访问日志: `logs/access.log`

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8848/api/memory/recall

# 检查数据库连接
python -c "from common.utils.mysql_conn import MySQLClient; MySQLClient().get_connect()"
```

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查数据库服务是否启动
   - 验证连接配置是否正确
   - 确认网络连通性

2. **向量化服务加载失败**

   - 检查模型文件是否存在
   - 确认内存是否充足
   - 验证依赖包版本

3. **API 响应超时**
   - 检查数据库性能
   - 调整超时配置
   - 优化查询参数

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
python run_api.py

# 查看详细错误信息
tail -f logs/nohup.out

# 测试API接口存储与查询功能
python test_memory_api.py
```
