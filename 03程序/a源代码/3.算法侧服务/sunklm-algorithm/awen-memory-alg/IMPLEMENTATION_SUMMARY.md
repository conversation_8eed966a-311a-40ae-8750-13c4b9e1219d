# 记忆检索系统实现总结

## 概述

本次实现了一个完整的短期和长期记忆检索系统，支持通过不同的模型 ID 实现不同的记忆检索模式，并且短期记忆和长期记忆使用不同的数据库表/集合，实现数据隔离和性能优化。

## 实现的功能

### 1. 多模型记忆检索

- **默认模型 (MEMO_21918)**: 同时启用短期和长期记忆检索
- **短期记忆模型 (MEMO_SHORT_TERM)**: 只启用短期记忆检索
- **长期记忆模型 (MEMO_LONG_TERM)**: 只启用长期记忆检索

### 2. 数据库分离架构

- **短期记忆数据库**:
  - ES 索引: `memo`
  - Milvus 集合: `memo`
  - MySQL 表: `memo_storage_stas_info`
- **长期记忆数据库**:
  - ES 索引: `memo_long`
  - Milvus 集合: `memo_long`
  - MySQL 表: `memo_storage_stas_info_long`
- **共享数据库**:
  - 模型参数表: `memo_retrieval_model_parms_info`

### 3. 动态数据库切换

- 根据模型参数中的 `SHORT_TERM` 和 `LONG_TERM` 配置自动选择对应的数据库
- 支持在检索过程中动态切换数据库
- 确保不同类型记忆数据的完全隔离

## 核心组件

### 1. 配置管理 (`common/conf/db_config.py`)

- `MEMORY_DB_MAPPING`: 定义不同记忆类型的数据库映射
- `get_memory_db_config()`: 根据记忆类型获取对应的数据库配置

### 2. 初始化脚本 (`init/init_all.py`)

- 创建短期和长期记忆的数据库表/集合
- 插入三种模型的参数配置
- 支持自动检测和跳过已存在的数据库对象

### 3. 记忆存储服务 (`mcpProtocol/service/MemoryService.py`)

- `MemorySave`: 支持指定记忆类型进行数据存储
- `MemoryRecall`: 支持指定记忆类型进行数据检索
- 自动根据模型参数切换数据库

### 4. 示例代码

- `examples/short_long_term_example.py`: 基础使用示例
- `examples/short_long_db_separation_example.py`: 数据库分离示例

## 技术特点

### 1. 数据隔离

- 短期和长期记忆数据完全隔离，互不影响
- 可以独立优化不同记忆类型的存储和检索性能
- 便于独立管理和维护

### 2. 性能优化

- 短期记忆数据库可以针对高频访问进行优化
- 长期记忆数据库可以针对大容量存储进行优化
- 减少单个数据库的负载压力

### 3. 扩展性

- 可以轻松添加更多记忆类型和对应的数据库配置
- 支持不同记忆类型使用不同的存储策略
- 便于后续功能扩展

### 4. 灵活性

- 可以根据业务需求调整数据库配置
- 支持动态切换数据库进行检索
- 便于 A/B 测试和性能调优

## 使用流程

### 1. 初始化数据库

```bash
python init/init_all.py
```

### 2. 存储记忆数据

```python
# 存储短期记忆
short_term_save = MemorySave(db_config, memory_type="SHORT_TERM")
memory_id = short_term_save.memory_save(question, answer, user_id, agent_id, session_id)

# 存储长期记忆
long_term_save = MemorySave(db_config, memory_type="LONG_TERM")
memory_id = long_term_save.memory_save(question, answer, user_id, agent_id, session_id)
```

### 3. 检索记忆数据

```python
# 检索短期记忆
short_term_recall = MemoryRecall(db_config, memory_type="SHORT_TERM")
template, results = short_term_recall.memory_recall(query, model_id="MEMO_SHORT_TERM", ...)

# 检索长期记忆
long_term_recall = MemoryRecall(db_config, memory_type="LONG_TERM")
template, results = long_term_recall.memory_recall(query, model_id="MEMO_LONG_TERM", ...)

# 混合检索（自动切换数据库）
mixed_recall = MemoryRecall(db_config, memory_type="SHORT_TERM")
template, results = mixed_recall.memory_recall(query, model_id="MEMO_21918", ...)
```

## 配置说明

### 模型参数配置

模型参数存储在数据库的 `memo_retrieval_model_parms_info` 表中，包含：

- `SHORT_TERM`: 是否启用短期记忆检索
- `LONG_TERM`: 是否启用长期记忆检索
- 各种检索参数（TOP_N、RETRIEVAL_TYPE、时间因子、权重因子、评分因子等）

### 数据库映射配置

通过 `MEMORY_DB_MAPPING` 配置不同记忆类型的数据库映射：

```python
MEMORY_DB_MAPPING = {
    "SHORT_TERM": {
        "ES": {"index_name": "memo"},
        "MILVUS": {"collection_name": "memo"},
        "MYSQL": {"stats_table": "memo_storage_stas_info"}
    },
    "LONG_TERM": {
        "ES": {"index_name": "memo_long"},
        "MILVUS": {"collection_name": "memo_long"},
        "MYSQL": {"stats_table": "memo_storage_stas_info_long"}
    }
}
```

## 优势

### 1. 架构优势

- **模块化设计**: 各组件职责清晰，便于维护和扩展
- **配置驱动**: 通过配置文件管理各种参数，便于调整
- **数据库分离**: 不同类型记忆使用不同数据库，提高性能

### 2. 功能优势

- **多模式支持**: 支持短期、长期和混合记忆检索
- **动态切换**: 根据模型参数自动切换数据库
- **数据隔离**: 不同类型记忆数据完全隔离

### 3. 性能优势

- **负载分散**: 不同类型记忆的负载分散到不同数据库
- **优化空间**: 可以针对不同记忆类型进行专门的性能优化
- **扩展性**: 支持水平扩展和垂直扩展

## 扩展方向

### 1. 更多记忆类型

- 中期记忆
- 专业记忆
- 情感记忆
- 场景记忆

### 2. 更复杂的检索策略

- 多级检索
- 智能路由
- 自适应参数调整

### 3. 性能优化

- 缓存机制
- 异步处理
- 批量操作

### 4. 监控和管理

- 性能监控
- 数据统计
- 自动化管理

## 总结

本次实现了一个功能完整、架构清晰的短期和长期记忆检索系统。通过数据库分离、动态切换、配置驱动等技术手段，实现了高性能、高可用、易扩展的记忆检索功能。系统支持多种检索模式，能够满足不同场景的需求，为后续的功能扩展和性能优化奠定了良好的基础。
