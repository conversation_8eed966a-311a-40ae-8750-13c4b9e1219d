# -*- coding: utf-8 -*-
"""
数据库初始化脚本：支持两种模式
MODE = 'fresh'  # 删除重建所有结构
MODE = 'safe'   # 只创建不存在的结构
"""
import os
import sys
import mysql.connector
from pymilvus import utility, CollectionSchema, Collection

# ====== 配置模式 ======
MODE = 'fresh'  # 'fresh' 或 'safe'

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from common.utils.milvus_conn import MilvusClient
from common.utils.es_conn import EsClient
from common.conf.db_config import db_config, es_index_parm, field_mapping

# ========== MySQL 表结构 ==========
MYSQL_TABLES = {
    "memo_storage_stas_info": '''
        CREATE TABLE IF NOT EXISTS memo_storage_stas_info (
            memory_id VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '记忆ID',
            recall_stas INT(11) NULL DEFAULT NULL COMMENT '记忆召回次数',
            recall_stas_txt INT(11) NULL DEFAULT NULL COMMENT '文本召回次数',
            recall_stas_vct INT(11) NULL DEFAULT NULL COMMENT '向量召回次数',
            recall_stas_mlt INT(11) NULL DEFAULT NULL COMMENT '混合召回次数',
            create_time DATETIME NULL DEFAULT NULL,
            update_time DATETIME NULL DEFAULT NULL,
            PRIMARY KEY (memory_id) USING BTREE 
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '短期记忆检索统计表' ROW_FORMAT = DYNAMIC;
    ''',
    "memo_storage_stas_info_long": '''
        CREATE TABLE IF NOT EXISTS memo_storage_stas_info_long (
            memory_id VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '记忆ID',
            recall_stas INT(11) NULL DEFAULT NULL COMMENT '记忆召回次数',
            recall_stas_txt INT(11) NULL DEFAULT NULL COMMENT '文本召回次数',
            recall_stas_vct INT(11) NULL DEFAULT NULL COMMENT '向量召回次数',
            recall_stas_mlt INT(11) NULL DEFAULT NULL COMMENT '混合召回次数',
            create_time DATETIME NULL DEFAULT NULL,
            update_time DATETIME NULL DEFAULT NULL,
            PRIMARY KEY (memory_id) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '长期记忆检索统计表' ROW_FORMAT = DYNAMIC;
    ''',
    "memo_retrieval_model_parms_info": '''
        CREATE TABLE IF NOT EXISTS memo_retrieval_model_parms_info (
            model_id VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模型ID',
            create_time DATETIME NULL DEFAULT NULL COMMENT '记忆体创建时间',
            update_time DATETIME NULL DEFAULT NULL COMMENT '记忆体更新时间',
            parms TEXT NULL DEFAULT NULL COMMENT '模型参数',
            PRIMARY KEY (model_id) USING BTREE,
            INDEX create_time(create_time) USING BTREE,
            INDEX update_time(update_time) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '检索模型参数表' ROW_FORMAT = DYNAMIC;
    '''
}

# 默认插入的模型参数
DEFAULT_MODEL_ID = "MEMO_21918"
DEFAULT_MODEL_PARMS = '{"SHORT_TERM": true, "SHORT_TERM_MODEL": {"TOP_N": 5, "RETRIEVAL_TYPE": "MLT", "TIME_FACTOR": true, "TIME_FACTOR_PARMS": {"ORIGIN": "NOW", "SCALE": "30D", "DECAY": 0.5, "FACTOR": 1.0}, "WEIGHT_FACTOR": true, "WEIGHT_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}, "SCORE_FACTOR": true, "SCORE_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}}, "LONG_TERM": true, "LONG_TERM_MODEL": {"TOP_N": 5, "RETRIEVAL_TYPE": "MLT", "TIME_FACTOR": true, "TIME_FACTOR_PARMS": {"ORIGIN": "NOW", "SCALE": "30D", "DECAY": 0.5, "FACTOR": 1.0}, "WEIGHT_FACTOR": true, "WEIGHT_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}, "SCORE_FACTOR": true, "SCORE_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}}}'
SHORT_TERM_MODEL_ID = "MEMO_SHORT_TERM"
SHORT_TERM_MODEL_PARMS = '{"SHORT_TERM": true, "SHORT_TERM_MODEL": {"TOP_N": 5, "RETRIEVAL_TYPE": "MLT", "TIME_FACTOR": true, "TIME_FACTOR_PARMS": {"ORIGIN": "NOW", "SCALE": "30D", "DECAY": 0.5, "FACTOR": 1.0}, "WEIGHT_FACTOR": true, "WEIGHT_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}, "SCORE_FACTOR": true, "SCORE_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}}, "LONG_TERM": false}'
LONG_TERM_MODEL_ID = "MEMO_LONG_TERM"
LONG_TERM_MODEL_PARMS = '{"SHORT_TERM": false, "LONG_TERM": true, "LONG_TERM_MODEL": {"TOP_N": 5, "RETRIEVAL_TYPE": "MLT", "TIME_FACTOR": true, "TIME_FACTOR_PARMS": {"ORIGIN": "NOW", "SCALE": "30D", "DECAY": 0.5, "FACTOR": 1.0}, "WEIGHT_FACTOR": true, "WEIGHT_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}, "SCORE_FACTOR": true, "SCORE_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 2.0}}}'
DEFAULT_MODEL_CREATE_TIME = "2025-06-19 17:44:54"
DEFAULT_MODEL_UPDATE_TIME = "2025-06-19 17:44:54"

# ========== 初始化 MySQL ==========
def init_mysql_tables():
    print("\n初始化 MySQL 表...")
    try:
        mysql_conf = db_config['MYSQL']
        conn = mysql.connector.connect(
            host=mysql_conf['host'],
            port=mysql_conf['port'],
            user=mysql_conf['user'],
            password=mysql_conf['password'],
            database=mysql_conf['db']
        )
        cursor = conn.cursor()
        for table, ddl in MYSQL_TABLES.items():
            print(f"正在创建表: {table}")
            cursor.execute(ddl)
        # 检查并插入模型参数
        model_configs = [
            (DEFAULT_MODEL_ID, DEFAULT_MODEL_PARMS, "默认模型（短期+长期记忆）"),
            (SHORT_TERM_MODEL_ID, SHORT_TERM_MODEL_PARMS, "短期记忆模型"),
            (LONG_TERM_MODEL_ID, LONG_TERM_MODEL_PARMS, "长期记忆模型")
        ]
        for model_id, model_params, description in model_configs:
            cursor.execute(f"SELECT COUNT(*) FROM memo_retrieval_model_parms_info WHERE model_id = %s", (model_id,))
            count = cursor.fetchone()[0]
            if count == 0:
                print(f"插入{description}参数: {model_id}")
                insert_sql = f"""
                INSERT INTO memo_retrieval_model_parms_info (model_id, create_time, update_time, parms)
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(insert_sql, (model_id, DEFAULT_MODEL_CREATE_TIME, DEFAULT_MODEL_UPDATE_TIME, model_params))
            else:
                print(f"{description}参数已存在: {model_id}")
        conn.commit()
        cursor.close()
        conn.close()
        print("MySQL 表初始化完成！")
    except Exception as e:
        print(f"MySQL 表初始化失败: {e}")
        raise

# ========== 初始化 Milvus ==========
def init_milvus_collections(mode='fresh'):
    print("\n初始化 Milvus 集合...")
    milvus_conf = db_config['MILVUS']
    mil = MilvusClient(
        host=milvus_conf['host'],
        port=milvus_conf['port'],
        db_name=milvus_conf['db_name']
    )
    qa_schema = field_mapping['QA_RECORDS']
    for col in [milvus_conf['collection_name'], f"{milvus_conf['collection_name']}_long"]:
        if utility.has_collection(col):
            if mode == 'fresh':
                print(f"删除 Milvus 集合: {col}")
                collection = Collection(col)
                collection.drop()
                print(f"重建 Milvus 集合: {col}")
                mil.do_create_col(col, description=f"{col}集合", colType="QA_RECORDS")
            else:
                print(f"Milvus 集合已存在: {col}，跳过创建")
        else:
            print(f"创建 Milvus 集合: {col}")
            mil.do_create_col(col, description=f"{col}集合", colType="QA_RECORDS")
    print("Milvus 集合初始化完成！")

# ========== 初始化 Elasticsearch ==========
def init_es_indices(mode='fresh'):
    print("\n初始化 Elasticsearch 索引...")
    es_conf = db_config['ES']
    from elasticsearch import Elasticsearch
    es = Elasticsearch(
        hosts=[f"http://{es_conf['host']}:{es_conf['port']}"] ,
        request_timeout=es_conf['request_timeout'],
        max_retries=es_conf['max_retries'],
        retry_on_timeout=es_conf['retry_on_timeout']
    )
    for idx in [es_conf['index_name'], f"{es_conf['index_name']}_long"]:
        if es.indices.exists(index=idx):
            if mode == 'fresh':
                print(f"删除 Elasticsearch 索引: {idx}")
                es.indices.delete(index=idx)
                print(f"重建 Elasticsearch 索引: {idx}")
                es_index_parm['mappings']['properties']['memory_id'] = {"type": "keyword"}
                es.indices.create(index=idx, body=es_index_parm)
            else:
                print(f"Elasticsearch 索引已存在: {idx}，跳过创建")
        else:
            print(f"创建 Elasticsearch 索引: {idx}")
            es_index_parm['mappings']['properties']['memory_id'] = {"type": "keyword"}
            es.indices.create(index=idx, body=es_index_parm)
    print("Elasticsearch 索引初始化完成！")

# ========== 主入口 ==========
def init_databases(mode='fresh'):
    print("="*80)
    print(f"初始化 Milvus、Elasticsearch、MySQL 数据库，模式: {mode}")
    print("="*80)
    init_milvus_collections(mode)
    init_es_indices(mode)
    init_mysql_tables()
    print("\n数据库初始化全部完成！")

if __name__ == "__main__":
    try:
        init_databases(MODE)
    except Exception as e:
        print(f"数据库初始化过程中发生错误: {str(e)}") 