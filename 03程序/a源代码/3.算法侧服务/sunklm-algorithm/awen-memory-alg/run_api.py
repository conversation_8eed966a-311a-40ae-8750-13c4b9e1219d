#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Jun 22 21:19:43 2025

@author: yangzhanda
"""

from flask import Flask, request, jsonify
from mcpProtocol.service.MemoryService import MemorySave, MemoryRecall
from common.conf.db_config import db_config
from common.utils.log_utils import logger

app = Flask(__name__)


# 创建类实例

MemoRecall = MemoryRecall(db_config)


# 将类方法封装为接口
@app.route('/api/memory/recall', methods=['POST'])
def memory_recall_api():
    # 获取请求数据
    data = request.json
    
    # 提取必要参数
    required_params = ['query', 'model_id', 'user_id']
    for param in required_params:
        if param not in data:
            return jsonify({
                "status": "error",
                "message": f"Missing required parameter: {param}"
            }), 400
    
    try:
        # 调用记忆检索方法
        result = MemoRecall.memory_recall(
            query=data['query'],
            model_id=data['model_id'],
            user_id=data['user_id'],
            scene_id=data.get('scene_id'),
            session_id=data.get('session_id'),
            query_vector=data.get('query_vector'),
            ast_user_id=data.get('ast_user_id'),
            ast_scene_id=data.get('ast_scene_id'),
            ast_session_id=data.get('ast_session_id')
        )
        return jsonify({
            "status": "success",
            "data": result
        })
    except Exception as e:
        logger.error(f"API error: {str(e)}", exc_info=True)
        # 如果是ES更新相关的错误，返回成功但带警告
        if any(x in str(e) for x in ['update', 'document missing', 'not found', '404']):
            logger.warning(f"ES相关错误被忽略，返回Milvus结果: {str(e)}")
            return jsonify({
                "status": "success",
                "data": None,
                "warning": f"ES相关错误被忽略: {str(e)}"
            })
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


@app.route('/api/memory/save', methods=['POST'])
def memory_save_api():
    # 获取请求数据
    data = request.json
    
    # 提取必要参数
    required_params = ['question', 'answer', 'user_id', 'scene_id', 'session_id']
    for param in required_params:
        if param not in data:
            return jsonify({
                "status": "error",
                "message": f"Missing required parameter: {param}"
            }), 400
    
    try:
        # 调用记忆检索方法
        memory_type = data.get('memory_type', 'SHORT_TERM')
        MemoSave = MemorySave(db_config, memory_type)
        result = MemoSave.memory_save(
            question=data['question'],
            answer=data['answer'],
            user_id=data['user_id'],
            scene_id=data.get('scene_id'),
            session_id=data.get('session_id')
        )
        
        return jsonify({
            "status": "success",
            "data": result
        })
    
    except Exception as e:
        logger.error(f"API error: {str(e)}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=8848)
    
    
"""

SAVE

{
"question"   : "测试问题",
"answer"     : "啦啦啦啦",
"user_id"    : "test001",
"scene_id"   : "test001",
"session_id" : "test001"
}

RECALL

{
"query" : "银行卡密码忘记了怎么重置？",
"model_id" : "MEMO_21918",
"user_id" : "customer01",
"scene_id" : "ZNKF",
"session_id" : "customer01_001",
"ast_user_id"  : ["customer02"],
"ast_scene_id" : ["BKRF"]
}

"""


