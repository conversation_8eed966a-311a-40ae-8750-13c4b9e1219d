# 修复版本冲突的Dockerfile
FROM python:3.10-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 复制requirements文件
COPY requirements.txt .

# 安装依赖，使用清华镜像源
RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 设置权限
RUN chmod +x run.sh

EXPOSE 8848

CMD ["bash", "run.sh", "start"] 