from common.conf.server_config import *
from sentence_transformers import SentenceTransformer
from typing import List
import  numpy as np
import time
from tqdm import tqdm
import torch
from common.utils.log_utils import logger
try:
    import torch_npu
    HAS_NPU = hasattr(torch, 'npu') and torch.npu.is_available()
except ImportError:
    HAS_NPU = False
logger = logger.bind(name="data_process")

_emd_model_dict = {}

class EmbeddingService:
    def __init__(self,model_name: str = EMBEDDING_MODEL_NAME):
        """
        Author：mabenjiang
        Description:
            初始化EmbeddingService实例。
         Args:
            model_name (str): 预训练模型的名称，默认值为EMBEDDING_MODEL_NAME环境变量。
        """
        # self.model = model
        if HAS_NPU:
            device = 'npu'
            self.model = SentenceTransformer(embedding_model_dict[model_name], device=device)
        else:
            self.model = SentenceTransformer(embedding_model_dict[model_name], device=DEVICE if torch.cuda.is_available() else 'cpu')

    def embedding_split_text(self, split_text: List[str]) -> List[np.ndarray]:
        """
        Author：mabenjiang
        Description:
            对一系列文本片段进行嵌入编码。
        Args:
            split_text (List[str]): 包含待编码文本片段的列表。
        Returns:
             List[np.ndarray]: 包含对应文本片段嵌入向量的列表。
        """
        vec_list = []
        total_texts = len(split_text)
        start_time = time.time()
        for idx, key in enumerate(tqdm(split_text, desc="Embedding Progress", ncols=100, bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'), start=1):
            vec = self.model.encode(key, normalize_embeddings=True, show_progress_bar=False)
            vec_list.append(vec)
        end_time = time.time()
        logger.info(f"Embedding completed in {end_time - start_time:.2f} seconds")
        return vec_list
    
    def embedding_query(self, query: str) -> np.ndarray:
        """
        Author：mabenjiang
        Description:
            对单个查询文本进行嵌入编码。
         Args:
              query (str): 待编码的查询文本。
         Returns:
            查询文本的嵌入向量。
        """
        return self.model.encode(query, normalize_embeddings=True)


def normalization(scores):
    """
    分数归一化
    """
    scores = np.array(scores)
    if len(scores) == 0:
        return scores
    min_score = np.min(scores)
    max_score = np.max(scores)
    if max_score == min_score:
        return np.ones_like(scores)
    return (scores - min_score) / (max_score - min_score)