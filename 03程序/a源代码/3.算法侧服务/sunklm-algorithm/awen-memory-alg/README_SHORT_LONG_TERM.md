# 短期和长期记忆检索功能

## 概述

本功能通过不同的模型 ID 实现不同的记忆检索模式，支持短期记忆、长期记忆以及混合记忆检索。短期记忆和长期记忆使用不同的数据库表/集合，实现数据隔离和性能优化。

## 功能特性

- **多种检索模式**: 支持短期记忆、长期记忆和混合记忆检索
- **参数化配置**: 通过数据库中的模型参数配置检索策略
- **数据库分离**: 短期记忆和长期记忆使用不同的数据库表/集合
- **数据隔离**: 不同类型记忆数据完全隔离，互不影响
- **灵活配置**: 可以轻松添加新的模型配置和检索策略

## 数据库架构

### 短期记忆数据库

- **ES 索引**: `memo`
- **Milvus 集合**: `memo`
- **MySQL 表**: `memo_storage_stas_info`

### 长期记忆数据库

- **ES 索引**: `memo_long`
- **Milvus 集合**: `memo_long`
- **MySQL 表**: `memo_storage_stas_info_long`

### 共享数据库

- **模型参数表**: `memo_retrieval_model_parms_info`（所有记忆类型共享）

## 支持的模型类型

### 1. 默认模型 (MEMO_21918)

- **配置**: `SHORT_TERM: true, LONG_TERM: true`
- **功能**: 同时启用短期和长期记忆检索
- **适用场景**: 需要全面记忆检索的场景

### 2. 短期记忆模型 (MEMO_SHORT_TERM)

- **配置**: `SHORT_TERM: true, LONG_TERM: false`
- **功能**: 只启用短期记忆检索
- **适用场景**: 需要快速、近期记忆的场景

### 3. 长期记忆模型 (MEMO_LONG_TERM)

- **配置**: `SHORT_TERM: false, LONG_TERM: true`
- **功能**: 只启用长期记忆检索
- **适用场景**: 需要深度、历史记忆的场景

## 数据库初始化

### 1. 运行初始化脚本

```bash
python init/init_all.py
```

该脚本会自动：

- 创建短期记忆的数据库表/集合
- 创建长期记忆的数据库表/集合
- 插入三种模型的参数配置
- 设置默认的检索参数

### 2. 模型参数配置

初始化后，数据库中会包含以下模型配置：

```sql
-- 查看已配置的模型
SELECT model_id, parms FROM memo_retrieval_model_parms_info;
```

## 使用方法

### 1. 记忆存储

#### 存储短期记忆

```python
from mcpProtocol.service.MemoryService import MemorySave
from common.conf.db_config import db_config

# 创建短期记忆存储服务
memory_save = MemorySave(db_config, memory_type="SHORT_TERM")

# 存储短期记忆
memory_id = memory_save.memory_save(
    question="问题内容",
    answer="回答内容",
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

#### 存储长期记忆

```python
# 创建长期记忆存储服务
memory_save = MemorySave(db_config, memory_type="LONG_TERM")

# 存储长期记忆
memory_id = memory_save.memory_save(
    question="问题内容",
    answer="回答内容",
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

### 2. 记忆检索

#### 使用短期记忆模型

```python
from mcpProtocol.service.MemoryService import MemoryRecall
from common.conf.db_config import db_config

# 创建短期记忆检索服务
memory_recall = MemoryRecall(db_config, memory_type="SHORT_TERM")

# 检索短期记忆
template, results = memory_recall.memory_recall(
    query="查询问题",
    model_id="MEMO_SHORT_TERM",  # 短期记忆模型
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

#### 使用长期记忆模型

```python
# 创建长期记忆检索服务
memory_recall = MemoryRecall(db_config, memory_type="LONG_TERM")

# 检索长期记忆
template, results = memory_recall.memory_recall(
    query="查询问题",
    model_id="MEMO_LONG_TERM",  # 长期记忆模型
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

#### 使用默认模型（自动切换数据库）

```python
# 创建记忆检索服务（默认使用短期记忆数据库）
memory_recall = MemoryRecall(db_config, memory_type="SHORT_TERM")

# 使用默认模型，系统会根据配置自动切换数据库
template, results = memory_recall.memory_recall(
    query="查询问题",
    model_id="MEMO_21918",  # 默认模型，会根据配置自动切换数据库
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

## 数据库映射配置

系统通过 `MEMORY_DB_MAPPING` 配置来管理不同记忆类型的数据库映射：

```python
MEMORY_DB_MAPPING = {
    "SHORT_TERM": {
        "ES": {"index_name": "memo"},
        "MILVUS": {"collection_name": "memo"},
        "MYSQL": {"stats_table": "memo_storage_stas_info"}
    },
    "LONG_TERM": {
        "ES": {"index_name": "memo_long"},
        "MILVUS": {"collection_name": "memo_long"},
        "MYSQL": {"stats_table": "memo_storage_stas_info_long"}
    }
}
```

## 模型参数配置详解

### 短期记忆模型参数

```json
{
  "SHORT_TERM": true,
  "SHORT_TERM_MODEL": {
    "TOP_N": 5, // 返回结果数量
    "RETRIEVAL_TYPE": "MLT", // 检索类型：MLT(混合)、TXT(文本)、EMD(向量)
    "TIME_FACTOR": true, // 是否启用时间因子
    "TIME_FACTOR_PARMS": {
      "ORIGIN": "NOW", // 时间原点
      "SCALE": "30D", // 时间尺度
      "DECAY": 0.5, // 衰减度
      "FACTOR": 1.0 // 缩放因子
    },
    "WEIGHT_FACTOR": true, // 是否启用权重因子
    "WEIGHT_FACTOR_PARMS": {
      "MODE": "LOG1P", // 变换函数
      "FACTOR": 2.0 // 缩放因子
    },
    "SCORE_FACTOR": true, // 是否启用评分因子
    "SCORE_FACTOR_PARMS": {
      "MODE": "LOG1P", // 变换函数
      "FACTOR": 2.0 // 缩放因子
    }
  },
  "LONG_TERM": false
}
```

### 长期记忆模型参数

```json
{
  "SHORT_TERM": false,
  "LONG_TERM": true,
  "LONG_TERM_MODEL": {
    "TOP_N": 5,
    "RETRIEVAL_TYPE": "MLT",
    "TIME_FACTOR": true,
    "TIME_FACTOR_PARMS": {
      "ORIGIN": "NOW",
      "SCALE": "30D",
      "DECAY": 0.5,
      "FACTOR": 1.0
    },
    "WEIGHT_FACTOR": true,
    "WEIGHT_FACTOR_PARMS": {
      "MODE": "LOG1P",
      "FACTOR": 2.0
    },
    "SCORE_FACTOR": true,
    "SCORE_FACTOR_PARMS": {
      "MODE": "LOG1P",
      "FACTOR": 2.0
    }
  }
}
```

## 添加新的模型配置

### 1. 通过 SQL 插入新配置

```sql
INSERT INTO memo_retrieval_model_parms_info (model_id, create_time, update_time, parms)
VALUES (
    'MEMO_CUSTOM',
    NOW(),
    NOW(),
    '{
        "SHORT_TERM": true,
        "SHORT_TERM_MODEL": {
            "TOP_N": 3,
            "RETRIEVAL_TYPE": "TXT",
            "TIME_FACTOR": false,
            "WEIGHT_FACTOR": true,
            "WEIGHT_FACTOR_PARMS": {"MODE": "LOG1P", "FACTOR": 1.5},
            "SCORE_FACTOR": false
        },
        "LONG_TERM": false
    }'
);
```

### 2. 使用新模型

```python
template, results = memory_recall.memory_recall(
    query="查询问题",
    model_id="MEMO_CUSTOM",  # 新配置的模型ID
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

## 示例代码

### 基础使用示例

运行基础的使用示例：

```bash
python examples/short_long_term_example.py
```

### 数据库分离示例

运行数据库分离示例：

```bash
python examples/short_long_db_separation_example.py
```

该示例演示了：

1. 使用不同记忆类型存储和检索数据
2. 验证数据库隔离效果
3. 如何添加新的模型配置

## 检索策略说明

### 短期记忆检索

- 专注于近期、相关的记忆
- 通常返回较少但更相关的结果
- 适合快速响应的场景
- 使用短期记忆数据库（memo）

### 长期记忆检索

- 搜索更广泛的历史记忆
- 可能返回更多但相关性稍低的结果
- 适合深度分析和历史回顾
- 使用长期记忆数据库（memo_long）

### 混合记忆检索

- 结合短期和长期记忆的优势
- 提供最全面的记忆检索
- 适合需要全面信息的场景
- 根据配置自动切换数据库

## 数据库分离优势

### 1. 性能优化

- 短期记忆数据库可以针对高频访问进行优化
- 长期记忆数据库可以针对大容量存储进行优化
- 减少单个数据库的负载压力

### 2. 数据隔离

- 短期和长期记忆数据完全隔离
- 避免不同类型记忆之间的相互影响
- 便于独立管理和维护

### 3. 扩展性

- 可以轻松添加更多记忆类型
- 支持不同记忆类型使用不同的存储策略
- 便于后续功能扩展

### 4. 灵活性

- 可以根据业务需求调整数据库配置
- 支持动态切换数据库进行检索
- 便于 A/B 测试和性能调优

## 注意事项

1. **初始化顺序**: 在使用记忆检索功能之前，必须先运行初始化脚本
2. **模型参数**: 确保数据库中的模型参数配置正确
3. **数据库映射**: 确保 `MEMORY_DB_MAPPING` 配置正确
4. **性能考虑**: 不同记忆类型的性能可能有所不同
5. **数据一致性**: 确保短期和长期记忆数据的一致性

## 故障排除

### 常见问题

1. **模型 ID 未找到**

   - 检查数据库中是否存在对应的模型参数记录
   - 运行初始化脚本重新插入模型参数

2. **检索结果为空**

   - 检查对应的数据库中是否有相关的记忆数据
   - 验证检索参数是否正确
   - 确认使用的是正确的记忆类型

3. **配置错误**

   - 检查模型参数 JSON 格式是否正确
   - 确保 SHORT_TERM 和 LONG_TERM 配置合理
   - 验证数据库映射配置是否正确

4. **数据库连接失败**

   - 检查数据库服务是否正常运行
   - 验证数据库连接参数是否正确
   - 确认数据库表/集合是否已创建

### 日志查看

系统会记录详细的日志信息，包括：

- 模型参数加载
- 数据库切换过程
- 检索策略选择
- 检索结果统计

查看日志以获取更多调试信息。

## 扩展功能

### 自定义检索策略

可以通过修改模型参数来实现自定义的检索策略：

- 调整时间因子参数
- 修改权重因子配置
- 自定义评分因子设置

### 动态参数调整

支持在运行时动态调整模型参数，实现更灵活的检索策略。

### 性能优化

可以根据实际使用情况优化不同模型的参数配置，提高检索效率和准确性。

### 多记忆类型支持

可以轻松扩展支持更多记忆类型，如中期记忆、专业记忆等。
