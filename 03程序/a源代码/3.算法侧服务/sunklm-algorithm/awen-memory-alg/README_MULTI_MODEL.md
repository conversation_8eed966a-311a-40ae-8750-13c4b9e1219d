# 多模型 ID 数据库表/集合映射功能

## 概述

本功能允许不同的模型 ID 使用不同的数据库表/集合，实现数据隔离和模型级别的数据管理。

## 功能特性

- **数据隔离**: 不同模型 ID 的数据存储在不同的 ES 索引、Milvus 集合和 MySQL 表中
- **动态配置**: 通过配置文件轻松添加新的模型 ID 和对应的数据库映射
- **向后兼容**: 保持与现有代码的兼容性，默认使用原有配置
- **自动初始化**: 提供脚本自动创建新模型 ID 对应的数据库表/集合

## 配置说明

### 1. 模型映射配置

在 `common/conf/db_config.py` 中的 `MODEL_TABLE_MAPPING` 配置不同模型 ID 的数据库映射：

```python
MODEL_TABLE_MAPPING = {
    # 默认模型配置（当model_id不在映射中时使用）
    "DEFAULT": {
        "ES": {
            "index_name": "memo"
        },
        "MILVUS": {
            "collection_name": "memo"
        },
        "MYSQL": {
            "stats_table": "memo_storage_stas_info"
        }
    },

    # 自定义模型配置
    "MEMO_21918": {
        "ES": {
            "index_name": "memo_21918"
        },
        "MILVUS": {
            "collection_name": "memo_21918"
        },
        "MYSQL": {
            "stats_table": "memo_storage_stas_info_21918"
        }
    }
}
```

### 2. 添加新模型配置

要添加新的模型 ID，只需在 `MODEL_TABLE_MAPPING` 中添加新的配置项：

```python
"NEW_MODEL_ID": {
    "ES": {
        "index_name": "memo_new_model"
    },
    "MILVUS": {
        "collection_name": "memo_new_model"
    },
    "MYSQL": {
        "stats_table": "memo_storage_stas_info_new_model"
    }
}
```

## 使用方法

### 1. 初始化数据库表/集合

在添加新的模型 ID 后，需要初始化对应的数据库表/集合：

```bash
# 初始化单个模型ID
python init/init_model_tables.py --model-id MEMO_21918

# 初始化所有配置的模型ID
python init/init_model_tables.py --all
```

### 2. 使用不同模型 ID

#### 记忆存储

```python
from mcpProtocol.service.MemoryService import MemorySave
from common.conf.db_config import db_config

# 使用默认模型ID
default_memory_save = MemorySave(db_config, model_id="DEFAULT")

# 使用自定义模型ID
custom_memory_save = MemorySave(db_config, model_id="MEMO_21918")

# 存储记忆
memory_id = custom_memory_save.memory_save(
    question="问题内容",
    answer="回答内容",
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

#### 记忆检索

```python
from mcpProtocol.service.MemoryService import MemoryRecall
from common.conf.db_config import db_config

# 使用默认模型ID
default_memory_recall = MemoryRecall(db_config, model_id="DEFAULT")

# 使用自定义模型ID
custom_memory_recall = MemoryRecall(db_config, model_id="MEMO_21918")

# 检索记忆
template, compress_results = custom_memory_recall.memory_recall(
    query="查询问题",
    model_id="MEMO_21918",
    user_id="user001",
    agent_id="scene001",
    session_id="session001"
)
```

## 数据隔离验证

不同模型 ID 的数据完全隔离，不会相互影响：

- **ES 索引隔离**: 不同模型 ID 的数据存储在不同的 ES 索引中
- **Milvus 集合隔离**: 不同模型 ID 的向量数据存储在不同的 Milvus 集合中
- **MySQL 表隔离**: 不同模型 ID 的统计信息存储在不同的 MySQL 表中

## 示例代码

运行完整的使用示例：

```bash
python examples/multi_model_example.py
```

该示例演示了：

1. 使用不同模型 ID 存储和检索数据
2. 验证数据隔离效果
3. 如何添加新的模型配置

## 注意事项

1. **初始化顺序**: 在使用新模型 ID 之前，必须先运行初始化脚本创建对应的数据库表/集合
2. **配置一致性**: 确保所有环境中的 `MODEL_TABLE_MAPPING` 配置一致
3. **性能考虑**: 每个模型 ID 都会创建独立的数据库连接，注意资源使用
4. **数据迁移**: 如果需要将现有数据迁移到新的模型 ID，需要手动处理数据迁移

## 故障排除

### 常见问题

1. **模型 ID 未找到**

   - 检查 `MODEL_TABLE_MAPPING` 中是否包含该模型 ID
   - 系统会自动使用 "DEFAULT" 配置

2. **数据库表/集合不存在**

   - 运行初始化脚本：`python init/init_model_tables.py --model-id YOUR_MODEL_ID`

3. **连接失败**
   - 检查数据库连接配置
   - 确保数据库服务正常运行

### 日志查看

系统会记录详细的日志信息，包括：

- 模型 ID 配置加载
- 数据库连接状态
- 数据操作结果

查看日志以获取更多调试信息。

## 扩展功能

### 自定义配置

除了基本的表/集合名称，还可以配置：

- 数据库连接参数
- 索引参数
- 集合参数

### 动态配置

支持从外部配置文件或数据库加载模型映射配置，实现动态配置管理。

### 监控和统计

为每个模型 ID 提供独立的监控和统计功能，便于性能分析和问题排查。
