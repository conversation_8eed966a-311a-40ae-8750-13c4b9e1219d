#!/bin/bash

# 端口号
memory_service_port=8848
SERVER="./run_api.py"

usage() {
    echo "用法: sh run.sh [start|stop|restart|status|test|init|logs]"
    echo "  start   - 启动记忆服务"
    echo "  stop    - 停止记忆服务"
    echo "  restart - 重启记忆服务"
    echo "  status  - 查看服务状态"
    echo "  test    - 运行API测试"
    echo "  init    - 初始化数据库"
    echo "  logs    - 查看实时日志"
    exit 1
}

is_exist(){
  # 使用 netstat 或 ss 来检查端口占用，如果没有则使用简单的文件检查
  if command -v netstat >/dev/null 2>&1; then
    pid=$(netstat -tlnp 2>/dev/null | grep ":$memory_service_port " | awk '{print $7}' | cut -d'/' -f1)
  elif command -v ss >/dev/null 2>&1; then
    pid=$(ss -tlnp 2>/dev/null | grep ":$memory_service_port " | awk '{print $7}' | cut -d'/' -f1)
  else
    # 如果没有网络工具，使用简单的文件检查
    if [ -f "./logs/nohup.out" ]; then
      return 0
    else
      return 1
    fi
  fi
  
  if [ -z "$pid" ]; then
    return 1
  else
    echo 'pid: ' $pid
    return 0
  fi
}

init_db(){
  echo "初始化数据库..."
  python ./init/init_all.py
  if [ $? -eq 0 ]; then
    echo "数据库初始化成功"
  else
    echo "数据库初始化失败"
    exit 1
  fi
}

start(){
  # 先初始化数据库
  init_db
  is_exist
  if [ $? -eq "0" ]; then
    echo "服务已在运行，请先停止！"
  else
    echo "启动记忆服务..."
    # 创建日志目录
    mkdir -p ./logs
    nohup python $SERVER > ./logs/nohup.out 2>&1 &
    echo "启动成功，日志见 ./logs/nohup.out"
    sleep 3
    is_exist
    if [ $? -eq "0" ]; then
      echo "Pid 是 $pid"
      echo "服务地址: http://127.0.0.1:$memory_service_port"
    else
      echo "启动失败，请检查日志"
      exit 1
    fi
  fi
}

stop(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "杀死进程 $pid"
    kill -9 $pid
    sleep 3
    is_exist
    if [ $? -eq "0" ]; then
      echo "停止失败，请检查！"
      exit 2
    else
      echo "服务已成功停止"
    fi
  else
    echo "服务未在运行"
  fi
}

status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "服务正在运行，Pid 是 $pid"
    echo "服务地址: http://127.0.0.1:$memory_service_port"
    # 测试服务是否响应
    echo "测试服务响应..."
    if curl -s http://127.0.0.1:$memory_service_port/api/memory/recall > /dev/null 2>&1; then
      echo "✅ 服务响应正常"
    else
      echo "⚠️  服务可能未完全启动，请稍等或检查日志"
    fi
  else
    echo "服务未在运行"
  fi
}

test_api(){
  echo "运行API测试..."
  if ! is_exist; then
    echo "服务未运行，请先启动服务"
    exit 1
  fi
  
  # 等待服务完全启动
  echo "等待服务启动..."
  sleep 5
  
  # 运行测试
  python test_memory_api.py
  if [ $? -eq 0 ]; then
    echo "✅ API测试通过"
  else
    echo "❌ API测试失败"
    exit 1
  fi
}

logs(){
  if [ -f "./logs/nohup.out" ]; then
    echo "查看实时日志 (Ctrl+C 退出)..."
    tail -f ./logs/nohup.out
  else
    echo "日志文件不存在，服务可能未启动"
  fi
}

restart(){
  stop
  start
}

case "$1" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  "test")
    test_api
    ;;
  "init")
    init_db
    ;;
  "logs")
    logs
    ;;
  *)
    usage
    ;;
esac 