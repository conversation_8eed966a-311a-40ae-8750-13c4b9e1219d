# SUNKLM 智能算法服务平台
# Generated by AI, be careful to check the content.

## 项目概述

SUNKLM 是一个基于微服务架构的智能算法服务平台，专注于提供大语言模型(LLM)、智能体(Agent)、知识检索(RAG)、OCR识别等AI算法服务。该平台采用分布式架构设计，支持多模态处理、工具调用、知识管理等功能。

## 系统架构

### 项目目录结构
```
sunklm-algorithm/
├── awen-gateway-alg/          # API网关服务 (端口: 9080)
├── awen-ag-process-alg/       # 智能体处理服务 (端口: 9081)
├── awen-mcp-alg/             # MCP工具协议服务 (端口: 9082)
├── awen-knowledge-alg/       # 知识处理服务 (端口: 8014)
├── awen-retrieval-alg/       # 检索服务 (端口: 8013)
├── awen-ocr-alg/            # OCR识别服务
├── agent_server/            # Agent算法服务 (端口: 8012)
├── llm_server/              # LLM基础服务
├── llm_base_server/         # LLM基础服务器
├── tool_server/             # 工具服务器
├── sft_server/              # 微调服务器
├── ws_server/               # WebSocket服务器
├── web/                     # Web演示界面
├── mcp_client/              # MCP客户端
├── benchmarks/              # 基准测试
├── test/                    # 测试用例
└── data/                    # 数据目录
```

### 详细系统架构图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层"
        WebUI[Web界面]
        API[API客户端]
        Mobile[移动端]
    end

    %% 网关层
    subgraph "网关层 (端口: 9080)"
        Gateway[awen-gateway-alg<br/>API网关服务]
        LB[负载均衡器]
        Auth[认证授权]
        RateLimit[限流控制]
    end

    %% 核心服务层
    subgraph "核心服务层"
        subgraph "智能体服务 (端口: 9081)"
            AgentProcess[awen-ag-process-alg<br/>智能体处理服务]
            AgentEngine[Agent执行引擎]
            DialogManager[对话管理器]
        end

        subgraph "工具协议服务 (端口: 9082)"
            MCPService[awen-mcp-alg<br/>MCP工具协议服务]
            CodeExecutor[代码执行器]
            DocParser[文档解析器]
            APIConnector[外部API连接器]
        end

        subgraph "知识服务 (端口: 8014)"
            KnowledgeService[awen-knowledge-alg<br/>知识处理服务]
            TextProcessor[文本处理器]
            Vectorizer[向量化引擎]
            FAQExtractor[FAQ提取器]
        end

        subgraph "检索服务 (端口: 8013)"
            RetrievalService[awen-retrieval-alg<br/>检索服务]
            VectorSearch[向量检索]
            SemanticSearch[语义搜索]
        end

        subgraph "OCR服务"
            OCRService[awen-ocr-alg<br/>OCR识别服务]
            PaddleOCR[PaddleOCR引擎]
            ImageProcessor[图像处理器]
        end
    end

    %% 算法服务层
    subgraph "算法服务层"
        subgraph "LLM服务"
            LLMServer[llm_server<br/>LLM基础服务]
            LLMBaseServer[llm_base_server<br/>LLM基础服务器]
            ModelManager[模型管理器]
        end

        subgraph "Agent算法 (端口: 8012)"
            AgentServer[agent_server<br/>Agent算法服务]
            ReasoningEngine[推理引擎]
            ToolManager[工具管理器]
        end

        subgraph "专用服务"
            SFTServer[sft_server<br/>微调服务器]
            ToolServer[tool_server<br/>工具服务器]
            WSServer[ws_server<br/>WebSocket服务器]
        end
    end

    %% 数据存储层
    subgraph "数据存储层"
        subgraph "关系型数据库"
            MySQL[(MySQL<br/>**********:3306)]
            AgentDB[(AGENT_FLOW)]
            PrimaryDB[(AWEN_PRIMARY)]
        end

        subgraph "缓存层"
            RedisCluster[(Redis集群<br/>**********:7000-7002)]
            Cache[缓存数据]
            Session[会话存储]
        end

        subgraph "向量数据库"
            Milvus[(Milvus<br/>向量数据库)]
            VectorIndex[向量索引]
            Embeddings[嵌入向量]
        end

        subgraph "搜索引擎"
            Elasticsearch[(Elasticsearch<br/>搜索引擎)]
            FullTextIndex[全文索引]
            LogIndex[日志索引]
        end

        subgraph "对象存储"
            MinIO[(MinIO<br/>***********:9000)]
            FileStorage[文件存储]
            ModelStorage[模型存储]
        end
    end

    %% 外部服务
    subgraph "外部服务"
        LLMModels[大语言模型<br/>***********:9020]
        QichachaAPI[企查查API]
        ThirdPartyAPI[第三方API]
    end

    %% 连接关系
    WebUI --> Gateway
    API --> Gateway
    Mobile --> Gateway

    Gateway --> LB
    Gateway --> Auth
    Gateway --> RateLimit

    Gateway --> AgentProcess
    Gateway --> MCPService
    Gateway --> KnowledgeService
    Gateway --> RetrievalService
    Gateway --> OCRService

    AgentProcess --> AgentServer
    AgentProcess --> LLMServer
    AgentProcess --> MCPService

    MCPService --> CodeExecutor
    MCPService --> DocParser
    MCPService --> APIConnector

    KnowledgeService --> TextProcessor
    KnowledgeService --> Vectorizer
    KnowledgeService --> MinIO

    RetrievalService --> Milvus
    RetrievalService --> Elasticsearch

    OCRService --> PaddleOCR
    OCRService --> ImageProcessor

    AgentServer --> MySQL
    AgentServer --> RedisCluster
    AgentServer --> LLMModels

    LLMServer --> LLMModels
    LLMServer --> ModelStorage

    %% 数据库连接
    AgentProcess --> MySQL
    KnowledgeService --> MySQL
    RetrievalService --> Milvus

    %% 缓存连接
    Gateway --> RedisCluster
    AgentProcess --> RedisCluster

    %% 外部API连接
    APIConnector --> QichachaAPI
    APIConnector --> ThirdPartyAPI

    %% 样式定义
    classDef userLayer fill:#e1f5fe
    classDef gatewayLayer fill:#f3e5f5
    classDef serviceLayer fill:#e8f5e8
    classDef algorithmLayer fill:#fff3e0
    classDef dataLayer fill:#fce4ec
    classDef externalLayer fill:#f1f8e9

    class WebUI,API,Mobile userLayer
    class Gateway,LB,Auth,RateLimit gatewayLayer
    class AgentProcess,MCPService,KnowledgeService,RetrievalService,OCRService serviceLayer
    class LLMServer,AgentServer,SFTServer,ToolServer,WSServer algorithmLayer
    class MySQL,RedisCluster,Milvus,Elasticsearch,MinIO dataLayer
    class LLMModels,QichachaAPI,ThirdPartyAPI externalLayer
```

### 服务交互流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Gateway as API网关<br/>(9080)
    participant Agent as 智能体服务<br/>(9081)
    participant MCP as MCP工具服务<br/>(9082)
    participant Knowledge as 知识服务<br/>(8014)
    participant Retrieval as 检索服务<br/>(8013)
    participant LLM as LLM服务
    participant DB as 数据库
    participant Cache as Redis缓存

    %% 智能体对话流程
    User->>Gateway: 发送对话请求
    Gateway->>Gateway: 请求验证 & 限流
    Gateway->>Agent: 转发到智能体服务

    Agent->>Cache: 获取会话历史
    Cache-->>Agent: 返回历史记录

    alt 需要知识检索
        Agent->>Retrieval: 请求知识检索
        Retrieval->>Knowledge: 查询向量数据
        Knowledge-->>Retrieval: 返回相关文档
        Retrieval-->>Agent: 返回检索结果
    end

    alt 需要工具调用
        Agent->>MCP: 请求工具执行
        MCP->>MCP: 执行代码/调用API
        MCP-->>Agent: 返回执行结果
    end

    Agent->>LLM: 发送LLM请求
    LLM-->>Agent: 返回生成结果

    Agent->>DB: 保存对话记录
    Agent->>Cache: 更新会话缓存

    Agent-->>Gateway: 返回响应
    Gateway-->>User: 返回最终结果
```

### 数据流架构图

```mermaid
flowchart LR
    %% 输入数据流
    subgraph "数据输入"
        UserInput[用户输入]
        FileUpload[文件上传]
        APIData[API数据]
    end

    %% 数据处理层
    subgraph "数据处理层"
        TextProcess[文本处理]
        ImageProcess[图像处理]
        VectorProcess[向量化处理]
        KnowledgeExtract[知识提取]
    end

    %% 存储层
    subgraph "数据存储层"
        StructuredData[(结构化数据<br/>MySQL)]
        VectorData[(向量数据<br/>Milvus)]
        CacheData[(缓存数据<br/>Redis)]
        FileData[(文件数据<br/>MinIO)]
        SearchIndex[(搜索索引<br/>Elasticsearch)]
    end

    %% 服务层
    subgraph "服务层"
        QueryService[查询服务]
        RetrievalService[检索服务]
        GenerationService[生成服务]
        AnalysisService[分析服务]
    end

    %% 输出层
    subgraph "数据输出"
        APIResponse[API响应]
        WebDisplay[Web展示]
        FileExport[文件导出]
        RealtimeStream[实时流]
    end

    %% 数据流连接
    UserInput --> TextProcess
    FileUpload --> ImageProcess
    APIData --> KnowledgeExtract

    TextProcess --> StructuredData
    TextProcess --> VectorData
    ImageProcess --> FileData
    VectorProcess --> VectorData
    KnowledgeExtract --> SearchIndex

    StructuredData --> QueryService
    VectorData --> RetrievalService
    CacheData --> QueryService
    FileData --> AnalysisService
    SearchIndex --> RetrievalService

    QueryService --> APIResponse
    RetrievalService --> WebDisplay
    GenerationService --> RealtimeStream
    AnalysisService --> FileExport

    %% 样式
    classDef inputStyle fill:#e3f2fd
    classDef processStyle fill:#f1f8e9
    classDef storageStyle fill:#fce4ec
    classDef serviceStyle fill:#fff3e0
    classDef outputStyle fill:#e8f5e8

    class UserInput,FileUpload,APIData inputStyle
    class TextProcess,ImageProcess,VectorProcess,KnowledgeExtract processStyle
    class StructuredData,VectorData,CacheData,FileData,SearchIndex storageStyle
    class QueryService,RetrievalService,GenerationService,AnalysisService serviceStyle
    class APIResponse,WebDisplay,FileExport,RealtimeStream outputStyle
```

## 核心服务模块

### 1. API网关服务 (awen-gateway-alg)
- **功能**: 统一接口网关，负责请求路由、负载均衡、限流控制
- **端口**: 9080
- **主要接口**:
  - `/ai/gateway/chatModel` - 模型对话接口
  - `/ai/gateway/chatAgent` - 智能体对话接口
  - `/ai/gateway/callCode` - 代码执行接口
  - `/ai/gateway/callMcpTool` - MCP工具调用接口

### 2. 智能体处理服务 (awen-ag-process-alg)
- **功能**: 智能体执行引擎，支持多轮对话、工具调用、知识检索
- **端口**: 9081
- **主要接口**:
  - `/agent/chatAgent` - 智能体对话处理

### 3. MCP工具协议服务 (awen-mcp-alg)
- **功能**: Model Context Protocol工具服务，提供代码执行、文档解析、外部API调用等工具
- **端口**: 9082
- **支持工具**:
  - 代码执行器
  - 文档解析器 (PDF、Word、Excel等)
  - 产品矩阵查询
  - 企查查API调用
  - 语音处理 (ASR/TTS)

### 4. 知识处理服务 (awen-knowledge-alg)
- **功能**: 文档处理、文本切分、向量化、知识库管理
- **端口**: 8014
- **主要接口**:
  - `/RAG-alg/file_format` - 文件格式转换
  - `/RAG-alg/text_split` - 文本切分
  - `/RAG-alg/text_vector` - 文本向量化
  - `/RAG-alg/faq_extract` - FAQ提取

### 5. OCR识别服务 (awen-ocr-alg)
- **功能**: 基于PaddleOCR的文字识别服务
- **支持格式**: PDF、图片等多种格式
- **部署方式**: 支持CPU和GPU部署

## 技术栈

### 后端框架
- **FastAPI**: 现代高性能Web框架 (网关、智能体服务)
- **Flask**: 轻量级Web框架 (知识处理、Agent服务)
- **uvicorn**: ASGI服务器

### AI/ML框架
- **LangChain**: LLM应用开发框架
- **PaddleOCR**: 文字识别框架
- **OpenAI**: 大模型API接口

### 数据存储
- **MySQL**: 关系型数据库 (**********:3306)
- **Redis Cluster**: 缓存集群 (**********:7000-7002)
- **Milvus**: 向量数据库
- **Elasticsearch**: 搜索引擎
- **MinIO**: 对象存储 (***********:9000)

### 开发工具
- **Python 3.11**: 主要开发语言
- **Docker**: 容器化部署
- **Conda**: 环境管理

### 技术架构层次图

```mermaid
graph TB
    %% 表现层
    subgraph "表现层 (Presentation Layer)"
        WebUI[Web用户界面]
        RestAPI[REST API]
        WebSocket[WebSocket接口]
        MobileAPI[移动端API]
    end

    %% 网关层
    subgraph "网关层 (Gateway Layer)"
        APIGateway[API网关<br/>FastAPI + uvicorn]
        LoadBalancer[负载均衡]
        Authentication[身份认证]
        RateLimiting[限流控制]
        RequestRouting[请求路由]
    end

    %% 业务服务层
    subgraph "业务服务层 (Business Service Layer)"
        subgraph "智能体服务"
            AgentService[智能体服务<br/>FastAPI]
            DialogManager[对话管理]
            IntentRecognition[意图识别]
            ContextManager[上下文管理]
        end

        subgraph "工具服务"
            MCPService[MCP工具服务<br/>FastAPI]
            CodeExecution[代码执行]
            DocumentParsing[文档解析]
            ExternalAPI[外部API调用]
        end

        subgraph "知识服务"
            KnowledgeService[知识服务<br/>Flask]
            TextSplitting[文本切分]
            Vectorization[向量化]
            FAQExtraction[FAQ提取]
        end
    end

    %% 算法引擎层
    subgraph "算法引擎层 (Algorithm Engine Layer)"
        subgraph "LLM引擎"
            LLMEngine[大语言模型引擎]
            ModelManager[模型管理器]
            PromptEngine[提示词引擎]
            ResponseProcessor[响应处理器]
        end

        subgraph "AI算法"
            NLPProcessor[NLP处理器<br/>LangChain]
            OCREngine[OCR引擎<br/>PaddleOCR]
            VectorEngine[向量引擎]
            SearchEngine[搜索引擎]
        end

        subgraph "推理引擎"
            ReasoningEngine[推理引擎]
            ToolManager[工具管理器]
            WorkflowEngine[工作流引擎]
            DecisionEngine[决策引擎]
        end
    end

    %% 数据访问层
    subgraph "数据访问层 (Data Access Layer)"
        subgraph "数据库访问"
            MySQLDAO[MySQL数据访问]
            RedisDAO[Redis数据访问]
            MilvusDAO[Milvus数据访问]
            ElasticsearchDAO[ES数据访问]
        end

        subgraph "文件访问"
            MinIODAO[MinIO文件访问]
            LocalFileDAO[本地文件访问]
            NetworkFileDAO[网络文件访问]
        end

        subgraph "外部服务访问"
            LLMServiceDAO[LLM服务访问]
            ThirdPartyDAO[第三方API访问]
            MessageQueueDAO[消息队列访问]
        end
    end

    %% 基础设施层
    subgraph "基础设施层 (Infrastructure Layer)"
        subgraph "数据存储"
            MySQL[(MySQL数据库)]
            Redis[(Redis缓存)]
            Milvus[(Milvus向量库)]
            Elasticsearch[(Elasticsearch)]
            MinIO[(MinIO对象存储)]
        end

        subgraph "计算资源"
            CPUCluster[CPU集群]
            GPUCluster[GPU集群]
            ContainerOrchestration[容器编排<br/>Docker]
        end

        subgraph "网络与安全"
            NetworkInfra[网络基础设施]
            SecurityInfra[安全基础设施]
            MonitoringInfra[监控基础设施]
        end
    end

    %% 连接关系
    WebUI --> APIGateway
    RestAPI --> APIGateway
    WebSocket --> APIGateway
    MobileAPI --> APIGateway

    APIGateway --> LoadBalancer
    APIGateway --> Authentication
    APIGateway --> RateLimiting
    APIGateway --> RequestRouting

    RequestRouting --> AgentService
    RequestRouting --> MCPService
    RequestRouting --> KnowledgeService

    AgentService --> LLMEngine
    AgentService --> ReasoningEngine
    MCPService --> NLPProcessor
    MCPService --> OCREngine
    KnowledgeService --> VectorEngine
    KnowledgeService --> SearchEngine

    LLMEngine --> LLMServiceDAO
    NLPProcessor --> MySQLDAO
    OCREngine --> MinIODAO
    VectorEngine --> MilvusDAO
    SearchEngine --> ElasticsearchDAO
    ReasoningEngine --> RedisDAO

    MySQLDAO --> MySQL
    RedisDAO --> Redis
    MilvusDAO --> Milvus
    ElasticsearchDAO --> Elasticsearch
    MinIODAO --> MinIO
    LLMServiceDAO --> CPUCluster
    LLMServiceDAO --> GPUCluster

    %% 样式定义
    classDef presentationStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef gatewayStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef businessStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef algorithmStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef dataStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef infraStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class WebUI,RestAPI,WebSocket,MobileAPI presentationStyle
    class APIGateway,LoadBalancer,Authentication,RateLimiting,RequestRouting gatewayStyle
    class AgentService,MCPService,KnowledgeService,DialogManager,IntentRecognition,ContextManager,CodeExecution,DocumentParsing,ExternalAPI,TextSplitting,Vectorization,FAQExtraction businessStyle
    class LLMEngine,ModelManager,PromptEngine,ResponseProcessor,NLPProcessor,OCREngine,VectorEngine,SearchEngine,ReasoningEngine,ToolManager,WorkflowEngine,DecisionEngine algorithmStyle
    class MySQLDAO,RedisDAO,MilvusDAO,ElasticsearchDAO,MinIODAO,LocalFileDAO,NetworkFileDAO,LLMServiceDAO,ThirdPartyDAO,MessageQueueDAO dataStyle
    class MySQL,Redis,Milvus,Elasticsearch,MinIO,CPUCluster,GPUCluster,ContainerOrchestration,NetworkInfra,SecurityInfra,MonitoringInfra infraStyle
```

## 环境要求

- Python 3.11+
- CUDA 10.1+ (GPU部署)
- Docker & Docker Compose
- MySQL 5.7+
- Redis 6.0+
- MinIO

## 快速开始

### 1. 环境准备
```bash
# 创建conda环境
conda create -n mcp python=3.11
conda activate mcp

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件
修改各服务的配置文件中的数据库连接、API地址等配置项：
- MySQL: `**********:3306`
- Redis: `**********:7000-7002`
- MinIO: `***********:9000`

### 3. 启动服务

#### 启动网关服务
```bash
cd awen-gateway-alg
chmod +x run.sh
./run.sh start
```

#### 启动智能体服务
```bash
cd awen-ag-process-alg
python AgentMain.py
```

#### 启动MCP服务
```bash
cd awen-mcp-alg
./run.sh start
```

#### 启动知识处理服务
```bash
cd awen-knowledge-alg
./run.sh start
```

### 4. 验证服务
```bash
# 检查服务状态
curl http://localhost:9080/ai/gateway/health
curl http://localhost:9081/agent/health
curl http://localhost:9082/health
```

## Docker部署

### 构建镜像
```bash
# 知识处理服务
cd awen-knowledge-alg
docker build -t sunklm/knowledge-alg .

# MCP服务
cd awen-mcp-alg
docker build -t sunklm/mcp-alg .
```

### 运行容器
```bash
docker run -d -p 8014:8014 sunklm/knowledge-alg
docker run -d -p 9082:9082 sunklm/mcp-alg
```

## API文档

### 智能体对话接口
```http
POST /ai/gateway/chatAgent
Content-Type: application/json

{
  "query": "你好，请介绍一下你的功能",
  "stream": false,
  "modelType": "qwen2.5-72b-instruct-gptq-int4",
  "baseUrl": "http://***********:9020/v1/chat/completions",
  "company": "qwen",
  "deployMode": 0,
  "history": [],
  "toolList": [],
  "knowledgeInfo": {"status": 0}
}
```

### 文档处理接口
```http
POST /RAG-alg/file_format
Content-Type: application/json

{
  "file_id": "123456",
  "parse_method": "auto"
}
```

## 开发指南

### 添加新工具
1. 在 `awen-mcp-alg/mcpProtocol/service/` 目录下创建工具文件
2. 使用 `@mcp.tool()` 装饰器注册工具函数
3. 重启MCP服务

### 扩展知识处理
1. 在 `awen-knowledge-alg/src/data_processors/` 添加处理器
2. 在 `format_service.py` 中注册新的处理方法
3. 更新API接口

## 监控与日志

- 日志文件位置: `./logs/`
- 服务状态监控: 各服务提供健康检查接口
- 性能监控: 支持线程池监控和请求追踪

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: SUNKLM团队
- 邮箱: <EMAIL>
- 文档: [项目文档](docs/)

## 服务端口配置

| 服务名称 | 端口 | 协议 | 描述 |
|---------|------|------|------|
| API网关 | 9080 | HTTP | 统一接口网关 |
| 智能体服务 | 9081 | HTTP | Agent处理服务 |
| MCP服务 | 9082 | HTTP/SSE | 工具协议服务 |
| 知识处理 | 8014 | HTTP | 文档处理服务 |
| 检索服务 | 8013 | HTTP | 知识检索服务 |
| Agent算法 | 8012 | HTTP | Agent算法服务 |
| OCR检测 | 8865 | HTTP | OCR检测服务 |
| OCR系统 | 8868 | HTTP | OCR系统服务 |
| 结构化布局 | 8871 | HTTP | 文档布局分析 |

## 配置说明

### 数据库配置
```python
# MySQL配置
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@123"
MYSQL_DATABASE = "AGENT_FLOW"  # 或 "AWEN_PRIMARY"

# Redis集群配置
STARTUP_NODES = [
    {"host": "**********", "port": 7000},
    {"host": "**********", "port": 7001},
    {"host": "**********", "port": 7002},
]
```

### 模型配置
```python
# 默认模型配置
MODEL_TYPE = "qwen2.5-72b-instruct-gptq-int4"
API_BASE = "http://***********:9020/v1/chat/completions"
COMPANY = "qwen"
TEMPERATURE = 0.01
MAX_SEQ_LEN = 8192
MAX_OUT_LEN = 8192
```

### MinIO配置
```python
ENDPOINT = "***********:9000"
ACCESS_KEY = "minioadmin"
SECRET_KEY = "minioadmin"
BUCKET = "sunklm-dev"
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i:9080

   # 检查日志
   tail -f logs/nohup.out
   ```

2. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证网络连接和防火墙设置
   - 确认用户名密码正确

3. **模型调用失败**
   - 检查模型服务是否可用
   - 验证API_BASE地址是否正确
   - 确认模型类型配置匹配

4. **文件上传失败**
   - 检查MinIO服务状态
   - 验证存储桶权限
   - 确认文件大小限制

### 性能优化

1. **线程池配置**
   ```python
   THREAD_POOL_SIZE = 40  # 根据服务器性能调整
   ```

2. **缓存配置**
   - 启用Redis缓存
   - 配置合适的过期时间
   - 使用连接池

3. **数据库优化**
   - 添加适当索引
   - 配置连接池
   - 定期清理日志

## 安全配置

### API安全
- 实现请求限流 (默认: 10/minute)
- 添加API密钥验证
- 配置CORS策略

### 数据安全
- 敏感数据加密存储
- 定期备份数据库
- 配置访问控制

## 扩展开发

### 添加新的AI模型
1. 在配置文件中添加模型信息
2. 实现模型适配器
3. 更新路由配置

### 集成新的工具
1. 创建工具类继承基础工具接口
2. 实现工具的核心逻辑
3. 注册到MCP服务

### 自定义文档处理器
1. 继承基础处理器类
2. 实现特定格式的解析逻辑
3. 注册到格式处理服务

## 更新日志

### v1.0.0 (2024-12-01)
- 初始版本发布
- 支持基础的智能体对话功能
- 集成OCR识别服务
- 实现知识检索和文档处理
- 支持多模态处理 (文本、图像、语音)
- 实现MCP工具协议
- 提供统一API网关
