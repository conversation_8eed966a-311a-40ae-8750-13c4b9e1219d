import os
import json
import base64
from typing import Dict, List, Any, Optional
from pathlib import Path
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime


class DocxParser:
    """
    精准解析Docx文件的工具类
    支持提取文档中的图片、表格、页眉、页脚等详细信息
    """

    def __init__(self):
        self.namespace = {
            "w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main",
            "r": "http://schemas.openxmlformats.org/officeDocument/2006/relationships",
            "a": "http://schemas.openxmlformats.org/drawingml/2006/main",
            "pic": "http://schemas.openxmlformats.org/drawingml/2006/picture",
            "wp": "http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",
        }
        self.images = {}
        self.relationships = {}
        self.document_data = {
            "metadata": {},
            "content": [],
            "images": [],
            "tables": [],
            "headers": [],
            "footers": [],
            "styles": {},
        }

    def parse_docx(self, docx_path: str) -> Dict[str, Any]:
        """
        解析Docx文件的主函数
        """
        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"文件不存在: {docx_path}")

        with zipfile.ZipFile(docx_path, "r") as docx_zip:
            # 解析文档关系
            self._parse_relationships(docx_zip)

            # 提取图片
            self._extract_images(docx_zip)

            # 解析文档内容
            self._parse_document(docx_zip)

            # 解析页眉页脚
            self._parse_headers_footers(docx_zip)

            # 解析样式
            self._parse_styles(docx_zip)

            # 解析文档属性
            self._parse_document_properties(docx_zip)

        return self.document_data

    def _parse_relationships(self, docx_zip: zipfile.ZipFile):
        """解析文档关系"""
        try:
            rels_xml = docx_zip.read("word/_rels/document.xml.rels")
            root = ET.fromstring(rels_xml)

            for rel in root.findall(".//r:Relationship", self.namespace):
                rel_id = rel.get("Id")
                target = rel.get("Target")
                rel_type = rel.get("Type")
                self.relationships[rel_id] = {"target": target, "type": rel_type}
        except KeyError:
            pass

    def _extract_images(self, docx_zip: zipfile.ZipFile):
        """提取文档中的图片"""
        for filename in docx_zip.namelist():
            if filename.startswith("word/media/"):
                image_data = docx_zip.read(filename)
                image_name = os.path.basename(filename)

                # 将图片编码为base64
                image_base64 = base64.b64encode(image_data).decode("utf-8")

                self.images[image_name] = {
                    "filename": image_name,
                    "data": image_base64,
                    "size": len(image_data),
                    "format": os.path.splitext(image_name)[1].lower(),
                }

    def _parse_document(self, docx_zip: zipfile.ZipFile):
        """解析文档主体内容"""
        try:
            doc_xml = docx_zip.read("word/document.xml")
            root = ET.fromstring(doc_xml)

            body = root.find(".//w:body", self.namespace)
            if body is not None:
                self._parse_elements(body, self.document_data["content"])
        except KeyError:
            pass

    def _parse_elements(self, parent_element, content_list):
        """递归解析文档元素"""
        for element in parent_element:
            tag = element.tag.split("}")[-1] if "}" in element.tag else element.tag

            if tag == "p":  # 段落
                paragraph = self._parse_paragraph(element)
                if paragraph:
                    content_list.append(paragraph)

            elif tag == "tbl":  # 表格
                table = self._parse_table(element)
                if table:
                    content_list.append(table)
                    self.document_data["tables"].append(table)

            elif tag == "sectPr":  # 节属性
                section = self._parse_section_properties(element)
                if section:
                    content_list.append(section)

    def _parse_paragraph(self, p_element) -> Optional[Dict[str, Any]]:
        """解析段落"""
        paragraph = {
            "type": "paragraph",
            "text": "",
            "runs": [],
            "properties": {},
            "images": [],
        }

        # 解析段落属性
        p_pr = p_element.find("w:pPr", self.namespace)
        if p_pr is not None:
            paragraph["properties"] = self._parse_paragraph_properties(p_pr)

        # 解析段落内容
        for element in p_element:
            tag = element.tag.split("}")[-1] if "}" in element.tag else element.tag

            if tag == "r":  # 文本运行
                run = self._parse_run(element)
                if run:
                    paragraph["runs"].append(run)
                    paragraph["text"] += run["text"]

            elif tag == "drawing":  # 图片
                image_info = self._parse_drawing(element)
                if image_info:
                    paragraph["images"].append(image_info)
                    self.document_data["images"].append(image_info)

        return paragraph if paragraph["text"] or paragraph["images"] else None

    def _parse_run(self, r_element) -> Optional[Dict[str, Any]]:
        """解析文本运行"""
        run = {"text": "", "properties": {}}

        # 解析运行属性
        r_pr = r_element.find("w:rPr", self.namespace)
        if r_pr is not None:
            run["properties"] = self._parse_run_properties(r_pr)

        # 解析文本内容
        for element in r_element:
            tag = element.tag.split("}")[-1] if "}" in element.tag else element.tag

            if tag == "t":  # 文本
                text = element.text or ""
                run["text"] += text
            elif tag == "tab":  # 制表符
                run["text"] += "\t"
            elif tag == "br":  # 换行符
                run["text"] += "\n"

        return run if run["text"] else None

    def _parse_table(self, tbl_element) -> Dict[str, Any]:
        """解析表格"""
        table = {"type": "table", "rows": [], "properties": {}}

        # 解析表格属性
        tbl_pr = tbl_element.find("w:tblPr", self.namespace)
        if tbl_pr is not None:
            table["properties"] = self._parse_table_properties(tbl_pr)

        # 解析表格行
        for tr_element in tbl_element.findall("w:tr", self.namespace):
            row = {"cells": [], "properties": {}}

            # 解析行属性
            tr_pr = tr_element.find("w:trPr", self.namespace)
            if tr_pr is not None:
                row["properties"] = self._parse_row_properties(tr_pr)

            # 解析单元格
            for tc_element in tr_element.findall("w:tc", self.namespace):
                cell = {"content": [], "properties": {}}

                # 解析单元格属性
                tc_pr = tc_element.find("w:tcPr", self.namespace)
                if tc_pr is not None:
                    cell["properties"] = self._parse_cell_properties(tc_pr)

                # 解析单元格内容
                self._parse_elements(tc_element, cell["content"])

                row["cells"].append(cell)

            table["rows"].append(row)

        return table

    def _parse_drawing(self, drawing_element) -> Optional[Dict[str, Any]]:
        """解析图片"""
        image_info = {
            "type": "image",
            "filename": "",
            "description": "",
            "width": 0,
            "height": 0,
            "data": "",
        }

        # 查找图片关系ID
        blip = drawing_element.find(".//a:blip", self.namespace)
        if blip is not None:
            embed_id = blip.get(
                "{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed"
            )
            if embed_id and embed_id in self.relationships:
                target = self.relationships[embed_id]["target"]
                image_name = os.path.basename(target)

                if image_name in self.images:
                    image_info["filename"] = image_name
                    image_info["data"] = self.images[image_name]["data"]

        # 解析图片尺寸
        extent = drawing_element.find(".//wp:extent", self.namespace)
        if extent is not None:
            image_info["width"] = int(extent.get("cx", 0)) / 9525  # 转换为像素
            image_info["height"] = int(extent.get("cy", 0)) / 9525

        # 解析图片描述
        doc_pr = drawing_element.find(".//wp:docPr", self.namespace)
        if doc_pr is not None:
            image_info["description"] = doc_pr.get("descr", "")

        return image_info if image_info["filename"] else None

    def _parse_headers_footers(self, docx_zip: zipfile.ZipFile):
        """解析页眉页脚"""
        # 解析页眉
        for filename in docx_zip.namelist():
            if filename.startswith("word/header"):
                header_xml = docx_zip.read(filename)
                root = ET.fromstring(header_xml)
                header_content = []
                self._parse_elements(root, header_content)

                self.document_data["headers"].append(
                    {"filename": filename, "content": header_content}
                )

            elif filename.startswith("word/footer"):
                footer_xml = docx_zip.read(filename)
                root = ET.fromstring(footer_xml)
                footer_content = []
                self._parse_elements(root, footer_content)

                self.document_data["footers"].append(
                    {"filename": filename, "content": footer_content}
                )

    def _parse_styles(self, docx_zip: zipfile.ZipFile):
        """解析样式"""
        try:
            styles_xml = docx_zip.read("word/styles.xml")
            root = ET.fromstring(styles_xml)

            for style in root.findall(".//w:style", self.namespace):
                style_id = style.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}styleId"
                )
                style_name = style.find("w:name", self.namespace)

                if style_id:
                    self.document_data["styles"][style_id] = {
                        "name": (
                            style_name.get(
                                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val"
                            )
                            if style_name is not None
                            else ""
                        ),
                        "type": style.get(
                            "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}type",
                            "",
                        ),
                        "properties": {},
                    }
        except KeyError:
            pass

    def _parse_document_properties(self, docx_zip: zipfile.ZipFile):
        """解析文档属性"""
        try:
            core_xml = docx_zip.read("docProps/core.xml")
            root = ET.fromstring(core_xml)

            self.document_data["metadata"] = {
                "title": self._get_text_from_element(root, ".//dc:title"),
                "creator": self._get_text_from_element(root, ".//dc:creator"),
                "subject": self._get_text_from_element(root, ".//dc:subject"),
                "description": self._get_text_from_element(root, ".//dc:description"),
                "created": self._get_text_from_element(root, ".//dcterms:created"),
                "modified": self._get_text_from_element(root, ".//dcterms:modified"),
                "lastModifiedBy": self._get_text_from_element(
                    root, ".//cp:lastModifiedBy"
                ),
            }
        except KeyError:
            pass

    def _parse_paragraph_properties(self, p_pr_element) -> Dict[str, Any]:
        """解析段落属性"""
        properties = {}

        # 对齐方式
        jc = p_pr_element.find("w:jc", self.namespace)
        if jc is not None:
            properties["alignment"] = jc.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val", ""
            )

        # 缩进
        ind = p_pr_element.find("w:ind", self.namespace)
        if ind is not None:
            properties["indentation"] = {
                "left": ind.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}left",
                    "0",
                ),
                "right": ind.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}right",
                    "0",
                ),
                "firstLine": ind.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}firstLine",
                    "0",
                ),
            }

        return properties

    def _parse_run_properties(self, r_pr_element) -> Dict[str, Any]:
        """解析文本运行属性"""
        properties = {}

        # 字体
        r_fonts = r_pr_element.find("w:rFonts", self.namespace)
        if r_fonts is not None:
            properties["font"] = r_fonts.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}ascii",
                "",
            )

        # 字号
        sz = r_pr_element.find("w:sz", self.namespace)
        if sz is not None:
            properties["fontSize"] = sz.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val", ""
            )

        # 颜色
        color = r_pr_element.find("w:color", self.namespace)
        if color is not None:
            properties["color"] = color.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val", ""
            )

        # 样式标记
        if r_pr_element.find("w:b", self.namespace) is not None:
            properties["bold"] = True
        if r_pr_element.find("w:i", self.namespace) is not None:
            properties["italic"] = True
        if r_pr_element.find("w:u", self.namespace) is not None:
            properties["underline"] = True

        return properties

    def _parse_table_properties(self, tbl_pr_element) -> Dict[str, Any]:
        """解析表格属性"""
        properties = {}

        # 表格宽度
        tbl_w = tbl_pr_element.find("w:tblW", self.namespace)
        if tbl_w is not None:
            properties["width"] = {
                "value": tbl_w.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}w",
                    "",
                ),
                "type": tbl_w.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}type",
                    "",
                ),
            }

        return properties

    def _parse_row_properties(self, tr_pr_element) -> Dict[str, Any]:
        """解析表格行属性"""
        properties = {}

        # 行高
        tr_height = tr_pr_element.find("w:trHeight", self.namespace)
        if tr_height is not None:
            properties["height"] = {
                "value": tr_height.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val",
                    "",
                ),
                "rule": tr_height.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}hRule",
                    "",
                ),
            }

        return properties

    def _parse_cell_properties(self, tc_pr_element) -> Dict[str, Any]:
        """解析单元格属性"""
        properties = {}

        # 单元格宽度
        tc_w = tc_pr_element.find("w:tcW", self.namespace)
        if tc_w is not None:
            properties["width"] = {
                "value": tc_w.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}w",
                    "",
                ),
                "type": tc_w.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}type",
                    "",
                ),
            }

        # 单元格合并
        grid_span = tc_pr_element.find("w:gridSpan", self.namespace)
        if grid_span is not None:
            properties["colspan"] = grid_span.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val", "1"
            )

        return properties

    def _parse_section_properties(self, sect_pr_element) -> Dict[str, Any]:
        """解析节属性"""
        section = {"type": "section", "properties": {}}

        # 页面尺寸
        pg_sz = sect_pr_element.find("w:pgSz", self.namespace)
        if pg_sz is not None:
            section["properties"]["pageSize"] = {
                "width": pg_sz.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}w",
                    "",
                ),
                "height": pg_sz.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}h",
                    "",
                ),
                "orientation": pg_sz.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}orient",
                    "portrait",
                ),
            }

        # 页边距
        pg_mar = sect_pr_element.find("w:pgMar", self.namespace)
        if pg_mar is not None:
            section["properties"]["pageMargin"] = {
                "top": pg_mar.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}top",
                    "",
                ),
                "right": pg_mar.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}right",
                    "",
                ),
                "bottom": pg_mar.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}bottom",
                    "",
                ),
                "left": pg_mar.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}left",
                    "",
                ),
            }

        return section

    def _get_text_from_element(self, root, xpath) -> str:
        """从XML元素中获取文本"""
        element = root.find(
            xpath,
            {
                "dc": "http://purl.org/dc/elements/1.1/",
                "dcterms": "http://purl.org/dc/terms/",
                "cp": "http://schemas.openxmlformats.org/package/2006/metadata/core-properties",
            },
        )
        return element.text if element is not None else ""

    def to_json(self, output_path: str = None) -> str:
        """将解析结果转换为JSON格式"""
        json_data = json.dumps(self.document_data, ensure_ascii=False, indent=2)

        if output_path:
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(json_data)

        return json_data

    def to_markdown(self, output_path: str = None) -> str:
        """将解析结果转换为Markdown格式"""
        markdown_lines = []

        # 添加元数据
        if self.document_data["metadata"]:
            markdown_lines.append("# 文档信息\n")
            for key, value in self.document_data["metadata"].items():
                if value:
                    markdown_lines.append(f"**{key}**: {value}\n")
            markdown_lines.append("\n---\n")

        # 添加内容
        markdown_lines.append("# 文档内容\n")
        self._content_to_markdown(self.document_data["content"], markdown_lines)

        # 添加页眉
        if self.document_data["headers"]:
            markdown_lines.append("\n## 页眉\n")
            for i, header in enumerate(self.document_data["headers"]):
                markdown_lines.append(f"### 页眉 {i+1}\n")
                self._content_to_markdown(header["content"], markdown_lines)

        # 添加页脚
        if self.document_data["footers"]:
            markdown_lines.append("\n## 页脚\n")
            for i, footer in enumerate(self.document_data["footers"]):
                markdown_lines.append(f"### 页脚 {i+1}\n")
                self._content_to_markdown(footer["content"], markdown_lines)

        # 添加图片列表
        if self.document_data["images"]:
            markdown_lines.append("\n## 图片列表\n")
            for i, image in enumerate(self.document_data["images"]):
                markdown_lines.append(f"{i+1}. {image['filename']}")
                if image["description"]:
                    markdown_lines.append(f" - {image['description']}")
                markdown_lines.append(
                    f" - 尺寸: {image['width']:.0f}x{image['height']:.0f}\n"
                )

        markdown_content = "".join(markdown_lines)

        if output_path:
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(markdown_content)

        return markdown_content

    def _content_to_markdown(self, content_list: List[Dict], markdown_lines: List[str]):
        """将内容转换为Markdown格式"""
        for item in content_list:
            if item["type"] == "paragraph":
                if item["text"].strip():
                    markdown_lines.append(f"{item['text']}\n\n")

                # 添加段落中的图片
                for image in item["images"]:
                    markdown_lines.append(
                        f"![{image['description']}](data:image/{image['filename'].split('.')[-1]};base64,{image['data'][:50]}...)\n\n"
                    )

            elif item["type"] == "table":
                markdown_lines.append(self._table_to_markdown(item))

            elif item["type"] == "section":
                markdown_lines.append("\n---\n")

    def _table_to_markdown(self, table: Dict) -> str:
        """将表格转换为Markdown格式"""
        if not table["rows"]:
            return ""

        markdown_table = []

        # 处理表头
        if table["rows"]:
            header_row = table["rows"][0]
            header_cells = []
            separator_cells = []

            for cell in header_row["cells"]:
                cell_text = ""
                for content in cell["content"]:
                    if content["type"] == "paragraph":
                        cell_text += content["text"].strip()

                header_cells.append(cell_text or " ")
                separator_cells.append("---")

            markdown_table.append("| " + " | ".join(header_cells) + " |")
            markdown_table.append("| " + " | ".join(separator_cells) + " |")

            # 处理数据行
            for row in table["rows"][1:]:
                row_cells = []
                for cell in row["cells"]:
                    cell_text = ""
                    for content in cell["content"]:
                        if content["type"] == "paragraph":
                            cell_text += content["text"].strip()

                    row_cells.append(cell_text or " ")

                markdown_table.append("| " + " | ".join(row_cells) + " |")

        return "\n".join(markdown_table) + "\n\n"


def main():
    """主函数示例"""
    # 使用示例
    parser = DocxParser()

    # 解析文档
    docx_path = "/Users/<USER>/Desktop/大模型接口.docx"  # 替换为您的docx文件路径

    try:
        document_data = parser.parse_docx(docx_path)

        # 生成JSON文件
        json_output = parser.to_json("output.json")
        print("JSON文件已生成: output.json")

        # 生成Markdown文件
        markdown_output = parser.to_markdown("output.md")
        print("Markdown文件已生成: output.md")

        # 打印基本信息
        print(f"\n文档解析完成:")
        print(
            f"- 段落数: {len([c for c in document_data['content'] if c['type'] == 'paragraph'])}"
        )
        print(f"- 表格数: {len(document_data['tables'])}")
        print(f"- 图片数: {len(document_data['images'])}")
        print(f"- 页眉数: {len(document_data['headers'])}")
        print(f"- 页脚数: {len(document_data['footers'])}")

    except FileNotFoundError:
        print(f"错误: 文件 {docx_path} 不存在")
    except Exception as e:
        print(f"解析错误: {e}")


if __name__ == "__main__":
    main()
