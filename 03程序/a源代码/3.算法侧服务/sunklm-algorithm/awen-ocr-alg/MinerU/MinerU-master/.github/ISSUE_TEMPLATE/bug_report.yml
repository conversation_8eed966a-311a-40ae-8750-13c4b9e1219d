name: 🐛 Bug Report
description: Create a bug report for MinerU | MinerU 的 Bug 反馈
labels: bug

# We omit `title: "..."` so that the field defaults to blank. If we set it to
# empty string, Github seems to reject this .yml file.

body:
  - type: markdown
    attributes:
      value: |
        Thank you for submitting a MinerU 🐛 Bug Report! | 感谢您提交 MinerU 🐛 Bug 反馈！

  - type: checkboxes
    attributes:
      label: 🔎 Search before asking | 提交之前请先搜索
      description: >
        Please search the MinerU [Readme](https://github.com/opendatalab/MinerU), [Issues](https://github.com/opendatalab/MinerU/issues) and [Discussions](https://github.com/opendatalab/MinerU/discussions) to see if a similar bug report already exists.
      options:
        - label: I have searched the MinerU [Readme](https://github.com/opendatalab/MinerU) and found no similar bug report.
          required: true
        - label: I have searched the MinerU [Issues](https://github.com/opendatalab/MinerU/issues) and found no similar bug report.
          required: true
        - label: I have searched the MinerU [Discussions](https://github.com/opendatalab/MinerU/discussions) and found no similar bug report.
          required: true

  - type: checkboxes
    attributes:
      label: 🤖 Consult the online AI assistant for assistance | 在线 AI 助手咨询
      description: >
        This [online AI assistant](https://deepwiki.com/opendatalab/MinerU) is specifically trained to help with MinerU and related topics! It's available 24/7 and ready to provide insights.
      options:
        - label: I have consulted the [online AI assistant](https://deepwiki.com/opendatalab/MinerU) but was unable to obtain a solution to the issue.
          required: true

  - type: textarea
    id: description
    attributes:
      label: Description of the bug | 错误描述
      description: |
        Provide console output with error messages and/or screenshots of the bug. | 请提供详细报错信息或者截图
      placeholder: |
        💡 ProTip! Include as much information as possible (screenshots, logs, tracebacks etc.) to receive the most helpful response.
    validations:
      required: true
  
  - type: textarea
    id: reproduce
    attributes:
      label: How to reproduce the bug | 如何复现
      
      # Should not word-wrap this description here.
      description: |
        If you have questions about the parsing results or encounter errors during execution: | 如对解析结果有疑问或在运行中出现报错等异常:
        * Provide a minimal reproducible example. | 请提供一个最小可复现的demo。 
        * The demo should include the complete steps, code, and the PDF file to be parsed. | demo需要包含完整的操作步骤，代码，以及需要解析的PDF文件。
        * When reporting parsing result anomalies and runtime errors, reproducible PDF files are essential. If the document is too large or confidential, you can print the problematic page(s) via the browser and submit the corresponding example file.
        * 在反馈解析结果异常和运行时报错时，可复现的PDF文件是必不可少的，如文档过大或涉密，您可通过浏览器打印出出现问题的某一页或某几页再提交相应的示例文件。
        
        
        For problems when building or installing MinerU: | 在构建或安装 MinerU 时遇到的问题:
        * Give the **exact** build/install commands that were run. | 提供**确切**的构建/安装命令。
        * Give the **complete** output from these commands. | 提供这些命令的**完整**输出。
  
    validations:
      required: true

#  - type: markdown
#    attributes:
#      value: |
#        # The information below is required.


  - type: dropdown
    id: os_mode
    attributes:
      label: Operating System Mode | 操作系统类型
      #multiple: true
      options:
        -
        - Windows
        - Linux
        - MacOS
    validations:
      required: true

  - type: textarea
    id: os_name_version
    attributes:
      label: Operating System Version| 操作系统版本
      #multiple: true
      description: |
        * 如果您使用的是Linux系统，请提供Linux系统的**发行版名称**和**版本号**来帮助开发人员排查问题。 
        * If you are using a Linux system, please provide the Linux distribution and version number to help developers troubleshoot the issue.
        * 如果您使用的是Windows或MacOS系统，请提供操作系统的**版本号**来帮助开发人员排查问题。
        * If you are using a Windows or MacOS system, please provide the version number of the operating system to help developers troubleshoot the issue.
        * 例如：Ubuntu 22.04, CentOS 7.9, MacOS 15.1, Windows 11
        * For example: Ubuntu 22.04, CentOS 7.9, MacOS 15.1, Windows 11.

    validations:
      required: true

  - type: dropdown
    id: python_version
    attributes:
      label: Python version | Python 版本
      #multiple: true
      # Need quotes around `3.10` otherwise it is treated as a number and shows as `3.1`.
      options:
        -
        - "3.13"
        - "3.12"
        - "3.11"
        - "3.10"
    validations:
      required: true

  - type: dropdown
    id: software_version
    attributes:
      label: Software version | 软件版本 (mineru --version)
      #multiple: false
      options:
        -
        - "2.0.x"
    validations:
      required: true

  - type: dropdown
    id: device_mode
    attributes:
      label: Device mode | 设备模式
      #multiple: true
      options:
        -
        - cpu
        - cuda
        - mps
        - npu
    validations:
      required: true
