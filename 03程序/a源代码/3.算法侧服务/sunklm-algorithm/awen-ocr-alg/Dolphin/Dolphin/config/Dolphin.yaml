model:
  model_name_or_path: "./checkpoints/dolphin_model.bin"
  tokenizer_path: "./checkpoints/dolphin_tokenizer.json"
  extra_answer_tokens: True   # add <Answer/> token
  max_length: 4096
  decoder_layer: 10
  max_position_embeddings: 4096
  hidden_dimension: 1024
  swin_args:
    name: 'swin'
    img_size: [896, 896]
    patch_size: 4
    embed_dim: 128
    align_long_axis: False
    window_size: 7
    encoder_layer: [2, 2, 14, 2]
    num_heads: [4, 8, 16, 32]
