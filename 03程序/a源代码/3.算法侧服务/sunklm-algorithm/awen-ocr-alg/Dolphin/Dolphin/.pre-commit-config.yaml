repos:
  # 1. isort - 自动排序 Python imports
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1  # 使用固定版本号
    hooks:
      - id: isort
        name: isort (python)
        args: [--profile=black]  # 与 Black 兼容的配置
        language: python

  # 2. Black - 自动格式化 Python 代码
  - repo: https://github.com/psf/black
    rev: 25.1.0  # 使用固定版本号
    hooks:
      - id: black
        language: python

  # 3. flake8 - Python 静态检查
  - repo: https://github.com/pycqa/flake8
    rev: 7.2.0
    hooks:
      - id: flake8
        args: [--max-line-length=120, --ignore=E203]  # 设置行长度为 120
        additional_dependencies: [flake8-bugbear==24.12.12]  # 可选：增强检查

  # 4. pre-commit-hooks - 通用 Git 钩子
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace  # 删除行尾空格
      - id: end-of-file-fixer    # 确保文件以换行符结束
      - id: check-yaml           # 验证 YAML 文件语法
      - id: check-added-large-files  # 阻止大文件提交
        args: ["--maxkb=512"]
