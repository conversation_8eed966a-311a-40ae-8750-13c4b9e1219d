#!/user/bin/env python3
# -*- coding: utf-8 -*-
import os
current_path = os.path.dirname(os.getcwd()) #/home/<USER>/source/Awen/utils
parent_path = os.path.abspath('.')
# 项目路径
PROJECT_PATH = os.path.abspath('.')
#full_path = os.path.join(base_path, file_name)
# 向量模型字典
EMBEDDING_MODEL_NAME = "bge_m3" ##默认向量模型
embedding_model_dict = {
    'bge_m3': os.path.join(parent_path,"model/vec_model/bge-m3"),# m3e改为bge-m3模型
    # "bge_m3":"/home/<USER>/agentflow/sunklm-algorithm/awen-knowledge-alg/model/vec_model/bge-m3"
}
# 向量模型API 接口
EMBEDDING_MODEL_BASEURL = "http://***********:9002/v1/embeddings"
URL = "http://172.1.0.163:8011/Agent-alg/llm_chat"

MaxInsert = 5
# 向量维度
DIM = 1024
# 向量模型使用的设备
DEVICE = 'cuda:1'
# 语义切分模型
SEG_MODEl = os.path.join(parent_path, 'model/nlp_model/nlp_bert_document-segmentation_chinese-base')#'/home/<USER>/source/model/llm/nlp_bert_document-segmentation_chinese-base'

# ASR模型
ASR_MODEl = os.path.join(parent_path, 'model/asr_model/SenseVoiceSmall')#"/home/<USER>/source/model/asr/SenseVoiceSmall"

## 产品矩阵位置
MATRIX_PATH = os.path.join(parent_path, 'resources/product-matrix.json')#"/home/<USER>/source/Awen/resources/product-matrix.json"

DIM = 1024

# milvus 配置
KBS_CONFIG = {
    "milvus": {
        "password": '',
        "milvus_kb": ["KLM"], ## 目前只支持单一database：列表长度为1
        "port": 19530,
        "user": '',
        "milvus_col_name": "klm_qa",
        "host": "***********",
        "secure": False
    }
}

# 文档解析增强服务
GET_TASK = "http://***********:8888/data-aug/get_task"
CREAT_TASK = "http://***********:8888/data-aug/create_task"
CHANGE_TASK = "http://***********:9011/change"

# 图片解析服务配置
PDF_OCR_URL = "http://***********:9011/process"
# 图片下载位置
IMAGE_PATH = os.path.join(parent_path,"logs/mineru_logs")

# ES 配置
ES_URL = "http://**********:9200"
MINOBUCKET = "sunklm-dev"

#milvus及es的key值
match_text = "slice"
match_id = "file_id"
match_name = "file_name"
match_slice_id= "slice_id"
match_embedding = "slice_embedding"
match_abstract = "abstract"
match_abstract_embedding = "abstract_embedding"

match_text_faq = "answer"
match_id_faq = "faq_id"
match_name_faq = "question"
match_embedding_faq = "faq_embedding"
match_vector = "query_vector"




# 文本切片配置
CHUNK_SIZE_CUT = 10000

# 文本分割设置
CHUNK_SIZE = 500
CHUNK_OVERLAP = 100
# 摘要抽取方法配置
ABSTRACT_TYPE = "textrank"


# mino配置
ENDPOINT = "***********:9000"
# ACCESS_KEY = config['ACCESS_KEY']
# SECRET_KEY = config['SECRET_KEY']
ACCESS_KEY = 'minioadmin'
SECRET_KEY = 'minioadmin'

# mysql配置
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@123"
MYSQL_DATABASE = "AWEN_PRIMARY"

# Redis 集群的起始和结束节点
STARTUP_NODES = [
    {"host": "**********", "port": 7000},
    {"host": "**********", "port": 7001},
    {"host": "**********", "port": 7002},
]
REDIS_KEY = "knowledge-file-data"

TIMEZONE='Asia/Shanghai'

## 文本检索默认值
DEFAULT_TOPK = 5
DEFAULT_WEIGHT = 0.5
DEFAULT_THRESHOLD = 0
DEFAULT_MATCHTYPE = "keyMatch"
DEFAULT_MATCHCONTENT = "SPLIT"


## 开关参数默认值
IF_ABSTRACT = False  ## 是否提取摘要
IF_ABSTRACT_SEARCH = False ## 是否使用摘要检索
IF_RERANK = False    ## 是否进行rerank
IF_CONTEXTUAL_RETRIEVAL = False
IS_API = False


## 读取rerank模型
# rerank模型
RERANK_MODEL_DICT = {"bce-reranker-base_v1": parent_path+"/model/rerank_model/bce-reranker-base_v1",
                     "Conan-embedding-v1": parent_path+"/model/rerank_model/Conan-embedding-v1",
                     "jina-reranker-v2-base-multilingual":parent_path+"/model/rerank_model/jina-reranker-v2-base-multilingual",
                     #"MiniCPM-Reranker":"/home/<USER>/sunklm-algorithm/model/rerank_model/MiniCPM-Reranker",
                     "bge-reranker-v2-m3":parent_path+"/model/rerank_model/bge-reranker-v2-m3"}

## 默认rerank
DEFAULT_RERANK = "bge-reranker-v2-m3"

EMBEDDING_TOPN = 5
ES_TOPN = 10
MIN_FILE_LENGTH = 5
IFDISTANCES = False  ## 切片时是否启用距离合并


CHUNK_LENS_DOWN = 300
CHUNK_LENS_UP = 500

CLS_DICT = {
    '操作指导': 1,
    '产品简介': 2,
    '规范条文': 3,
    '释义': 4,
    '说明': 5,
    '通知': 6,
    '统计表格': 7,
    '问答对': 8,
    '协议': 9,
    '指导意见': 10
}


# 默认使用的模型
LLM_NAME = "Qwen2.5-72B-Instruct-GPTQ-Int4"
# 大模型相关配置
# 私有化部署的模型MAP
LLM_MAP = {
    "Qwen2.5-72B-Instruct-GPTQ-Int4": {
        "base_url": "http://172.1.0.134:9020/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode":0}}

# 最大输入长度
MAX_SEQ_LEN = 10000

# 最大输出长度：
MAX_OUT_LEN = 512
# 模型输出参数默认值
QUERY = "你好"
TEMPERATURE = 0.01
TOP_P = 0.01
TOP_K = 1
REWRITE = False
POSTPROCESS = False
STREAM = False
# 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
QUERY_PER_SECOND = 1

#输入窗口长度
CONTEXT_WINDOW = 50000


#过滤词表路径

FILTER_WORD_PATH = "/home/<USER>/agentflow/sunklm-algorithm/awen-knowledge-alg/resource/filter_data.txt"


FAQ_NUM = 10
FAQ_CONTEXT_LENGTH = 1000


# 架构
ARCHITECTURE = "arm"