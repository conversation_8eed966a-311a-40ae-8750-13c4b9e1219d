# -*- coding: utf-8 -*-
import re
import json
from src.llms.llm_chat import ModelAPI
from src.llms.prompt_template import ABSTRACT_TEMPLET_CN

class AbstractExtract:
    def get_abstract(self, text):
        # 基于本地部署得qwen大模型获取摘要
        prompt = ABSTRACT_TEMPLET_CN.format(text=text)
        llm = ModelAPI()
        abst = llm.generate(inputs=prompt)
        abstract =  abst["answer"]

        try:
            abstract = abstract.replace(" ", "").replace("\n", "")
            rule = r'json(.*?)```'
            abstract = json.loads(re.findall(rule, abstract)[0])["抽象式摘要"]
        except Exception as e:
            pass

        return abstract
