# -*- coding: utf-8 -*-
from src.utils.mysql_conn import MySQLClient
import json
from src.llms.llm_chat import ModelAPI
from src.llms.prompt_template import FILE_CLASSIFICATION_PROMPT2
from src.utils.log_utils import logger
from conf.server_config import CLS_DICT
logger = logger.bind(name="data_process")
class DocumentClassification:
    def __init__(self):
        """
        初始化方法
        
        这是类的构造函数，当创建一个新的类实例时会被自动调用。
        该构造函数的目的是为新创建的类实例初始化必要的属性或状态。
        在这个特定的例子中，它初始化了一个数据库客户端实例，使得后续可以进行数据库操作。
        """
        # 初始化MySQL客户端，用于后续的数据库操作
        self.mysql = MySQLClient()

    def _get_file_path(self, file_id):
        """
        根据文件ID获取文件路径。

        参数:
        file_id (str): 文件ID。

        返回:
        str 或 None: 文件路径，如果文件ID存在则返回文件路径，否则返回None。
        """
        try:
            # 建立与MySQL数据库的连接
            conn = self.mysql.get_connect()
            # 创建游标对象，用于执行SQL查询
            cursor = conn.cursor(buffered=True)
            # 准备SQL查询语句，用于根据file_id获取文件路径
            query = "SELECT file_path FROM xj_kg_file_convert_tb WHERE file_id = %s"
            # 执行SQL查询，参数化file_id以防止SQL注入
            cursor.execute(query, (file_id,))
            # 获取查询结果
            result = cursor.fetchone()
            # 如果结果存在，则返回文件路径；否则，返回None
            return result[0] if result else None
        except Exception as e:
            # 如果发生异常，记录错误信息并返回None
            logger.info(f"Error fetching file path for file_id {file_id}: {e}")
            return None

    def get_file_name(self, file_id):
        """
        通过文件ID获取文件名。

        参数:
        file_id (str): 文件的唯一ID。

        返回:
        str: 文件名，如果文件路径不存在则返回None。
        """
        # 获取文件的路径
        file_path = self._get_file_path(file_id)
        # 如果文件路径存在，则返回文件名
        if file_path:
            return file_path.split('/')[-1]
        else:
            # 如果文件路径不存在，记录日志并返回None
            logger.info(f"File path not found for file_id {file_id}")
            return None

    def classify_by_file_name(self, file_id):
        """
        根据文件名获取文件类别。

        该方法首先从给定的文件ID中获取文件名，然后使用预训练的模型API根据文件名获取文件类别。
        如果获取到文件名，则进一步处理以提取类别信息，并将该类别信息更新到数据库中。

        参数:
        file_id: 文件的唯一标识符，用于获取文件名和更新数据库。

        返回:
        无返回值，但会更新相关文件的类别信息到数据库。
        """
        # 获取文件名
        file_name = self.get_file_name(file_id)
        if file_name:
            # 初始化模型API
            llm = ModelAPI()
            # 构建基于文件名的分类提示
            prompt = FILE_CLASSIFICATION_PROMPT2.replace("${text}", file_name)
            # 使用模型生成答案
            answer = llm.generate(inputs=prompt)["answer"]
            # 处理答案以提取分类信息
            cls = answer.strip('```json\n').strip('```')
            cls = json.loads(cls)["classify_result"]
          
            # 记录分类信息的日志
            logger.info("输出类别", cls)
            logger.info("输出类别", CLS_DICT[cls])
            # 将分类信息更新到数据库
            try:
                # 获取数据库连接
                conn = self.mysql.get_connect()
                # 创建游标
                cursor = conn.cursor(buffered=True)
                # 准备更新分类信息的SQL语句
                update_query = "UPDATE xj_kg_file_convert_tb SET document_classification = %s WHERE file_id = %s"
                # 执行SQL更新操作
                cursor.execute(update_query, (CLS_DICT[cls], file_id))
                # 提交事务
                conn.commit()
                # 记录更新分类信息的日志
                logger.info(f"Inserted classification {cls} for file_id {file_id} into document_classification field")
            except Exception as e:
                # 异常处理：记录错误信息并回滚事务
                logger.info(f"Error inserting classification for file_id {file_id}: {e}")
                conn.rollback()
