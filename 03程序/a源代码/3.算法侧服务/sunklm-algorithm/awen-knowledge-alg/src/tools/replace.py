#!/user/bin/env python3
# -*- coding: utf-8 -*-
"""
该文件模块主要是工具类函数
"""
import re



def replace_all(input, toReplace, replaceWith ): 
    """"
    将文本中的一个词用另一个词替换
    """
    while input.find(toReplace) > -1:
        input = input.replace(toReplace, replaceWith)
    return input


def is_number(s):
    """"
    判断是否是数据
    """
    try:
        float(s)
        return True
    except ValueError:
        pass

    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass

    return False


def is_chinese(string):
    """
    检查整个字符串是否包含中文
    :param string: 需要检查的字符串
    :return: bool
    """
    for ch in string:
        if u'\u4e00' <= ch <= u'\u9fff':
            return True

    return False


def replace_num(s):
    """"
    将大写数字转换成小写数据
    """
    s2 = ""
    if len(s) == 2:

        s = s.replace('一百', '100')
        s = s.replace('九十', '90')
        s = s.replace('八十', '80')
        s = s.replace('七十', '70')
        s = s.replace('六十', '60')
        s = s.replace('五十', '50')
        s = s.replace('四十', '40')
        s = s.replace('三十', '30')
        s = s.replace('二十', '20')
    else:
        s = s.replace('一百〇', '10')
        if s == '一百一十':
            s = s.replace('一百一十', '110')
        elif '一百一十' in s:
            s = s.replace('一百一十', '11')
        s = s.replace('九十', '9')
        s = s.replace('八十', '8')
        s = s.replace('七十', '7')
        s = s.replace('六十', '6')
        s = s.replace('五十', '5')
        s = s.replace('四十', '4')
        s = s.replace('三十', '3')
        s = s.replace('二十', '2')
    if len(s) == 1:  # 需要先处理二十，后处理十，不然十已经被替换
        s = s.replace('十', '10')
    else:
        s = s.replace('十', '1')
    for n in s:

        n = n.replace('一', '1')
        n = n.replace('二', '2')
        n = n.replace('三', '3')
        n = n.replace('四', '4')
        n = n.replace('五', '5')
        n = n.replace('六', '6')
        n = n.replace('七', '7')
        n = n.replace('八', '8')
        n = n.replace('九', '9')
        s2 += n
    return s2


def containstr(s, rel):
    """"
    判断是否满足正则表达式
    """
    searchObj = re.search(rel, s, re.M|re.I)
    if searchObj:
        return True
    else:
        return False

