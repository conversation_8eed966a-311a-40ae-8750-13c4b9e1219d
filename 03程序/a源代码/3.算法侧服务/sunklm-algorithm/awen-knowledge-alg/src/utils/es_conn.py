from elasticsearch import Elasticsearch
from conf.server_config import *
from elasticsearch import helpers
from src.utils.log_utils import *
from src.rag.embedding import LoadsEmbeddingModel
logger = logger.bind(name="utils")

from src.utils.awen_schema import *
class EsClient:
    def __init__(self):
        """
        es引擎配置
        """
        self.client =Elasticsearch(ES_URL)
        if not IS_API:
            logger.info(f"调用向量化模型 {IS_API}")
            self.embedding_dict = LoadsEmbeddingModel().loads_model()

    def query_body(self, query, top_k):
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"match": {"{}".format(match_text): query}},
                    ],
                }
            },
            "size": top_k
        }
        return query


    def search(self, query, partition_names, top_k):
        """
            es检索文件
            :param query:用户问题
            :param top_k:输出候选集个数
            :param partition_name: 类目库
            :return:
        """
        try:
            logger.info(partition_names)
            partition_names = [name.lower().replace("-", "_") for name in partition_names]
            query = self.query_body(query, top_k)
            logger.info(f"[ES检索] ES检索问题: {query}")
            logger.info(f"[ES检索] ES检索开始")
            res = self.client.search(
                index=','.join(partition_names),  # 注意：索引名需要用逗号分隔
                body=query
            )
            logger.info(f"[ES检索] ES检索解析")
            #logger.info(res)
            result = []
            #logger.info(res)
            for line in res['hits']['hits']:
                #logger.info(line)
                search_split = {"from": {"fileId": line['_source'][match_id], "fileName": line['_source'][match_name],
                                         "sliceId": int(line['_id']), "sliceSource":"ES"},
                               "splitText": line['_source'][match_text], "score": line['_score']}
                result.append(search_split)
            output = sorted(result, key=lambda k: k['score'], reverse=True)
            logger.info(f"[ES检索] ES检索结果: {output}")
            #return output
            return return_dict(code=ReturnCode.SUCESS, data=output, message="sucess")
        except Exception as e:
            return return_dict(code=ReturnCode.ERROR, data=[], message="es 检索异常"+ str(e))

    def load_data(self, partition_name, id_list, splits, abstracts, file_id, file_name,batch_size=50):
        partition_name = partition_name.lower().replace("-", "_")
        total_actions = len(id_list)
        for start in range(0, total_actions, batch_size):
            actions = []
            for i in range(start, min(start + batch_size, total_actions)):
                custom_id = id_list[i]
                item = splits[i]
                abst = abstracts[i]
                action = {
                    "_index": partition_name,
                    "_id": custom_id,  # 使用提供的_id
                    "_source": {
                        match_id: file_id,
                        match_name: file_name,
                        match_text: item,
                        match_abstract: abst,
                    }
                }
                actions.append(action)
            helpers.bulk(self.client, actions)
        return 'data load success!!'

    def del_data(self, partition_name, slice_ids, batch_size=100):
        """
        删除ES中的向量数据
        :param partition_name: 类目库
        :param slice_ids: 要删除的文档ID列表
        :param batch_size: 每批删除的数据量
        :return:
        """
        modified_partition_name = partition_name.lower().replace("-", "_")
        logger.info(f"[ES检索] 从ES删除向量数据，向量索引为：{modified_partition_name}")

        total_deleted = 0
        for i in range(0, len(slice_ids), batch_size):
            actions = []
            for custom_id in slice_ids[i:i+batch_size]:
                action = {
                    "_op_type": "delete",
                    "_index": modified_partition_name,
                    "_id": custom_id
                }
                actions.append(action)

            try:
                success, errors = helpers.bulk(self.client, actions)
                total_deleted += len(actions)
                logger.info(f"[ES检索] 从ES删除向量数据，向量索引为：{modified_partition_name}，删除数据条数为：{len(actions)}")
                if errors:
                    logger.warning(f"[ES检索] 从ES删除向量数据时遇到错误：{errors}")
            except helpers.BulkIndexError as e:
                logger.error(f"[ES检索] 从ES删除向量数据失败，错误信息：{str(e)}")
                return return_dict(code=ReturnCode.ERROR, data=[], message="es 删除向量数据异常" + str(e))
            except Exception as e:
                logger.error(f"[ES检索] 从ES删除向量数据失败，错误信息：{str(e)}")
                return return_dict(code=ReturnCode.ERROR, data=[], message="es 删除向量数据异常" + str(e))

        logger.info(f"[ES检索] 总共删除数据条数为：{total_deleted}")
        return 'data delete success!!'

    def vector_query_body(self, query_vec, top_k,partition_names):
        knn = {
            "field": match_embedding,
            "query_vector": query_vec,
            "k": top_k,
            "num_candidates": 100,
            "filter":{
                "terms":{"filter_filed":partition_names}
            }
        }
        return knn
    # 创建带向量的索引
    def create_vector_index(self, kg_code):
        """
            创建带向量的索引
            :param partition_name: 类目库
            :return:
        """
        mappings = {
            "properties": {
                "file_id": {
                    "type": "long"
                },
                "file_name": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "slice": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "slice_embedding": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True,
                    "similarity": "l2_norm"
                },
                "filter_field":{
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    } 
                }
            }
        }
        try:
            if not self.client.indices.exists(index=kg_code):
                self.client.indices.create(index=kg_code, body={"mappings": mappings})
                logger.info(f"[ES检索] 创建索引成功，索引名称为：{kg_code}")
            else:
                logger.info(f"[ES检索] 索引已存在，索引名称为：{kg_code}")
            return return_dict(code=ReturnCode.SUCESS, data=[], message="success")
        except Exception as e:
            logger.error(f"[ES检索] 索引创建异常: {str(e)}")
            return return_dict(code=ReturnCode.ERROR, data=[], message=f"es 索引创建异常: {str(e)}")

    # es向量检索
    def vector_search(self, query, partition_names,kg_code, top_k, model_name):
        """
            es向量检索
            :param query_vec:用户问题转换成的向量
            :param top_k:输出候选集个数
            :param partition_name: 类目库
            :return:
        """
        if IS_API:
            query_vec  = [LoadsEmbeddingModel().embedding_api(query)]
        else:
            embedding = self.embedding_dict[model_name]
            query_vec = embedding.embedding_query(query).tolist()
        try:
            partition_names = [name.lower().replace("-", "_") + "v" for name in partition_names]
            query = self.vector_query_body(query_vec, top_k,partition_names)
            res = self.client.search(
                index= kg_code,  # 注意：索引名需要用逗号分隔
                knn=query
            )
            #logger.info(res)
            result = []
            #logger.info(res)
            for line in res['hits']['hits']:
                #logger.info(line)
                search_split = {"from": {"fileId": line['_source'][match_id], "fileName": line['_source'][match_name],
                                         "sliceId": int(line['_id']),"sliceSource":"ESVector"},
                                "splitText": line['_source'][match_text], "score": line['_score']}
                result.append(search_split)
            output = sorted(result, key=lambda k: k['score'], reverse=True)
            #ogger.info(f"[ES检索] ES检索结果: {output}")
            #return output
            return return_dict(code=ReturnCode.SUCESS, data=output, message="sucess")
        except Exception as e:
            return return_dict(code=ReturnCode.ERROR, data=[], message="es 检索异常"+ str(e))
    # 导入带向量的数据
    def load_data_with_vector(self, partition_name, id_list, splits, file_id, file_name, kg_code,batch_size=300):
        """
        ES向量检索
        :param partition_name: 类目库
        :param id_list: 用户问题转换成的向量
        :param splits: 元组的列表，列表中的每一项是元组（文本，向量）
        :param file_id: 文件ID
        :param file_name: 文件名
        :param batch_size: 每批插入的数据量
        :return:
        """
        # 按照一定规则修改partition_name
        modified_partition_name = partition_name.lower().replace("-", "_") + "v"
        logger.info(f"[ES检索] 向ES插入向量数据，向量索引为：{modified_partition_name}")

        # Check if the index exists, if not, create it
        if not self.client.indices.exists(index=kg_code):
            self.create_vector_index(kg_code)

        total_inserted = 0
        for i in range(0, len(splits), batch_size):
            actions = []
            for custom_id, (text, embedding) in zip(id_list[i:i+batch_size], splits[i:i+batch_size]):
                # 拼接插入数据结构
                action = {
                    "_index": kg_code,
                    "_id": custom_id,  # 使用提供的_id
                    "_source": {
                        "file_id": file_id,
                        "file_name": file_name,
                        "slice": text,
                        "slice_embedding": embedding,
                        "filter_field":partition_name
                    }
                }
                actions.append(action)

            # 批量插入
            try:
                success, _ = helpers.bulk(self.client, actions)
                total_inserted += len(actions)
                logger.info(f"[ES检索] 向ES插入向量数据，向量索引为：{modified_partition_name}，插入数据条数为：{len(actions)}")
            except Exception as e:
                logger.error(f"[ES检索] 向ES插入向量数据失败，错误信息：{str(e)}")
                return return_dict(code=ReturnCode.ERROR, data=[], message="es 插入向量数据异常" + str(e))

        logger.info(f"[ES检索] 总共插入数据条数为：{total_inserted}")
        return 'data load success!!'