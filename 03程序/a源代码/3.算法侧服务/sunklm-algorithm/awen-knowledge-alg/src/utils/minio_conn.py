
from minio import Minio
from conf.server_config import *
class MinioClient:
    def __init__(self, endpoint=ENDPOINT, access_key=ACCESS_KEY, secret_key=SECRET_KEY):
        """
        初始化Minio连接
        :param endpoint: Minio服务的端点URL
        :param access_key: 访问密钥
        :param secret_key: 秘密密钥
        """
        self.endpoint = endpoint
        self.access_key = access_key
        self.secret_key = secret_key
    def get_connect(self):
        """
        创建Minio客户端连接
        """
        return Minio(
            endpoint=self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=False
        )


