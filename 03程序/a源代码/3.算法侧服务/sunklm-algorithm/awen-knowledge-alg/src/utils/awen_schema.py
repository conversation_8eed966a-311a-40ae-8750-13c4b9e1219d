# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  awen_schema.py
   Description : 枚举值定义
   Author :  Arno
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

from enum import IntEnum
'''
参数返回的枚举值定义
'''
class ReturnCode(IntEnum):
    SUCESS = 0
    ERROR = 1


class HeadersCode():
    NORMAL_HEADERS = {'Content-Type': 'application/json'}

class ChatFormatCode:
    """
    openai:openai格式输入[{'role':'user','content':'你好'},{'role':'assistant','content':'你好。很高兴为您服务'}]
    glm：GLM格式输入\n<|im_start|>role\n你好<|im_end|>\n<|im_start|>assistant\n你好。很高兴为您服务<|im_end|>
    raw:不对query做任何处理
    """
    OPENAI_FORMAT = "openai"
    GLM_FORMAT = "glm"
    RAW_FORMAT = "raw"

class CompanyCode():
    PRIVATE = 0 #私有化部署的模型的标志，company为：厂商名_private
    OPEN = 1 #开源部署的模型的标志，company为：厂商名_open

class SearchCode():
    DATA_FILE = 'FILE'
    DATA_FAQ = 'FAQ'

    MATCH_EMD = 'emdMatch'
    MATCH_KEY = 'keyMatch'
    MATCH_MLT = 'mltMatch'
    ES_MATCH_MLT ="esMltMatch"
    ABS_MATCH_MLT = "mltAbsMatch"
    MATCH_CONTENT_SPLIT = 'SPLIT'
    MATCH_CONTENT_ABSTRACT = 'ABSTRACT'
    MATCH_CONTENT_MIX = 'MIX'

class FileStatus(IntEnum):
    Unprocessed = 0,
    Structuring = 2,
    Structured_Successfully = 3,
    Structured_Failed = 4,
    Start_Slice = 5
    Slicing = 6,
    Sliced_Successfully = 7,
    Sliced_Failed = 8,
    Start_Vector = 9
    Vectorizing = 10,
    Vectorized_Successfully = 11,
    Vectorized_Failed = 12,

class SliceStatus(IntEnum):
    Pending_Vectorization =0,
    Vectorizing =1,
    Available_Retrieval =2,
    Vectorization_Failed =3,
    Pending_Insertion_ES =4,
    Insertion_ES_Successful =5,

class FaqStatus(IntEnum):
    # INSERT_YOUR_CODE
    FAQ_Unprocessed = 0,
    FAQ_Vectorization_Start = 1,
    FAQ_Vectorizing = 2,
    FAQ_Vectorized_Successfully = 3,
    FAQ_Vectorization_Failed = 4,

    # INSERT_YOUR_CODE
class ParseMethod(IntEnum):
    SIMPLE_PARSE = 0
    DEEP_PARSE = 1
    IMAGE_PARSE = 2
