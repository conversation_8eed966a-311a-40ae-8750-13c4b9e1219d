from rediscluster import Redis<PERSON><PERSON>
import json
from conf.server_config import STARTUP_NODES
from src.utils.log_utils import *
logger = logger.bind(name="utils")

class RedisConnector:
    def __init__(self):
        self.startup_nodes = STARTUP_NODES
        self.rc = RedisCluster(startup_nodes=self.startup_nodes, decode_responses=True)

    def update_file_status(self, key, field_id, new_status):
        try:
            value = self.rc.hget(key, field_id)
            value_dict = json.loads(value)
            value_dict['fileStatus'] = new_status
            new_value = json.dumps(value_dict)
            result = self.rc.hset(key, field_id, new_value)
            if result:
                logger.info(f"Value for {field_id} in key '{key}' has been updated.")
            else:
                logger.info("Failed to update the value.")
        except Exception as e:
            logger.error(f"Failed to update file status: {e}")

    def update_end_time(self, key, field_id, end_time_key, new_end_time):
        try:
            value = self.rc.hget(key, field_id)
            value_dict = json.loads(value)
            value_dict[end_time_key] = new_end_time
            new_value = json.dumps(value_dict)
            result = self.rc.hset(key, field_id, new_value)
            if result:
                logger.info(f"Value for {field_id} in key '{key}' has been updated.")
            else:
                logger.info("Failed to update the value.")
        except Exception as e:
            logger.error(f"Failed to update end time: {e}")
