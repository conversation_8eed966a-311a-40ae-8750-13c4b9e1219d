from pymilvus import (
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
)
from pymilvus import connections,db
from conf.server_config import *
from src.utils.log_utils import return_dict
import numpy as np
from src.rag.embedding import LoadsEmbeddingModel
from scipy.linalg import norm
from src.utils.awen_schema import *
from src.utils.log_utils import logger
import traceback
logger = logger.bind(name="utils")
import time
class MilvusClient:
    def __init__(self,
                 host=KBS_CONFIG['milvus']['host'],
                 port=KBS_CONFIG['milvus']['port'],
                 db_name=KBS_CONFIG['milvus']['milvus_kb'][0]):
        self.host = host
        self.port = port
        self.dbname = db_name
        self._connect()
        self.flush_time = 0
        self.flush_delete_time = 0
        self.flush_time_faq = 0
    
    def _connect(self):
        connections.connect(db_name=self.dbname, host=self.host, port=self.port)

    # def _connect(self):
    #     try:
    #         if self.dbname not in db.list_database():
    #             db.create_database(self.dbname)
    #             logger.info(f"Created database {self.dbname}")
    #     except Exception as e:
    #         logger.error(f"Error creating database {self.dbname}: {str(e)}")
    #         raise
            
    #     connections.connect(db_name=self.dbname, host=self.host, port=self.port)



    def _collection_exists(self, col_name):
        """Private method to check if a collection exists."""
        return utility.has_collection(col_name)

    def _partition_exists(self, col_name, partition_name):
        return utility.has_partition(col_name, partition_name)

    def do_drop_col(self, col_name):
        """Drops a collection if it exists."""
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            collection.release()
            collection.drop()
            print(f'Collection {col_name} deleted successfully.')
        else:
            print(f'Collection {col_name} does not exist.')
    def do_create_col(self, col_name, description, colType):
        """
        创建集合
        col_name: 集合名
        description: 描述信息
        colType: 集合类型，决定字段配置
        """
        field_mapping = {
            "FILE": {
                "primary_key": match_slice_id,
                "fields": [
                    FieldSchema(name=match_slice_id, dtype=DataType.INT64, is_primary=True, auto_id=False),
                    FieldSchema(name=match_id, dtype=DataType.INT64),
                    FieldSchema(name=match_name, dtype=DataType.VARCHAR, max_length=5000),
                    FieldSchema(name=match_text, dtype=DataType.VARCHAR, max_length=65535),
                    FieldSchema(name=match_abstract, dtype=DataType.VARCHAR, max_length=65535, is_optional=True),
                    FieldSchema(name=match_embedding, dtype=DataType.FLOAT_VECTOR, dim=DIM),
                    FieldSchema(name=match_abstract_embedding, dtype=DataType.FLOAT_VECTOR, dim=DIM, is_optional=True),
                ],
                "index_fields": [match_embedding, match_abstract_embedding]
            },
            "FAQ": {
                "primary_key": match_id_faq,
                "fields": [
                    FieldSchema(name=match_id, dtype=DataType.INT64),
                    FieldSchema(name=match_id_faq, dtype=DataType.INT64, is_primary=True, auto_id=False),
                    FieldSchema(name=match_name_faq, dtype=DataType.VARCHAR, max_length=5000),
                    FieldSchema(name=match_text_faq, dtype=DataType.VARCHAR, max_length=5000),
                    FieldSchema(name=match_embedding_faq, dtype=DataType.FLOAT_VECTOR, dim=DIM),
                ],
                "index_fields": [match_embedding_faq]
            }
        }

        if colType not in field_mapping:
            raise ValueError("Unsupported colType: {}".format(colType))

        primary_key_name = field_mapping[colType]["primary_key"]
        fields = field_mapping[colType]["fields"]
        index_fields = field_mapping[colType]["index_fields"]

        schema = CollectionSchema(fields=fields,
                                  description=description,
                                  enable_dynamic_field=True)

        collection = Collection(name=col_name,
                                schema=schema,
                                using='default',
                                shards_num=2,
                                consistency_level="Strong")

        index_params = {
            "metric_type": "IP",
            "index_type": "IVF_FLAT",
            "params": {"nlist": 128}
        }

        for index_field in index_fields:
            collection.create_index(
                field_name=index_field,
                index_params=index_params,
                index_name=f"{index_field}_index"
            )

        return collection
    
    def do_delete_col(self, col_name):
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            collection.release()
            collection.drop()
            logger.info(f'Collection {col_name} deleted successfully.')
        else:
            logger.info(f'Collection {col_name} does not exist.')

    def add_partition(self, partition_name, col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        """
        增加分区
        partition_name_list：需要新增的分区名（类别库名）
        """
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            if collection.has_partition(partition_name):
                logger.info(f'已有的分区{collection.partitions}')
            else:
                collection.create_partition(partition_name)
        else:
            logger.info(f'不存在集合{col_name}')

    def del_partition(self, partition_name, col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            if collection.has_partition(partition_name):
                collection.drop_partition(partition_name)
            else:
                logger.info(f'待删除的{partition_name}分区不存在')
        else:
            logger.info(f'无集合{col_name}')
    def insert_faq(self, file_id, ids, partition_name, questions, vecs, answers, col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        partition_name = partition_name.lower().replace("-", "_")
        
        # 获取或创建collection
        if self._collection_exists(col_name):
            collection = Collection(col_name)
        else:
            logger.info(f'集合{col_name}不存在')
            collection = self.do_create_col(col_name, f"{col_name}", "FAQ")
            print(f'milvus数据库中集合{col_name}不存在，do_create_faq_col创建相应的集合')

        # 检查并创建partition
        if not self._partition_exists(col_name, partition_name):
            self.add_partition(partition_name, col_name)
            print(f"milvus数据库中集合{col_name}中不存在{partition_name}，已创建")

        file_id_list = [file_id] * len(ids)
        batch_size = 1000
        data_size = len(ids)
        insert_data = [file_id_list, ids, questions, answers, vecs]

        # 分批插入
        for start in range(0, data_size, batch_size):
            end = min(start + batch_size, data_size)
            batch_data = [data[start:end] for data in insert_data]
            mr = collection.insert(batch_data, partition_name=partition_name)
            logger.info(f'Inserted batch from {start} to {end}')   
            # 每20批flush一次
        self.flush_time_faq += 1
        if self.flush_time_faq % 20 == 0:
            collection.flush()
            time.sleep(1)
            self.flush_time_faq = 0

        logger.info(f'数据插入成功，共插入{data_size}条数据，总计{collection.num_entities}条数据')


    def insert(self, partition_name, slice_id_list, file_id, texts, vec_list, file_name, col_name=KBS_CONFIG["milvus"]["milvus_col_name"], abstracts=None, abstract_vectors=None):
        """
        向milvus向量数据库中插入实体
        参数说明:
        partition_name: 分区名称
        slice_id_list: 切片ID列表
        file_id: 文件ID
        texts: 文本内容列表
        vec_list: 向量列表
        file_name: 文件名
        col_name: 集合名称
        abstracts: 摘要列表(可选)
        abstract_vectors: 摘要向量列表(可选)
        """
        partition_name = partition_name.lower().replace("-", "_")

        # 获取或创建集合
        collection = Collection(col_name) if self._collection_exists(col_name) else self.do_create_col(col_name, f"{col_name}", "FILE")

        # 检查并创建分区
        if not self._partition_exists(col_name, partition_name):
            self.add_partition(partition_name, col_name)
            logger.info(f"已在集合 {col_name} 中创建分区 {partition_name}")

        # 准备数据
        file_id_list = [file_id] * len(slice_id_list)
        file_name_list = [file_name] * len(slice_id_list)
        abstracts = abstracts if abstracts is not None else [""] * len(slice_id_list)
        abstracts = [str(abs) if not isinstance(abs, str) else abs for abs in abstracts]
        abstract_vectors = abstract_vectors if abstract_vectors is not None else [[0.0] * DIM] * len(slice_id_list)

        # 创建实体字典列表
        entities = []
        for i in range(len(slice_id_list)):
            entity = {
                match_slice_id: slice_id_list[i],
                match_id: file_id_list[i],
                match_name: file_name_list[i],
                match_text: texts[i],
                match_abstract: abstracts[i],
                match_embedding: vec_list[i],
                match_abstract_embedding: abstract_vectors[i]
            }
            entities.append(entity)

        # 分批插入数据
        batch_size = 1000
        for start in range(0, len(entities), batch_size):
            end = min(start + batch_size, len(entities))
            batch_entities = entities[start:end]
            collection.insert(batch_entities, partition_name=partition_name)
            logger.info(f'已插入批次数据: {start} 到 {end}')

        # 定期刷新数据
        self.flush_time += 1
        if self.flush_time % 20 == 0:
            collection.flush()
            time.sleep(1)
            self.flush_time = 0

        logger.info(f'数据插入完成，集合中共有 {collection.num_entities} 条实体')
    def del_entity(self, partition_name, expr, col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        """
        删除实体
        partition_name：分区名（类别名）
        expr：表达式
        """
        partition_name = partition_name.lower().replace("-", "_")
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            logger.info(f"删除前{collection.num_entities}条数据")
            if collection.has_partition(partition_name):
                result = collection.delete(expr, partition_name=partition_name)
                self.flush_delete_time += 1
                logger.info(f"删除后{collection.num_entities}条数据,{result}")     
                if  self.flush_delete_time % 20 == 0:  # Flush every 10 batches
                    collection.flush()
                    time.sleep(1)
                    self.flush_delete_time = 0
            else:
                logger.info(f'待删除的{partition_name}分区不存在')# logger.info(f"删除后{collection.num_entities}条数据,{result}")
        else:
            logger.info(f'无集合{col_name}')

    def get_doc_by_ids(self, ids, partition_name, col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        """
        根据id从milvus查询数据
        """
        result = []
        if self._partition_exists(col_name, partition_name):
            collection = Collection(col_name)
            partition = collection.partition(partition_name)
            data_list = partition.query(expr=f'id in {ids}',
                                         output_fields=["split_text",
                                                        "file_name",
                                                        "file_id"],
                                         consistency_level="Strong")
            print(data_list)
            for data in data_list:
                text = data.pop("split_text")
                value = data.pop("file_name")
                result.append([text, value])
        return result
    def delete_by_id(self, ids, pk, partition_name,col_name=KBS_CONFIG["milvus"]["milvus_col_name"]):
        # 定义过滤条件
        filter_expr = f"{pk} in {ids}"
        print(filter_expr)
        # 构造过滤表达式，使用 OR 连接每个 ID 的等式表达式
        # expr = ' || '.join([f'slice_id == {id}' for id in ids])
        # expr =f"id == {id}"
        # 执行删除操作
        self.del_entity(partition_name,filter_expr,col_name)

        logger.info(f'删除{ids}数据成功')

    # 刷新数据，确保删除操作在检索时可见
    def search(self, match_content, data_type, query, partition_names, top_k, model_name, col_name, param=None):
        """
        从分区中查询数据
        query：用户问题
        partition_names：分区名（类别名）
        """
        """
           Description:从分区中查询数据
           Args:
               data_type(str):是文件检索还是FAQ检索
               query （str）: 用户问题，不能为空
               partition_names（list）：待检索分区列表
               top_k （int）：召回前top-K的切片或问答对
               model_name （str）：向量检索算法，BGE或M3E
               col_name（str）：collection名称
               param（dict）：检索配置字典，默认为{"metric_type": "IP", "params": {"nprobe": 10}} 
           Returns:
                输出向量化检索的top-k切片或者问题列表
        """
        #  embedding　
        # 把name改为小写、"-"改为"_".
        partition_names = [name.lower().replace("-", "_") for name in partition_names]
        #logger.info(partition_names)
        logger.info('[Milvus向量库] 开始获取向量化参数和模型')
        try:
            collection = Collection(col_name)
            collection.load()
            if param is None:
                param = {"metric_type": "IP", "params": {"nprobe": 10}}
            output_fields = []
            if data_type == 'FILE':
                output_fields = [match_id, match_name, match_text, match_slice_id]
                if match_content == False:
                    anns_field_emd = match_embedding
                else:
                    anns_field_emd = match_abstract_embedding
            else:
                output_fields = [match_id_faq, match_name_faq, match_text_faq]
                anns_field_emd = match_embedding_faq

            # embedding_dict = LoadsEmbeddingModel().loads_model()
            # embedding = embedding_dict[model_name]
        except:
            logger.error(traceback.print_exc())
            errorMsg =  "embedding model {} not exist".format(model_name)
            return return_dict(code=ReturnCode.ERROR, data=[], message=errorMsg)
        
            
        logger.info('[Milvus向量库] 开始问题向量化')
        if IS_API:
            con_vec = [LoadsEmbeddingModel().embedding_api(query)]
        else:
            embedding_dict = LoadsEmbeddingModel().loads_model()
            embedding = embedding_dict[model_name]
            con_vec = [embedding.embedding_query(query)]
        logger.info('[Milvus向量库] 开始进行向量检索')
        #logger.info("con_vec len {}".format(con_vec[0].shape))

        try:
            res = collection.search(con_vec,
                                    anns_field=anns_field_emd,
                                    param=param,
                                    partition_names=partition_names,
                                    limit=top_k,
                                    output_fields=output_fields)

            #logger.info('[Milvus向量库] 向量检索内容.{}'.format(res))
            logger.info('[Milvus向量库] 向量检索结束。')
            ret = []
            if data_type == 'FILE':
                for result in res[0]:
                    ret.append({"from": {"fileId": result.entity.get(match_id),
                                         "fileName": result.entity.get(match_name),
                                         "sliceId": result.entity.get(match_slice_id),
                                          "sliceSource":"Milvus"},
                                "splitText": result.entity.get(match_text), "score": result.distance})
            else:
                for result in res[0]:
                    ret.append({"from": {"fileId": result.entity.get(match_id),"fileName": result.entity.get(match_name_faq), "faqId": result.entity.get(match_id_faq)},
                                "question": result.entity.get(match_name_faq), "answer":result.entity.get(match_text_faq), "score": result.distance})

                    logger.info(f"ret {ret}")
        except Exception as e:
            return return_dict(code=ReturnCode.ERROR, data=[], message="Milvus search error"+str(e))

        return return_dict(code=ReturnCode.SUCESS, data=ret, message="sucess")