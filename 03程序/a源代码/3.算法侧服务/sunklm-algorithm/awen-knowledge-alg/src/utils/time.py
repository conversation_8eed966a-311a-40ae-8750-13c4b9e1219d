
from conf.server_config import TIMEZONE
from datetime import datetime
import pytz

def get_current_time():
    """
    获取当前时间，格式为字符串，例如：2023-04-01 12:00:00
    """
    # 获取上海时区的时间
    tz = pytz.timezone(TIMEZONE)
    now = datetime.now(tz)
    return now.strftime('%Y-%m-%d %H:%M:%S')

def time_difference(start_time: str, end_time: str) -> int:
    """
    计算两个时间字符串的差值，返回秒数

    参数:
    start_time (str): 开始时间，格式为 '%Y-%m-%d %H:%M:%S'
    end_time (str): 结束时间，格式为 '%Y-%m-%d %H:%M:%S'

    返回:
    int: 时间差的秒数
    """
    # 确保时间格式正确
    try:
        start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
        end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        raise ValueError("时间格式不正确，应为 '%Y-%m-%d %H:%M:%S'")

    # 计算时间差
    time_delta = end_dt - start_dt

    # 返回秒数
    return int(time_delta.total_seconds())
