# -*- coding: utf-8 -*-
from contextlib import contextmanager
from src.rag.text_splitter import ChineseRecursiveTextSplitter, SeparatorSpliter, SemanticSpliter, FixedLengthSplitter,RegexSplitter
from src.rag.ai_splitter import AliTextSplitter
import ast
import traceback
from src.utils.snowflake import Snowflake
from conf.server_config import *
from src.utils.log_utils import logger
from src.utils.awen_schema import FileStatus,SliceStatus
from src.tools.abstract import AbstractExtract
import time
from typing import  Optional
from src.utils.time import *
from src.rag.text_splitter import ChunkMarkerSplitter
from src.rag.chapter_split import ChapterSplitter
logger = logger.bind(name="data_process")

class TextSplitterService:
    """
    文本分割器类，用于根据不同的分割策略对文本进行分割并存储结果到数据库。
    """

    def __init__(self,conn):
        # self.db = MySQLClient()
        self.conn = conn
        self.snowflake = Snowflake(1, 1)
        # self.rs = rs

    @contextmanager
    def _get_cursor(self):
        cursor = self.conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()

    def get_text_split_config(self, file_id):
        """
        根据文件ID获取文本分割配置。

        参数:
            file_id (str): 文件ID

        返回:
            tuple: 包含分割类型和分割参数的元组，如果未找到则返回(None, None)。
        """
        query = """
        SELECT s.algo_code, s.slice_param
        FROM xj_kg_vector_slice_conf_tb vsc
        JOIN xj_kg_slice_conf_tb s ON vsc.slice_conf_id = s.slice_conf_id
        WHERE vsc.file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None, None
            return result[0], result[1]
    
    def get_file_name(self,file_id):
        query = """
        SELECT file_name
        FROM xj_kg_file_info_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None
            return result[0]

    def get_text_content(self, file_id):
        """
        根据文件ID获取文本内容。

        参数:
            file_id (str): 文件ID

        返回:
            tuple: 包含内容、文本ID和创建用户ID的元组，如果未找到则返回(None, None, None)。
        """
        query = """
        SELECT content, text_id, create_user_id
        FROM xj_kg_file_convert_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None, None, None
            return result[0], result[1], result[2]
    
    def _is_abstract_extract(self, file_id):
        query = """
        SELECT is_abstract_extract
        FROM xj_kg_vector_slice_conf_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None
            return result[0]
    
    def _is_meta_extract(self, file_id):
        query = """
        SELECT is_meta_extract
        FROM xj_kg_vector_slice_conf_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None
            return result[0]
    
    def is_deep_process(self, file_id):
        query = """
        SELECT is_deep_process
        FROM xj_kg_vector_slice_conf_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None
            return result[0]

    def update_slice_status(self, slice_ids, status):
        """
        更新多个切片的状态。

        参数:
            slice_ids (list): 切片ID的列表。
            status (int): 新的状态值。
        """
        sql = "UPDATE xj_kg_slice_info_tb SET is_enable = %s WHERE slice_id = %s;"
        params = [(status, sid) for sid in slice_ids]
        with self._get_cursor() as cursor:
            cursor.executemany(sql, params)
            self.conn.commit()

    def update_file_status(self, file_id: int, new_status: int) -> None:
        """
        Author：mabenjiang
        Description: 更新数据库中指定文件ID的文件状态

        Args:
            file_id (int): 要更新状态的文件的唯一标识符
            new_status (int): 文件的新状态，应对应于FileStatus枚举中的一个值

        Returns:
            None: 此函数不返回任何值，它执行的是数据更新操作
        """
        sql = """
            UPDATE xj_kg_file_info_tb
            SET file_status = %s
            WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(sql, (new_status, file_id))
            self.conn.commit()
    
    def get_file_status(self, file_id: int) -> Optional[int]:
        """
        Author：mabenjiang
        Description: 从数据库中获取指定文件ID的文件状态

        Args:
            file_id (int): 要检索状态的文件的唯一标识符

        Returns:
            Optional[int]: 文件的状态值，如果找不到对应文件，则返回None
        """
        sql = """
            SELECT file_status 
            FROM xj_kg_file_info_tb 
            WHERE is_enable = 1 AND file_id = %s
        """
        with self._get_cursor() as cursor:
            cursor.execute(sql, (file_id,))
            data = cursor.fetchone()
            if data is None:
                return None
            return data[0]

    def insert_splits(self, split_type, text_id, file_id, create_user_id, split_list, split_abstract_list):
        """
        将分割结果插入到数据库。

        参数:
            split_type (str): 分割类型。
            text_id (str): 文本ID。
            file_id (str): 文件ID。
            create_user_id (str): 创建用户ID。
            split_list (list): 分割后的文本列表。
        """
        inserts_sql = """
        INSERT INTO xj_kg_slice_info_tb (slice_id, text_id, file_id, title, content, status, data_size, is_enable, create_user_id, is_delete,slice_idx)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        data_to_insert = []
        for split,abstract in zip(split_list, split_abstract_list):
            slice_id = self.snowflake.next_id()
            status = SliceStatus.Pending_Insertion_ES.value if split_type == "non_spliter" else SliceStatus.Pending_Vectorization.value
            data_to_insert.append((slice_id, text_id, file_id, abstract, split, status, len(split), 1, create_user_id, 0, 0))
        with self._get_cursor() as cursor:
            cursor.executemany(inserts_sql, data_to_insert)
            self.conn.commit()

    def split_text(self, content, split_type, split_param):
        """
        根据分割类型和参数对文本进行分割。

        参数:
            content (str): 文本内容。
            split_type (str): 分割类型。
            split_param (dict): 分割参数。

        返回:
            list: 分割后的文本列表。
        """
        ## 递归分割
        if split_type == "recursive_spliter":
            chunk_size = split_param.get("spliter_size")
            overlap = split_param.get("overlap_length")
            recursive_spliter = ChineseRecursiveTextSplitter(
                keep_separator=True,
                is_separator_regex=True,
                chunk_size=chunk_size,
                chunk_overlap=overlap,
                separators=[
                    "\n\n",
                    "\n",
                    " ",
                    ".",
                    ",",
                    "\u200b",  # Zero-width space
                    "\uff0c",  # Fullwidth comma
                    "\u3001",  # Ideographic comma
                    "\uff0e",  # Fullwidth full stop
                    "\u3002",  # Ideographic full stop
                    "",
                ]
            )
            split_list = recursive_spliter.split_text(content)
        ## 语义分割
        elif split_type == "ali_spliter":
            split_list = AliTextSplitter().split_text(content)
            # logger.info(f"传入长度-{len(content)}")
            # split_list = SemanticSpliter().split_text(content, ifDistances=IFDISTANCES)
            logger.info(f"切片个数- {len(split_list)}")
        ## 定长分割
        elif split_type == "fixedlength_spliter":
            chunk_size = split_param.get("spliter_size")
            # NLTKTextSplitter()
            # split_list = CharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=CHUNK_OVERLAP)
            # split_list = TokenTextSplitter(chunk_size=chunk_size, chunk_overlap=CHUNK_OVERLAP).split_text(content)
            split_list = FixedLengthSplitter(fixed_length=chunk_size, overlap=CHUNK_OVERLAP).split_text(content)
        ## 句子定长分割
        elif split_type == "semantic_spliter":
            logger.info(f"传入长度-{len(content)}")
            split_list = SemanticSpliter().split_text(content, ifDistances=IFDISTANCES)
            logger.info(f"切片个数- {len(split_list)}")
        ## 字符分割
        elif split_type == "separator_spliter":
            separators = split_param.get("spliter_symbol")
            split_list = SeparatorSpliter().split_text(content,separators)
        ## 章节分割
        elif split_type == "chapter_spliter":
            split_list = ChapterSplitter().split_text(content)
            logger.info(f"切片split_list - {split_list}")
            logger.info(f"切片个数- {len(split_list)}")
        
        elif split_type == "regex_splitter":
            sperator = split_param.get("spliter_symbol")
            logger.info(f"分隔符参数: {sperator}, 类型: {type(sperator)}")
            split_list = RegexSplitter(sperator).split_text(content)
        ## 不分割
        elif split_type == "non_spliter":
            split_list = [content]

        else:
            raise ValueError(f'未知的分割类型：{split_type}')
        return split_list

    def text_split(self, params):
        """
        根据文件ID分割文本。

        参数:
            params (dict): 包含文件ID的字典。

        该方法遍历文件ID列表，获取每个文件的分割配置，读取文本内容，
        执行文本分割，并将结果存储到数据库中。同时，它会更新文件的状态。
        如果在处理过程中遇到异常，它会回滚事务并标记文件状态为失败。
        最后，它会提交事务并关闭数据库连接。
        """
        files_ids = params.get("filesId")
        for id in files_ids:
            try:
                # 查询文件状态
                current_status = self.get_file_status(id)
                if current_status != FileStatus.Start_Slice.value:
                    logger.warning(f'文件ID为{id}的状态不是切片开始，当前状态为{current_status}，跳过切片')
                    continue

                slice_start_time = get_current_time()
                self.update_file_status(id, FileStatus.Slicing.value)
                logger.info(f'来源数据编号{id}切片中')
                file_name = self.get_file_name(id)
                
                content, text_id, create_user_id = self.get_text_content(id)
                if content is None or text_id is None or create_user_id is None:
                    raise ValueError(f'未找到文件{file_name}为{id}的文本内容')
                    
                time_start = time.time()
                is_deep_process = self.is_deep_process(id)
                
                # 修改这里: 深度处理直接用$#$分割
                if is_deep_process == 2:
                    split_type = "ocr_spliter"
                    # split_list = content.split("$#$")
                    # 移除空字符串和只包含空白字符的项
                    # split_list = [s.strip() for s in split_list if s.strip()]
                    splitter = ChunkMarkerSplitter()
                    split_list = splitter.split_text(content)
                else:
                    split_type, split_param_str = self.get_text_split_config(id)
                    if split_type is None or split_param_str is None:
                        raise ValueError(f'未找到文件{file_name}为{id}的分割配置')
                    split_param = ast.literal_eval(split_param_str)
                    split_list = self.split_text(content, split_type, split_param)

                if len(split_list) == 0:
                    raise ValueError(f'切片长度为0')
                
                logger.info(f"{id}$$${file_name}$$${split_type}$$$切片时间：{time.time() - time_start}")
                #with open('output.txt', 'a') as file:
                #    message = f"{id}$$${file_name}$$${split_type}$$$切片时间：{time.time() - time_start}\n"
                #    file.write(message)
                if self._is_meta_extract(id) == 1:
                    split_list = [f"{file_name} - {split}" for split in split_list]
                ## 对切片生成摘要 如果
                if self._is_abstract_extract(id) == 1:
                    ab = AbstractExtract()
                    split_abstract_list = []
                    for split in split_list:
                        abstract = ab.get_abstract(split)
                        split_abstract_list.append(abstract)
                        # logger.info(f"abstract : {abstract}")
                        # logger.info(f"split : {split}")
                else:
                    split_abstract_list =["没有抽取摘要" for i in range(len(split_list))]

                self.insert_splits(split_type, text_id, id, create_user_id, split_list, split_abstract_list)
                self.update_file_status(id, FileStatus.Sliced_Successfully.value)
                # self.rs.update_file_status(REDIS_KEY,id,FileStatus.Sliced_Successfully.value)

                slice_end_time =get_current_time()
                # self.rs.update_time(REDIS_KEY, id, "sliceEndTime", slice_end_time)
                logger.info("----切片运行时间：{}----".format(time_difference(slice_start_time,slice_end_time)))
                logger.info(f'来源数据编号{id}切片中')
                logger.info(f'来源数据编号{file_name}{id}切片成功')
            except Exception as e:
                logger.error(traceback.print_exc())
                logger.error(f"处理文件ID为{id}时发生错误: {str(e)}")
                self.conn.rollback()
                self.update_file_status(id, FileStatus.Sliced_Failed.value)  # 更新文件状态为处理失败
                # self.rs.update_file_status(REDIS_KEY,id,FileStatus.Sliced_Failed.value)
                logger.info(f'来源数据编号{id}切片失败')
            finally:
                self.conn.commit()
        # self.close()