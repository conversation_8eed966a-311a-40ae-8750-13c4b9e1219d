
import os
import re
import json
from typing import Dict, Any, Optional, List
from src.service.llm_base_service import QueryLLMService
from src.data_processors.cosine_similarity import CosineSimilarity
from src.data_processors.text_detector import TextQualityDetector
from src.llms import prompt_template
from src.utils.log_utils import logger

logger = logger.bind(name="post_process_service")


class PostProcessService:
    def __init__(self):
        self.output_dict = {}
        self.llm_service = QueryLLMService()

    def post_process(self, data):
        """
        对数据进行后处理

        Args:
            data: 输入数据，应包含content和filename字段

        Returns:
            处理后的数据字典
        """
        processed_data = self._process_data(data)
        self.output_dict = processed_data
        return self.output_dict

    def _process_data(self, data):
        """
        实现数据处理逻辑，增加摘要、关键词、标签、文件名、页眉页脚等信息

        Args:
            data: 输入数据字典，应包含：
                - content: 文本内容 (必需)
                - filename: 文件名 (必需)
                - 其他可选字段

        Returns:
            增强后的数据字典
        """
        logger.info("开始数据后处理")

        # 获取基础输入参数
        content = data.get('content', '')
        filename = data.get('filename', '')

        if not content:
            logger.warning("输入内容为空，返回原始数据")
            return data

        if not filename:
            logger.warning("文件名为空，使用默认文件名")
            filename = "未知文件"

        try:
            # 1. 生成摘要
            summary = self._generate_summary(content)

            # 2. 提取关键词
            keywords = self._extract_keywords(content)

            # 3. 生成标签
            tags = self._generate_tags(content, keywords)

            # 4. 提取页眉页脚信息
            header, footer = self._extract_header_footer(content)

            # 5. 检测语言类型
            language_type = self._detect_language(content)

            # 6. 生成上下文信息
            context = self._generate_context(content)

            # 7. 构建增强的数据字典
            enhanced_data = {
                **data,  # 保留原始数据
                'filename': filename,
                'content': content,
                'summary': summary,
                'keywords': keywords,
                'tags': tags,
                'header': header,
                'footer': footer,
                'language_type': language_type,
                'context': context,
                'content_length': len(content),
                'word_count': len(content.split()),
                'processing_info': {
                    'processed_at': self._get_current_timestamp(),
                    'processor_version': '1.0.0',
                    'features_extracted': ['summary', 'keywords', 'tags', 'header', 'footer', 'language', 'context']
                }
            }

            logger.info(f"数据后处理完成，文件: {filename}")
            return enhanced_data

        except Exception as e:
            logger.error(f"数据后处理失败: {str(e)}")
            # 返回原始数据加上错误信息
            return {
                **data,
                'filename': filename,
                'processing_error': str(e),
                'processing_status': 'failed'
            }

    def _generate_summary(self, content: str) -> str:
        """
        生成文本摘要

        Args:
            content: 文本内容

        Returns:
            生成的摘要
        """
        try:
            logger.info("开始生成摘要")

            # 使用LLM服务生成抽象式摘要
            prompt = prompt_template.ABSTRACT_TEMPLET_CN.format(text=content)
            summary_result = self.llm_service.llm.generate(prompt)

            # 尝试解析JSON格式的摘要
            if isinstance(summary_result, str):
                try:
                    import json
                    # 尝试提取JSON中的摘要
                    if '```json' in summary_result:
                        json_start = summary_result.find('```json') + 7
                        json_end = summary_result.find('```', json_start)
                        json_str = summary_result[json_start:json_end].strip()
                        summary_data = json.loads(json_str)
                        summary = summary_data.get('抽象式摘要', summary_result)
                    else:
                        summary = summary_result
                except:
                    summary = summary_result
            else:
                summary = str(summary_result)

            # 如果摘要太长，截取前500字符
            if len(summary) > 500:
                summary = summary[:500] + "..."

            logger.info("摘要生成完成")
            return summary

        except Exception as e:
            logger.error(f"摘要生成失败: {str(e)}")
            # 使用简单的截取作为备选方案
            return content[:200] + "..." if len(content) > 200 else content

    def _extract_keywords(self, content: str, num_keywords: int = 10) -> List[str]:
        """
        提取关键词

        Args:
            content: 文本内容
            num_keywords: 提取的关键词数量

        Returns:
            关键词列表
        """
        try:
            logger.info("开始提取关键词")

            # 使用CosineSimilarity的extract_keyword方法
            keywords = CosineSimilarity.extract_keyword(content, num_keywords)

            # 确保返回的是列表
            if isinstance(keywords, str):
                keywords = [keywords]
            elif not isinstance(keywords, list):
                keywords = list(keywords) if keywords else []

            logger.info(f"关键词提取完成，共{len(keywords)}个")
            return keywords

        except Exception as e:
            logger.error(f"关键词提取失败: {str(e)}")
            # 使用简单的词频统计作为备选方案
            return self._simple_keyword_extraction(content, num_keywords)

    def _simple_keyword_extraction(self, content: str, num_keywords: int) -> List[str]:
        """
        简单的关键词提取备选方案

        Args:
            content: 文本内容
            num_keywords: 提取的关键词数量

        Returns:
            关键词列表
        """
        try:
            import re
            from collections import Counter

            # 简单的中文分词（基于标点符号）
            words = re.findall(r'[\u4e00-\u9fff]+', content)

            # 过滤长度小于2的词
            words = [word for word in words if len(word) >= 2]

            # 统计词频
            word_freq = Counter(words)

            # 返回最频繁的词
            return [word for word, _ in word_freq.most_common(num_keywords)]

        except Exception as e:
            logger.error(f"简单关键词提取失败: {str(e)}")
            return []

    def _generate_tags(self, content: str, keywords: List[str]) -> List[str]:
        """
        生成标签

        Args:
            content: 文本内容
            keywords: 关键词列表

        Returns:
            标签列表
        """
        try:
            logger.info("开始生成标签")

            tags = []

            # 1. 基于关键词生成标签
            tags.extend(keywords[:5])  # 取前5个关键词作为标签

            # 2. 基于内容类型生成标签
            content_lower = content.lower()

            # 文档类型标签
            if any(word in content_lower for word in ['合同', '协议', '条款']):
                tags.append('合同文档')
            if any(word in content_lower for word in ['报告', '分析', '总结']):
                tags.append('报告文档')
            if any(word in content_lower for word in ['手册', '指南', '说明']):
                tags.append('指导文档')
            if any(word in content_lower for word in ['政策', '规定', '制度']):
                tags.append('政策文档')
            if any(word in content_lower for word in ['技术', '开发', '编程']):
                tags.append('技术文档')

            # 3. 基于内容长度生成标签
            if len(content) < 500:
                tags.append('简短文档')
            elif len(content) < 2000:
                tags.append('中等文档')
            else:
                tags.append('长文档')

            # 4. 去重并限制数量
            tags = list(set(tags))[:10]

            logger.info(f"标签生成完成，共{len(tags)}个")
            return tags

        except Exception as e:
            logger.error(f"标签生成失败: {str(e)}")
            return keywords[:5] if keywords else []

    def _extract_header_footer(self, content: str) -> tuple:
        """
        提取页眉页脚信息

        Args:
            content: 文本内容

        Returns:
            (header, footer) 元组
        """
        try:
            logger.info("开始提取页眉页脚")

            lines = content.split('\n')
            lines = [line.strip() for line in lines if line.strip()]

            if not lines:
                return "", ""

            # 提取页眉（前3行中的短行，通常是标题或页眉）
            header_candidates = []
            for line in lines[:3]:
                if len(line) < 100 and line:  # 短行更可能是页眉
                    header_candidates.append(line)

            header = " | ".join(header_candidates) if header_candidates else ""

            # 提取页脚（后3行中的短行，通常包含页码、日期等）
            footer_candidates = []
            for line in lines[-3:]:
                if len(line) < 100 and line:  # 短行更可能是页脚
                    # 检查是否包含页码、日期等特征
                    import re
                    if re.search(r'\d+', line) or any(word in line for word in ['页', '第', '共', '日期', '时间']):
                        footer_candidates.append(line)

            footer = " | ".join(footer_candidates) if footer_candidates else ""

            logger.info("页眉页脚提取完成")
            return header, footer

        except Exception as e:
            logger.error(f"页眉页脚提取失败: {str(e)}")
            return "", ""

    def _detect_language(self, content: str) -> int:
        """
        检测语言类型

        Args:
            content: 文本内容

        Returns:
            语言类型编码
        """
        try:
            logger.info("开始检测语言类型")

            # 使用TextQualityDetector检测语言
            detector = TextQualityDetector(content)
            language_type = detector.detect_language()

            logger.info(f"语言检测完成，类型: {language_type}")
            return language_type

        except Exception as e:
            logger.error(f"语言检测失败: {str(e)}")
            # 默认返回中文
            return 1

    def _generate_context(self, content: str) -> str:
        """
        生成上下文信息

        Args:
            content: 文本内容

        Returns:
            上下文信息
        """
        try:
            logger.info("开始生成上下文")

            # 使用LLM服务生成上下文
            context_result = self.llm_service.contextual_retrieval(content)

            # 尝试解析JSON格式的上下文
            if isinstance(context_result, str):
                try:
                    import json
                    if '```json' in context_result:
                        json_start = context_result.find('```json') + 7
                        json_end = context_result.find('```', json_start)
                        json_str = context_result[json_start:json_end].strip()
                        context_data = json.loads(json_str)
                        context = context_data.get('上下文', context_result)
                    else:
                        context = context_result
                except:
                    context = context_result
            else:
                context = str(context_result)

            # 限制上下文长度
            if len(context) > 200:
                context = context[:200] + "..."

            logger.info("上下文生成完成")
            return context

        except Exception as e:
            logger.error(f"上下文生成失败: {str(e)}")
            # 使用简单的上下文生成
            return self._simple_context_generation(content)

    def _simple_context_generation(self, content: str) -> str:
        """
        简单的上下文生成备选方案

        Args:
            content: 文本内容

        Returns:
            简单的上下文描述
        """
        try:
            # 提取前100字符作为上下文
            context = content[:100].replace('\n', ' ').strip()
            if len(content) > 100:
                context += "..."
            return context
        except Exception as e:
            logger.error(f"简单上下文生成失败: {str(e)}")
            return "文档内容"

    def _get_current_timestamp(self) -> str:
        """
        获取当前时间戳

        Returns:
            时间戳字符串
        """
        try:
            from datetime import datetime
            return datetime.now().isoformat()
        except Exception as e:
            logger.error(f"获取时间戳失败: {str(e)}")
            return "unknown"
