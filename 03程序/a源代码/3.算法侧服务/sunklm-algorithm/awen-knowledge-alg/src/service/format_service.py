from contextlib import contextmanager
from typing import List, <PERSON><PERSON>, Optional
from src.data_processors.change_format import ChangeFileTxt
from src.utils.minio_conn import *
from src.data_processors.cosine_similarity import *
from src.utils.snowflake import *
import requests
import traceback
import json
from src.utils.awen_schema import FileStatus,ParseMethod
from src.utils.log_utils import logger
from src.data_processors.text_detector import *
from conf.server_config import *
from src.service.llm_base_service import QueryLLMService
# from utils.service.ocr_service import PDFProcessor
import chardet
from src.utils.time import *
import asyncio
import aiohttp
import re
import xml.etree.ElementTree as ET
from src.rag.text_splitter import FixedLengthSplitter
logger = logger.bind(name="data_process")

class FileProcessorService:
    def __init__(self, conn, minio):
        self.conn = conn
        self.minio_client = minio
        self.snowflake = Snowflake(1, 1)
        # self.pdf_processor = PDFProcessor(minio)
        # self.rs = rs

    @contextmanager
    def _get_cursor(self):
        cursor = self.conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()

    def update_file_status(self, file_id: int, new_status: int) -> None:
        """
        Author：mabenjiang
        Description: 更新数据库中指定文件ID的文件状态

        Args:
            file_id (int): 要更新状态的文件的唯一标识符
            new_status (int): 文件的新状态，应对应于FileStatus枚举中的一个值

        Returns:
            None: 此函数不返回任何值，它执行的是数据更新操作
        """
        with self._get_cursor() as cursor:
            sql = """
                UPDATE xj_kg_file_info_tb
                SET file_status = %s
                WHERE file_id = %s;
            """
            try:
                cursor.execute(sql, (new_status, file_id))
                self.conn.commit()
            except Exception as e:
                self.conn.rollback()  # 回滚事务以防止数据库锁定或不一致
                logger.error(f"更新文件状态失败, file_id: {file_id}, error: {e}")
                raise

    def get_file_info(self, file_id: int) -> Tuple[Optional[str], ...]:
        """
        Author：mabenjiang
        Description: 从数据库中获取指定文件ID的文件信息

        Args:
            file_id (int): 要检索信息的文件的唯一标识符

        Returns:
            Tuple[Optional[str], ...]: 包含文件类别代码、文件名、文件路径和创建用户ID的元组，
                                       如果找不到对应文件，则所有元素均为None
        """
        with self._get_cursor() as cursor:
            sql = """
                SELECT category_code, file_name, file_path, create_user_id 
                FROM xj_kg_file_info_tb 
                WHERE is_enable = 1 AND file_id = %s
            """
            cursor.execute(sql, (file_id,))
            data = cursor.fetchone()
            if data is None:
                return None, None, None, None
            return data
        
    
    def get_faq_extract_status(self, file_id: int) -> Optional[int]:
        """
        Author：mabenjiang
        Description: 根据file_id从xj_kg_vector_slice_conf_tb表中查询is_faq_extract字段

        Args:
            file_id (int): 要查询的文件的唯一标识符

        Returns:
            Optional[int]: is_faq_extract字段的值，如果找不到对应文件，则返回None
        """
        with self._get_cursor() as cursor:
            sql = """
                SELECT is_faq_extract 
                FROM xj_kg_vector_slice_conf_tb 
                WHERE file_id = %s
            """
            cursor.execute(sql, (file_id,))
            data = cursor.fetchone()
            if data is None:
                return None
            return data[0]
    
    def get_create_user_id_and_text_id_and_content(self, file_id: int) -> Tuple[Optional[int], Optional[int], Optional[str]]:
        """
        Author：mabenjiang
        Description: 根据file_id从xj_kg_file_info_tb表中查询create_user_id字段和text_id字段和content字段

        Args:
            file_id (int): 要查询的文件的唯一标识符

        Returns:
            Tuple[Optional[int], Optional[int], Optional[str]]: 包含create_user_id、text_id和content字段的值，如果找不到对应文件，则返回None
        """
        with self._get_cursor() as cursor:
            sql = """
                SELECT create_user_id, text_id, content 
                FROM xj_kg_file_convert_tb 
                WHERE file_id = %s
            """
            cursor.execute(sql, (file_id,))
            data = cursor.fetchone()
            if data is None:
                return None, None, None
            return data[0], data[1], data[2]
        
    def insert_faq_info(self, file_faq_id: int, text_id: int, file_id: int, question: str, answer: str, status: int, is_enable: int, is_delete: int, create_user_id: int) -> None:
        """
        Author：mabenjiang
        Description: 向xj_kg_file_faq_info_tb表中插入FAQ信息

        Args:
            file_faq_id (int): FAQ的唯一标识符
            text_id (int): 文本的唯一标识符
            file_id (int): 文件的唯一标识符
            question (str): FAQ的问题
            answer (str): FAQ的答案
            status (int): FAQ的状态
            is_enable (int): 是否启用FAQ
            is_delete (int): 是否删除FAQ
            create_user_id (int): 创建FAQ的用户ID

        Returns:
            None: 此函数执行数据库插入操作，不返回任何值
        """
        with self._get_cursor() as cursor:
            insert_sql = """
                INSERT INTO xj_kg_file_faq_info_tb
                (file_faq_id, text_id, file_id, question, answer, status, is_enable, is_delete, create_user_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_sql, (file_faq_id, text_id, file_id, question, answer, status, is_enable, is_delete, create_user_id))
            self.conn.commit()
            
    def extract_faqs_for_file(self, file_id: int, faq_num: int, faq_context_length: int) -> Tuple[Optional[int], Optional[int], Optional[List[str]], Optional[List[str]]]:
        """
        Author：mabenjiang
        Description: 根据file_id提取FAQ，对每个切片都提取相同数量的FAQ

        Args:
            file_id (int): 要查询的文件的唯一标识符
            faq_num (int): 每个切片要提取的FAQ数量 
            faq_context_length (int): 每个切片的长度

        Returns:
            Tuple[Optional[int], Optional[int], Optional[List[str]], Optional[List[str]]]: 包含create_user_id, text_id, questions, answers的元组
        """
        faq_extract_status = self.get_faq_extract_status(file_id)
        logger.info(f"faq_extract_status:{faq_extract_status}")
        
        if faq_extract_status == 1:
            create_user_id, text_id, content = self.get_create_user_id_and_text_id_and_content(file_id)
            if content:
                try:
                    # 使用FixedLengthSplitter切分内容
                    splitter = FixedLengthSplitter(faq_context_length, 10)
                    chunks = splitter.split_text(content)
                    
                    all_questions = []
                    all_answers = []
                    faq_service = QueryLLMService()
                    
                    # 为每个chunk都提取相同数量的FAQ
                    for chunk in chunks:
                        faq_result = faq_service.extract_faq_dp(num=faq_num, context=chunk)
                        
                        if isinstance(faq_result, dict):
                            questions = faq_result.get('query', [])
                            answers = faq_result.get('answer', [])
                        else:
                            faq_result_dict = json.loads(faq_result)
                            questions = faq_result_dict.get('query', [])
                            answers = faq_result_dict.get('answer', [])
                            
                        all_questions.extend(questions)
                        all_answers.extend(answers)
                    
                    return create_user_id, text_id, all_questions, all_answers
                    
                except Exception as e:
                    logger.error(f"Error extracting FAQs for file ID {file_id}: {str(e)}")
                    return create_user_id, text_id, None, None
                    
            return create_user_id, text_id, None, None
            
        return None, None, None, None

    def extract_faqs_for_file_arm(self, file_id: int, faq_num: int, faq_context_length: int) -> Tuple[Optional[int], Optional[int], Optional[List[str]], Optional[List[str]]]:
        """
        Author：mabenjiang
        Description: 根据file_id提取FAQ，对每个切片都提取相同数量的FAQ

        Args:
            file_id (int): 要查询的文件的唯一标识符 
            faq_num (int): 每个切片要提取的FAQ数量
            faq_context_length (int): 每个切片的长度

        Returns:
            Tuple[Optional[int], Optional[int], Optional[List[str]], Optional[List[str]]]: 包含create_user_id, text_id, questions, answers的元组
        """
        faq_extract_status = self.get_faq_extract_status(file_id)
        logger.info(f"faq_extract_status:{faq_extract_status}")
        
        if faq_extract_status == 1:
            create_user_id, text_id, content = self.get_create_user_id_and_text_id_and_content(file_id)
            if content:
                try:
                    # 使用FixedLengthSplitter切分内容
                    splitter = FixedLengthSplitter(faq_context_length, 10)
                    chunks = splitter.split_text(content)
                    
                    all_questions = []
                    all_answers = []
                    faq_service = QueryLLMService()
                    
                    # 为每个chunk提取FAQ
                    for chunk in chunks:
                        faq_result = faq_service.extract_faq(num=faq_num, context=chunk)
                        
                        if isinstance(faq_result, dict):
                            faq_result = json.dumps(faq_result, ensure_ascii=False)
                            
                        # 解析问题和答案
                        pattern1 = r'<question>(.*?)</question>'
                        chunk_questions = re.findall(pattern1, faq_result, re.DOTALL)
                        
                        pattern2 = r'<answer>(.*?)</answer>'
                        chunk_answers = re.findall(pattern2, faq_result, re.DOTALL)
                        
                        # 确保问答对数量一致
                        min_length = min(len(chunk_questions), len(chunk_answers))
                        chunk_questions = chunk_questions[:min_length]
                        chunk_answers = chunk_answers[:min_length]
                        
                        all_questions.extend(chunk_questions)
                        all_answers.extend(chunk_answers)
                    
                    return create_user_id, text_id, all_questions, all_answers
                    
                except Exception as e:
                    logger.error(f"Error extracting FAQs for file ID {file_id}: {str(e)}")
                    return create_user_id, text_id, None, None
                    
            return create_user_id, text_id, None, None
            
        return None, None, None, None
    
    def insert_convert_file(self, file_id: int, category_code: str, content: str, keywords: str, language_type: int,
                            max_similarity: float, is_similars: str, create_user_id: int, file_path: str,
                            file_char_size: int) -> None:
        """
        Author：mabenjiang
        Description: 将转换后的文件信息存储到数据库中

        Args:
            file_id (int): 文件的唯一标识符
            category_code (str): 文件的类别代码
            content (str): 文件的文本内容
            keywords (str): 文件的关键字，用逗号分隔
            language_type (int): 文件的语言类型编码
            max_similarity (float): 文件的最大相似度
            is_similars (str): 是否存在相似文件的信息
            create_user_id (int): 创建文件的用户ID
            file_path (str): 文件的路径
            file_char_size (int): 文件的字符大小

        Returns:
            None: 此函数执行数据库插入操作，不返回任何值
        """
        insert_sql = """
            INSERT INTO xj_kg_file_convert_tb
            (text_id, file_id, category_code, content, keywords, language_type, max_similarity, id_similars,
             create_user_id, file_path, file_char_size)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        text_id = self.snowflake.next_id()
        with self._get_cursor() as cursor:
            cursor.execute(insert_sql, (text_id, file_id, category_code, content, keywords, language_type,
                                        max_similarity, is_similars, create_user_id, file_path, file_char_size))
            self.conn.commit()

    def is_deep_process(self, file_id):
        query = """
        SELECT is_deep_process
        FROM xj_kg_vector_slice_conf_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
            if result is None:
                return None
            return result[0]
    def detect_and_convert_encoding(self, file_path: str) -> str:
        """
        Detect the encoding of the file and convert it to UTF-8 if necessary.

        Args:
            file_path (str): The path to the file.

        Returns:
            str: The content of the file in UTF-8 encoding.
        """
        with open(file_path, 'rb') as file:
            raw_data = file.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding']

        if encoding.lower() != 'utf-8':
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)

        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    def file_process(self, new_path: str, key_num: int, sim_num: int) -> Tuple[str, int, str, float, str]:
        """
        Author：mabenjiang
        Description: 处理文件，包括读取文件内容、提取关键词、检测语言类型等操作

        Args:
            new_path (str): 文件的本地路径
            key_num (int): 需要提取的关键词数量
            sim_num (int): 用于计算相似度的文档数量，此处未在函数内部使用

        Returns:
            Tuple[str, int, str, float, str]: 包含处理结果的元组，依次为：
                - keywords_str (str): 提取的关键词，以逗号分隔
                - lang (int): 检测到的语言类型编码
                - id_similars (str): 相似文档的ID列表，此处返回空字符串
                - max_similarity (float): 最大相似度，此处初始化为0
                - content (str): 文件的原始文本内容

        Note:
            此函数首先读取文件内容，然后使用CosineSimilarity模块的extract_keyword方法提取关键词，
            并使用detect_language方法检测文件的语言类型。sim_num参数目前在函数中未被使用。
        """
        with open(new_path, 'r', encoding='UTF-8') as file:
            content = file.read()
        keywords = CosineSimilarity.extract_keyword(content, key_num)
        keywords_str = ', '.join(keywords)
        detect = TextQualityDetector(content)
        lang = detect.detect_language()
        max_similarity = 0
        id_similars = ''
        return keywords_str, lang, id_similars, max_similarity, content
        
    def download_and_save_from_minio(self, bucket_name: str, object_path: str, local_dir: str) -> Tuple[str, str]:
        """
        Author：mabenjiang
        Description: 从MinIO对象存储中下载文件，并将其保存到本地指定目录

        Args:
            bucket_name (str): MinIO桶的名称
            object_path (str): 桶内对象的路径
            local_dir (str): 本地要保存文件的目录

        Returns:
            Tuple[str, str]: 一个元组，包含本地保存文件的完整路径和文件名
        """
        if not os.path.exists(local_dir):
            os.makedirs(local_dir)
        response = self.minio_client.get_object(bucket_name, object_path)
        file_name = os.path.basename(object_path)
        save_path = os.path.join(local_dir, file_name)
        with open(save_path, "wb") as file:
            for data in response:
                file.write(data)
        return save_path
    
    def is_deep_file(self, filename):
        # 获取文件扩展名
        ext = os.path.splitext(filename)[1].lower()

        # 判断扩展名是否是.png、.jpg或.jpeg
        if ext in (".pdf", ".docx",".doc",".pptx",".ppt"):
            return 1
        elif ext in ('.png', '.jpg', '.jpeg'):
            return 2
        else:
            return 3
#  深度解析格式转换
    def is_change_format_unstructured(self, filename):

        ext = os.path.splitext(filename)[1].lower()
        if ext in (".ppt", ".doc",".pptx"):
            return 1
        else:
            return 0
        # return 0
        #  图片提取格式转换
    def is_change_format_mineru(self, filename):

        ext = os.path.splitext(filename)[1].lower()
        if ext in (".ppt", ".doc",".pptx",".docx"):
            return 1
        else:
            return 0
        #  普通解析格式转换
    def is_change_format_sim(self,filename):

        ext = os.path.splitext(filename)[1].lower()
        if ext in (".ppt", ".doc",".pptx"):
            return 1
        else:
            return 0
        
    def soffice_change(self, bucket_name: str, object_path: str) -> Optional[str]:
        """
        Convert a document to PDF using the /change endpoint.
        
        Args:
            bucket_name: MinIO bucket name
            object_path: Path to the object in the bucket
            
        Returns:
            URL of the converted PDF in MinIO, or None if conversion failed
        """
        url = CHANGE_TASK
        headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
        data = {
            "url": object_path,
            "bucket": bucket_name
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                response_json = response.json()
                if response_json["success"]:
                    return response_json.get("minio_pdf_path")
                else:
                    logger.error(f"Conversion failed: {response_json.get('message')}")
                    return None
            else:
                logger.error(f"Error: {response.status_code} - {response.text}")
                return None
        except requests.RequestException as e:
            logger.error(f"HTTP Request failed: {e}")
            return None

    def unstructed_parse(self, bucket_name: str, object_path: str) -> str:
        """
        Author：mabenjiang
        Description: 向指定API发送文件解析任务，并返回任务的ID

        Args:
            bucket_name (str): MinIO桶的名称
            object_path (str): 桶内对象的路径

        Returns:
            str: 解析任务的ID，如果请求失败则抛出ValueError

        Raises:
            ValueError: 当HTTP请求返回非200状态码时，包含错误状态码和响应文本
        """
        url = CREAT_TASK
        headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
        data = {
            "ulr_type": "minio",
            "url": object_path,
            "bucket": bucket_name,
            "params": {
                "languages": ["chi_sim", "en"],
                "is_chunck": True,
                "chunk_config": {
                    "max_characters": 512,
                    "new_after_n_chars": 512,
                    "chunk_placeholder": "$#$"
        }
            }
        }
        response = requests.post(url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            response_json = response.json()
            return response_json['retMap']['task_id']
        else:
            raise ValueError(f"Error: {response.status_code} - {response.text}")
    
    # def poll_task_status(self, task_id: str, max_retries: int = 1800, retry_delay: int = 10) -> None:
    #     """
    #     Author：mabenjiang
    #     Description: 轮询给定的任务状态，直到任务完成或达到最大重试次数

    #     Args:
    #         task_id (str): 要查询的任务ID
    #         max_retries (int, optional): 最大重试次数，默认为180次
    #         retry_delay (int, optional): 每次重试之间的延迟时间（秒），默认为10秒

    #     Returns:
    #         None: 此函数执行轮询操作，不返回任何值

    #     Note:
    #         如果任务状态为"completed"，函数将记录结果URL并退出轮询。
    #         如果任务状态不是"processing"且不是"completed"，函数将记录当前状态。
    #         如果API返回错误，函数将记录错误信息并终止轮询。
    #         如果达到最大重试次数，函数将记录这一信息并停止轮询。
    #     """
    #     url = GET_TASK
    #     headers = {'Content-Type': 'application/json'}
    #     payload = {"task_id": task_id}
    #     for _ in range(max_retries):
    #         try:
    #             response = requests.post(url, json=payload, headers=headers)
    #             data = response.json()
    #             if data["retCode"] == 0:
    #                 status = data["retMap"]["status"]
    #                 if status == "completed":
    #                     result_url = data['retMap']['result_url']
    #                     return result_url
    #                 elif status == "processing":
    #                     logger.info(f"Task status: {status}")
    #                 elif status == "failed":
    #                     logger.info(f"Task status: {status}")
    #                     break
    #             else:
    #                 logger.error(f"Error: {data['retMsg']}")
    #                 break
    #             time.sleep(retry_delay)
    #         except RequestException as e:
    #             logger.error(f"Request failed: {e}")
    #             break
    #     logger.info("Reached maximum number of retries.")

    # ... existing apiManger ...

    async def poll_task_status_async(self, task_id: str, max_retries: int = 20, retry_delay: int = 60) -> Optional[str]:
        """
        Author：mabenjiang
        Description: 异步轮询给定的任务状态，直到任务完成或达到最大重试次数

        Args:
            task_id (str): 要查询的任务ID
            max_retries (int, optional): 最大重试次数，默认为180次
            retry_delay (int, optional): 每次重试之间的延迟时间（秒），默认为10秒

        Returns:
            Optional[str]: 任务完成后的结果URL，如果失败则返回None
        """
        url = GET_TASK
        headers = {'Content-Type': 'application/json'}
        payload = {"task_id": task_id}

        async with aiohttp.ClientSession() as session:
            for _ in range(max_retries):
                try:
                    async with session.post(url, json=payload, headers=headers) as response:
                        data = await response.json()
                        if data["retCode"] == 0:
                            status = data["retMap"]["status"]
                            if status == "completed":
                                result_url = data['retMap']['result_url']
                                return result_url
                            elif status == "processing":
                                logger.info(f"Task status: {status}")
                            elif status == "failed":
                                logger.info(f"Task status: {status}")
                                break
                        else:
                            logger.error(f"Error: {data['retMsg']}")
                            break
                    await asyncio.sleep(retry_delay)
                except aiohttp.ClientError as e:
                    logger.error(f"Request failed: {e}")
                    break
            logger.info("Reached maximum number of retries.")
            return None
        
    def mineru_pdf_ocr(self, pdf_file, parse_method='auto', is_json_md_dump=True, output_dir='/root/output'):
        """
        处理PDF文件并返回内容列表
        :param pdf_file: PDF文件路径
        :param parse_method: 解析方法，默认为'auto'
        :param is_json_md_dump: 是否以JSON和Markdown格式输出，默认为True
        :param output_dir: 输出目录，默认为'/root/output'
        :return: 内容列表
        """
        url = PDF_OCR_URL
        params = {
            'parse_method': parse_method,
            'is_json_md_dump': 'true' if is_json_md_dump else 'false',
            'output_dir': output_dir
        }
        headers = {
            'accept': 'application/json'
        }
        files = {
            'pdf_file': (os.path.basename(pdf_file), open(pdf_file, 'rb'), 'application/pdf')
        }
        response = requests.post(url, params=params, headers=headers, files=files)
        if response.status_code == 200:
            json_data = response.json()
            content_list = json_data.get('content_list', [])
            return content_list
        else:
            print(f"Error: {response.status_code}. Reason: {response.text}.")
            return []

    def simple_parse_file(self, file_path, MINOBUCKET):
        file_name = os.path.basename(file_path)
        try:
            # 检查文件是否需要格式转换
            if self.is_change_format_sim(file_name):
                file_path = self.soffice_change(MINOBUCKET, file_path)
                if not file_path:
                    raise ValueError(f"soffice_change failed for {file_name}")
            save_path = self.download_and_save_from_minio(MINOBUCKET, file_path, "../../resources/cache")
            logger.info(save_path)
            # 直接返回文件路径，不进行深度解析
            return save_path

        except Exception as e:
            logger.error(f"普通解析文件{file_name}时发生错误: {str(e)}")
            raise

    async def unstructured_parse_file(self, file_path, MINOBUCKET):
        file_name = os.path.basename(file_path)
        try:
            # Check if the file needs format conversion
            if self.is_change_format_unstructured(file_name):
                file_path = self.soffice_change(MINOBUCKET, file_path)
                if not file_path:
                    raise ValueError(f"soffice_change failed for {file_name}")

            # Perform deep parsing
            task_id = self.unstructed_parse(MINOBUCKET, file_path)
            result_url = await self.poll_task_status_async(task_id)

            # Download the result if available, otherwise download the original file
            save_path = self.download_and_save_from_minio(
                MINOBUCKET, result_url, "../../resources/cache"
            )
            return save_path

        except Exception as e:
            logger.error(f"深度解析文件{file_name}时发生错误: {str(e)}")
            raise

    def mineru_parse_file(self, file_path: str, delimiter: str = "$#$") -> str:
        """
        Author：mabenjiang
        Description: 处理图片文件并生成文本输出

        Args:
            file_path (str): 要处理的文件路径
            delimiter (str, optional): 用于分隔文本切片的分隔符。默认为"$#$"

        Returns:
            str: 输出文件的路径
        """
        file_name = os.path.basename(file_path)
        
        # 检查文件是否需要格式转换
        if self.is_change_format_mineru(file_name):
            file_path = self.soffice_change(MINOBUCKET, file_path)
            if not file_path:
                raise ValueError(f"soffice_change failed for {file_name}")
            
        save_path = self.download_and_save_from_minio(
                MINOBUCKET, file_path, "../../resources/cache"
            )
        # 处理PDF文件并获取内容列表
        # pdf_processor = PDFProcessor(self.minio_client, max_length=800)
        content_list = self.mineru_pdf_ocr(save_path)
        
        # 处理内容列表以获取切片、文本切片和长度
        # slices, text_slices, lengths = pdf_processor.process_and_get_result(content_list)
        # logger.info(slices)
        
        # 将切片写入文本文件，使用指定的分隔符
        output_dir = "../../resources/cache"
        output_file = os.path.join(output_dir, f"{os.path.splitext(os.path.basename(file_name))[0]}.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(content_list, f, ensure_ascii=False)
        
        # 返回输出文件的路径
        return output_file

    def ocr_parse_file(self, file_path: str,ocr_on_figures) -> str:
        """
        Author：mabenjiang
        Description: 使用新的处理接口处理文件

        Args:
            file_path (str): 要处理的文件路径

        Returns:
            str: MinIO中处理后的文件路径
        """
        logger.info(ocr_on_figures)
        try:
            # 准备请求数据
            url = PDF_OCR_URL
            headers = {'Content-Type': 'application/json'}
            data = {
                "url": file_path,
                "bucket": MINOBUCKET,
                "perform_ocr_on_figures": ocr_on_figures
            }

            # 发送请求
            response = requests.post(url, headers=headers, json=data)
            
            # 检查响应
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    minio_chunks_path = result.get("minio_chunks_path")
                    if minio_chunks_path:
                        # 下载处理后的文件
                        save_path = self.download_and_save_from_minio(
                            MINOBUCKET,
                            minio_chunks_path.replace(f"{MINOBUCKET}/", ""),
                            "../../resources/cache"
                        )
                        return save_path
                    else:
                        raise ValueError("Response does not contain minio_chunks_path")
                else:
                    raise ValueError(f"Processing failed: {result.get('message')}")
            else:
                raise ValueError(f"Request failed with status code: {response.status_code}")

        except Exception as e:
            logger.error(f"处理文件失败: {str(e)}")
            raise
        
    def file_format(self, params: dict) -> None:
        # 获取文件ID列表
        ids = params.get("filesId")
        if not ids:
            raise ValueError("'filesId' 参数不能为空.")

        # 获取关键词数量和相似文档数量，默认值分别为10和3
        key_num = params.get("keyNum", 10)
        sim_num = params.get("simNum", 3)
        faq_num = params.get("faqNum", FAQ_NUM)
        faq_context_length = params.get("faqContextLength",FAQ_CONTEXT_LENGTH)
        # 遍历每个文件ID
        for file_id in ids:
            try:
                # 记录开始时间
                format_start_time =get_current_time()
                # self.rs.update_time(REDIS_KEY, file_id, "formatStartTime", format_start_time)
                
                # 获取文件信息
                category_code, file_name, file_path, create_user_id = self.get_file_info(file_id)
                # 检查文件信息是否完整
                if any(x is None for x in [category_code, file_path, create_user_id, file_name]):
                    raise ValueError(f"未找到文件ID为{file_id}的内容")

                # 更新文件状态为结构化中
                self.update_file_status(file_id, FileStatus.Structuring.value)
                # self.rs.update_file_status(REDIS_KEY,file_id,FileStatus.Structuring.value)
                logger.info(f"{file_name}正在结构化中")
                # 获取当前路径
                current_path = os.path.dirname(os.getcwd())
                logger.info(current_path)
                is_deep_process = self.is_deep_process(file_id)

                if (is_deep_process == ParseMethod.DEEP_PARSE.value and self.is_deep_file(file_name) == 1) or self.is_deep_file(file_name) == 2:
                    # save_path = self.deep_parse_file(file_path,MINOBUCKET)
                    save_path = self.ocr_parse_file(file_path,ocr_on_figures=True)

                if (is_deep_process == ParseMethod.IMAGE_PARSE.value) and self.is_deep_file(file_name) == 1:
                    save_path = self.ocr_parse_file(file_path,ocr_on_figures=False)
                
                if is_deep_process == ParseMethod.SIMPLE_PARSE.value:
                    save_path = self.simple_parse_file(file_path,MINOBUCKET)
                try:
                    file_name = os.path.basename(save_path)
                    converter = ChangeFileTxt(save_path, file_name)
                    new_path, new_file_name = converter.change_file_format()
                except Exception as e:
                    logger.warning(f"常规解析失败，尝试使用其他解析方式：{file_name}")
                    raise
                # 处理文件以提取内容和关键词
                keywords, lang, id_similars, max_similarity, content = self.file_process(new_path, key_num, sim_num)

                # 插入转换后的文件信息
                self.insert_convert_file(file_id, category_code, content, keywords, lang, max_similarity, id_similars,
                                         create_user_id, file_path, len(content))
                 # 提取FAQ                architecture = os.uname().machine  # 读取服务器的架构
                architecture = os.uname().machine  # 读取服务器的架构
                if architecture == "x86_64":
                    create_user_id, text_id, questions, answers = self.extract_faqs_for_file(file_id, faq_num, faq_context_length)
                else:
                    create_user_id, text_id, questions, answers = self.extract_faqs_for_file_arm(file_id, faq_num, faq_context_length)
                if questions and answers:
                    for question, answer in zip(questions, answers):
                        file_faq_id = self.snowflake.next_id()
                        self.insert_faq_info(file_faq_id, text_id, file_id, question, answer,1, 1, 0, create_user_id)
                # 更新文件状态为结构化成功
                self.update_file_status(file_id, FileStatus.Structured_Successfully.value)
                # self.rs.update_file_status(REDIS_KEY,file_id,FileStatus.Structured_Successfully.value)
                logger.info(f"{file_name}结构化成功")
                format_end_time = get_current_time()
                # self.rs.update_time(REDIS_KEY, file_id, "formatEndTime", format_end_time)
                logger.info("----结构化运行时间：{}----".format(time_difference(format_start_time,format_end_time)))
                try:
                    os.remove(save_path)
                    logger.info(f"已删除下载到本地的文件：{save_path}")
                    if new_path and os.path.exists(new_path):
                        os.remove(new_path)
                        logger.info(f"已删除结构化生成的文件：{new_path}")
                except Exception as delete_err:
                    logger.error(f"删除文件失败: {delete_err}")
                    raise
            except Exception as err:
                logger.error(traceback.format_exc())
                logger.error(f"处理文件为{file_name}时发生错误: {str(err)}")
                self.conn.rollback()
                self.update_file_status(file_id, FileStatus.Structured_Failed.value)
                # self.rs.update_file_status(REDIS_KEY,file_id,FileStatus.Structured_Failed.value)
            finally:
                self.conn.commit()