from contextlib import contextmanager
from src.utils.mysql_conn import *
from src.utils.es_conn import *
from src.rag.embedding import LoadsEmbeddingModel
from src.utils.log_utils import logger
from src.utils.awen_schema import SliceStatus, FileStatus
from src.service.llm_base_service import QueryLLMService
import traceback
from typing import  Optional
from src.utils.time import *
from conf.server_config import *
logger = logger.bind(name="data_process")


class VectorStoreGenerator:
    def __init__(self,conn,es,ms):
        self.conn = conn
        self.es = es
        self.ms = ms
        # self.is_api = is_api
        if not IS_API:
            logger.info(f"调用向量化模型 {IS_API}")
            self.embedding_dict = LoadsEmbeddingModel().loads_model()
        # self.rs = rs

    @contextmanager
    def _get_cursor(self):
        cursor = self.conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()

    def _get_file_name(self, file_id):
        query = """
        SELECT file_name
        FROM xj_kg_file_info_tb
        WHERE file_id = %s;
        """
        # 执行SQL查询，传入文件ID作为参数
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            # 获取查询结果的第一行数据（文件名称），并返回文件名称本身
            result = cursor.fetchone()
            return result[0] if result else None

    def _get_slice_status(self, slice_id):
        query = """
        SELECT status FROM xj_kg_slice_info_tb WHERE slice_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (slice_id,))
            result = cursor.fetchone()
            return result[0] if result else None

    def _get_slices(self, file_id, slice_id=None):
        if slice_id is None:
            # 查询所有属于给定文件ID且启用的切片
            query = """
            SELECT slice_id, content, title, content_label
            FROM xj_kg_slice_info_tb
            WHERE file_id = %s AND is_enable = 1 AND is_delete = 0;
            """
            with self._get_cursor() as cursor:
                cursor.execute(query, (file_id,))
                # 返回所有查询结果的切片ID和内容的列表
                results = cursor.fetchall()
            slice_ids_list = [row[0] for row in results]
            contents = [row[1] for row in results]
            abstracts = [row[2] for row in results]
            content_labels = [row[3] for row in results]
        else:
            # 遍历切片ID列表，逐个查询每个切片的内容
            query = """
            SELECT content, title, content_label
            FROM xj_kg_slice_info_tb
            WHERE slice_id = %s AND is_delete = 0 AND is_enable = 1;
            """
            slice_ids_list = []
            contents = []
            abstracts = []
            content_labels = []
            for id in slice_id:
                with self._get_cursor() as cursor:
                    cursor.execute(query, (id,))
                    result = cursor.fetchone()
                if result is None:
                    logger.warning(f'未找到该slice_id {id}')
                    continue
                # 将查询到的切片ID和内容添加到结果列表中
                logger.info(f"slice_id {id}")
                slice_ids_list.append(id)
                contents.append(result[0])
                abstracts.append(result[1])
                content_labels.append(result[2])
        return slice_ids_list, contents, abstracts, content_labels

    def _get_vector_config(self, file_id):
        query = """
        SELECT vsc.category_code, kgi.kg_code, vc.vector_code
        FROM xj_kg_vector_slice_conf_tb vsc
        JOIN xj_kg_space_info_tb kgi ON vsc.kg_id = kgi.kg_id
        JOIN xj_kg_vector_conf_tb vc ON kgi.vector_id = vc.vector_id
        WHERE vsc.file_id = %s;
        """
        # 使用游标执行SQL查询，传入文件ID作为查询参数
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
        # 如果查询结果为空，则返回None
        if result is None:
            return None, None, None
        # 分别提取查询结果中的类别代码、知识图谱代码和向量代码
        category_code, kg_code, vector_code = result
        return category_code, kg_code, vector_code
    def _is_abstract_extract(self, file_id):
        query = """
        SELECT is_abstract_extract
        FROM xj_kg_vector_slice_conf_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
            result = cursor.fetchone()
        if result is None:
            return None
        return result[0]
    def _update_slice_status(self, slice_ids, status):
        sql = "UPDATE xj_kg_slice_info_tb SET status = %s WHERE slice_id = %s;"
        params = [(status, sid) for sid in slice_ids]
        with self._get_cursor() as cursor:
            cursor.executemany(sql, params)
            self.conn.commit()

    def _get_faq_faqid(self,faqs_id):
        query = """
        SELECT question,answer
        FROM xj_kg_faq_info_tb
        WHERE faq_id = %s;
        """
        questions = []
        answers = []
        for faq_id in faqs_id:
            with self._get_cursor() as cursor:
                cursor.execute(query, (faq_id,))
                result = cursor.fetchone()
            if result is None:
                logger.warning(f'未找到该faq_id {faq_id}')
                continue
            # 将查询到的切片ID和内容添加到结果列表中
            logger.info(f"faq_id {faq_id}")
            questions.append(result[0])
            answers.append(result[1])
        return questions, answers, faqs_id

    def _get_faq_flieid(self,file_id):
        query = """
            SELECT question,answer,faq_id
            FROM xj_kg_faq_info_tb
            WHERE file_id = %s ;
            """
        with self._get_cursor() as cursor:
            cursor.execute(query, (file_id,))
                # 返回所有查询结果的切片ID和内容的列表
            results = cursor.fetchall()
        questions = [row[0] for row in results]
        answers = [row[1] for row in results]
        faqs_id = [row[2] for row in results]
        return questions, answers, faqs_id

    def _get_faq_vector_config(self, faq_id):
        query = """
        SELECT s.kg_code, f.category_code, v.vector_code
        FROM xj_kg_faq_info_tb AS f
        JOIN xj_kg_space_info_tb AS s ON f.kg_id = s.kg_id
        JOIN xj_kg_vector_conf_tb AS v ON s.vector_id = v.vector_id
        WHERE f.faq_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query, (faq_id,))
            result = cursor.fetchone()
        if result is None:
            return None, None, None
        return result

    def _update_file_status(self, file_id, new_status):
        sql = """
            UPDATE xj_kg_file_info_tb
            SET file_status = %s
            WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(sql, (new_status, file_id))
            self.conn.commit()

    def _update_faq_status(self, faq_id, new_status):
        sql = """
            UPDATE xj_kg_faq_info_tb
            SET faq_status = %s
            WHERE faq_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(sql, (new_status, faq_id))
            self.conn.commit()


    # 批量更新FAQ状态
    # 批量更新FAQ状态
    def _batch_update_faq_status(self, faq_ids, new_status):
        query = """
            UPDATE xj_kg_faq_info_tb
            SET faq_status = %s
            WHERE faq_id IN ({})
            """.format(','.join(['%s'] * len(faq_ids)))
        
        # Create params list with new_status as first element followed by faq_ids
        params = [new_status] + list(faq_ids)
        
        with self._get_cursor() as cursor:
            cursor.execute(query, params)
            self.conn.commit()


    def _get_file_status(self, file_id: int) -> Optional[int]:
        """
        Author：mabenjiang
        Description: 从数据库中获取指定文件ID的文件状态

        Args:
            file_id (int): 要检索状态的文件的唯一标识符

        Returns:
            Optional[int]: 文件的状态值，如果找不到对应文件，则返回None
        """
        sql = """
            SELECT file_status 
            FROM xj_kg_file_info_tb 
            WHERE is_enable = 1 AND file_id = %s
        """
        with self._get_cursor() as cursor:
            cursor.execute(sql, (file_id,))
            data = cursor.fetchone()
        if data is None:
            return None
        return data[0]

    def _get_slice_tags(self, file_id):
        """
        根据file id 查询 xj_kg_slice_tag_association_tb 表中对应的slice id 和tag_id，
        并根据查出来的tag_id 查询 xj_kg_data_tag_tb表中的tag_name。

        Args:
            file_id (int): 文件ID。

        Returns:
            dict: 包含slice id和对应tag_name的字典。
        """
        # 查询xj_kg_slice_tag_association_tb表
        query_association = """
        SELECT slice_id, tag_id
        FROM xj_kg_slice_tag_association_tb
        WHERE file_id = %s;
        """
        with self._get_cursor() as cursor:
            cursor.execute(query_association, (file_id,))
            association_results = cursor.fetchall()

        # 查询xj_kg_data_tag_tb表
        tag_names = {}
        for slice_id, tag_id in association_results:
            if slice_id not in tag_names:
                tag_names[slice_id] = []

            query_tag = """
            SELECT tag_name
            FROM xj_kg_data_tag_tb
            WHERE tag_id = %s;
            """
            with self._get_cursor() as cursor:
                cursor.execute(query_tag, (tag_id,))
                tag_result = cursor.fetchone()
            if tag_result:
                tag_names[slice_id].append(tag_result[0])
            else:
                logger.warning(f'未找到tag_id {tag_id} 对应的tag_name')

        return tag_names

    def process_slices_with_contextual_retrieval(self, slices):
        """
        使用LLM服务对切片进行上下文检索处理。

        Args:
            slices (list): 原始文本切片列表。
            chat_history (list, optional): 上下文历史信息。

        Returns:
            list: 包含上下文信息的处理后切片列表。
        """
        qs = QueryLLMService()
        processed_slices = []

        # 遍历每个切片，调用contextual_retrieval并拼接上下文信息
        for slice_text in slices:
            contextual_info = qs.contextual_retrieval(content=slice_text)
            combined_text = slice_text + "\n" + contextual_info
            processed_slices.append(combined_text)

        return processed_slices

    def generate_vector_store_for_files(self, file_ids, slice_id, is_contextual_retrieval=False):
        slice_ids = []
        # 遍历所有的文件ID，逐个处理
        for file_id in file_ids:
            try:
                 # 查询文件状态
                vector_start_time =get_current_time()
                if slice_id is None:
                    current_status = self._get_file_status(file_id)
                    if current_status != FileStatus.Start_Vector.value:
                        logger.warning(f'文件ID为{file_id}的状态不是向量化开始，当前状态为{current_status}，跳过向量化')
                        continue
                    # vector_start_time =get_current_time()
                    # self.rs.update_time(REDIS_KEY, file_id, "vectorStartTime", vector_start_time)
                # 获取文件名称
                file_name = self._get_file_name(file_id)
                if file_name is None:
                    raise ValueError(f'File ID {file_id} not found')

                # 获取切片ID、切片内容和摘要
                slice_ids, slices, abstracts,content_labels = self._get_slices(file_id, slice_id)
                if not slices:
                    raise ValueError(f'No slices found for file ID {file_id}{file_name}')

                # for i, content_label in enumerate(content_labels):
                #     if content_label is not None:
                #         slices[i] = content_label

                # 获取文件的向量配置，包括分类代码、知识空间代码和向量代码
                category_code, kg_code, vector_code = self._get_vector_config(file_id)
                if any([x is None for x in [kg_code, category_code, vector_code]]):
                    raise ValueError(f'Vectorization config not found for file ID {file_id}{file_name}')

                # 检查切片长度，确保每个切片不超过65535字节
                for i, text in enumerate(slices):
                    text_bytes = text.encode('utf-8')
                    if len(text_bytes) > 65535:
                        slices[i] = text_bytes[:65535].decode('utf-8', errors='ignore')
                        logger.warning(f'Slice ID {slice_ids[i]} exceeds 65535 bytes and has been truncated.')

                # 获取切片的当前状态
                status = self._get_slice_status(slice_ids[0])

                # 如果切片处于待插入到ES的状态，则执行ES加载并更新状态为插入成功
                if status == SliceStatus.Pending_Insertion_ES.value:
                    self.es.load_data(category_code, slice_ids, slices, abstracts, file_id, file_name)
                    self._update_slice_status(slice_ids, SliceStatus.Insertion_ES_Successful.value)
                    self._update_file_status(file_id, FileStatus.Vectorized_Successfully.value)
                else:
                    # 否则，先更新文件状态为正在向量化
                    if slice_id is None:
                        self._update_file_status(file_id, FileStatus.Vectorizing.value)
                        # self.rs.update_file_status(REDIS_KEY,file_id,FileStatus.Vectorizing.value)
                    self._update_slice_status(slice_ids, SliceStatus.Vectorizing.value)
                    # 获取切片的标签
                    slice_tags = self._get_slice_tags(file_id)
                    logger.info(slice_tags)
                    # 拼接标签和切片内容
                    for i, slice_text in enumerate(slices):
                        tags = slice_tags.get(slice_ids[i], [])
                        if tags:
                            slices[i] = f"Tags: {', '.join(tags)} {slice_text}"

                    # 根据标志位决定是否对切片进行上下文检索处理
                    if is_contextual_retrieval:
                        slices = self.process_slices_with_contextual_retrieval(slices)
                    if IS_API:
                        em_result_slices = LoadsEmbeddingModel().embedding_api_batch(slices)
                    else:
                        em = self.embedding_dict[vector_code]
                        em_result_slices = em.embedding_split_text(slices)
                    # 根据match_content标志位决定是否生成摘要的嵌入向量
                    if self._is_abstract_extract(file_id) == 1:
                        if IS_API:
                            em_result_abstracts = LoadsEmbeddingModel().embedding_api_batch(abstracts)
                        else:
                            em_result_abstracts = em.embedding_split_text(abstracts)
                        self.ms.insert(category_code, slice_ids, file_id, slices, em_result_slices, file_name, kg_code, abstracts, em_result_abstracts)
                    else:
                        # em_result_abstracts = None

                    # 插入切片和摘要的数据到Milvus
                        # self.ms.insert(match_content, category_code, slices, em_result_slices, em_result_abstracts, slice_ids, file_id, kg_code, file_name)
                        self.ms.insert(category_code, slice_ids, file_id, slices, em_result_slices, file_name, kg_code)
                    # 将数据加载到ES中
                    self.es.load_data(category_code, slice_ids, slices, abstracts, file_id, file_name)
                    # self.es.load_data_with_vector(category_code, slice_ids, list(zip(slices, em_result_slices)), file_id, file_name)

                    # 更新切片和文件状态为可检索和向量化成功
                    self._update_slice_status(slice_ids, SliceStatus.Available_Retrieval.value)
                    vector_end_time =get_current_time()
                    if slice_id is None:
                        self._update_file_status(file_id, FileStatus.Vectorized_Successfully.value)
                        # self.rs.update_file_status(REDIS_KEY,file_id, FileStatus.Vectorized_Successfully.value)
                        # self.rs.update_time(REDIS_KEY, file_id, "vectorEndTime", vector_end_time)
                    logger.info("----向量化运行时间：{}----".format(time_difference(vector_start_time,vector_end_time)))
            except Exception as e:
                # 处理异常并记录错误日志
                logger.error(traceback.print_exc())
                logger.error(f"Error vectorizing file ID {file_id}{file_name}: {str(e)}")
                self.conn.rollback()
                self._update_file_status(file_id, FileStatus.Vectorized_Failed.value)
                # self.rs.update_file_status(REDIS_KEY,file_id, FileStatus.Vectorized_Failed.value)
                self._update_slice_status(slice_ids, SliceStatus.Vectorization_Failed.value)


    def generate_vector_store_for_faqs(self, file_ids, slice_ids):
        if not file_ids:
            # 如果file_ids为空，则使用_get_faq_faqid获取FAQ数据
            questions, answers, faqs_id = self._get_faq_faqid(slice_ids)
            if not questions or not answers:
                raise ValueError(f'FAQ data not found for slice IDs {slice_ids}')

            for faq_id, question, answer in zip(slice_ids, questions, answers):
                try:
                    kg_code, category_code, vector_code = self._get_faq_vector_config(faq_id)
                    if any([x is None for x in [kg_code, category_code, vector_code]]):
                        raise ValueError(f'Vectorization config not found for FAQ ID {faq_id}')

                    logger.info(f'FAQ ID {faq_id} vectorization config: {category_code}, {kg_code}, {vector_code}')
                    # 更新状态为向量化中
                    self._update_faq_status(faq_id,FaqStatus.FAQ_Vectorizing.value)
                    if IS_API:
                        em_result = LoadsEmbeddingModel().embedding_api(question)
                    else:
                        em = self.embedding_dict[vector_code]
                        em_result = em.embedding_query(question)
                    logger.info(f"Input parameters: faq_id={faq_id}, category_code={category_code}, question={question}, em_result={em_result}, answer={answer}, kg_code={kg_code}")
                    self.ms.insert_faq(faq_id, [faq_id], category_code, [question], [em_result], [answer], kg_code)
                    self._update_faq_status(faq_id,FaqStatus.FAQ_Vectorized_Successfully.value)
                except Exception as e:
                    logger.error(f"Error vectorizing FAQ ID {faq_id}: {str(e)}")
                    self.conn.rollback()
                    self._update_faq_status(faq_id,FaqStatus.FAQ_Vectorization_Failed.value)
                finally:
                    self.conn.commit()
        else:
            for file_id in file_ids:
                try:
                    questions, answers, faqs_id = self._get_faq_flieid(file_id)
                    if not questions or not answers:
                        raise ValueError(f'FAQ data not found for file ID {file_id}')

                    kg_code, category_code, vector_code = self._get_faq_vector_config(faqs_id[0])
                    if any([x is None for x in [kg_code, category_code, vector_code]]):
                        raise ValueError(f'Vectorization config not found for FAQ ID {faqs_id[0]}')

                    logger.info(f'FAQ ID {faqs_id[0]} vectorization config: {category_code}, {kg_code}, {vector_code}')

                    self._update_file_status(file_id, FileStatus.Vectorizing.value)
                    self._batch_update_faq_status(faqs_id,FaqStatus.FAQ_Vectorizing.value)

                    if IS_API:
                        em_result = LoadsEmbeddingModel().embedding_api_batch(questions)
                    else:
                        em = self.embedding_dict[vector_code]
                        em_result = em.embedding_split_text(questions)

                    self.ms.insert_faq(file_id, faqs_id, category_code, questions, em_result, answers, kg_code)
                    self._update_file_status(file_id, FileStatus.Vectorized_Successfully.value)
                    self._batch_update_faq_status(faqs_id,FaqStatus.FAQ_Vectorized_Successfully.value)
                except Exception as e:
                    logger.error(f"Error vectorizing FAQ file ID {file_id}: {str(e)}")
                    self.conn.rollback()
                    self._update_file_status(file_id, FileStatus.Vectorized_Failed.value)
                    self._batch_update_faq_status(faqs_id,FaqStatus.FAQ_Vectorization_Failed.value)
                finally:
                    self.conn.commit()
        # self.close()

    def generate_vector_store(self, params):
        """
        文本向量化主函数
        """
        slice_ids = params.get("slicesId")
        file_ids = params.get("filesId")
        vector_type = params.get("vectorType")
        # match_content = params.get("matchContent", IF_RERANK)  ## matchType: 1)False:按照切片匹配，发挥1切片 2)True:按照摘要匹配，泛化切片
        is_contextual_retrieval = params.get("isContextualRetrieval", IF_CONTEXTUAL_RETRIEVAL)
        if vector_type == "FILE":
            self.generate_vector_store_for_files(file_ids, slice_ids,is_contextual_retrieval)
        elif vector_type == "FAQ":
            self.generate_vector_store_for_faqs(file_ids, slice_ids)

    def delete_vector_ids(self, params):
        slice_ids = params.get("slicesId")
        partition_name = params.get("categoryCode")
        col_name = params.get("kgCode")
        self.ms.delete_by_id(slice_ids, "slice_id", partition_name, col_name)
        self.es.del_data(partition_name, slice_ids)

    def delete_faq_vector_ids(self, params):
        faq_ids = params.get("faqsId")
        partition_name = params.get("categoryCode")
        col_name = params.get("kgCode")
        self.ms.delete_by_id(faq_ids, "faq_id", partition_name, col_name)


    def create_col(self, params):
        col_name = params.get("kgCode")
        des = params.get("description")
        col_type = params.get("colType")
        try:
            # 实例化MilvusClient，考虑复用性，可能需要在更高层次实现
            # 使用字典映射重构，提高代码可维护性
            self.ms.do_create_col(col_name, des, col_type)
        except Exception as e:
            logging.error(f"执行过程中发生异常: {e}")

    def create_partiton(self, params):
        partition_name = params.get("categoryCode")
        col_name = params.get("kgCode")
        self.ms.add_partition(partition_name, col_name)
