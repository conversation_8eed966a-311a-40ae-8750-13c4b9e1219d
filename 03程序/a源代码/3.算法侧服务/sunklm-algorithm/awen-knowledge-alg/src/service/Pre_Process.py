"""
文本预处理服务
- 替换连续空格、换行、制表符（知识库能力）
- 删除URL、电子邮箱地址（知识库能力）
- 清理特殊字符和无效内容
- 标准化文本格式
"""

import re
from typing import Optional, Dict


class TextPreProcessorService:
    """
    文本预处理类，用于处理文本中的特殊字符和模式
    包括替换连续空格、换行、制表符，以及删除URL和电子邮箱地址
    """

    def __init__(self):
        """初始化预处理服务，编译所有正则表达式模式"""
        self._compile_patterns()
        self.processing_stats = {
            'total_processed': 0,
            'urls_removed': 0,
            'emails_removed': 0,
            'whitespace_normalized': 0
        }

    def _compile_patterns(self):
        """编译所有正则表达式模式，提高性能"""

        # === URL相关模式 ===
        # 增强的URL模式，支持更多协议和域名格式
        self.url_pattern = re.compile(
            r'(?i)(?::|：)?\b(?:'
            r'(?:https?|ftp|ftps|sftp|file)://[^\s<>"\']+|'  # 标准协议URL
            r'www\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*[^\s<>"\']*|'  # www开头
            r'[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*\.'
            r'(?:com|cn|net|org|edu|gov|io|me|app|co|ai|info|biz|mil|int|arpa|'
            r'ac|ad|ae|af|ag|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|'
            r'ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|co|cr|cs|cu|cv|cw|cx|cy|cz|de|dj|dk|dm|do|dz|ec|ee|eg|eh|er|es|et|eu|fi|fj|fk|fm|fo|fr|'
            r'ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|'
            r'ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|'
            r'na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|'
            r'sa|sb|sc|sd|se|sg|sh|si|sj|sk|sl|sm|sn|so|sr|st|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|'
            r'ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|za|zm|zw)'
            r'(?:/[^\s<>"\']*)?'
            r')\b',
            re.IGNORECASE
        )

        # URL描述词与URL组合的模式
        self.url_with_context_pattern = re.compile(
            r'(?:网址|网站|链接|地址|URL|url|Url)[:：]\s*'
            r'(?:https?://[^\s<>"\']+|www\.[^\s<>"\']+|[^\s<>"\']+\.[a-z]{2,}[^\s<>"\']*)',
            re.IGNORECASE
        )

        # === 邮箱相关模式 ===
        # 增强的邮箱模式，支持更多格式
        self.email_pattern = re.compile(
            r'(?::|：)?\b[a-zA-Z0-9](?:[a-zA-Z0-9._-]*[a-zA-Z0-9])?@'
            r'[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?'
            r'(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\b'
        )

        # 邮箱描述词与邮箱组合的模式
        self.email_with_context_pattern = re.compile(
            r'(?:邮箱|邮件|电子邮箱|电子邮件|Email|email|E-mail|e-mail)[:：]\s*'
            r'[a-zA-Z0-9](?:[a-zA-Z0-9._-]*[a-zA-Z0-9])?@'
            r'[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?'
            r'(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*',
            re.IGNORECASE
        )

        # === 电话号码模式 ===
        self.phone_pattern = re.compile(
            r'(?:电话|手机|联系电话|Tel|tel|TEL|Phone|phone)[:：]\s*'
            r'(?:\+?86[-\s]?)?'
            r'(?:1[3-9]\d{9}|'  # 手机号
            r'0\d{2,3}[-\s]?\d{7,8}|'  # 固定电话
            r'\d{3,4}[-\s]?\d{7,8})'  # 其他格式
        )

        # === 其他清理模式 ===
        # 连接词模式
        self.conjunction_pattern = re.compile(r'\s*[，,]?\s*(?:和|与|及|或|以及)\s*[，,]?\s*(?=[，。；：！？\s]|$)')

        # 空白字符模式
        self.whitespace_pattern = re.compile(r'\s+')

        # 重复标点符号模式
        self.repeated_punctuation_patterns = {
            'comma': re.compile(r'[，,]{2,}'),
            'period': re.compile(r'[。.]{2,}'),
            'colon': re.compile(r'[：:]{2,}'),
            'semicolon': re.compile(r'[；;]{2,}'),
            'exclamation': re.compile(r'[！!]{2,}'),
            'question': re.compile(r'[？?]{2,}')
        }

        # 无效结尾模式
        self.invalid_ending_pattern = re.compile(r'(?:联系我们|联系方式|更多信息|详情请见|欢迎咨询)[:：]\s*$')

        # HTML标签模式
        self.html_tag_pattern = re.compile(r'<[^>]+>')

        # 特殊字符模式
        self.special_char_pattern = re.compile(r'[^\w\s\u4e00-\u9fff，。；：！？""''（）【】《》〈〉「」『』〔〕［］｛｝]')

    def normalize_whitespace(self, text: str) -> str:
        """
        标准化文本中的空白字符

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        # 替换连续的空白字符（空格、换行、制表符等）为单个空格
        normalized = self.whitespace_pattern.sub(' ', text)
        self.processing_stats['whitespace_normalized'] += 1
        return normalized.strip()

    def remove_urls(self, text: str) -> str:
        """
        删除文本中的URL及其描述词

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        original_text = text

        # 先处理带有描述词的URL（如"网址：http://..."）
        text = self.url_with_context_pattern.sub('', text)

        # 再处理普通URL
        text = self.url_pattern.sub('', text)

        # 统计删除的URL数量
        if text != original_text:
            self.processing_stats['urls_removed'] += 1

        return text

    def remove_emails(self, text: str) -> str:
        """
        删除文本中的电子邮箱地址及其描述词

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        original_text = text

        # 先处理带有描述词的邮箱（如"邮箱：<EMAIL>"）
        text = self.email_with_context_pattern.sub('', text)

        # 再处理普通邮箱
        text = self.email_pattern.sub('', text)

        # 统计删除的邮箱数量
        if text != original_text:
            self.processing_stats['emails_removed'] += 1

        return text

    def remove_phone_numbers(self, text: str) -> str:
        """
        删除文本中的电话号码

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        return self.phone_pattern.sub('', text)

    def remove_html_tags(self, text: str) -> str:
        """
        删除HTML标签

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        return self.html_tag_pattern.sub('', text)

    def clean_special_characters(self, text: str) -> str:
        """
        清理特殊字符，保留中文、英文、数字和常用标点符号

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        return self.special_char_pattern.sub('', text)

    def clean_punctuation(self, text: str) -> str:
        """
        清理重复的标点符号

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        # 处理重复的标点符号
        for punct_type, pattern in self.repeated_punctuation_patterns.items():
            if punct_type == 'comma':
                text = pattern.sub('，', text)
            elif punct_type == 'period':
                text = pattern.sub('。', text)
            elif punct_type == 'colon':
                text = pattern.sub('：', text)
            elif punct_type == 'semicolon':
                text = pattern.sub('；', text)
            elif punct_type == 'exclamation':
                text = pattern.sub('！', text)
            elif punct_type == 'question':
                text = pattern.sub('？', text)

        return text

    def clean_conjunctions(self, text: str) -> str:
        """
        清理多余的连接词

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        return self.conjunction_pattern.sub('', text)

    def clean_invalid_endings(self, text: str) -> str:
        """
        清理无效的结尾（如"联系我们："等）

        Args:
            text: 输入文本

        Returns:
            处理后的文本
        """
        if not text:
            return text

        return self.invalid_ending_pattern.sub('', text)

    def process_text(self, text: str, options: Optional[Dict[str, bool]] = None) -> str:
        """
        对文本进行完整的预处理

        Args:
            text: 输入文本
            options: 处理选项字典，可以控制哪些处理步骤被执行
                - remove_urls: 是否删除URL (默认True)
                - remove_emails: 是否删除邮箱 (默认True)
                - remove_phones: 是否删除电话号码 (默认True)
                - remove_html: 是否删除HTML标签 (默认True)
                - clean_special_chars: 是否清理特殊字符 (默认False)
                - normalize_whitespace: 是否标准化空白字符 (默认True)
                - clean_punctuation: 是否清理重复标点 (默认True)
                - clean_conjunctions: 是否清理连接词 (默认True)
                - clean_endings: 是否清理无效结尾 (默认True)

        Returns:
            处理后的文本
        """
        if not text or not isinstance(text, str):
            return text or ""

        # 默认处理选项
        default_options = {
            'remove_urls': True,
            'remove_emails': True,
            'remove_phones': True,
            'remove_html': True,
            'clean_special_chars': False,
            'normalize_whitespace': True,
            'clean_punctuation': True,
            'clean_conjunctions': True,
            'clean_endings': True
        }

        # 合并用户选项
        if options:
            default_options.update(options)

        # 保存原始文本，用于调试和统计
        original_text = text

        try:
            # 1. 删除HTML标签
            if default_options['remove_html']:
                text = self.remove_html_tags(text)

            # 2. 标准化空白字符
            if default_options['normalize_whitespace']:
                text = self.normalize_whitespace(text)

            # 3. 删除URL
            if default_options['remove_urls']:
                text = self.remove_urls(text)

            # 4. 删除邮箱
            if default_options['remove_emails']:
                text = self.remove_emails(text)

            # 5. 删除电话号码
            if default_options['remove_phones']:
                text = self.remove_phone_numbers(text)

            # 6. 清理特殊字符
            if default_options['clean_special_chars']:
                text = self.clean_special_characters(text)

            # 7. 清理重复标点符号
            if default_options['clean_punctuation']:
                text = self.clean_punctuation(text)

            # 8. 清理连接词
            if default_options['clean_conjunctions']:
                text = self.clean_conjunctions(text)

            # 9. 清理无效结尾
            if default_options['clean_endings']:
                text = self.clean_invalid_endings(text)

            # 10. 最终的空白字符标准化
            if default_options['normalize_whitespace']:
                text = self.normalize_whitespace(text)

            # 更新统计信息
            self.processing_stats['total_processed'] += 1

            # 如果处理后文本为空，但原始文本不为空，则返回空字符串
            if not text.strip() and original_text.strip():
                return ""

            return text.strip()

        except Exception as e:
            # 如果处理过程中出现异常，返回原始文本
            print(f"文本处理异常: {e}")
            return original_text

    def get_processing_stats(self) -> Dict[str, int]:
        """
        获取处理统计信息

        Returns:
            统计信息字典
        """
        return self.processing_stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            'total_processed': 0,
            'urls_removed': 0,
            'emails_removed': 0,
            'whitespace_normalized': 0
        }

    def batch_process(self, texts: list, options: Optional[Dict[str, bool]] = None) -> list:
        """
        批量处理文本

        Args:
            texts: 文本列表
            options: 处理选项

        Returns:
            处理后的文本列表
        """
        if not texts:
            return []

        return [self.process_text(text, options) for text in texts]
