"""
文本预处理
- 替换连续空格、换行、制表符（知识库能力）
- 删除url、电子邮箱地址（知识库能力）
"""

import re

class TextPreProcessorService:
    """
    文本预处理类，用于处理文本中的特殊字符和模式
    包括替换连续空格、换行、制表符，以及删除URL和电子邮箱地址
    """
    
    def __init__(self):
        # URL正则表达式模式 - 精确匹配URL本身
        self.url_pattern = re.compile(r'(https?://\S+|www\.\S+|\S+\.(com|cn|net|org|edu|gov|io|me|app|co|ai)\S*)')
        # 电子邮箱正则表达式模式 - 精确匹配邮箱本身
        self.email_pattern = re.compile(r'(\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b)')
        # URL描述词与URL组合的模式，包括前后可能的文本
        self.url_with_context_pattern = re.compile(r'([^，。；：！？\s]*URL：\s*)(https?://\S+|www\.\S+|\S+\.(com|cn|net|org|edu|gov|io|me|app|co|ai)\S*)([^，。；：！？\s]*)')
        # 邮箱描述词与邮箱组合的模式，包括前后可能的文本
        self.email_with_context_pattern = re.compile(r'([^，。；：！？\s]*邮箱：\s*)(\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b)([^，。；：！？\s]*)')
        # 连接词模式，用于处理删除URL或邮箱后可能留下的连接词
        self.conjunction_pattern = re.compile(r'\s*(和|与|及|或)\s*(?=(\s|$|，|。|；|：|！|？))')
    
    def replace_whitespace(self, text):
        """
        替换文本中的连续空格、换行和制表符为单个空格
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 替换连续的空白字符（空格、换行、制表符等）为单个空格
        return re.sub(r'\s+', ' ', text)
    
    def remove_urls(self, text):
        """
        删除文本中的URL及其描述词
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 先处理带有"URL："前缀的情况
        text = self.url_with_desc_pattern.sub('', text)
        # 再处理普通URL
        text = self.url_pattern.sub('', text)
        return text
    
    def remove_emails(self, text):
        """
        删除文本中的电子邮箱地址及其描述词
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 处理后的文本
        """
        # 先处理带有"邮箱："前缀的情况
        text = self.email_with_desc_pattern.sub('', text)
        # 再处理普通邮箱
        text = self.email_pattern.sub('', text)
        return text
    
    def process_text(self, text):
        """
        对文本进行完整的预处理，包括替换连续空格、换行、制表符，以及删除URL和电子邮箱地址
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 处理后的文本
        """
        if not text or not isinstance(text, str):
            return text
            
        # 保存原始文本，用于调试
        original_text = text
        
        try:
            # 先替换连续空格、换行、制表符，使文本更规范
            text = self.replace_whitespace(text)
            
            # 处理带上下文的URL模式（包括描述词和可能的连接词）
            text = self.url_with_context_pattern.sub('', text)
            
            # 处理带上下文的邮箱模式（包括描述词和可能的连接词）
            text = self.email_with_context_pattern.sub('', text)
            
            # 删除普通URL
            text = self.url_pattern.sub('', text)
            
            # 删除普通邮箱
            text = self.email_pattern.sub('', text)
            
            # 处理剩余的"URL："或"邮箱："文本
            text = re.sub(r'URL：\s*', '', text)
            text = re.sub(r'邮箱：\s*', '', text)
            
            # 处理可能出现的连接词（如：和、与、及、或）
            text = self.conjunction_pattern.sub('', text)
            
            # 处理可能出现的连续逗号、句号等标点符号
            text = re.sub(r'[，,]{2,}', '，', text)
            text = re.sub(r'[。.]{2,}', '。', text)
            text = re.sub(r'[：:]{2,}', '：', text)
            
            # 处理可能出现的连续空格
            text = re.sub(r'\s+', ' ', text)
            
            # 处理可能出现的前后空格
            text = text.strip()
            
            # 处理特殊情况：如果文本以"联系我们："等结尾，则删除
            text = re.sub(r'[^，。；：！？\s]*：$', '', text)
            
            # 如果处理后文本为空，但原始文本不为空，则返回一个空格
            if not text and original_text:
                return " "
                
            return text
        except Exception as e:
            # 如果处理过程中出现异常，返回原始文本
            print(f"文本处理异常: {e}")
            return original_text
