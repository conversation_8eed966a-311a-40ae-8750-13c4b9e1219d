#!/user/bin/env python3
# -*- coding: utf-8 -*-
"""
根据规则对文档进行结构化解析
"""
import json
from src.tools.replace import is_chinese, containstr
import re
import warnings

warnings.filterwarnings('ignore')


class  ChapterSplitter:
    def __init__(self):
        self.mulu_leibie1 = 0  # 第几条
        self.mulu_leibie2 = 0  # 1.1 1.2
        self.mulu_leibie3 = 0  # 第1类
        self.mulu_leibie4 = 0  # 第一部分
        self.mulu_leibie5 = 0  # 第一章
        self.mulu_leibie6 = 0  # 1.   2.
        self.mulu_leibie7 = 0  # 1、   2、
        self.mulu_leibie8 = 0  # 标准论文格式

    def getno(self, s: str,
              mulu_leibie: str):
        # 配置标题规则
        # if '......' in s or '……' in s:  # 目录不要有省略号 ......
        #     return  None
        if mulu_leibie == 'A':  # 第几条\一、（一）
            searchObj = re.search(r'^第[\d一二三四五六七八九十]+条', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[一二三四五六七八九十百〇]+、', s, re.M | re.I)
            if searchObj and '；' not in s and '。' not in s:
                # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            searchObj = re.search(r'^\([一二三四五六七八九十]+\)', s, re.M | re.I)
            # 英文括号（一）
            if searchObj and '；' not in s and '。' not in s and ('，' not in s):
                # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            searchObj = re.search(r'^（[一二三四五六七八九十]+）', s, re.M | re.I)
            # 中文括号（一）
            if searchObj and '；' not in s and '。' not in s and ('，' not in s):
                # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
        elif mulu_leibie == 'C':  # # 第一章、第一条、（一、或1.）
            s = s.replace("．", ".")
            searchObj = re.search(r'^第[一二三四五六七八九十]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[1-9]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[\d一二三四五六七八九十]+条', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[一二三四五六七八九十百]+、', s, re.M | re.I)
            if searchObj and '；' not in s and '，' not in s:
                # 只是文字，不是句子，没标点符号
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}[.]', s, re.M | re.I)
            searchObj2 = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1})',
                                   s, re.M | re.I)
            # if searchObj2 and '。' not in s and '；' not in s :#and len(s)>5
            #     return searchObj2.group(0)
            if searchObj and not searchObj2 and '。' not in s and '；' \
                    not in s and '、' not in s:
                return searchObj.group(0)
            return None
        elif mulu_leibie == 'E':  # # 第一章、1.、1.1、
            s = s.replace("．", ".")
            searchObj = re.search(r'^第[一二三四五六七八九十]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[1-9]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[一二三四五六七八九十百]+、', s, re.M | re.I)
            if searchObj and '；' not in s and '，' not in s:
                # 只是文字，不是句子，没标点符号
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}[.]', s, re.M | re.I)
            searchObj2 = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1})',
                                   s, re.M | re.I)
            if searchObj2 and '。' not in s and '；' not in s:  # and len(s)>5
                return searchObj2.group(0)
            if searchObj and not searchObj2 and '。' not in s and '；' \
                    not in s and '、' not in s:
                return searchObj.group(0)
            return None
        elif mulu_leibie == 'D':  # # 第一部分、第一条、一、
            searchObj = re.search(r'^第[一二三四五六七八九十]+部分', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[一二三四五六七八九十]+条', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[一二三四五六七八九十百]+、', s, re.M | re.I)
            # 只是文字，不是句子，没标点符号
            if searchObj and '；' not in s and '。' not in s and ('，' not in s):
                return searchObj.group(0)
            return None
        elif mulu_leibie == 'B':  # 1.1 1.2
            searchObj = re.search(r'^第[\d]+类[：]', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[\d]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[一二三四五六七八九十]+部分', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            s = s.replace("．", ".")
            searchObj = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1}'
                                  r'([.][1-9][0-9]{0, 1}))', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1})',
                                  s, re.M | re.I)
            if searchObj and (is_chinese(s[-1])) and '……' not in s:  # or x1<200
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}[.]', s, re.M | re.I)
            if searchObj and is_chinese(s[-1]) and len(s) < 30 and '。' \
                    not in s and '；' not in s:  # 第一级标题长度不长
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0,2}', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0,2}[\)）、]', s,
                                  re.M | re.I)  # 中英文括号、顿号去掉
            if searchObj:
                return searchObj.group(0)
        elif mulu_leibie == 'G':  # 包含第1章，1.1，一、，（一）
            searchObj = re.search(r'^摘 {2}要', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^关键词', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^结束语', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^目 {2}录', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^参考文献', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^第[\d]+章', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[一二三四五六七八九十百〇]+、', s, re.M | re.I)
            if searchObj and '；' not in s and '。' not in s:
                # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            searchObj = re.search(r'^\([一二三四五六七八九十]+\)', s, re.M | re.I)
            # 英文括号（一）
            if searchObj:  # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            searchObj = re.search(r'^（[一二三四五六七八九十]+）', s, re.M | re.I)
            # 英文括号（一）
            if searchObj:  # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            searchObj = re.search(r'^（[1-9]+）', s, re.M | re.I)  # 中文括号（一）
            if searchObj and len(s) > 30:  # 只是文字，不是句子，没标点符号or '：' in s
                return searchObj.group(0)
            s = s.replace("．", ".")
            searchObj = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1})',
                                  s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1}'
                                  r'([.][1-9][0-9]{0, 1}))', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}([.][1-9][0-9]{0, 1})',
                                  s, re.M | re.I)
            if searchObj and (is_chinese(s[-1])) and '……' not in s:  # or x1<200
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0, 1}[.]', s, re.M | re.I)
            if searchObj and is_chinese(s[-1]) and len(s) < 30 and '。' \
                    not in s and '；' not in s:  # 第一级标题长度不长
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0,2}', s, re.M | re.I)
            if searchObj:
                return searchObj.group(0)
            searchObj = re.search(r'^[1-9][0-9]{0,2}[\)）、]', s, re.M | re.I)
            # 中英文括号、顿号去掉
            if searchObj:
                return searchObj.group(0)
    def split_text(self, content: str):
        """
        结构化文档解析,将文本按章节结构分割
        
        Args:
            content: str - 待分割的文本内容
            
        Returns:
            List[str] - 分割后的文本片段列表
        """
        # 重置计数器
        self.mulu_leibie1 = self.mulu_leibie2 = self.mulu_leibie3 = 0  
        self.mulu_leibie4 = self.mulu_leibie5 = self.mulu_leibie6 = 0
        self.mulu_leibie7 = self.mulu_leibie8 = 0
        
        # 将文本按行分割
        word_context = content.split('\n')
        
        # 统计文档结构特征
        for line in word_context:
            if not line.strip():
                continue
            if containstr(line, r'^第[\d一二三四五六七八九十]+条'):
                self.mulu_leibie1 += 1
            if containstr(line, r'^[1-9][0-9]{0,1}([.][1-9][0-9]{0,1})'):
                self.mulu_leibie2 += 1
            if containstr(line, r'^[1-9][0-9]{0,1}[.]') and not containstr(line, r'^[1-9][0-9]{0,1}([.][1-9][0-9]{0,1})'):
                self.mulu_leibie6 += 1
            if containstr(line, r'^第[\d]+类[：]'):
                self.mulu_leibie3 += 1
            if containstr(line, r'^[一二三四五六七八九十][、]'):
                self.mulu_leibie7 += 1
            if containstr(line, r'^第[一二三四五六七八九十]+部分'):
                self.mulu_leibie4 += 1
            if containstr(line, r'^第[一二三四五六七八九十]+章'):
                self.mulu_leibie5 += 1
            if containstr(line, r'^第[1-9]+章'):
                self.mulu_leibie5 += 1
            if containstr(line, r'^摘  要|引  言|目  录'):
                self.mulu_leibie8 += 1

        # 确定文档类型
        if self.mulu_leibie4 >= 2 and self.mulu_leibie1 > 3:
            mulu_leibie = 'D'  # 第几部分格式
        elif self.mulu_leibie5 >= 3 and self.mulu_leibie1 > 3:
            mulu_leibie = 'C'  # 第几章格式
        elif self.mulu_leibie5 >= 3 and self.mulu_leibie6 > 5:
            mulu_leibie = 'E'  # 第几章+数字格式 
        elif self.mulu_leibie1 > self.mulu_leibie2 or self.mulu_leibie1 > 10 or self.mulu_leibie7 > 4:
            mulu_leibie = 'A'  # 条款格式
        elif self.mulu_leibie1 + self.mulu_leibie2 + self.mulu_leibie3 + self.mulu_leibie4 + self.mulu_leibie5 + self.mulu_leibie6 < 3:
            mulu_leibie = 'F'  # 无结构
        else:
            mulu_leibie = 'B'  # 数字编号格式
        if self.mulu_leibie8 >= 3:
            mulu_leibie = 'G'  # 标准论文格式

        # 分割文本
        text_chunks = []
        current_chunk = []
        
        def save_chunk():
            if current_chunk:
                chunk_text = ' '.join(chunk.strip() for chunk in current_chunk if chunk.strip())
                if chunk_text:
                    text_chunks.append(chunk_text)
                current_chunk.clear()

        # 处理不同类型的文档
        if mulu_leibie == 'F':  # 无结构文档
            return [content.strip()]
        else:
            for line in word_context:
                if not line.strip() or not is_chinese(line):
                    continue
                    
                if self.getno(line, mulu_leibie):
                    # 保存之前的文本块
                    save_chunk()
                    # 开始新的文本块
                    current_chunk.append(line.strip())
                else:
                    if current_chunk:
                        current_chunk.append(line.strip())
                    else:
                        # 第一段文本,作为标题
                        current_chunk.append(line.strip())

            # 保存最后一个文本块
            save_chunk()

        return [chunk for chunk in text_chunks if chunk]

