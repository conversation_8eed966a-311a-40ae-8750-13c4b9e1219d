import re
from abc import ABC
from langchain.text_splitter import CharacterTextSplitter,RecursiveCharacterTextSplitter
from modelscope.pipelines import pipeline
from conf.server_config import *
from typing import List, Optional, Any
from langchain.text_splitter import TokenTextSplitter
from src.rag.embedding import EmbeddingService,LoadsEmbeddingModel
from conf.server_config import EMBEDDING_MODEL_NAME
##
import copy
import re
from typing import Any, Dict, Iterable, List, Literal, Optional, Sequence, Tuple, cast

import numpy as np
from langchain_community.utils.math import (
    cosine_similarity,
)
from langchain_core.documents import BaseDocumentTransformer, Document
from langchain_core.embeddings import Embeddings
##
import warnings
import logging
logging.getLogger("langchain_community.utils.math").setLevel(logging.WARNING)
warnings.filterwarnings('ignore')

from src.utils.log_utils import logger
logger = logger.bind(name="data_process")


class FixedLengthSplitter:
    """
    基于固定长度加重叠分割
    """

    def __init__(self, fixed_length: int, overlap: int):
        """
        初始化FixedLengthSplitter。

        参数:
            fixed_length (int): 每个切片的固定长度。
            overlap (int): 切片之间的重叠部分长度。
        """
        self.fixed_length = fixed_length
        self.overlap = overlap

    def split_text(self, text: str) -> List[str]:
        """
        对文本进行切分，每个切片固定长度，并且具有重叠部分。

        参数:
            text (str): 要被切分的文本。

        返回:
            List[str]: 一个包含非空子字符串的列表，根据固定长度和重叠部分切分所得。
        """
        if self.fixed_length <= 0:
            raise ValueError("fixed_length must be a positive integer.")
        if self.overlap < 0:
            raise ValueError("overlap must be a non-negative integer.")
        if self.fixed_length <= self.overlap:
            raise ValueError("fixed_length must be greater than overlap.")

        slices = []
        start = 0
        text_length = len(text)

        while start < text_length:
            end = start + self.fixed_length
            slices.append(text[start:end])
            start += self.fixed_length - self.overlap

        return [s for s in slices if s]

class RegexSplitter:
    """
    使用正则表达式将文本进行切分的类。
    """

    def __init__(self, pattern_str: str):
        """
        使用指定的正则表达式模式字符串初始化RegexSplitter。

        参数:
            pattern_str (str): 用于切分文本的正则表达式模式字符串。
        """
        self.pattern_str = pattern_str
        self._validate_regex()

    def _validate_regex(self):
        """
        验证用户提供的正则表达式模式字符串的正确性。
        如果模式无效，则引发ValueError。
        """
        try:
            re.compile(self.pattern_str)
        except re.error as e:
            raise ValueError(f"Invalid regular expression pattern: {self.pattern_str}. Error: {str(e)}")

    def split_text(self, text: str) -> List[str]:
        """
        使用指定的正则表达式模式字符串对文本进行切分。

        参数:
            text (str): 要被切分的文本。

        返回:
            List[str]: 一个包含非空子字符串的列表，通过正则表达式切分所得。
        """
        parts = re.split(self.pattern_str, text)
        return [part for part in parts if part]

class SemanticSpliter:
    """
    基于语义分割
    """
    def __init__(self):
        self.lens_down = CHUNK_LENS_DOWN
        self.lens_up = CHUNK_LENS_UP
        self.buffer_size = 1
        self.sentence_split_regex = r"(?<=[?!。？！])\s+"
        # embedding_dict = LoadsEmbeddingModel().loads_model()
        if not IS_API:
            logger.info(f"调用向量化模型 {IS_API}")
            embedding_dict = LoadsEmbeddingModel().loads_model()
            self.embeddings = embedding_dict[EMBEDDING_MODEL_NAME]


    def combine_sentences(self, sentences: List[dict], buffer_size: int = 1) -> List[dict]:
        """Combine sentences based on buffer size.

        Args:
            sentences: List of sentences to combine.
            buffer_size: Number of sentences to combine. Defaults to 1.

        Returns:
            List of sentences with combined sentences.
        """

        # Go through each sentence dict
        for i in range(len(sentences)):
            # Create a string that will hold the sentences which are joined
            combined_sentence = ""

            # Add sentences before the current one, based on the buffer size.
            for j in range(i - buffer_size, i):
                # Check if the index j is not negative
                # (to avoid index out of range like on the first one)
                if j >= 0:
                    # Add the sentence at index j to the combined_sentence string
                    combined_sentence += sentences[j]["sentence"] + " "

            # Add the current sentence
            combined_sentence += sentences[i]["sentence"]

            # Add sentences after the current one, based on the buffer size
            for j in range(i + 1, i + 1 + buffer_size):
                # Check if the index j is within the range of the sentences list
                if j < len(sentences):
                    # Add the sentence at index j to the combined_sentence string
                    combined_sentence += " " + sentences[j]["sentence"]

            # Then add the whole thing to your dict
            # Store the combined sentence in the current sentence dict
            sentences[i]["combined_sentence"] = combined_sentence

        return sentences

    def calculate_cosine_distances(self, sentences: List[dict]) -> Tuple[List[float], List[dict]]:
        """Calculate cosine distances between sentences.

        Args:
            sentences: List of sentences to calculate distances for.

        Returns:
            Tuple of distances and sentences.
        """
        distances = []
        for i in range(len(sentences) - 1):
            embedding_current = sentences[i]["combined_sentence_embedding"]
            embedding_next = sentences[i + 1]["combined_sentence_embedding"]

            # Calculate cosine similarity
            similarity = cosine_similarity([embedding_current], [embedding_next])[0][0]

            # Convert to cosine distance
            distance = 1 - similarity

            # Append cosine distance to the list
            distances.append(distance)

            # Store distance in the dictionary
            sentences[i]["distance_to_next"] = distance

        # Optionally handle the last sentence
        # sentences[-1]['distance_to_next'] = None  # or a default value

        return distances, sentences


    def _calculate_sentence_distances(
            self, single_sentences_list: List[str]
    ) -> Tuple[List[float], List[dict]]:
        """Split text into multiple components."""

        _sentences = [
            {"sentence": x, "index": i} for i, x in enumerate(single_sentences_list)
        ]
        sentences = self.combine_sentences(_sentences, self.buffer_size)
        if IS_API:
            embeddings = LoadsEmbeddingModel().embedding_api_batch([x["combined_sentence"] for x in sentences])
        else:
            embeddings = self.embeddings.embedding_split_text(
            [x["combined_sentence"] for x in sentences]
        )
        for i, sentence in enumerate(sentences):
            sentence["combined_sentence_embedding"] = embeddings[i]

        return self.calculate_cosine_distances(sentences)

   #按照固定长度分割
    def _split_lens(self, text_list, text, length):
        tmp = text[:int(length)]
        # print(tmp)
        # 将固定长度的字符串添加到列表中
        text_list.append(tmp)
        # 将原串替换
        text = text.replace(tmp, '')
        if len(text) < length + 1:
            # 直接添加或者舍弃
            text_list.append(text)
        else:
            self._split_lens(text_list, text, length)
        return text_list
    def _cut_sentences(self, sentences: List[str]) -> List[str]:
        """ 对无法使用'。？！'分句的过长文档，对于超过lens_up的文本， 按照换行符（\n）切分为lens_down长度， 如果还是超长，直接切分 """
        cut_sentences = []
        for sentence in sentences:
            if len(sentence) > self.lens_down:
                tmp_sentence_list = sentence.split("\n")
                ## 如果按照\n仍然超过长度
                for line in tmp_sentence_list:
                    if len(line) > self.lens_down*2:
                        tmp_sentence_list = []
                        tmp_sentence_list = self._split_lens(tmp_sentence_list, sentence, self.lens_down)
                        break
                ## 超长处理完毕
                sentence_list = []
                split_sum = ""
                for split in tmp_sentence_list:
                    split_sum += split
                    if len(split_sum) > self.lens_down:
                        sentence_list.append(split_sum)
                        split_sum = ""
                sentence_list.append(split_sum)
                cut_sentences += sentence_list
            else:
                cut_sentences.append(sentence)

        return cut_sentences

    def _split_sentences_distances(
            self,
            text: str,
            ifDistances=False
    ):
        # Splitting the essay (by default on '.', '?', and '!')
        single_sentences_list = re.split(self.sentence_split_regex, text)
        single_sentences_list = self._cut_sentences(single_sentences_list) ## 对过长的分句进行处理

        # logger.info(f"single_sentences_list: {single_sentences_list}")
        #print("single_sentences_list: " + str(len(single_sentences_list)))
        # having len(single_sentences_list) == 1 would cause the following
        # np.percentile to fail.
        if ifDistances:
            if len(single_sentences_list) == 1:
                return [dict(sentence = single_sentences_list[0])],[0]
            distances, sentences = self._calculate_sentence_distances(single_sentences_list)
        #print("distances:" + str(distances))
        #print("sentences:" + str(sentences))
            return sentences, distances
        else:
            return single_sentences_list, [0]

    def split_text(self, text: str, ifDistances=False) ->  List[str]:
        """
         Description:
            使用提供的分隔符列表对文本进行切分。
         Args:
            text (str): 要被切分的文本。
        Returns:
            List[str]: 切分后的文本部分列表，其中不包含任何空字符串。
        """
        slices, distances = self._split_sentences_distances(text)
        slices_list = []
        merge_slice = ""
        #logger.info(f"slices_list: {slices}")   ##分句
        #logger.info(f"distances: {distances}")  ##相似度分数
        #logger.info(f"breakpoint_distance_threshold: {breakpoint_distance_threshold}")
        for i in range(len(slices)):
            if ifDistances:
                ## 当前切片和下个切片的距离
                slice = slices[i]['sentence']
                breakpoint_distance_threshold = np.mean(distances)
                distance = distances[i - 1] if i > 0 else 0.0
                if len(merge_slice) < self.lens_down:
                    merge_slice += slice
                else:
                    if distance <= breakpoint_distance_threshold:
                        merge_slice += slice
                    else:
                        merge_slice += slice
                        merge_slice = ""
                        break
                if len(merge_slice) > self.lens_up:
                    merge_slice += slice
                    merge_slice = ""
            else:
                slice = slices[i]
                # logger.info(slices)
                ## 长度小于lens_down 增加长度
                if len(merge_slice) < self.lens_down:
                    merge_slice += slice
                else:
                    slices_list.append(merge_slice)
                    merge_slice = slice

        if merge_slice != "":
            slices_list.append(merge_slice)

        return slices_list


class SeparatorSpliter:
    """
    基于固定字符分割
    """

    def split_text(self, text: str, code: List[str]) ->  List[str]:
        """
         Description:
            使用提供的分隔符列表对文本进行切分。
         Args:
            text (str): 要被切分的文本。
            code (List[str]): 分隔符列表，每个元素是一个字符，用于确定文本的切分点。
        Returns:
            List[str]: 切分后的文本部分列表，其中不包含任何空字符串。
        """
        pattern = '[' + ''.join(code) + ']'
        parts = re.split(pattern, text)
        # 输出判空过滤
        return [part for part in parts if part]

class AliTextSplitter(CharacterTextSplitter, ABC):
    """
    阿里云语义分割模型的实现类。
    该类继承自CharacterTextSplitter，并实现了使用阿里云NLP模型进行文本分割的功能。
    """
    def __init__(self):
        """
        初始化AliTextSplitter实例。

        调用超类的初始化方法。
        """
        super().__init__()

    def split_text(self, text: str) -> List[str]:
        """
        Description:
            使用阿里云NLP模型对文本进行语义分割。
        Args:
            text: str - 需要进行语义分割的文本。
        Returns:
            List[str] - 分割后的文本句子列表。

        该方法首先对输入的文本进行预处理，去除多余的换行符和空格，然后使用阿里云的文档分割模型进行分割。
        模型运行在CPU上，以减少对显卡资源的需求。分割结果经过进一步处理，去除空的分割项，最后返回分割后的句子列表。
        """
        # 格式化文本，减少多余的换行符和空格，为后续的分割做准备
        # use_document_segmentation参数指定是否用语义切分文档，此处采取的文档语义分割模型
        # 为达摩院开源的nlp_bert_document-segmentation_chinese-base，
        # 论文见https://arxiv.org/abs/2107.09278
        # 如果使用模型进行文档语义切分，那么需要安装modelscope[nlp]：pip install
        # "modelscope[nlp]" -f https://modelscope.oss-cn-beijing.aliyuncs.com/
        # releases/repo.html
        # 考虑到使用了三个模型，可能对于低配置gpu不太友好，因此这里将模型load进cpu计算，
        # 有需要的话可以替换device为自己的显卡id
        text = re.sub(r"\n{3,}", r"\n", text)
        text = re.sub(r'\s', " ", text)
        text = re.sub("\n\n", "", text)

        # 初始化文档分割pipeline，使用指定的模型和设备（CPU）
        p = pipeline(
            task="document-segmentation",
            model=SEG_MODEl,
            device="cuda:0")

        # 对处理后的文本进行分割处理
        try:
            result = p(documents=text)
        # 从分割结果中提取句子列表，去除空项
            sent_list = [i for i in result["text"].split("\n\t") if i]
        except:
            # sent_list = SeparatorSpliter().split_text(text, "\n\n")
            sent_list = FixedLengthSplitter(500, CHUNK_OVERLAP).split_text(text)
        return sent_list


class ChineseRecursiveTextSplitter(RecursiveCharacterTextSplitter, ABC):
    """
    递归文本分割算法实现
    """

    def __init__(
            self,
            separators: Optional[List[str]] = None,
            keep_separator: bool = True,
            is_separator_regex: bool = True,
            **kwargs: Any,
    ) -> None:
        """Create a new TextSplitter."""
        super().__init__(keep_separator=keep_separator, **kwargs)
        self._separators = separators or [
            "\n\n",
            "\n",
            "。|！|？",
            r"\.\s|\!\s|\?\s",
            r"；|;\s",
            r"，|,\s"
        ]
        self._is_separator_regex = is_separator_regex

    def _split_text_with_regex_from_end(
            self, text: str, separator: str, keep_separator: bool
    ) -> List[str]:
        # Now that we have the separator, split the text
        if separator:
            if keep_separator:
                # The parentheses in the pattern keep the delimiters in the result.
                _splits = re.split(f"({separator})", text)
                splits = ["".join(i) for i in zip(_splits[0::2], _splits[1::2])]
                if len(_splits) % 2 == 1:
                    splits += _splits[-1:]
                # splits = [_splits[0]] + splits
            else:
                splits = re.split(separator, text)
        else:
            splits = list(text)
        return [s for s in splits if s != ""]

    def _split_text(self, text: str, separators: List[str]) -> List[str]:
        """Split incoming text and return chunks."""
        final_chunks = []
        # Get appropriate separator to use
        separator = separators[-1]
        new_separators = []
        for i, _s in enumerate(separators):
            _separator = _s if self._is_separator_regex else re.escape(_s)
            if _s == "":
                separator = _s
                break
            if re.search(_separator, text):
                separator = _s
                new_separators = separators[i + 1:]
                break

        _separator = separator if self._is_separator_regex else re.escape(
            separator)
        splits = self._split_text_with_regex_from_end(text, _separator,
                                                      self._keep_separator)

        # Now go merging things, recursively splitting longer texts.
        _good_splits = []
        _separator = "" if self._keep_separator else separator
        for s in splits:
            if self._length_function(s) < self._chunk_size:
                _good_splits.append(s)
            else:
                if _good_splits:
                    merged_text = self._merge_splits(_good_splits, _separator)
                    final_chunks.extend(merged_text)
                    _good_splits = []
                if not new_separators:
                    final_chunks.append(s)
                else:
                    other_info = self._split_text(s, new_separators)
                    final_chunks.extend(other_info)
        if _good_splits:
            merged_text = self._merge_splits(_good_splits, _separator)
            final_chunks.extend(merged_text)
        return [re.sub(r"\n{2,}", "\n", chunk.strip()) for chunk in final_chunks
                if chunk.strip() != ""]

    
class MinuerSplitter:
    """
    内容分割器类
    用于将文档内容分割成较小的块，支持文本、表格和图片内容
    """
    
    def __init__(self, target_length=600, length_deviation=200):
        """
        初始化内容分割器
        
        参数:
            target_length: 每个块的目标长度
            length_deviation: 允许的长度偏差范围
        """
        self.target_length = target_length
        self.length_deviation = length_deviation
        self.min_length = max(target_length - length_deviation, 100)  # 设置最小长度下限
        self.max_length = target_length + length_deviation

    def split_content_by_length(self, content_list):
        """
        使用动态规划方式根据长度限制将内容分割成更均匀的块
        
        参数:
            content_list: 内容项列表 (可以是JSON字符串或列表对象)
        
        返回:
            包含内容项的块列表
        """
        import json
        
        # 如果输入是字符串，尝试解析为JSON
        if isinstance(content_list, str):
            try:
                content_list = json.loads(content_list)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON string provided for content_list")

        # 计算每个内容项的长度
        content_lengths = []
        for item in content_list:
            content = self._get_content_string(item)
            content_lengths.append((len(content), item))

        result_chunks = []
        current_chunk = []
        current_length = 0
        
        # 使用滑动窗口和前瞻策略进行分块
        i = 0
        while i < len(content_lengths):
            length, item = content_lengths[i]
            
            # 如果单个内容项超过最大长度，单独作为一个块
            if length > self.max_length:
                if current_chunk:
                    result_chunks.append(current_chunk)
                result_chunks.append([item])
                i += 1
                current_chunk = []
                current_length = 0
                continue
            
            # 计算添加当前项后的总长度
            potential_length = current_length + length
            
            # 如果添加后长度在目标范围内
            if potential_length <= self.max_length:
                current_chunk.append(item)
                current_length = potential_length
                i += 1
            # 如果当前块太小，尝试查看下一项
            elif current_length < self.min_length and i + 1 < len(content_lengths):
                next_length = content_lengths[i + 1][1]
                if current_length + next_length <= self.max_length:
                    # 当前项会导致分块过大，但下一项更合适
                    i += 1
                    continue
                else:
                    # 当前块已经是最优的了
                    if current_chunk:
                        result_chunks.append(current_chunk)
                    current_chunk = [item]
                    current_length = length
                    i += 1
            else:
                # 当前块达到合适大小，开始新块
                if current_chunk:
                    result_chunks.append(current_chunk)
                current_chunk = [item]
                current_length = length
                i += 1
        
        # 添加最后一个块
        if current_chunk:
            result_chunks.append(current_chunk)
            
        return result_chunks

    def _get_content_string(self, item):
        """
        获取内容项的字符串表示
        
        参数:
            item: 内容项（可以是文本、表格或图片）
        
        返回:
            内容的字符串表示
        """
        if item["type"] == "text":
            # 删除文本中的换行符
            return item["text"].replace('\n', '')
        elif item["type"] == "table":
            return item.get("table_body", "")
        elif item["type"] == "image":
            # 对于图片，使用图片标签作为内容
            return f'<img src="http://{{ENDPOINT}}/{{MINOBUCKET}}/{item["img_path"]}" alt="Image" />'

    def format_chunk_content(self, chunk):
        """
        将块的内容合并为单个字符串
        
        参数:
            chunk: 内容项列表
        
        返回:
            合并后的字符串
        """
        formatted_content = ""
        
        for item in chunk:
            formatted_content += self._get_content_string(item)
            
        return formatted_content

    def split_text(self, content_list, start_page=None, end_page=None):
        """
        处理文档内容并返回格式化的块
        
        参数:
            content_list: 内容项列表
            start_page: 起始页码（包含）
            end_page: 结束页码（包含）
        
        返回:
            格式化内容字符串的列表
        """
        # 根据页码范围筛选内容
        if start_page is not None or end_page is not None:
            filtered_content = []
            for item in content_list:
                page_idx = item.get('page_idx', 0)
                if start_page is not None and page_idx < start_page:
                    continue
                if end_page is not None and page_idx > end_page:
                    continue
                filtered_content.append(item)
        else:
            filtered_content = content_list

        chunks = self.split_content_by_length(filtered_content)
        print(chunks)
        return [self.format_chunk_content(chunk) for chunk in chunks]
class ChunkMarkerSplitter:
    """
    处理带有特定chunk标记的文档的分割和恢复
    格式: "--- CHUNK {i} ---\n\n{content}\n\n"
    """
    
    def __init__(self):
        self.chunk_pattern = r"---\s*CHUNK\s*\d+\s*---\s*\n\n"
    
    def split_text(self, text: str) -> List[str]:
        """
        将带有chunk标记的文本分割成单独的chunks
        
        Args:
            text: 包含chunk标记的完整文本
            
        Returns:
            List[str]: 分割后的文本块列表
        """
        # 使用正则表达式分割文本
        chunks = re.split(self.chunk_pattern, text)
        # 过滤掉空字符串并去除每个chunk首尾的空白
        return [chunk.strip() for chunk in chunks if chunk.strip()]

'''
# 示例：
# 初始化RegexSplitter，使用分隔符逗号和空格
splitter = RegexSplitter(r"[,\s]+")

# 使用split_text方法对文本进行切分
result = splitter.split_text("Hello, world! This is a test.")

# 输出结果: ['Hello', 'world!', 'This', 'is', 'a', 'test.']
print(result)
'''
