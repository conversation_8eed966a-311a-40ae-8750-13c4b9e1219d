from typing import List
from abc import ABC
import re
import torch
# 使用华为的昇腾设备请将下面的注释的打开，
# import torch_npu
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from langchain.text_splitter import CharacterTextSplitter
from src.rag.text_splitter import FixedLengthSplitter
from conf.server_config import *
import warnings
warnings.filterwarnings("ignore")
"""

# 首先判断相邻两句是否需要分段
# 如果需要分段，保存第一句，从第二句重新开始
# 如果不需要分段：
#    将两句合并为当前段落
#    尝试继续添加后续句子
#    每次添加新句子前检查：
#       是否会超过最大长度
#       是否与当前段落有语义关联性
#    直到无法继续合并为止
# 保存当前段落，更新索引到下一个未处理的句子
# 这样的处理方式会：
# 更灵活地处理文本分段
# 允许多个句子合并成一个段落
# 同时保证段落不会超过最大长度
# 保持段落内的语义连贯性


"""
class AliTextSplitter(CharacterTextSplitter, ABC):
    """
    阿里云语义分割模型的实现类。
    该类继承自CharacterTextSplitter，并实现了使用阿里云NLP模型进行文本分割的功能。
    """
    def __init__(self):
        """
        初始化AliTextSplitter实例。
        """
        super().__init__()
        # 初始化模型和分词器
        self.model_path = SEG_MODEl
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
        if torch.cuda.is_available():
            self.device = torch.device("cuda:1")
            print("GPU is available. Using GPU.")
        # 使用华为的昇腾设备请将下面的注释的打开，将上面的GPU的注释掉
        # if torch.npu.is_available():
        #     self.device = torch.device("npu:1")
        #     print("NPU is available. Using NPU.")
        else:
            self.device = torch.device("cpu")
            print("GPU is not available. Using CPU.")
        self.model = self.model.to(self.device)
        self.model.eval()
    def segment_document(self, text: str, threshold: float = 0.5, max_length: int = 500) -> str:
        """
        使用模型进行文档分段
        """
        # 将文本分成句子
        sentences = text.split("。")
        sentences = [s + "。" for s in sentences if s]  # 给每个非空句子加上句号
        
        results = []
        i = 0
        
        while i < len(sentences) - 1:
            # 先判断相邻两句
            current_text = sentences[i] + sentences[i+1]
            inputs = self.tokenizer(current_text,
                                  return_tensors="pt",
                                  padding=True,
                                  truncation=True,
                                  max_length=512)
            
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                predictions = torch.softmax(outputs.logits, dim=-1)
                score = predictions[0][1].item()
            
            if score > threshold:
                # 需要分段，保存第一句，从第二句重新开始
                results.append(sentences[i])
                i += 1
            else:
                # 不需要分段，尝试继续合并后续句子
                current_segment = current_text
                next_idx = i + 2
                
                while next_idx < len(sentences):
                    # 检查添加下一句是否会超过最大长度
                    if len(current_segment + sentences[next_idx]) > max_length:
                        break
                    
                    # 检查与下一句的语义关联性
                    check_text = current_segment + sentences[next_idx]
                    inputs = self.tokenizer(check_text,
                                         return_tensors="pt",
                                         padding=True,
                                         truncation=True,
                                         max_length=512)
                    
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}
                    
                    with torch.no_grad():
                        outputs = self.model(**inputs)
                        predictions = torch.softmax(outputs.logits, dim=-1)
                        score = predictions[0][1].item()
                    
                    if score > threshold:
                        break
                    
                    # 可以继续合并
                    current_segment += sentences[next_idx]
                    next_idx += 1
                
                # 保存当前段落，更新索引
                results.append(current_segment)
                i = next_idx
        
        # 处理最后剩余的句子
        if i < len(sentences):
            # 检查是否可以与前一个片段合并
            if (results and 
                len(results[-1] + sentences[i]) <= max_length and 
                len(results) > 1):
                results[-1] = results[-1] + sentences[i]
            else:
                results.append(sentences[i])
        
        return "\n\t".join(results)

    def split_text(self, text: str) -> List[str]:
        """
        使用阿里云NLP模型对文本进行语义分割。
        
        Args:
            text: str - 需要进行语义分割的文本。
        Returns:
            List[str] - 分割后的文本句子列表。
        """
        # 格式化文本
        text = re.sub(r"\n{3,}", r"\n", text)
        text = re.sub(r'\s', " ", text)
        text = re.sub("\n\n", "", text)

        try:
            # 使用模型进行文档分割
            segmented_text = self.segment_document(text)
            # 从分割结果中提取句子列表，去除空项
            sent_list = [i for i in segmented_text.split("\n\t") if i]
        except Exception as e:
            print(f"分割发生错误: {e}")
            # 使用固定长度分割作为后备方案
            splitter = FixedLengthSplitter(fixed_length=500, overlap=50)
            sent_list = splitter.split_text(text)

        return sent_list
