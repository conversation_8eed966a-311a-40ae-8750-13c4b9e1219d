# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  text_detector.py
   Description : 用于检测文本完整性及语言
   Author :  mabenjiang
   date： 2024/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
import logging
import chardet
from langdetect import detect
class TextQualityDetector:
    """
    用于检测文本完整性及语言的类。

    初始化时传入待检测的文本。
    """

    def __init__(self, text: str) -> None:
        """
        Description:: 待检测的文本字符串
        Args:
            text (str): 待检测的文本字符串
        """
        self.text = text

    def check_integrity(self) -> bool:
        """
        Author：mabenjiang
        Description: 使用chardet库分析文本的编码和置信度，以此判断文本是否可读。
        Returns:
            bool: 如果文本有明确的编码且置信度高，则返回True（文本完整），否则返回False（文本可能损坏）。
        """
        result = chardet.detect(self.text.encode('utf-8'))
        encoding = result['encoding']
        confidence = result['confidence']

        if encoding is not None and confidence > 0.5:
            logging.info(f"The text seems to be in {encoding} with {confidence * 100}% confidence.")
            return True
        else:
            logging.info("The text appears to be damaged or unreadable.")
            return False
    def detect_language(self) -> int:
        """
        Description: 检测文本的语言。
        Author：mabenjiang
        Returns:
            int: 返回语言代码，其中1表示英文，0表示中文或其他语言。
        """
        try:
            language = detect(self.text)
            if language == 'zh-cn' or language == 'zh-tw' or language.startswith('zh'):
                return 0  # 中文
            elif language == 'en':
                return 1  # 英文
            else:
                return 0  # 其他语言
        except:
            return 0  # 处理检测失败的情况