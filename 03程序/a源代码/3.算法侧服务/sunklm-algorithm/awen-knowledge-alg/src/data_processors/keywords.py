import jieba

class TF_IDF():
    def __init__(self):
        #self.stopword = False
        self.dict_path = '../../resources/dict.txt'
        self.stop_words_path = '../../resources/stopwords.txt'

    def word_frequency(self, word_list:list):
        result = {}
        for word in word_list:
            if word not in result:
                result[word] = 1
            else:
                result[word] += 1
        sorted_result = (sorted(result.items(), key=lambda item: item[1], reverse=True))
        return sorted_result

    def stop_words(self):
        stop_words = set()
        with open(self.stop_words_path, 'r', encoding='utf-8') as f:
            for line in f:
                stop_words.add(line.strip())
        return stop_words

    '''
    text:待输入文本
    stopword：是否使用停用词，是：True， 否：False
    '''
    def tf_idf_words(self, text:str, stopword:bool):
        jieba.load_userdict(self.dict_path)
        seg_list = jieba.cut(text, cut_all=False)
        if stopword == True:
            stop_words = self.stop_words()
            result = [word for word in seg_list if word not in stop_words]
        else:
            #seg_list = jieba.cut(text, cut_all=False)
            result = list(seg_list)

        print(result)
        final_result = self.word_frequency(result)

        return final_result

'''
#test
text = "你好别人欠我好几万，请律师需要多少费用?"
tf = TF_IDF()
r = tf.tf_idf_words(text,True)
print(r)
'''