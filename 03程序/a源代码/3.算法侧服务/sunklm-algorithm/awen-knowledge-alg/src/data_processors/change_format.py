# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  change_format.py
   Description :文件类型转换
   Author :  mabenjiang
   date： 2024/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
import subprocess
from pdfminer.pdfparser import PDFParser
from pdfminer.pdfdocument import PDFDocument
from pdfminer.pdfpage import PDFPage
from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
from pdfminer.converter import PDFPageAggregator
from pdfminer.layout import LTTextBoxHorizontal
from pdfminer.layout import LAParams
from pdfminer.pdfpage import PDFTextExtractionNotAllowed
import docx
import openpyxl
import xlrd
import pandas as pd
import logging
import pptx
import warnings
import os
import json
import mistune
from bs4 import BeautifulSoup
import logging
from pdfminer.high_level import extract_text

# 设置日志级别为 WARNING，这样 DEBUG 和 INFO 级别的日志将不会被输出
logging.getLogger("pdfminer").setLevel(logging.WARNING)
warnings.filterwarnings('ignore')
from src.utils.log_utils import logger
logger = logger.bind(name="data_process")
class ChangeFileTxt:

    def __init__(self, file_path: str, file_name: str):
        """
        Description:初始化类实例，存储文件路径和文件名。
        Args:
            file_path(str):文件的完整路径
            file_name(str): 文件名，包含扩展名
        """
        self.file_path = file_path
        self.file_name = file_name

    def c_pdf(self) -> tuple:
        """
        Description:
        将PDF文件转换为TXT文件。
        此函数使用pdfminer提取PDF文件中的文本内容，并将其保存为TXT文件。
        
        Return:
            tuple: 包含新TXT文件的路径和文件名
        """
        try:
            # 准备新的TXT文件路径和文件名
            txt_file_path = os.path.splitext(self.file_path)[0] + '.txt'
            txt_file_name = os.path.splitext(self.file_name)[0] + '.txt'

            # 使用pdfminer的高级接口提取文本
            text = extract_text(self.file_path)

            # 写入TXT文件
            with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
                txt_file.write(text)

            # 输出成功消息
            logger.info('PDF文件转换成功')
            
            # 返回TXT文件的路径和文件名
            return txt_file_path, txt_file_name

        except PDFTextExtractionNotAllowed:
            logger.error("文档不允许提取文本")
            raise
        except Exception as e:
            logger.error(f"PDF转换失败: {str(e)}")
            raise

    def c_docx(self) -> tuple:
        """
        Description:
        将.docx文件转换为.txt文件。
        此函数读取一个.docx文件，提取其中的文本和表格数据，并将其保存为一个.txt文件。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 使用python-docx库加载.docx文档
        file = docx.Document(self.file_path)

        # 定义新的.txt文件的路径和文件名
        new_path = self.file_path[:-4] + 'txt'  # 修改原文件路径的扩展名为.txt
        new_file_name = self.file_name[:-4] + 'txt'  # 修改原文件名的扩展名为.txt

        # 打开新的.txt文件，准备写入
        with open(new_path, 'w', encoding='utf-8') as f:
            # 遍历文档中的所有段落
            for paragraph in file.paragraphs:
                # 如果段落文本非空，则写入.txt文件
                if paragraph.text.strip():  # 使用strip()去除文本前后的空白字符
                    f.write(paragraph.text + '\n')

            # 如果文档中包含表格，则遍历所有表格
            if len(file.tables) > 0:
                for table in file.tables:
                    max_cols = 0
                    for row in table.rows:
                        try:
                    # 获取实际存在的单元格数量
                            actual_cols = sum(1 for cell in row.cells if cell.text.strip())
                            max_cols = max(max_cols, actual_cols)

                    # 打印行信息和实际列数
                        except Exception as e:
                            print(f"Error processing row: {e}")
                            continue
            # 遍历表格中的每一行
                    for row in table.rows:
                        try:
                    # 获取实际存在的单元格
                            row_cells = [cell.text for cell in row.cells if cell.text.strip()]

                    # 填充缺失的单元格
                            row_cells += [''] * (max_cols - len(row_cells))

                    # 将一行中的所有单元格文本连接为一个字符串，用制表符分隔
                            row_str = '\t'.join(row_cells)

                    # 将处理好的行数据写入.txt文件
                            f.write(row_str + '\n')
                        except Exception as e:
                            logger.info(f"Error processing row: {e}")
                            continue
        # 输出转换成功的消息
        logger.info('.docx件转化成功')

        # 返回新生成的TXT文件的完整路径和文件名
        return new_path, new_file_name
    # linux 特有代码
    # def c_doc(self):
    #     # 获取文件名
    #     logger.info(self.file_path)
    #     directory = os.path.dirname(self.file_path)
    #     logger.info(f"directory {directory}")
    #     # print(directory)
    #     # 构建输出文件路径
    #     docx_path = self.file_path[:-3] + 'docx'
    #     # print(docx_path)

    #     # 使用 libreoffice 命令行工具进行转换
    #     command = [
    #         # libreoffice版本，根据实际安装情况调整
    #         'soffice',
    #         '--convert-to',
    #         'docx',
    #         '--headless',
    #         '--outdir',
    #         directory,
    #         self.file_path
    #     ]
    #     try:
    #         result = subprocess.run(command, check=True, capture_output=True, text=True)
    #         logging.info(result.stdout)
    #         logging.info(f"Conversion successful: {docx_path}")
    #     except subprocess.CalledProcessError as e:
    #         logging.info(f"Conversion failed: {e.stderr}")
    #     logging.info(docx_path)
    #     file = docx.Document(docx_path)
    #     # 定义新的.txt文件的路径和文件名
    #     new_path = self.file_path[:-3] + 'txt'  # 修改原文件路径的扩展名为.txt
    #     new_file_name = self.file_name[:-3] + 'txt'  # 修改原文件名的扩展名为.txt

    #     # 打开新的.txt文件，准备写入
    #     with open(new_path, 'w', encoding='utf-8') as f:
    #         # 遍历文档中的所有段落
    #         for paragraph in file.paragraphs:
    #             # 如果段落文本非空，则写入.txt文件
    #             if paragraph.text.strip():  # 使用strip()去除文本前后的空白字符
    #                 f.write(paragraph.text + '\n')
    #         # 如果文档中包含表格，则遍历所有表格
    #         if len(file.tables) > 0:
    #             for table in file.tables:
    #                 # 遍历表格中的每一行
    #                 for row in table.rows:
    #                     # 将一行中的所有单元格文本连接为一个字符串，用制表符分隔
    #                     row_str = '\t'.join([cell.text for cell in row.cells])
    #                     # 将处理好的行数据写入.txt文件
    #                     f.write(row_str + '\n')
    #     # 输出转换成功的消息
    #     logging.info('.doc文件转化成功')
    #     return new_path,new_file_name
    def c_txt(self)-> tuple:
        """
        Description:
        将.txt文件转换为.txt件。
        此函数读取一个.txt文件，提取其中的文本和表格数据，并将其保存为一个.txt文件。
        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """

        logging.info('.txt文件转化成功')

        return self.file_path, self.file_name

    def c_xls(self)-> tuple:
        """
         Author：mabenjiang
         Description:
        将.xls文件转换为包含JSON数据的.txt文件。
        此函数读取.xls文件，解析其中的工作表，处理合并单元格，并将数据以JSON格式保存到.txt文件中。
        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 使用xlrd库打开.xls文件
        wb = xlrd.open_workbook(self.file_path)

        # 初始化数据列表，用于存储所有工作表的数据
        data = []

        # 遍历工作簿中的每一个工作表
        for sheet_index in range(wb.nsheets):
            sheet = wb.sheet_by_index(sheet_index)
            merged_cells = sheet.merged_cells

            # 遍历工作表中的每一行
            for row in range(sheet.nrows):
                # 创建一个字典来存储当前行的数据
                row_data = {}

                # 遍历行中的每个单元格
                for col in range(sheet.ncols):
                    # 判断当前单元格是否位于合并单元格区域内
                    is_merged = False
                    for merged_cell in merged_cells:
                        if merged_cell.min_row <= row <= merged_cell.max_row and merged_cell.min_col <= col <= merged_cell.max_col:
                            is_merged = True
                            break

                    if is_merged:
                        # 如果是合并单元格的一部分，获取合并单元格的第一个单元格的值
                        cell_value = sheet.cell(row=merged_cell.min_row, column=merged_cell.min_col).value
                    else:
                        # 如果不是合并单元格，直接获取当前单元格的值
                        cell_value = sheet.cell(row, col).value

                    # 使用第一行的单元格作为键，将单元格的值添加到字典中
                    row_data[sheet.cell(0, col).value] = cell_value

                # 将字典添加到数据列表中
                data.append(row_data)

        # 将数据列表转换为Pandas DataFrame
        df = pd.DataFrame(data)

        # 将DataFrame转换为JSON格式，orient参数设置为'records'以获得每行数据为一个字典的列表
        json_data = df.to_json(orient='records', force_ascii=False)

        # 定义新文件的路径和文件名
        new_path = self.file_path[:-3] + 'txt'
        new_file_name = self.file_name[:-3] + 'txt'

        # 检查新文件的目录是否存在，如果不存在则创建
        if not os.path.exists(os.path.dirname(new_path)):
            os.makedirs(os.path.dirname(new_path))

        # 写入JSON数据到新文件中
        with open(new_path, 'w', encoding='utf-8') as f:
            f.write(json_data)

        # 打印成功转换的信息
        logging.info('.xls文件转换成功')

        # 返回新文件的路径和文件名
        return new_path, new_file_name

    def c_xlsx(self)-> tuple:
        """
        Author：mabenjiang
        Description:
        将.xlsx文件转换为包含JSON数据的.txt文件。

        此函数读取.xlsx文件，解析其中的工作表，处理合并单元格，并将数据以JSON格式保存到.txt文件中。
        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 初始化数据列表，用于存储所有工作表的数据
        data = []

        # 使用openpyxl库加载.xlsx文件
        wb = openpyxl.load_workbook(self.file_path)

        # 遍历工作簿中的每个工作表
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]

            # 获取工作表中的合并单元格区域
            merged_cells = sheet.merged_cells.ranges

            # 遍历工作表中的每一行，假设第一行是标题行，从第二行开始读取数据
            for row in sheet.iter_rows(min_row=2, values_only=False):
                # 创建一个字典来存储当前行的数据
                row_data = {}

                # 遍历行中的每个单元格
                for i, cell in enumerate(row):
                    # 检查当前单元格是否位于合并单元格区域内
                    is_merged = False
                    for merged_cell in merged_cells:
                        if cell.coordinate in merged_cell:
                            is_merged = True
                            break

                    if is_merged:
                        # 如果是合并单元格的一部分，获取合并单元格的第一个单元格的值
                        cell_value = sheet.cell(row=merged_cell.min_row, column=merged_cell.min_col).value
                    else:
                        # 如果不是合并单元格，直接获取当前单元格的值
                        cell_value = cell.value

                    # 使用第一行的单元格作为键，将单元格的值添加���字典中
                    # 注意：列索引需要加1，因为在openpyxl中列索引是从1开始的
                    row_data[sheet.cell(row=1, column=i + 1).value] = cell_value

                # 将字典添加到数据列表中
                data.append(row_data)

        # 将数据列表转换为Pandas DataFrame
        df = pd.DataFrame(data)

        # 将DataFrame转换为JSON格式，orient参数设置为'records'以获得每行数据为一个字典的列表
        json_data = df.to_json(orient='records', force_ascii=False)

        # 定义新文件的路径和文件名
        new_path = self.file_path[:-4] + 'txt'
        new_file_name = self.file_name[:-4] + 'txt'

        # 检查新文件的目录是否存在，如果不存在则创建
        if not os.path.exists(os.path.dirname(new_path)):
            os.makedirs(os.path.dirname(new_path))

        # 将JSON数据写入新文件中
        with open(new_path, 'w', encoding='utf-8') as f:
            f.write(json_data)

        # 打印成功转换的信息
        logging.info('.xlsx文件转换成功')

        # 返回新文件的路径和文件名
        return new_path, new_file_name
    def c_csv(self) -> tuple:
        """
        将.csv文件转换为.txt文件。

        此函数读取.csv文件，将数据以文本格式��存到.txt文件中。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 使用pandas读取CSV文件
        df = pd.read_csv(self.file_path)

        # 将DataFrame转换为字符串形式，不包含索引和列名
        text_data = df.to_string(index=False, header=False)

        # 定义新文件的路径和文件名
        new_path = self.file_path[:-4] + '.txt'
        new_file_name = self.file_name[:-4] + '.txt'

        # 检查新文件的目录是否存在，如果不存在则创建
        if not os.path.exists(os.path.dirname(new_path)):
            os.makedirs(os.path.dirname(new_path))

        # 将文本数据写入新文件中
        with open(new_path, 'w', encoding='utf-8') as f:
            f.write(text_data)

        # 打印成功转换的信息
        logging.info('.csv文件转换成功')

        # 返回新文件的路径和文件名
        return new_path, new_file_name
    def c_json(self) -> tuple:
        """
        将.json文件转换为包含JSON数据的.txt文件。

        此函数读取.json文件，将数据以JSON格式保存到.txt文件中。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 使用json库加载.json文档
        with open(self.file_path, 'r', encoding='utf-8') as json_file:
            data = json.load(json_file)

        # 定义新的.txt文件的路径和文件名
        new_path = self.file_path[:-5] + '.txt'  # 修改原文件路径的扩展名为.txt
        new_file_name = self.file_name[:-5] + '.txt'  # 修改原文件名的扩展名为.txt

        # 打开新的.txt文件，准备写入
        with open(new_path, 'w', encoding='utf-8') as txt_file:
            # 将JSON数据转换为字符串并写入.txt文件
            json.dump(data, txt_file, indent=4, ensure_ascii=False)

        # 输出转换成功的消息
        logging.info('.json文件转换成功')

        # 返回新生成的TXT文件的完整路径和文件名
        return new_path, new_file_name
    def c_html(self) -> tuple:
        """
        将.html文件转换为.txt文件。

        此函数读取.html文件，提取其中的文本数据，并将其保存到一个.txt文件中。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 使用BeautifulSoup库解析HTML文档
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as html_file:
            soup = BeautifulSoup(html_file, 'html.parser')

        # 提取文本内容
        text = soup.get_text()

        # 定义新的.txt文件的路径和文件名
        new_path = self.file_path[:-5] + '.txt'  # 修改原文件路径的扩展名为.txt
        new_file_name = self.file_name[:-5] + '.txt'  # 修改原文件名的扩展名为.txt

        # 打开新的.txt文件，准备写入
        with open(new_path, 'w', encoding='utf-8') as txt_file:
            # 写入提取的文本数据
            txt_file.write(text)

        # 输出转换成功的消息
        logging.info('.html文件转换成功')

        # 返回新生成的TXT文件的完整路径和文件名
        return new_path, new_file_name

    def c_pptx(self) -> tuple:
        """
        将.pptx文件转换为.txt文件。

        此函数读取.pptx文件，提取其中的文本���据（包括正文、表格、备注等），并将其保存到一个.txt文件中。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        # 初始化文本列表
        text_list = []

        # 使用pptx库加载.pptx文件
        prs = pptx.Presentation(self.file_path)

        # 遍历演示文稿中的所有幻灯片
        for slide_number, slide in enumerate(prs.slides, 1):
            # 添加幻灯片编号
            text_list.append(f"\n=== 幻灯片 {slide_number} ===\n")

            # 提取幻灯片中的所有形状
            for shape in slide.shapes:
                # 提取文本框中的文本
                if hasattr(shape, "text") and shape.text.strip():
                    text_list.append(shape.text.strip())

                # 提取表格内容
                if shape.has_table:
                    table_data = []
                    for row in shape.table.rows:
                        row_data = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_data.append(cell.text.strip())
                        if row_data:
                            table_data.append('\t'.join(row_data))
                    if table_data:
                        text_list.append('\n'.join(table_data))

            # 提取备注
            if slide.has_notes_slide:
                notes_slide = slide.notes_slide
                if notes_slide.notes_text_frame.text.strip():
                    text_list.append(f"\n备注：{notes_slide.notes_text_frame.text.strip()}\n")

        # 将文本列表合并成单个字符串，使用双换行符分隔
        text = '\n\n'.join(filter(None, text_list))

        # 定义新的.txt文件的路径和文件名
        new_path = self.file_path[:-5] + '.txt'
        new_file_name = self.file_name[:-5] + '.txt'

        # 打开新的.txt文件，准备写入
        with open(new_path, 'w', encoding='utf-8') as txt_file:
            txt_file.write(text)

        # 输出转换成功的消息
        logger.info('.pptx文件转换成功')

        # 返回新生成的TXT文件的完整路径和文件名
        return new_path, new_file_name
    # def c_ppt(self):
    #     # 获取文件名
    #     directory = os.path.dirname(self.file_path)
    #     # 构建输出文件路径
    #     pdf_path = self.file_path[:-3] + 'pdf'
    #     # 使用 libreoffice 命令行工具进行转换
    #     command = [
    #         # libreoffice版本，根据实际安装情况调整
    #         'soffice',
    #         '--convert-to',
    #         'pdf',
    #         '--headless',
    #         '--outdir',
    #         directory,
    #         self.file_path
    #     ]
    #     try:
    #         result = subprocess.run(command, check=True, capture_output=True, text=True)
    #         logging.info(result.stdout)
    #         logging.info(f"Conversion successful: {pdf_path}")
    #     except subprocess.CalledProcessError as e:
    #         logging.info(f"Conversion failed: {e.stderr}")
    #     logging.info(pdf_path)
    #     with open(pdf_path, 'rb') as file:
    #         # 创建PDF文档分析器
    #         parser = PDFParser(file)
    #         # 创建PDF文档对象
    #         doc = PDFDocument(parser)

    #         # 检查文档是否允许文本提取
    #         if not doc.is_extractable:
    #             raise PDFTextExtractionNotAllowed("文档不允许提取文本")

    #         # 创建PDF资源管理器
    #         rsrcmgr = PDFResourceManager()
    #         # 创建PDF设备对象，用于聚合页面布局
    #         laparams = LAParams()
    #         device = PDFPageAggregator(rsrcmgr, laparams=laparams)
    #         # 创建PDF解释器对象
    #         interpreter = PDFPageInterpreter(rsrcmgr, device)

    #         # 准备新的TXT文件路径和文件名
    #         txt_file_path = os.path.splitext(self.file_path)[0] + '.txt'
    #         txt_file_name = os.path.splitext(self.file_name)[0] + '.txt'

    #         # 写入TXT文件
    #         with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
    #             # 遍历PDF的每一页
    #             for page in PDFPage.create_pages(doc):
    #                 # 解析页面
    #                 interpreter.process_page(page)
    #                 # 获取页面布局
    #                 layout = device.get_result()
    #                 # 遍历布局中的每一个对象
    #                 for element in layout:
    #                     # 如果对象是水平文本框
    #                     if isinstance(element, LTTextBoxHorizontal):
    #                         # ���取文本并写入TXT文件
    #                         txt_file.write(element.get_text())
    #     # 输出成功消息
    #     logging.info('PDF文件转换成功')
    #     # 返回TXT文件的路径和文件名
    #     return txt_file_path, txt_file_name

    def c_md(self) -> tuple:
        """
        使用mistune库将.md文件转换为.txt文件。
        ...
        """
        # 创建一个Renderer实例，这里使用默认的HTMLRenderer
        renderer = mistune.HTMLRenderer()

        # 创建一个Markdown解析器实例，传入renderer参数
        markdown_parser = mistune.Markdown(renderer=renderer)

        with open(self.file_path, 'r', encoding='utf-8') as md_file:
            content = md_file.read()
            # 使用markdown_parser来解析内容
            text = markdown_parser(content)

        # 修改原文件路径和文件名的扩展名为.txt
        new_path = self.file_path[:-3] + '.txt'
        new_file_name = self.file_name[:-3] + '.txt'

        # 打开新的.txt文件，准备写入
        with open(new_path, 'w', encoding='utf-8') as txt_file:
            # 写入提取的文本数据
            txt_file.write(text)

        # 输出转换成功的消息
        logging.info('.md文件转换成功')

        # 返回新生成的TXT文件的完整路径和文件名
        return new_path, new_file_name

    def change_file_format(self):
        """
        Description:
        根据文件扩展名判断文件类型，并调用相应的函数将文件转换为.txt格式。
        此方法首先分割文件名以获取扩展名，然后根据扩展名调用相应的转换函数。
        如果文件是.txt、.pdf、.docx、.xlsx或.xls格式，它将调用对应的转换函数。
        否则，该方法将不会执行任何操作。

        Return:
            tuple: 包含新TXT文件的完整路径和文件名的元组。
        """
        filename_list = self.file_name.split('.')
        if filename_list[-1] == 'txt':
            return self.c_txt()
        if filename_list[-1] == 'pdf':
            return self.c_pdf()
        if filename_list[-1] == 'docx':
            return self.c_docx()
        if filename_list[-1] == 'xlsx':
            return self.c_xlsx()
        if filename_list[-1] == 'xls':
            return self.c_xls()
        if filename_list[-1] == 'html':
            return self.c_html()
        if filename_list[-1] == 'pptx':
            return self.c_pptx()
        # if filename_list[-1] == "ppt":
        #     return self.c_ppt()
        if filename_list[-1] == 'md':
            return self.c_md()
        if filename_list[-1] == 'json':
            return self.c_json()
        if filename_list[-1] == 'csv':
            return self.c_csv()
        # if filename_list[-1] == 'doc':
        #     return self.c_doc()