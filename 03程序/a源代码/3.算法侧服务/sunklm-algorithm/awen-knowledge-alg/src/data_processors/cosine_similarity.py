# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  cosine_similarity.py
   Description :抽取关键词和计算相似度
   Author :  mabenjiang
   date： 2024/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
# 正则包
import re
# html 包
import html
# 自然语言处理包
import jieba
import jieba.analyse
# 机器学习包
from sklearn.metrics.pairwise import cosine_similarity
from langdetect import detect


class CosineSimilarity(object):
    """
    余弦相似度计算类，用于计算两个文本的余弦相似度。
    """
    def __init__(self, content_x1, content_y2):
        """
        Description:
            初始化方法，传入两个待比较的文本。
        Args:
            content_x1: 第一个文本
            content_y2: 第二个文本
        """
        self.s1 = content_x1
        self.s2 = content_y2

    @staticmethod
    def extract_keyword(content, topk=3):
        """
        Description:
            提取文本中的关键词。

        Args:
            content: 待提取关键词的文本
            topk: 返回的关键词数量
        return: 返回提取的关键词列表
        """
        # 正则过滤 html 标签
        re_exp = re.compile(r'(<style>.*?</style>)|(<[^>]+>)', re.S)
        content = re_exp.sub(' ', content)
        # html 转义符实体化
        content = html.unescape(content)
        # 切割
        seg = [i for i in jieba.cut(content, cut_all=True) if i != '']
        # 提取关键词
        keywords = jieba.analyse.extract_tags("|".join(seg), topK=topk, withWeight=False)
        return keywords

    @staticmethod
    def one_hot(word_dict, keywords):
        """
         Description:
            对关键词进行one-hot编码。
         Args:
            word_dict: 单词与索引的映射字典
            keywords: 关键词列表
        Return: 返回one-hot编码后的列表
        """
        cut_code = [0] * len(word_dict)
        for word in keywords:
            cut_code[word_dict[word]] += 1
        return cut_code

    def similarity(self):
        """
        Description:
        主方法，执行文本的相似度计算。
        """
        # 去除停用词
        jieba.analyse.set_stop_words("../../resources/stopwords.txt")
        # 提取关键词
        keywords1 = self.extract_keyword(self.s1)
        keywords2 = self.extract_keyword(self.s2)
        # 词的并集
        union = set(keywords1).union(set(keywords2))
        # 编码
        word_dict = {}
        i = 0
        for word in union:
            word_dict[word] = i
            i += 1
        # oneHot编码
        s1_cut_code = self.one_hot(word_dict, keywords1)
        s2_cut_code = self.one_hot(word_dict, keywords2)
        # 余弦相似度计算
        sample = [s1_cut_code, s2_cut_code]
        # 除零处理
        try:
            sim = cosine_similarity(sample)
            return sim[1][0]
        except :
            return 0.0

def get_score(x1, x2):
    """
    Description:
        计算两个文本的相似度得分。

    Args:
        x1: 第一个文本
        x2: 第二个文本
    return: 返回相似度得分
    """
    obj = CosineSimilarity(x1, x2)
    similarity_score = obj.similarity()
    similarity = round(similarity_score, 2)*100
    return similarity




