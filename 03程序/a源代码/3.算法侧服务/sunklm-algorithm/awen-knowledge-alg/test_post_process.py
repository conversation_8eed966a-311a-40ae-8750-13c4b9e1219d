"""
测试Post_Process.py的功能
验证数据后处理服务的各种功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_post_process_service():
    """测试后处理服务"""
    print("=== 测试后处理服务 ===")
    
    try:
        from src.service.Post_Process import PostProcessService
        
        # 创建服务实例
        service = PostProcessService()
        
        # 测试数据
        test_data = {
            "content": """
            技术文档：Python编程指南
            
            第一章 Python基础
            Python是一种高级编程语言，具有简洁的语法和强大的功能。
            它广泛应用于Web开发、数据分析、人工智能等领域。
            
            主要特点：
            1. 语法简洁易读
            2. 跨平台支持
            3. 丰富的第三方库
            4. 强大的社区支持
            
            第二章 开发环境
            安装Python解释器和开发工具是开始编程的第一步。
            推荐使用PyCharm或VSCode作为开发环境。
            
            注意事项：
            - 选择合适的Python版本
            - 配置虚拟环境
            - 安装必要的依赖包
            
            结论：
            Python是一门优秀的编程语言，适合初学者学习。
            通过系统的学习和实践，可以快速掌握Python编程技能。
            
            页码：第1页，共10页
            日期：2024年1月1日
            """,
            "filename": "Python编程指南.pdf",
            "file_type": "技术文档",
            "author": "技术团队"
        }
        
        print(f"输入数据:")
        print(f"  文件名: {test_data['filename']}")
        print(f"  内容长度: {len(test_data['content'])} 字符")
        
        # 调用后处理服务
        result = service.post_process(test_data)
        
        print(f"\n处理结果:")
        print(f"  文件名: {result.get('filename', 'N/A')}")
        print(f"  摘要: {result.get('summary', 'N/A')[:100]}...")
        print(f"  关键词: {result.get('keywords', [])}")
        print(f"  标签: {result.get('tags', [])}")
        print(f"  页眉: {result.get('header', 'N/A')}")
        print(f"  页脚: {result.get('footer', 'N/A')}")
        print(f"  语言类型: {result.get('language_type', 'N/A')}")
        print(f"  上下文: {result.get('context', 'N/A')[:100]}...")
        print(f"  内容长度: {result.get('content_length', 'N/A')}")
        print(f"  词数: {result.get('word_count', 'N/A')}")
        
        processing_info = result.get('processing_info', {})
        print(f"  处理信息:")
        print(f"    处理时间: {processing_info.get('processed_at', 'N/A')}")
        print(f"    处理器版本: {processing_info.get('processor_version', 'N/A')}")
        print(f"    提取特征: {processing_info.get('features_extracted', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 后处理服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_empty_content():
    """测试空内容处理"""
    print("\n=== 测试空内容处理 ===")
    
    try:
        from src.service.Post_Process import PostProcessService
        
        service = PostProcessService()
        
        # 空内容测试
        empty_data = {
            "content": "",
            "filename": "空文档.txt"
        }
        
        result = service.post_process(empty_data)
        
        print(f"空内容处理结果:")
        print(f"  文件名: {result.get('filename', 'N/A')}")
        print(f"  是否保留原始数据: {'是' if 'content' in result else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 空内容测试失败: {e}")
        return False

def test_missing_filename():
    """测试缺少文件名的情况"""
    print("\n=== 测试缺少文件名处理 ===")
    
    try:
        from src.service.Post_Process import PostProcessService
        
        service = PostProcessService()
        
        # 缺少文件名测试
        no_filename_data = {
            "content": "这是一个没有文件名的测试文档。包含一些基本的文本内容用于测试处理功能。"
        }
        
        result = service.post_process(no_filename_data)
        
        print(f"缺少文件名处理结果:")
        print(f"  文件名: {result.get('filename', 'N/A')}")
        print(f"  摘要: {result.get('summary', 'N/A')[:50]}...")
        print(f"  关键词数量: {len(result.get('keywords', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缺少文件名测试失败: {e}")
        return False

def test_different_content_types():
    """测试不同类型的内容"""
    print("\n=== 测试不同内容类型 ===")
    
    try:
        from src.service.Post_Process import PostProcessService
        
        service = PostProcessService()
        
        # 不同类型的测试内容
        test_cases = [
            {
                "name": "合同文档",
                "data": {
                    "content": "甲乙双方合同协议书。根据相关法律法规，双方达成如下协议条款：第一条 合同目的...",
                    "filename": "合作协议.docx"
                }
            },
            {
                "name": "技术报告", 
                "data": {
                    "content": "系统性能分析报告。本报告对系统进行了全面的技术分析和性能评估，总结如下...",
                    "filename": "性能报告.pdf"
                }
            },
            {
                "name": "操作手册",
                "data": {
                    "content": "用户操作指南手册。本手册详细说明了系统的使用方法和注意事项...",
                    "filename": "用户手册.txt"
                }
            }
        ]
        
        for test_case in test_cases:
            print(f"\n{test_case['name']}:")
            result = service.post_process(test_case['data'])
            
            tags = result.get('tags', [])
            print(f"  标签: {tags}")
            print(f"  关键词: {result.get('keywords', [])[:3]}")  # 只显示前3个
            
        return True
        
    except Exception as e:
        print(f"❌ 不同内容类型测试失败: {e}")
        return False

def test_individual_methods():
    """测试单独的方法"""
    print("\n=== 测试单独方法 ===")
    
    try:
        from src.service.Post_Process import PostProcessService
        
        service = PostProcessService()
        test_content = "这是一个测试文档，包含Python编程、数据分析、机器学习等技术内容。"
        
        # 测试关键词提取
        print("1. 关键词提取:")
        keywords = service._extract_keywords(test_content, 5)
        print(f"   结果: {keywords}")
        
        # 测试标签生成
        print("\n2. 标签生成:")
        tags = service._generate_tags(test_content, keywords)
        print(f"   结果: {tags}")
        
        # 测试页眉页脚提取
        print("\n3. 页眉页脚提取:")
        header, footer = service._extract_header_footer(test_content)
        print(f"   页眉: {header}")
        print(f"   页脚: {footer}")
        
        # 测试语言检测
        print("\n4. 语言检测:")
        language = service._detect_language(test_content)
        print(f"   语言类型: {language}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单独方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 开始Post_Process服务测试...")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("后处理服务", test_post_process_service),
        ("空内容处理", test_empty_content),
        ("缺少文件名", test_missing_filename),
        ("不同内容类型", test_different_content_types),
        ("单独方法测试", test_individual_methods)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print(f"🎉 Post_Process服务测试完成！")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，Post_Process服务工作正常！")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，Post_Process服务基本可用")
    else:
        print("❌ 多数测试失败，Post_Process服务需要修复")
    
    print("\n💡 Post_Process服务功能:")
    print("   • 自动生成文档摘要")
    print("   • 提取关键词和标签")
    print("   • 识别页眉页脚信息")
    print("   • 检测文档语言类型")
    print("   • 生成上下文信息")
    print("   • 统计文档基本信息")
    print("   • 调用已有的LLM和算法服务")
    print("   • 支持外部传入文件名和内容")

if __name__ == "__main__":
    main()
