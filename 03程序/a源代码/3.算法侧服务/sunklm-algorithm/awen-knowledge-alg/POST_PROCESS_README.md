# Post_Process 数据后处理服务

## 概述

Post_Process 服务是一个文档数据后处理服务，用于增强文档数据，添加摘要、关键词、标签、页眉页脚等元数据信息。

## 功能特性

### 核心功能

1. **摘要生成**
   - 使用 `AbstractExtract.get_abstract` 方法生成文档摘要
   - 支持自动提取文档核心内容
   - 提供备选摘要生成方案

2. **关键词提取**
   - 使用 `FileProcessorService.file_process` 方法提取关键词
   - 支持中文分词和词频统计
   - 可配置关键词数量

3. **页眉页脚处理**
   - **预留OCR服务接口**：优先使用OCR服务传入的页眉页脚信息
   - 支持从文本内容自动提取页眉页脚
   - 接口参数：`ocr_header` 和 `ocr_footer`

4. **标签生成**
   - 基于关键词自动生成标签
   - 根据文档类型识别（合同、报告、手册等）
   - 支持自定义标签规则

5. **语言检测**
   - 自动检测文档语言类型
   - 支持中文/英文识别
   - 返回语言类型编码

6. **上下文信息**
   - 生成文档上下文摘要
   - 用于改善搜索检索效果

### 数据结构

#### 输入数据格式

```python
{
    "content": "文档文本内容",           # 必需
    "filename": "文档文件名",           # 必需
    "ocr_header": "OCR提取的页眉",      # 可选，OCR服务传入
    "ocr_footer": "OCR提取的页脚",      # 可选，OCR服务传入
    # 其他原始数据字段...
}
```

#### 输出数据格式

```python
{
    # 原始数据（保留所有输入字段）
    "content": "原始文本内容",
    "filename": "文件名",
    
    # 新增的处理结果
    "summary": "生成的摘要",
    "keywords": ["关键词1", "关键词2", ...],
    "tags": ["标签1", "标签2", ...],
    "header": "页眉信息",
    "footer": "页脚信息",
    "language_type": 1,  # 1=中文, 2=英文
    "context": "上下文信息",
    "content_length": 1234,
    "word_count": 567,
    
    # 处理信息
    "processing_info": {
        "processed_at": "2024-01-01T12:00:00",
        "processor_version": "1.0.0",
        "features_extracted": ["summary", "keywords", ...]
    }
}
```

## 使用方法

### 基本使用

```python
from src.service.Post_Process import PostProcessService

# 初始化服务（可选提供数据库连接和MinIO客户端）
service = PostProcessService(conn=db_connection, minio=minio_client)

# 处理数据
input_data = {
    "content": "这是一个测试文档...",
    "filename": "test.pdf"
}

result = service.post_process(input_data)
print(result)
```

### 使用OCR服务接口

```python
# 包含OCR服务传入的页眉页脚信息
input_data = {
    "content": "文档内容...",
    "filename": "document.pdf",
    "ocr_header": "公司文档 - 机密",      # OCR服务提供
    "ocr_footer": "第1页 | 2024年"       # OCR服务提供
}

result = service.post_process(input_data)

# 页眉页脚信息会优先使用OCR服务提供的数据
print(result["header"])  # "公司文档 - 机密"
print(result["footer"])  # "第1页 | 2024年"
```

## 技术实现

### 依赖的服务

1. **AbstractExtract** (`src.tools.abstract.AbstractExtract`)
   - 提供 `get_abstract(text)` 方法
   - 用于生成文档摘要

2. **FileProcessorService** (`src.service.format_service.FileProcessorService`)
   - 提供 `file_process(path, key_num, sim_num)` 方法
   - 用于提取关键词和处理文件

3. **TextQualityDetector** (`src.data_processors.text_detector.TextQualityDetector`)
   - 提供语言检测功能

### OCR服务集成

服务预留了OCR服务接口，支持以下方式：

1. **优先级处理**：
   - 如果输入数据包含 `ocr_header` 或 `ocr_footer` 字段
   - 优先使用OCR服务提供的页眉页脚信息
   - 如果OCR只提供部分信息，自动补充缺失部分

2. **接口设计**：
   ```python
   def _get_header_footer_info(self, content, ocr_header='', ocr_footer=''):
       # 优先使用OCR数据
       if ocr_header or ocr_footer:
           return ocr_header, ocr_footer
       # 否则从文本提取
       return self._extract_from_content(content)
   ```

## 文件说明

### 主要文件

- `src/service/Post_Process.py` - 完整版本的后处理服务
- `src/service/Post_Process_Simple.py` - 简化版本（避免复杂依赖）
- `test_post_process.py` - 完整版本测试文件
- `test_post_process_simple.py` - 简化版本测试文件

### 测试结果

简化版本测试结果：
```
📊 测试结果: 5/5 通过
✅ 所有测试通过，Post_Process服务工作正常！
```

测试覆盖：
- ✅ 后处理服务基本功能
- ✅ OCR接口预留功能
- ✅ 摘要提取功能
- ✅ 关键词提取功能
- ✅ 不同内容类型处理

## 配置说明

### 初始化参数

```python
PostProcessService(conn=None, minio=None)
```

- `conn`: 数据库连接对象（用于FileProcessorService）
- `minio`: MinIO客户端对象（用于FileProcessorService）
- 如果不提供这些参数，会使用简化版本的处理器

### 可配置项

- `num_keywords`: 提取的关键词数量（默认10个）
- 摘要长度限制（默认500字符）
- 上下文长度限制（默认200字符）

## 错误处理

服务具有完善的错误处理机制：

1. **输入验证**：检查必需字段
2. **异常捕获**：各个处理步骤都有异常处理
3. **备选方案**：主要功能失败时提供备选实现
4. **错误信息**：返回详细的错误信息和状态

## 扩展性

### 添加新的处理功能

1. 在 `_process_data` 方法中添加新的处理步骤
2. 实现对应的处理方法
3. 更新输出数据结构
4. 添加相应的测试用例

### OCR服务扩展

可以通过以下方式扩展OCR服务集成：

1. 添加更多OCR字段（如表格信息、图片描述等）
2. 支持OCR置信度信息
3. 集成多种OCR服务提供商

## 注意事项

1. **依赖管理**：完整版本需要安装较多依赖包
2. **性能考虑**：大文档处理可能需要较长时间
3. **内存使用**：处理大量文档时注意内存管理
4. **编码问题**：确保文本内容使用UTF-8编码

## 更新日志

- v1.0.0: 初始版本，实现基本功能
- v1.0.0-simple: 简化版本，避免复杂依赖
- 预留OCR服务接口，支持页眉页脚信息传入
