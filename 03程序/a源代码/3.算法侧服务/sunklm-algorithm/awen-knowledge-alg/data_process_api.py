import sys
import time
import traceback
import json
import asyncio
import aiohttp
import os
import threading
import time
import json
from flask import Flask, request, jsonify
from src.service.vectorization_service import VectorStoreGenerator
from src.service.format_service import FileProcessorService
from src.service.spliter_service import *
from src.utils.log_utils import return_dict
from src.utils.log_utils import logger
from src.utils.db import POOL
from src.utils.minio_conn import MinioClient
from src.utils.milvus_conn import MilvusClient
from src.utils.es_conn import EsClient

flat = MaxInsert
flat_delete = MaxInsert
logger = logger.bind(name="data_process")
minio = MinioClient().get_connect()
es = EsClient()
ms = MilvusClient()

app = Flask(__name__)

@app.route('/RAG-alg/file_format', methods=['POST'])
def file_format():
    # executor = ThreadPoolExecutor(5)
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        # 提交任务到线程池
        conn = POOL.connection()
        dp = FileProcessorService(conn,minio)
        dp.file_format(params)
        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        # return return_dict('0', {"result": "文本切片成功。"}, 'success')
        return return_dict('0', {'result': '文本格式化成功。'}, 'success')  # 正常完成时返回响应
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())


@app.route('/RAG-alg/text_split', methods=['POST'])
def text_split():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        # 提交任务到线程池
        conn = POOL.connection()
        ts = TextSplitterService(conn)
        ts.text_split(params)

        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        # return return_dict('0', {"result": "文本切片成功。"}, 'success')
        return return_dict('0', {'result': '文本切片成功。'}, 'success')  # 正常完成时返回响应
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())

# @app.route('/RAG-alg/text_vector', methods=['POST'])
# def text_vector():
#     time_start = time.time()
#     try:
#         params = json.loads(request.get_data(as_text=True))
#         logger.info("入参--------" + str(params))
#         # 提交任务到线程池
#         conn = POOL.connection()
#         while True:
#             if flat >0:
#                 flat -=1
#                 vs = VectorStoreGenerator(conn,es,ms)
#                 slice_ids = params.get("slicesId")
#                 file_ids = params.get("filesId")
#                 vector_type = params.get("vectorType")
#                 # match_content = params.get("matchContent", IF_RERANK)  ## matchType: 1)False:按照切片匹配，发挥1切片 2)True:按照摘要匹配，泛化切片
#                 # is_contextual_retrieval = params.get("isContextualRetrieval", IF_CONTEXTUAL_RETRIEVAL)
#                 if vector_type == "FILE":
#                     result = vs.generate_vector_store_for_files(file_ids, slice_ids)
#                     # asyncio.run(vs.insert_vector(file_ids,slice_ids))
#                     # return ruslt
#                 elif vector_type == "FAQ":
#                     vs.generate_vector_store_for_faqs(file_ids, slice_ids)
#                 asyncio.run(vs.generate_vector_store(params))
#                 logger.info(result)
#                 logger.info("----算法运行时间：{}----".format(time.time() - time_start))
#                 flat +=1
#                 # return return_dict('0', {"result": "文本切片成功。"}, 'success')
#                 return return_dict('0', {'result': result}, 'success')  # 正常完成时返回响应
#             else:
#                 time.sleep(0.1)
#     except Exception as e:
#         logger.error("--------错误信息--------")
#         logger.error(e)
#         logger.error("--------定位信息--------")
#         logger.error("{}".format(traceback.format_exc()))
#         logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
#         return return_dict('1', 'error', traceback.format_exc())



@app.route('/RAG-alg/text_vector', methods=['POST'])
def text_vector():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        
        # 提交任务到线程池
        conn = POOL.connection()

        def background_task():
            global flat 
            while True:
                if flat > 0:
                    flat -= 1
                    logger.info("开始执行任务当前剩余线程数{}".format(flat))
                    vs = VectorStoreGenerator(conn, es, ms)
                    vs.generate_vector_store(params)
                    # logger.info(result)
                    logger.info("----算法运行时间：{}----".format(time.time() - time_start))
                    flat += 1
                    break
                else:
                    time.sleep(0.1)

        # 创建并启动后台线程
        thread = threading.Thread(target=background_task)
        thread.start()

        # 立即返回响应给客户端
        return return_dict('0', {'result': '任务已提交'}, 'success')
    
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())

@app.route('/RAG-alg/delete_vector', methods=['POST'])
def delete_vector():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        
        # 提交任务到线程池
        conn = POOL.connection()

        def background_task():
            global flat_delete 
            while True:
                if flat_delete > 0:
                    flat_delete -= 1
                    logger.info("开始执行任务当前剩余线程数{}".format(flat_delete))
                    vs = VectorStoreGenerator(conn, es, ms)
                    vs.delete_vector_ids(params)
                    # logger.info(result)
                    logger.info("----算法运行时间：{}----".format(time.time() - time_start))
                    flat_delete += 1
                    break
                else:
                    time.sleep(0.1)

        # 创建并启动后台线程
        thread = threading.Thread(target=background_task)
        thread.start()

        # 立即返回响应给客户端
        return return_dict('0', {'result': '任务已提交'}, 'success')
    
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())
    
# @app.route('/RAG-alg/delete_vector', methods=['POST'])
# def delete_vector():
#     time_start = time.time()
#     try:
#         params = json.loads(request.get_data(as_text=True))
#         logger.info("入参--------" + str(params))
#         conn = POOL.connection()
#         vs = VectorStoreGenerator(conn,es,ms)
#         vs.delete_vector_ids(params)
#         logger.info("----算法运行时间：{}----".format(time.time() - time_start))
#         return return_dict('0', {"result": "向量化删除成功。"}, 'success')
#     except Exception as e:
#         logger.error("--------错误信息--------")
#         logger.error(e)
#         logger.error("--------定位信息--------")
#         logger.error("{}".format(traceback.format_exc()))
#         logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
#         return return_dict('1', 'error', traceback.format_exc())
    
@app.route('/RAG-alg/delete_faq_vector', methods=['POST'])
def delete_faq_vector():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        conn = POOL.connection()
        vs = VectorStoreGenerator(conn,es,ms)
        vs.delete_faq_vector_ids(params)
        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        return return_dict('0', {"result": "向量化删除成功。"}, 'success')
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())
    
@app.route('/RAG-alg/create_col', methods=['POST'])
def create_col():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        conn = POOL.connection()
        vs = VectorStoreGenerator(conn,es,ms)
        vs.create_col(params)
        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        return return_dict('0', {"result": "colletion创建成功。"}, 'success')
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())

@app.route('/RAG-alg/delete_col', methods=['POST'])
def delete_col():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        conn = POOL.connection()
        vs = VectorStoreGenerator(conn,es,ms)
        vs.delete_col(params)
        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        return return_dict('0', {"result": "colletion删除成功。"}, 'success')
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)


@app.route("/RAG-alg/extract_faqs", methods=['POST'])
def extract_faqs():
    time_start = time.time()
    try:
        params = json.loads(request.get_data(as_text=True))
        logger.info("入参--------" + str(params))
        conn = POOL.connection()
        vs = FileProcessorService(conn, minio)
        
        file_id = params.get("file_id")
        faq_num = params.get("faq_num")
        faq_context_length = params.get("faq_context_length")
        architecture = os.uname().machine  # 读取服务器的架构
        if architecture == "x86_64":
            create_user_id, text_id, questions, answers = vs.extract_faqs_for_file(file_id, faq_num, faq_context_length)
        else:
            create_user_id, text_id, questions, answers = vs.extract_faqs_for_file_arm(file_id, faq_num, faq_context_length)
        # if ARCHITECTURE == "arm":
        #     create_user_id, text_id, questions, answers = vs.extract_faqs_for_file_arm(file_id, faq_num, faq_context_length)
        # else:
        #     create_user_id, text_id, questions, answers = vs.extract_faqs_for_file(file_id, faq_num, faq_context_length)
        
        if questions and answers:
            for question, answer in zip(questions, answers):
                file_faq_id = vs.snowflake.next_id()
                vs.insert_faq_info(file_faq_id, text_id, file_id, question, answer, 1, 1, 0, create_user_id)
        logger.info("----算法运行时间：{}----".format(time.time() - time_start))
        return return_dict('0', {"result": "FAQ抽取成功。"}, 'success')
    except Exception as e:
        logger.error("--------错误信息--------")
        logger.error(e)
        logger.error("--------定位信息--------")
        logger.error("{}".format(traceback.format_exc()))
        logger.info("--------算法运行时间：{}--------".format(time.time() - time_start))
        return return_dict('1', 'error', traceback.format_exc())


if __name__ == '__main__':
    print(r"""
     _                        _    _____  _                 
    / \    __ _   ___  _ __  | |_ |  ___|| |  ___ __      __
   / _ \  / _` | / _ \| '_ \ | __|| |_   | | / _ \\ \ /\ / /
  / ___ \| (_| ||  __/| | | || |_ |  _|  | || (_) |\ V  V / 
 /_/   \_\\__, | \___||_| |_| \__||_|    |_| \___/  \_/\_/  
          |___/                                                                        

    """, flush=True)
    port = sys.argv[1]
    app.run(host='0.0.0.0', port=port, debug=False)