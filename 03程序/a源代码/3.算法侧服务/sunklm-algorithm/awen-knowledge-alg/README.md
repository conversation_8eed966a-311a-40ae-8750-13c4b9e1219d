# awen-knowledge-alg
generated by AI

## 项目概述
awen-knowledge-alg是一个知识算法服务，提供数据处理、大模型交互、检索增强生成(RAG)等功能，支持文档摘要提取、关键词处理、文本向量化等后处理服务。

## 目录结构
```
awen-knowledge-alg/
├── Dockerfile               # Docker配置文件
├── POST_PROCESS_README.md   # 后处理服务详细文档
├── conf/                    # 配置文件目录
│   └── server_config.py     # 服务配置参数
├── logs/                    # 日志文件目录
├── resource/                # 资源文件目录
├── requirements.txt         # 项目依赖
├── run.sh                   # 服务启动脚本
├── src/                     # 源代码目录
│   ├── data_processors/     # 数据处理模块
│   │   ├── change_format.py # 格式转换
│   │   ├── cosine_similarity.py # 相似度计算
│   │   ├── keywords.py      # 关键词提取
│   │   └── text_detector.py # 文本检测
│   ├── llms/                # 大模型服务模块
│   │   ├── llm_chat.py      # 模型交互
│   │   └── prompt_template.py # 提示词模板
│   ├── rag/                 # RAG相关功能
│   │   ├── ai_splitter.py   # AI文本分割
│   │   ├── chapter_split.py # 章节分割
│   │   ├── embedding.py     # 嵌入生成
│   │   └── text_splitter.py # 文本分割
│   ├── service/             # 核心服务
│   │   ├── Post_Process.py  # 后处理服务
│   │   ├── format_service.py # 格式处理服务
│   │   ├── llm_base_service.py # LLM基础服务
│   │   ├── spliter_service.py # 分割服务
│   │   └── vectorization_service.py # 向量化服务
│   ├── tools/               # 工具类
│   │   ├── abstract.py      # 摘要提取
│   │   ├── document_classification.py # 文档分类
│   │   └── replace.py       # 文本替换
│   └── utils/               # 工具函数
│       ├── db.py            # 数据库工具
│       ├── es_conn.py       # Elasticsearch连接
│       ├── log_utils.py     # 日志工具
│       ├── minio_conn.py    # MinIO连接
│       └── redis_conn.py    # Redis连接
└── test/                    # 测试用例
```

## 架构设计
```mermaid
graph TD
    A[数据输入] --> B[数据处理模块]
    B --> C[文本分割]
    B --> D[关键词提取]
    C --> E[向量化服务]
    E --> F[检索服务]
    F --> G[LLM服务]
    G --> H[结果后处理]
    H --> I[输出]
    subgraph 核心服务
        PostProcessService
        FormatService
        LLMBasicService
        VectorizationService
    end
    subgraph 依赖组件
        Database
        MinIO
        Elasticsearch
        Redis
    end
```

## 核心功能
1. **数据处理**：文本检测、格式转换、相似度计算
2. **LLM服务**：问题改写、FAQ抽取、假设问题生成、一致性检测
3. **RAG功能**：文本智能分割、向量化处理、检索增强
4. **后处理服务**：文档摘要提取、关键词提取

## 安装与部署
1. 克隆项目到本地
2. 安装依赖包：`pip install -r requirements.txt`
3. 配置服务参数：修改`conf/server_config.py`
4. 启动服务：`sh run.sh`

## 使用示例
### 后处理服务使用
```python
from src.service.Post_Process import PostProcessService

# 初始化服务
service = PostProcessService(conn=db_connection, minio=minio_client)

# 处理数据
input_data = {
    "content": "这是一个测试文档...",
    "filename": "test.pdf"
}

result = service.post_process(input_data)
print(result)
```

## 依赖项
- langchain
- openai
- pandas
- python-docx
- PyPDF2
- python-pptx

## 日志与监控
日志文件位于`logs/`目录下，包含服务运行日志和错误信息。