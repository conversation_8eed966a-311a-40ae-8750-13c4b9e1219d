"""
测试Pre_Process.py的功能
验证文本预处理服务的各种功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.service.Pre_Process import TextPreProcessorService


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 测试用例1：空白字符标准化
    print("\n1. 空白字符标准化测试:")
    text1 = "这是一段包含   多个空格和\n\n换行符\t\t制表符的文本"
    print(f"原始文本: '{text1}'")
    result1 = preprocessor.normalize_whitespace(text1)
    print(f"处理结果: '{result1}'")

    # 测试用例2：URL删除
    print("\n2. URL删除测试:")
    test_urls = [
        "访问我们的网站：https://www.example.com",
        "更多信息请查看 www.baidu.com",
        "下载地址：ftp://files.example.com/download",
        "网址：http://test.cn/path?param=value",
        "请访问 example.org 了解详情"
    ]

    for i, text in enumerate(test_urls, 1):
        print(f"  测试{i}: '{text}'")
        result = preprocessor.remove_urls(text)
        print(f"  结果{i}: '{result}'")

    # 测试用例3：邮箱删除
    print("\n3. 邮箱删除测试:")
    test_emails = [
        "联系邮箱：<EMAIL>",
        "发送邮件到 <EMAIL>",
        "Email: <EMAIL>",
        "电子邮箱：<EMAIL>"
    ]

    for i, text in enumerate(test_emails, 1):
        print(f"  测试{i}: '{text}'")
        result = preprocessor.remove_emails(text)
        print(f"  结果{i}: '{result}'")

    return True


def test_enhanced_patterns():
    """测试增强的正则表达式模式"""
    print("\n" + "=" * 60)
    print("测试增强的正则表达式模式")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 测试电话号码删除
    print("\n1. 电话号码删除测试:")
    test_phones = [
        "联系电话：13812345678",
        "Tel: 010-12345678",
        "手机：+86 138-1234-5678",
        "电话：0571-88888888"
    ]

    for i, text in enumerate(test_phones, 1):
        print(f"  测试{i}: '{text}'")
        result = preprocessor.remove_phone_numbers(text)
        print(f"  结果{i}: '{result}'")

    # 测试HTML标签删除
    print("\n2. HTML标签删除测试:")
    test_html = [
        "这是<b>粗体</b>文本",
        "<p>段落内容</p>",
        "<a href='http://example.com'>链接</a>文本",
        "普通文本<br/>换行<div>块级元素</div>"
    ]

    for i, text in enumerate(test_html, 1):
        print(f"  测试{i}: '{text}'")
        result = preprocessor.remove_html_tags(text)
        print(f"  结果{i}: '{result}'")

    # 测试标点符号清理
    print("\n3. 标点符号清理测试:")
    test_punctuation = [
        "这是文本，，，多个逗号",
        "结束了。。。。",
        "问题？？？",
        "惊叹！！！！"
    ]

    for i, text in enumerate(test_punctuation, 1):
        print(f"  测试{i}: '{text}'")
        result = preprocessor.clean_punctuation(text)
        print(f"  结果{i}: '{result}'")

    return True


def test_comprehensive_processing():
    """测试综合处理功能"""
    print("\n" + "=" * 60)
    print("测试综合处理功能")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 复杂测试用例
    complex_texts = [
        {
            "name": "综合测试1",
            "text": "这是一段复杂文本，包含多个URL：https://example.com, www.baidu.com 和多个邮箱：<EMAIL>, <EMAIL>，以及   多个  空格和\n换行符，还有电话：13812345678"
        },
        {
            "name": "综合测试2",
            "text": "<p>HTML内容</p>网址：http://test.com，邮箱：<EMAIL>，，，多个逗号。。。。多个句号"
        },
        {
            "name": "综合测试3",
            "text": "联系我们：电话：010-12345678，邮箱：<EMAIL>，网站：www.example.com，更多信息请访问我们的官网"
        },
        {
            "name": "综合测试4",
            "text": "这是正常文本，没有需要清理的内容，应该保持原样。"
        }
    ]

    for test_case in complex_texts:
        print(f"\n{test_case['name']}:")
        print(f"原始文本: '{test_case['text']}'")

        # 使用默认选项处理
        result_default = preprocessor.process_text(test_case['text'])
        print(f"默认处理: '{result_default}'")

        # 使用自定义选项处理
        custom_options = {
            'remove_urls': True,
            'remove_emails': True,
            'remove_phones': False,  # 不删除电话
            'clean_special_chars': True  # 清理特殊字符
        }
        result_custom = preprocessor.process_text(test_case['text'], custom_options)
        print(f"自定义处理: '{result_custom}'")

    return True


def test_batch_processing():
    """测试批量处理功能"""
    print("\n" + "=" * 60)
    print("测试批量处理功能")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 批量测试文本
    batch_texts = [
        "文本1：包含URL https://example.com",
        "文本2：包含邮箱 <EMAIL>",
        "文本3：包含电话 13812345678",
        "文本4：正常文本内容",
        "文本5：包含HTML <b>标签</b>"
    ]

    print("批量处理测试:")
    print("原始文本列表:")
    for i, text in enumerate(batch_texts, 1):
        print(f"  {i}. '{text}'")

    # 批量处理
    processed_batch = preprocessor.batch_process(batch_texts)

    print("\n处理后文本列表:")
    for i, text in enumerate(processed_batch, 1):
        print(f"  {i}. '{text}'")

    return True


def test_statistics():
    """测试统计功能"""
    print("\n" + "=" * 60)
    print("测试统计功能")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 重置统计
    preprocessor.reset_stats()

    # 处理一些文本
    test_texts = [
        "包含URL：https://example.com 的文本",
        "包含邮箱：<EMAIL> 的文本",
        "包含   多个空格   的文本",
        "正常文本"
    ]

    print("处理文本并统计:")
    for text in test_texts:
        result = preprocessor.process_text(text)
        print(f"'{text}' -> '{result}'")

    # 获取统计信息
    stats = preprocessor.get_processing_stats()
    print(f"\n处理统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    return True


def test_mask_functionality():
    """测试星号替换功能"""
    print("\n" + "=" * 60)
    print("测试星号替换功能")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 测试星号替换功能
    mask_test_cases = [
        {
            "name": "URL星号替换",
            "text": "访问我们的网站：https://www.example.com 了解更多信息",
            "options": {"mask_urls": True, "remove_urls": False}
        },
        {
            "name": "邮箱星号替换",
            "text": "联系邮箱：<EMAIL> 或发送邮件到 <EMAIL>",
            "options": {"mask_emails": True, "remove_emails": False}
        },
        {
            "name": "电话号码星号替换",
            "text": "联系电话：13812345678，手机：+86 138-1234-5678",
            "options": {"mask_phones": True, "remove_phones": False}
        },
        {
            "name": "综合星号替换",
            "text": "联系我们：电话：010-12345678，邮箱：<EMAIL>，网站：https://www.example.com",
            "options": {"mask_urls": True, "mask_emails": True, "mask_phones": True,
                       "remove_urls": False, "remove_emails": False, "remove_phones": False}
        },
        {
            "name": "普通URL星号替换",
            "text": "请访问 www.baidu.com 或 example.org 了解详情",
            "options": {"mask_urls": True, "remove_urls": False}
        }
    ]

    for case in mask_test_cases:
        print(f"\n{case['name']}:")
        print(f"  原始文本: '{case['text']}'")
        result = preprocessor.process_text(case['text'], case['options'])
        print(f"  处理结果: '{result}'")

        # 验证描述词是否保留
        if "联系电话：" in case['text'] and "mask_phones" in case['options']:
            if "联系电话：" in result:
                print(f"  ✅ 电话描述词已保留")
            else:
                print(f"  ❌ 电话描述词未保留")

        if "邮箱：" in case['text'] and "mask_emails" in case['options']:
            if "邮箱：" in result:
                print(f"  ✅ 邮箱描述词已保留")
            else:
                print(f"  ❌ 邮箱描述词未保留")

        if "网站：" in case['text'] and "mask_urls" in case['options']:
            if "网站：" in result:
                print(f"  ✅ 网站描述词已保留")
            else:
                print(f"  ❌ 网站描述词未保留")

    return True


def test_specific_issues():
    """测试具体问题案例"""
    print("\n" + "=" * 60)
    print("测试具体问题案例")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    # 测试问题案例
    problem_cases = [
        {
            "name": "手机号码格式问题",
            "text": "手机：+86 138-1234-5678",
            "expected": ""  # 修改期望结果，因为我们现在完全删除带描述词的电话号码
        },
        {
            "name": "邮箱残留问题1",
            "text": "这是一段复杂文本，包含多个邮箱：<EMAIL>, <EMAIL>",
            "expected": "这是一段复杂文本，包含多个"
        },
        {
            "name": "邮箱残留问题2",
            "text": "联系我们：电话：010-12345678，邮箱：<EMAIL>，网站：www.example.com",
            "expected": "联系我们" 
        },
        {
            "name": "电话号码残留问题",
            "text": "还有电话：13812345678",
            "expected": "还有"
        }
    ]

    for case in problem_cases:
        print(f"\n{case['name']}:")
        print(f"  原始文本: '{case['text']}'")
        result = preprocessor.process_text(case['text'])
        print(f"  实际结果: '{result}'")
        print(f"  期望结果: '{case['expected']}'")
        print(f"  是否匹配: {'✅' if result == case['expected'] else '❌'}")

    return True


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)

    preprocessor = TextPreProcessorService()

    edge_cases = [
        ("空字符串", ""),
        ("None值", None),
        ("只有空格", "   "),
        ("只有换行", "\n\n\n"),
        ("只有URL", "https://example.com"),
        ("只有邮箱", "<EMAIL>"),
        ("混合空白字符", "\t\n  \r\n  \t"),
        ("特殊字符", "!@#$%^&*()_+{}|:<>?[]\\;'\",./-=`~"),
        ("中英文混合", "这是中文English混合text文本"),
        ("数字和符号", "123456789!@#$%^&*()")
    ]

    for name, text in edge_cases:
        print(f"\n{name}测试:")
        print(f"  输入: {repr(text)}")
        try:
            result = preprocessor.process_text(text)
            print(f"  输出: {repr(result)}")
        except Exception as e:
            print(f"  异常: {e}")

    return True


def main():
    """主测试函数"""
    print("🔧 开始Pre_Process服务测试...")
    print("=" * 80)

    # 运行所有测试
    tests = [
        ("基本功能", test_basic_functionality),
        ("增强模式", test_enhanced_patterns),
        ("综合处理", test_comprehensive_processing),
        ("批量处理", test_batch_processing),
        ("统计功能", test_statistics),
        ("星号替换", test_mask_functionality),
        ("具体问题", test_specific_issues),
        ("边界情况", test_edge_cases)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()

    # 显示测试结果
    print("\n" + "=" * 80)
    print(f"🎉 Pre_Process服务测试完成！")
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("✅ 所有测试通过，Pre_Process服务工作正常！")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，Pre_Process服务基本可用")
    else:
        print("❌ 多数测试失败，Pre_Process服务需要修复")

    print("\n💡 Pre_Process服务功能:")
    print("   • 标准化空白字符（空格、换行、制表符）")
    print("   • 删除URL和网址链接")
    print("   • 删除电子邮箱地址")
    print("   • 删除电话号码")
    print("   • 🆕 将URL替换为对应字符数的星号（保留描述词）")
    print("   • 🆕 将邮箱替换为对应字符数的星号（保留描述词）")
    print("   • 🆕 将电话号码替换为对应字符数的星号（保留描述词）")
    print("   • 删除HTML标签")
    print("   • 清理重复标点符号")
    print("   • 清理多余连接词")
    print("   • 清理无效结尾")
    print("   • 支持自定义处理选项")
    print("   • 支持批量处理")
    print("   • 提供处理统计信息")


if __name__ == "__main__":
    main()
