"""
测试Post_Process.py的功能
验证数据后处理服务的各种功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.service.Pre_Process import TextPreProcessorService

def main():
    # 使用示例
    preprocessor = TextPreProcessorService()
    
    print("\n===== 文本预处理测试 =====")
    
    # 测试用例1：基本文本处理
    text1 = "这是一段包含   多个空格和\n换行符的文本，还有URL：https://example.com 和邮箱：<EMAIL>"
    print("\n测试用例1 - 原始文本:\n", text1)
    processed_text1 = preprocessor.process_text(text1)
    print("测试用例1 - 处理后文本:\n", processed_text1)
    
    # 测试用例2：只有URL
    text2 = "这是一个URL链接：https://www.baidu.com"
    print("\n测试用例2 - 原始文本:\n", text2)
    processed_text2 = preprocessor.process_text(text2)
    print("测试用例2 - 处理后文本:\n", processed_text2)
    
    # 测试用例3：只有邮箱
    text3 = "联系我们：邮箱：<EMAIL>"
    print("\n测试用例3 - 原始文本:\n", text3)
    processed_text3 = preprocessor.process_text(text3)
    print("测试用例3 - 处理后文本:\n", processed_text3)
    
    # 测试用例4：复杂文本
    text4 = "这是一段复杂文本，包含多个URL：https://example.com, www.baidu.com 和多个邮箱：<EMAIL>, <EMAIL>，以及   多个  空格和\n换行符"
    print("\n测试用例4 - 原始文本:\n", text4)
    processed_text4 = preprocessor.process_text(text4)
    print("测试用例4 - 处理后文本:\n", processed_text4)
    
    print("\n===== 测试完成 =====")

if __name__ == "__main__":
    main()
