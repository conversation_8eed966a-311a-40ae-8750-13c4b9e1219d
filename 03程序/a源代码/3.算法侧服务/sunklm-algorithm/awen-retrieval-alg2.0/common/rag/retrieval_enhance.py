#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import math
import time
import numpy as np
from common.utils.log_utils import logger
from typing import List, Tuple, Union


class RetrievalEnhance():
    """
    检索算法增强工具类
    通过多种算法对文本检索/向量检索结果进行加权处理，调整原始结果的score及排序。
    """
    
    def __init__(self):
        """
        初始化检索增强工具，定义支持的变换函数
        """
        logger.info("初始化检索增强工具")
        
        # 支持的数值变换函数，键为函数名称，值为对应的函数对象
        self.valid_modifiers = {
            'log': np.log,
            'log1p': np.log1p,
            'log2p': lambda x: np.log2(x + 1),
            'ln': np.log,
            'ln1p': np.log1p,
            'ln2p': lambda x: np.log(x + 1) / np.log(2),
            'square': lambda x: x ** 2,
            'sqrt': np.sqrt,
            'reciprocal': lambda x: np.where(x != 0, 1.0 / x, 0)
        }
    
    
    def _min_max_standard(self, data, feature_range = (0.1, 1)):
        """
        最小-最大标准化函数，将数据缩放到指定范围
        
        Args:
            data: 输入数据，支持列表或NumPy数组
            feature_range: 目标范围元组，默认(0.1, 1)，避免零值导致后续计算问题
            
        Returns:
            标准化后的数据，保持与输入相同的数据类型
            
        """
        if len(data) == 0:
            return []
        
        try:
            # 转换为NumPy数组进行计算
            data_array = np.array(data, dtype=np.float64)
        except Exception as e:
            raise TypeError("输入数据必须为数值类型列表或NumPy数组") from e
        
        # 检查数据是否为常数（所有值都相同）
        if np.all(data_array == data_array[0]):
            min_val, max_val = feature_range
            return np.full_like(data_array, (min_val + max_val) / 2)
        
        # 计算最小值和最大值
        min_data = np.min(data_array)
        max_data = np.max(data_array)
        
        # 计算缩放因子
        min_range, max_range = feature_range
        scale = (max_range - min_range) / (max_data - min_data)
        
        # 应用标准化
        scaled_data = min_range + scale * (data_array - min_data)
        
        # 如果输入是列表，转换回列表
        if isinstance(data, list):
            return scaled_data.tolist()
        
        return scaled_data
    
    
    def enhance_by_keywords(self, 
                            texts_with_scores: List[Tuple[str, float]], 
                            keywords_with_weights: List[Tuple[str, float]],
                            min_keyword_length: int = 2,
                            output_format: str = 'factor'
                            ) -> Union[List[Tuple[str, float]], List[float]]:
        """
        基于关键词-权重对检索结果进行加权调整
        
        Args:
            texts_with_scores: 输入文本列表，格式为[(文本, 原始得分), ...]
            keywords_with_weights: 关键词-权重列表，格式为[(关键词, 权重), ...]
            min_keyword_length: 过滤过短关键词的最小长度，默认2
            output_format: 输出格式，'factor'返回调整因子，'resorted_matches'返回排序后的结果
            
        Returns:
            根据output_format返回调整因子列表或排序后的结果列表
        
        """
        logger.info("开始基于关键词进行检索结果增强")
        
        # 验证输入格式
        if not texts_with_scores:
            raise ValueError("输入文本列表不能为空")
            
        first_tuple = texts_with_scores[0]
        if not isinstance(first_tuple, tuple) or len(first_tuple) != 2:
            raise ValueError("输入列表中的元组格式应为(文本, 得分)")

        # 1、过滤过短的关键词
        filtered_keywords = [(k, w) for k, w in keywords_with_weights if len(k) >= min_keyword_length]
        
        # 如果没有有效关键词，直接返回原列表
        if not filtered_keywords:
            logger.warning("没有有效关键词，返回原始结果")
            if output_format == "resorted_matches": 
                return texts_with_scores
            else:
                return [item[1] for item in texts_with_scores]
        
        # 2、对每个文本计算调整后的得分
        results = []
        factors = []
        
        for text, original_score in texts_with_scores:
            # 初始化调整因子
            adjustment_factor = 1.0
            
            # 计算关键词匹配情况
            for keyword, weight in filtered_keywords:
                try:
                    # 检查关键词是否在文本中出现
                    if keyword in text:
                        # 计算关键词在文本中出现的次数
                        count = text.count(keyword)
                        
                        # 根据出现次数和权重调整因子
                        adjustment_factor += (1 - math.exp(-count)) * weight
                except:
                    continue
            
            # 计算调整后的得分
            adjusted_score = original_score * adjustment_factor
            
            # 添加到结果列表
            factors.append(adjustment_factor)
            results.append((text, adjusted_score))
        
        # 3、返回结果
        if output_format == "resorted_matches":
            # 按调整后的score降序排序后输出
            results.sort(key=lambda x: x[1], reverse=True)
            return results
        
        else:   
            # 返回未排序的调整因子
            return factors
            
    def enhance_by_customer_factors(self,
                                    texts_with_scores: List[Tuple[str, float, float]],
                                    modifier: str = 'log1p',
                                    weight: float = 1,
                                    output_format: str = 'factor'
                                    ) -> Union[List[Tuple[str, float, float]], List[float]]:
        """
        基于自定义因子及算法对检索结果进行加权调整
        
        Args:
            texts_with_scores: 输入文本列表，格式为[(文本, 原始得分, 自定义因子), ...]
            modifier: 应用于自定义因子的变换函数名称，默认'log1p'
            weight: 权重因子，默认1
            output_format: 输出格式，'factor'返回调整因子，'resorted_matches'返回排序后的结果
            
        Returns:
            根据output_format返回调整因子列表或排序后的结果列表
        
        """
        logger.info("开始基于自定义因子进行检索结果增强")
        
        # 验证输入格式
        if not texts_with_scores:
            raise ValueError("输入文本列表不能为空")
            
        first_tuple = texts_with_scores[0]
        if not isinstance(first_tuple, tuple) or len(first_tuple) != 3:
            raise ValueError("输入列表中的元组格式应为(文本, 得分, 因子)")
        
        try:
            texts = [i[0] for i in texts_with_scores]
            scores = [float(i[1]) for i in texts_with_scores]
            factors = [float(i[2]) for i in texts_with_scores]
        except Exception as e:
            raise TypeError("输入数据中的得分和因子必须为数值类型") from e

        # 变换函数名称标准化
        modifier = modifier.lower()
        
        if modifier not in self.valid_modifiers:
            valid_modifiers_str = ", ".join(self.valid_modifiers.keys())
            raise ValueError(f"不支持的变换函数: {modifier}，支持的函数: {valid_modifiers_str}")
        
        try:
            # 获取对应的变换函数
            mode = self.valid_modifiers[modifier]
            
            # 计算权重调整后的因子
            weighted_factors = mode(np.array(factors, dtype=float) * weight)
        except Exception as e:
            logger.error(f"变换函数应用错误: {str(e)}")
    
        # 计算加权后score
        weighted_scores = np.array(scores, dtype=float) * weighted_factors
        
        # 返回结果
        if output_format == 'resorted_matches':
            # 按调整后的score降序排序后输出
            results = [(a, b, c) for a, b, c in zip(texts, scores, weighted_scores)]
            results.sort(key=lambda x: x[2], reverse=True)
            return results
            
        else:
            # 返回加工后的因子
            return weighted_factors.tolist()
      
        
    def enhance_by_gauss_decay(self,
                               texts_with_scores: List[Tuple[str, float, float]],
                               origin: str = 'NOW',
                               scale: str = '30D',
                               decay: float = 0.5,
                               weight: float = 1,
                               output_format: str = 'factor'
                               ) -> Union[List[Tuple[str, float, float]], List[float]]:
        """
        基于时间衰减模型对检索结果进行加权调整
        
        Args:
            texts_with_scores: 输入文本列表，格式为[(文本, 原始得分, 时间戳), ...]
            origin: 衰减起点，'NOW'表示当前时间，或格式为'ND'表示N天前
            scale: 衰减时间尺度，格式为'ND'表示以N天为衰减单位
            decay: 衰减缓冲范围（天），默认0.5天
            weight: 权重因子，默认1
            output_format: 输出格式，'factor'返回衰减因子，'resorted_matches'返回排序后的结果
            
        Returns:
            根据output_format返回衰减因子列表或排序后的结果列表

        """
        logger.info("开始基于时间衰减进行检索结果增强")
        
        # 验证输入格式
        if not texts_with_scores:
            return []
            
        first_tuple = texts_with_scores[0]
        if not isinstance(first_tuple, tuple) or len(first_tuple) != 3:
            raise ValueError("输入列表中的元组格式应为(文本, 得分, 时间戳)")
        
        try:
            texts = [i[0] for i in texts_with_scores]
            scores = [float(i[1]) for i in texts_with_scores]
            timestamps = [float(i[2]) for i in texts_with_scores]
        except Exception as e:
            raise TypeError("输入数据中的得分和时间戳必须为数值类型") from e
        
        # 解析衰退起点
        origin = origin.lower()
        
        try:
            if origin in ("now", "0d"):
                origin = time.time()
            else:
                days = int(origin[:-1])
                origin = time.time() - days * 3600 * 24
        except Exception as e:
            logger.error(f"时间原点解析错误: {str(e)}，原点设置为当前时间")
            origin = time.time()
        
        # 解析衰退范围
        try:
            scale = scale.lower()
            scale = int(scale[:-1])
        except Exception as e:
            logger.error(f"时间尺度解析错误: {str(e)}，尺度设置为30天")
            scale = 30
            
        # 缓冲范围
        offset = decay
        
        # 计算与原点的时间差（天）
        day_diffs = [max(0, (origin - v) / 3600 / 24) for v in timestamps]
        day_diffs = np.array(day_diffs, dtype=float)
        distances = np.abs(day_diffs)
            
        # 处理offset缓冲范围
        within_offset = distances <= offset
        distances[within_offset] = 0
             
        # 高斯衰退计算
        decay_factors = np.exp(-(distances ** 2) / (2 * (scale ** 2))) * weight
        decay_factors[within_offset] = 1.0

        decay_scores = np.array(scores, dtype=float) * decay_factors 
        
        # 返回结果
        if output_format == 'resorted_matches':
            # 按调整后的score降序排序后输出
            results = [(a, b, c) for a, b, c in zip(texts, scores, decay_scores)]
            results.sort(key=lambda x: x[2], reverse=True)
            return results
            
        else:
            # 返回加工后的因子
            return decay_factors.tolist()
        

    def enhance_by_multi_factors(self,
                                 texts_with_scores: List[Tuple[str, float, float]],
                                 formu: str = 'product',
                                 standards: bool = True,
                                 boost_limit: float = 10,
                                 boost_limit_type: str = 'mult',
                                 output_format: str = 'factor'
                                 ) -> Union[List[Tuple[str, float]], List[float]]:
        """
        通过多因子加权模型调整检索结果得分
        
        Args:
            texts_with_scores: 输入文本列表，格式为[(文本, 因子1, 因子2, ...), ...]
            formu: 聚合公式，'product'为乘积，'sum'为求和，默认'product'
            standards: 是否进行最小-最大标准化，默认True
            boost_limit: 增强限制值
            boost_limit_type: 限制类型，'mult'为原始得分倍数限制，'abs'为绝对数值限制
            output_format: 输出格式，'factor'返回调整因子，'resorted_matches'返回排序后的结果
            
        Returns:
            根据output_format返回调整因子列表或排序后的结果列表
 
        """
        logger.info("开始基于多因子进行检索结果增强")
        
        if not texts_with_scores:
            return []
        
        # 验证输入格式
        first_tuple = texts_with_scores[0]
        if not isinstance(first_tuple, tuple) or len(first_tuple) < 2:
            raise ValueError("输入列表中的元组格式不正确")
        
        if not isinstance(first_tuple[0], str):
            raise ValueError("元组的第一个元素必须是字符串")
        
        # 提取所有键和数值
        keys = []
        value_arrays = []
        raw_scores = []
        
        for tpl in texts_with_scores:
            if not isinstance(tpl, tuple) or len(tpl) != len(first_tuple):
                raise ValueError("所有元组的长度必须一致")
            
            key = tpl[0]
            values = list(tpl[1:])
            raw_score = tpl[1]  # 默认第一个因子为原始得分
            
            # 验证数值元素类型
            for val in values:
                if not isinstance(val, (int, float)):
                    raise TypeError(f"数值元素必须是数字，但得到了 {type(val)}")
            
            keys.append(key)
            value_arrays.append(values)
            raw_scores.append(raw_score)
            
        # 转换为NumPy数组
        value_matrix = np.array(value_arrays, dtype=np.float64)
        
        if formu == 'product':
            # 使用NumPy的prod函数计算每一行的乘积
            products = np.prod(value_matrix, axis=1)
        else:
            products = np.sum(value_matrix, axis=1)
        
        # 限制增强
        if boost_limit_type == 'mult':
            products = np.where(products >= np.array(raw_scores, dtype=float) * boost_limit, 
                                np.array(raw_scores, dtype=float) * boost_limit, 
                                products)
        elif boost_limit_type == 'abs':
            products = np.where(products >= boost_limit, boost_limit, products)    
        
        # 标准化
        if standards:
            products = self._min_max_standard(products)

        # 返回结果
        if output_format == 'resorted_matches':
            # 按调整后的score降序排序后输出
            results = [(key, product) for key, product in zip(keys, products)]
            results.sort(key=lambda x: x[1], reverse=True)  
            return results
            
        else:
            # 返回加工后的因子
            return products.tolist()



    
if __name__ == "__main__":
    
    test = RetrievalEnhance()
    
    # 文本列表及原始相关性得分
    texts_with_scores = [
        ("如何申请信用卡才能快速批下来？", 1.8),
        ("信用卡申请的基本条件", 1.6),
        ("批下来的信用卡额度一般是多少？", 1.5),
        ("申请贷款的流程和注意事项", 1.4),
        ("银行理财产品介绍", 1.2)
    ]
    
    # 关键词权重
    keywords_with_weights = [
        ('批下来', 2.1),
        ('信用卡', 1.6),
        ('申请', 1.2)
    ]
    
    print(
    test.enhance_by_keywords(
        texts_with_scores,
        keywords_with_weights,
        output_format = 'factor' 
        ))

    print(
    test.enhance_by_keywords(
        texts_with_scores,
        keywords_with_weights,
        output_format = 'resorted_matches' 
        ))
    
    
    texts_with_scores = [
        ("如何申请信用卡才能快速批下来？", 1.8, 2),
        ("信用卡申请的基本条件", 1.6, 3),
        ("批下来的信用卡额度一般是多少？", 1.5, 5),
        ("申请贷款的流程和注意事项", 1.4, 1),
        ("银行理财产品介绍", 1.2, 3)
    ]
    
    print(
    test.enhance_by_customer_factors(
        texts_with_scores,
        modifier = 'log1p',
        weight = 2,
        output_format = 'factor' 
        ))
    
    print(
    test.enhance_by_customer_factors(
        texts_with_scores,
        modifier = 'log1p',
        weight = 1,
        output_format = 'resorted_matches' 
        ))

    texts_with_scores = [
        ("如何申请信用卡才能快速批下来？", 1.8, 1751355977), # -0
        ("信用卡申请的基本条件", 1.6, 1750146377),         # -14
        ("批下来的信用卡额度一般是多少？", 1.5, 1751096777), # -3
        ("申请贷款的流程和注意事项", 1.4, 1748763977),      # -21
        ("银行理财产品介绍", 1.2, 1743579977)              # -90
    ]
    
    print(
    test.enhance_by_gauss_decay(texts_with_scores, # (text, score, timestamps)
                                origin = '15d',    # 衰减起点 【NOW 7D ...】,
                                scale = '30D',     # 衰减速率 【30D ...】
                                decay = 0.5,       # 衰减度
                                weight = 1,        # 缩放因子
                                output_format = 'resorted_matches' # resorted_matches
                                ))


    texts_with_scores = [
        ("如何申请信用卡才能快速批下来？", 1.8, 3, 0.7, 1 ,2), 
        ("信用卡申请的基本条件",         1.6, 0.9 ,1.2, 5, 3), 
        ("批下来的信用卡额度一般是多少？", 1.5, 3.9 ,2.2, 1, 4),
        ("申请贷款的流程和注意事项",      1.4, 1.9 ,0.2, 1, 3), 
        ("银行理财产品介绍",            1.2, 0.9 ,1.2, 5, 1) 
    ]
    
    print(
    test.enhance_by_multi_factors(texts_with_scores,
                                 formu = 'product',    # sum
                                 standards = True,
                                 boost_limit = 10,
                                 boost_limit_type = None, # abs
                                 output_format = 'resorted_matches'   # resorted_matches
                                 ))

    






