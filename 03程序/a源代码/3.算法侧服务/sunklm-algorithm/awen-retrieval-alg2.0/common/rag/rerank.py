from __future__ import annotations
from langchain.pydantic_v1 import Extra
from sentence_transformers import CrossEncoder
from common.conf.ServerConfig import RERANK_MODEL_DICT, IF_RERANK_API, RERANK_API_DICT, DEFAULT_RERANK,DEVICE
from typing import List
from common.utils.utils import api_call
from common.utils.log_utils import logger
import time
logger = logger.bind(name="retrieval")

import torch
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

class AwenRerank:
    """
    Description:接入不同的重排序模型，然后对候选集进行重排序
    Args:
        model_path:重排序模型路径

    接入rerank模型服务时间为：20241015，该服务未接入宁波9月版代码
    """

    def __init__(self,
                 model_name: str=None,
                 model_path: str=None):
        # #如果使用外部的重排模型服务：
        if not IF_RERANK_API:
            # 如果存在model_name
            if model_name:
                if model_name in RERANK_MODEL_DICT:
                    model_path = RERANK_MODEL_DICT[model_name]
                else:
                    raise KeyError(f"未提供{model_name}模型，请联系算法人员")
            else:
                if model_path:
                    model_path = model_path
                else:
                    model_path = RERANK_MODEL_DICT[DEFAULT_RERANK]
            logger.info(f"rerank模型路径：{model_path}")
            if model_path.split("/")[-1] in ["jina-reranker-v2-base-multilingual","MiniCPM-Reranker"]:
                from transformers import AutoModelForSequenceClassification
                self.rerank_model = AutoModelForSequenceClassification.from_pretrained(
                    model_path,
                    torch_dtype="auto",
                    trust_remote_code=True,
                )
                self.rerank_model.to(device)  # or 'cpu' if no GPU is available
                self.rerank_model.eval()
            else:
                self.rerank_model = CrossEncoder(model_name = model_path,device=device)
            logger.info(f"rerank模型导入成功!!!")
        else:
            if model_name:
                if model_name in RERANK_MODEL_DICT:
                    self.model_name = model_name
                    logger.info(f"调用{model_name}rerank模型模型服务！！")
                else:
                    raise KeyError(f"未提供{model_name}模型服务，请联系算法人员")
            else:
                self.model_name = DEFAULT_RERANK
                logger.info(f"调用{DEFAULT_RERANK}rerank模型模型服务！！")



    def rerank(self, query:str, docs:List):
        """
        Description:rerank执行模块
        Args：
            query：用户问题
            docs：候选集片段
        Return:
            经过重排后的候选集列表
        """
        model_inputs = [[query, doc] for doc in docs]
        scores = self.rerank_model.predict(model_inputs)
        results = sorted(enumerate(scores), key=lambda x: x[1], reverse=True)
        return results

    def _rerank(self, query:str, docs:List):
        """
        Description:rerank执行模块
        Args：
            query：用户问题
            docs：候选集片段
        Return:
            经过重排后的候选集列表
        """
        inputs = {
                "model": RERANK_API_DICT[self.model_name]["model"],
                "documents": docs,
                "query": query
            }
        scores_dict = api_call(RERANK_API_DICT[self.model_name]["url"], inputs)["results"]
        scores = [item["relevance_score"] for item in scores_dict]
        results = sorted(enumerate(scores), key=lambda x: x[1], reverse=True)
        return results

    class Config:
        """Configuration for this pydantic object."""

        extra = Extra.forbid
        arbitrary_types_allowed = True

    def compress_documents(
        self,
        documents: list,
        query: str,
        top_n: int
    ) -> list:
        """
        Description:rerank结果输出
        Args：
            query：用户问题
            documents：候选集片段
            top_n：返回的top_k的片段
        Return:
            经过重排后的候选集文本列表
        """
        t0 = time.time()
        if len(documents) == 0:  # to avoid empty api call
            return []
        doc_list = list(documents)
        _docs = [d["splitText"] for d in doc_list]
        if IF_RERANK_API:
            results = self._rerank(query, _docs)
            logger.info(f"[RAG-文本检索算法] rerank方式：调用rerank_api")
        else:
            results = self.rerank(query, _docs)
            logger.info(f"[RAG-文本检索算法] rerank方式：调用本地rerank模型")
        final_results = []
        for r in results:
            doc = doc_list[r[0]]
            doc['score'] = round(r[1], 2)
            final_results.append(doc)
        #print(f"重排结束，结果为：{final_results}")
        t1 = round(time.time()-t0, 2)
        logger.info(f"[RAG-文本检索算法]  rerank耗时 {t1}")

        return final_results[:top_n]



if __name__=="__main__":
    # Example query and documents
    query = "流动性风险管理有哪些需求"
    documents = [{'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819079, 'sliceSource': 'ES'}, 'splitText': '监管类报表能够满足监管要求，辅助内部管理，支撑资产负债表的全面管理，主要包括银保监会要求的流动性、利率风险、汇率风险报表等，与监管要求严格保持一致。具体报表体系详见下方《ALM报表体系》。流动性指标是聚焦于流动性某个方面的比例/金额指标，反映在某一时点银行是否具有充足的流动性。流动性风险主要管理指标包括监管监测指标、内部流动性风险管理指标。其中监管监测指标需要严格按照监管文件要求计算，内部流动性风险管理指标则按照内部管理要求进行计算。这些指标从不同角度揭示当前流动性风险管理情况，通过有效监测这些指标实现流动性风险的合理管控。针对ALM标准化需求，本模块支持五个监管指标和八个监测指标：\n流动性风险限额管理\n风险限额是指按照一定计量方法对风险指标设定上/下限值，该值代表了银行在特定风险偏好下对某类指标所能容忍的最大风险。当各类风险敞口位于限额之下，则说明目前商业银行各项运营业务运行平稳，风险基本可控；当风险敞口一旦超过限额，则说明风险正在不断上升，此时应立即采取相应措施，在损失发生之前将风险敞口缩减至可控范围。', 'score': 0.8838158752506086}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819075, 'sliceSource': 'ES'}, 'splitText': '系统在明确分析方式和分析指标的基础上，分析口径从币种、机构、业务条线、期限（原始期限、剩余期限）、客户类型等维度对专项或组合分析，自由选择分析的时间段进行同比、环比分析等，从而全面掌握资产负债的总体情况。系统支持多层级粒度及维度分析资产负债结构，并呈现指定区间的变化趋势，包括各法人、各机构、科目、产品的资产负债规模及趋势变动情况、资产负债规模和利率变动趋势、资产负债盈利性及变动情况进行分析。流动性风险管理\n流动性风险管理总体要求\n流动性风险计量是流动性风险管理的前提。流动性风险的计量又分为静态计量、动态计量，两种计量方法互为补充，综合运用这两种计量方法才能全面衡量流动性风险。流动性风险静态计量主要是通过静态缺口、各类流动性指标，反映某个时点的流动性水平。通常这些流动性指标反映的是流动性风险的某个方面。在实际应用过程中需要综合运用、全面分析这些指标，才能准确定位流动性风险情况。', 'score': 0.806992553118207}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819077, 'sliceSource': 'ES'}, 'splitText': '流动性缺口反映潜在的流动性风险，如果缺口为正，意味着一定时期内到期资产大于到期负债，具有充足的现金流头寸，能够满足流动性需求；如果缺口为负，则表明流动性出现了短缺，需要通过同业市场拆借、动用流动性储备或变现流动性资产等方式来弥补资金缺口。结合流动性管理需要，在观测流动性缺口时，从满足监管角度出发需要计算G21流动性缺口及相应时间段的缺口率；从内部管理角度出发需要计算各条线的流动性缺口，包括司库、金融市场部、分行、其他，定位各条线面临的流动性风险。针对流动性缺口报表详见2.2.4《ALM报表体系》中《流动性缺口表》。流动性风险动态计量\n银行的业务是一个持续发展的过程，所以流动性风险也是在不断变化的，单纯的静态计量是某一个时点的情况反映，如静态计量假设客户正常还取款，而实际情况客户贷款会违约，定期存款会提前支取，这些客户行为都会使未来的现金流会发生变化，因此需要分析客户行为对现金流的影响。同时银行是持续经营的，业务会持续发展，因此需要参考业务经营计划考虑新业务，最终实现流动性风险动态计量。', 'score': 0.7954986598667345}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372343013407, 'sliceSource': 'ES'}, 'splitText': '数据日期：即以哪一天的数据为基础进行模拟。情景集合：下拉选择“情景集合”中已定义的情景集合，一次模拟仅支持一个情景集合（一个情景集合可包括多个情景组合），明确业务假设和市场假设。模拟周期；即模拟未来多久时间的业务变化或利率变化，若是基于压力测试的模拟则通常是30天，若是基于业务预算的模拟则通常是1年，可根据需要灵活选择。观察日期：是指模拟结果报表的日期，即在模拟周期内定义某一天或某几天，则在相应的报表中可查询分析这些日期的流动性风险、利率风险、净利息收入等情况。举例来讲，针对业务预算情景模拟，设置模拟基期是2022/12/31，模拟周期是1年，观察日期是1年内各季度季末，即可以查看2023/3/31、2023/6/30、2023/9/30、2023/12/31这4个日期的报表结果，其余日期不能查看。', 'score': 0.7758489849956287}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819072, 'sliceSource': 'ES'}, 'splitText': '资产负债管理系统业务需求说明书信雅达科技股份有限公司2023年2月文档信息文档修改记录表目录1 引言\t61.1 编写目的\t61.2 术语释义\t61.3 参考资料\t61.4 功能列表\t71.5 相关文档\t92 业务概述\t102.1 组合管理\t102.2 流动性风险管理\t112.2.1 流动性风险管理总体要求\t112.2.2 流动性风险静态计量\t112.2.3 流动性风险动态计量\t122.2.4 流动性风险指标计量\t122.2.5 流动性风险限额管理\t152.2.6 优质流动性资产管理\t162.2.7 流动性风险压力测试\t162.3 银行账簿利率风险管理\t192.3.1 银行账簿利率风险管理总体要求\t192.3.2 重定价现金流计量\t202.3.3 银行账簿利率风险限额管理\t202.3.4 净利息收入计量\t212.3.5 久期与市值计量\t212.3.6 利率风险压力测试\t222.4 汇率风险管理\t252.4.1 汇率风险管理总体要求\t25', 'score': 0.7528294109251238}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819076, 'sliceSource': 'ES'}, 'splitText': '流动性风险动态计量则是在现有业务基础上，考虑客户行为、新业务、利率变动等因素对流动性的影响，模拟未来一段时间内的流动性情况，以便于进行前瞻性流动性风险管理。通过流动性风险计量，明确流动性风险状况。在此基础上要做好流动性风险的监测与控制，即结合流动性风险管理偏好，设置配合的限额管理体系，持续监测风险；配置层次清晰的流动性储备资产，将流动性风险控制在承受范围内。同时还需开展流动性风险压力测试，模拟在各种极端小概率冲击情景下银行的流动性困境，以对银行流动性管理体系的脆弱性做出评价和判断，确定银行是否有能力抵御这种极端情况，评估现有的流动性储备资产是否能够有效应对，并根据评估结果及时调整流动性风险管理策略。流动性风险静态计量\n流动性风险静态计量是基于存量业务合同期限计量的未来现金流分布进行的现金流测算，分为对于流动性缺口计量以及流动性指标计量。', 'score': 0.7210060707674106}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372343013417, 'sliceSource': 'ES'}, 'splitText': '动态模拟\t情景配置\t情景配置\t汇率情景\t汇率情景方面，变化方式支持比例、点差和目标值三种方式；可自主定义不同生效日期。动态模拟\t情景配置\t情景配置\t情景集合\t情景集合功能即将定义好的业务情景和市场情景进行组合。动态模拟\t模拟执行\n\t模拟执行\n\t模拟执行\n\t定义一个模拟，同时明确相关要素，包括模拟对应的基期、模拟周期、观察日期、情景集、数据集。指标名称\t计算逻辑\t指标释义\t限额\t\n监管指标\t流动性覆盖率\t合格优质流动性资产÷未来30天现金净流出量\t确保商业银行具有充足的合格优质流动性资产，能够在规定的流动性压力情景下，通过变现这些资产满足未来至少30天的流动性需求。不低于100%\n监管指标\t流动性比例\t流动性资产余额÷流动性负债余额\t衡量三十天内到期资产和到期负债的比例\t不低于25%\n监管指标\t流动性匹配率\t加权资金来源÷加权资金运用\t反映填报机构加权资金来源与加权资金运用的比例，旨在引导商业银行合理配置长期稳定负债、高流动性或短期资产，避免过度依赖短期资金支持长期业务发展，提高流动性风险抵御能力。期限越长权重越接近1。', 'score': 0.6800257796246159}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819080, 'sliceSource': 'ES'}, 'splitText': '流动性风险实施限额管理，根据本行的业务规模、性质、复杂程度、流动性风险偏好和外部市场发展变化情况，设定流动性风险限额值。业务描述\n系统支持对已纳入报表统计的流动性风险指标分别设置预警限额值、管理限额值及监管标准值，并根据指标要求区分上限及下限，根据既定的限额进行风险预警。风险预警包括两个层面要求，一方面可以针对重点关注指标在首页界面以“仪表盘”形式直观展示，以及时获知指标变化情况；另一方面在《指标与限额执行情况》中根据限额预警要求进行风险提示。功能描述\n关于流动性风险指标的预警，系统支持分级预警机制，预警规则如下：\n对于处于正常状态且未突破预警值的，标注“正常态”。对于突破蓝色/黄色/橙色预警值的，标注“蓝色/黄色/橙色预警态”。以上预警等级结果在《指标与限额执行情况》中体现。优质流动性资产管理\n根据监管对合格优质流动性资产的定义和要求（合格优质流动性资产是指在流动性覆盖率所设定的压力情景下，能够通过出售或抵（质）押方式，在无损失或极小损失的情况下在金融市场快速变现的各类资产。合格优质流动性资产应当具有一定基本特征，如属于无变现障碍资产、风险低、易于定价等，并满足相关操作性要求，如商业银行应当具有变现合格优质流动性资产的政策、程序和系统等），系统支持对流动性储备进行分层和分类，一方面根据分层分类的结果统计对应的数据，生成对应报表《优质流动性资产明细表》，另一方面通过《优质流动性流动性资产情况表》监测分析优质流动性资产的规模和结果，并优化流动性储备资产规模和结构。具体报表参考2.2.4中《ALM报表体系》。', 'score': 0.6800205898176674}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372338819078, 'sliceSource': 'ES'}, 'splitText': '流动性风险指标计量\nALCO类报表，设计的ALCO报表的主要特点是从资管负债管理委员会视角，关注整体资产负债规模走势及关键指标情况。具体包括年度资源配置执行情况、监管指标与限额执行情况、指标历史统计、净利息收入三因素分析。内容涵盖当前资产负债业务规模、流动性风险管理情况、净利息收入情况等。管理类报表，主要是ALCO类报表的细化，从组合管理和风险管理维度，全方位分析和管理资产负债表结构化风险、流动性风险和银行账簿利率风险。内容分为组合管理、流动性风险管理、银行帐簿利率风险管理三大部分，其中，组合管理类报表从规模、期限、利率、方面深入分析当前资产负债不同业务的管理现状。规模方面更多侧重于余额、日均余额的趋势变化，掌握业务的发展态势；期限方面更多侧重于业务的原始期限结构分析，并支持新增业务的期限结构分析；利率方面更多侧重于从机构、行业、客户、产品等多维度观测外部定价水平，以分析本行存贷款盈利情况，从而指导业务发展；流动性风险管理类报表满足对于全行流动性缺口进行监测，能够根据监管要求设置压力测试参数，以G\t21为基础进行压力，反映监管要求下的流动性风险压力测试情况，全面了解在不同压力测试参数下资产负债整体情况，并且能够通过报表展示当前可用的优质流动性资产明细信息，持续监测优质流动性储备资产结果及规模情况以及融资业务种类情况，能够有效评价流动性安全，并在此基础上优化资产负债组合，实现在风险可控前提下的收益最大化；银行帐簿利率风险类报表包括静态计算和动态模拟全行重定价缺口，在应用自定义业务或者市场情景后，对于未来净利息收入进行模拟分析，以及测算各个项目的市值与久期。', 'score': 0.****************}, {'from': {'fileId': 1834074820465963008, 'fileName': '04_大模型_ALM业务需求说明书（标准版）V1.0.docx', 'sliceId': 1834073372343013413, 'sliceSource': 'ES'}, 'splitText': 'ALCO\tAsset-Liability Committee（资产负债管理委员会）LRM\tLiquidity Risk Management（流动性风险管理）IRRBB\tInterest Rate Risk in the Banking Book(银行账簿利率风险管理)NII\tNet Interest Income （净利息收入 ）EVE\tEconomic Value of Equity（经济价值）报表管理\t报表管理\t报表集\t该模块维护所有报表所对应的报表结构，时间段和报表目的。补录管理\t补录管理\t导入模板\t该模块用于维护补录模板。日志管理\t日志管理\t操作日志\t该模块用于记录用户在系统中进行的增、删、改、查，导入和导出操作，以及登录、登出操作。业务参数\t维度管理\t维度管理\t维度定义\t维度一方面体现在报表分析方面，可将一个维度或多个维度组合形成报表结构，可据此实现报表的多维度分析；另一方体现在动态模拟方面，设置预算COA，假定业务量变化、利率变化、客户行为模型等\n业务参数\t维度管理\t维度管理\t维度结构\t通过维度结构形成全行科目树、产品树等，后续可直接应用这些维度结构。', 'score': 0.6718634644087349}]
    Arerank = AwenRerank()
   # result = Arerank.bge_rerank(query,documents,5)
    final_results = Arerank.compress_documents(documents, query, 1)
    print(final_results)






