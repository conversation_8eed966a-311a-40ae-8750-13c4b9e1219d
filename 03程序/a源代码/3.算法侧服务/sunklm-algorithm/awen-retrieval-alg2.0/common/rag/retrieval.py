#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from common.utils.txt_algorithm import TxtAlgorithm
from common.rag.rerank import AwenRerank
from common.rag.retrieval_enhance import RetrievalEnhance
from common.utils.log_utils import logger, return_dict
from common.conf.awen_schema import ReturnCode

import traceback
import json


class Retrival(
        
        AwenRerank,        # 重排序模型
        TxtAlgorithm,      # 文本处理方法
        RetrievalEnhance   # 检索增强方法
        
        ):
    """
    文本检索 - 算法类

    """
    
    def __init__(self, dict_directory, DEFAULT_RERANK, RERANK_MODEL_DICT):

        # 检索增强方法
        RetrievalEnhance.__init__(self)
        # 文本处理算法
        TxtAlgorithm.__init__(self, dict_directory)
        # 重排序模型初始化
        AwenRerank.__init__(self,
                            model_name = DEFAULT_RERANK, 
                            model_path = RERANK_MODEL_DICT[DEFAULT_RERANK]
                            )
    
        
    def _get_vector_alg(self, collections):
        """
        Description: 获取向量化算法（BGE或M3E）
        Args:
             collection_name （str）: collection名称，需要从数据库通过collection名称获取对应的向量化方法
        Returns:
             向量化方法
            
        """
        vector_alg_dict = {}
        
        for collection in collections:
            try:
                logger.info(f"[文本检索服务] 获取向量化算法 {collection}")
                        
                with self.mysql.cursor() as cursor:
                    # 根据知识空间名称查询向量化配置编号
                    sql = "SELECT vector_id FROM xj_kg_space_info_tb WHERE kg_code = '{0}' LIMIT 1".format(collection)
                    cursor.execute(sql)
                    vector_id = cursor.fetchone()[0]
                
                    # 根据向量化配置编号查询向量化代码
                    sql = "SELECT vector_code FROM xj_kg_vector_conf_tb WHERE vector_id = '{0}' LIMIT 1".format(vector_id)
                    cursor.execute(sql)
                    vector_code = cursor.fetchone()[0] 
                    
                logger.info(f"[文本检索服务] {collection} 使用的向量模型 {vector_code}")
                vector_alg_dict[collection] = vector_code
        
            except Exception as e:
                logger.error(traceback.print_exc())
                logger.error('[文本检索服务]  数据库读取向量化算法失败。'+ str(e))
                vector_alg_dict[collection] = ReturnCode.ERROR
                
        return vector_alg_dict


    def _filter_by_threshold(self, result, threshold):
        """
        根据相似度分数对检索结果进行过滤
        Args:
            results (list): 检索结果列表
            threshold (float): 相似度分数阈值，默认为0.5
        Returns:
            list: 过滤后的结果列表
        """
        if not result:
            return result
        
        logger.info(f"[文本检索服务] 过滤前的结果数量: {len(result)}")
        
        filtered_results = []
        threshold = float(threshold)
        
        for item in result:
            score = float(item.get('score', 0))
            
            if score >= threshold:
                filtered_results.append(item)
                
        logger.info(f"[文本检索服务] 过滤后的结果数量: {len(filtered_results)}")
        
        return filtered_results
    

    def _compress_search_results(self, result, data_type, alg_type='bm25'):
        """
        """
        
        if data_type == 'FILE':
        
            for i in result:
                if alg_type == 'bm25':
                    i['splitText'] = self.abstract_by_bm25(i['splitText']) 
                elif alg_type == 'textrank':
                    i['splitText'] = self.abstract_by_text_rank(i['splitText']) 
                elif alg_type == 'llm':
                    i['splitText'] = self.abstract_by_llm(i['splitText']) 
        
        if data_type == 'FAQ':

            for i in result:
                if alg_type == 'bm25':
                    i['answer'] = self.abstract_by_bm25(i['answer']) 
                elif alg_type == 'textrank':
                    i['answer'] = self.abstract_by_text_rank(i['answer']) 
                elif alg_type == 'llm':
                    i['answer'] = self.abstract_by_llm(i['answer']) 
                    
        return result
        
    
    def _search_query_denosie(self, query):
        """
        文本降噪
        """
        # 测试方法
        return self.abstract_by_bm25(query)
        
    
    def _retrival_enhance_by_weighted_keywords(self, query, result, data_type, if_standard=False):
        """
        文本增强
        """
        num_result = len(result)
        
        if num_result < 1:
            return result
        
        # 开始检索增强
        # 1、准备数据（输入文本列表，格式为[(文本, 原始得分), ...]）
        if data_type == 'FILE':
            texts_with_scores = [(item.get('splitText'), item.get('score')) for item in result]
        elif data_type == 'FAQ':
            texts_with_scores = [(item.get('question') + item.get('answer'), item.get('score')) for item in result]
        
        # 2、生成关键词权重
        keywords_with_weights = self.keywords_by_bm25(query, topK=3, output_format='weight') 
        
        # 3、检索增强
        enhanced_score = self.enhance_by_keywords(
                                        texts_with_scores,
                                        keywords_with_weights,
                                        min_keyword_length = 2,
                                        output_format = 'factor'
                                        )
        # 4、结果标准化
        if if_standard:
            enhanced_score = self._min_max_standard(enhanced_score)
        
        # 5、修改原始score
        if len(result) == len(enhanced_score):
            for i in range(len(result)):
                result[i]['score'] = enhanced_score[i]
            return result
        
        else:
            logger.error("检索增强出现异常")
            return result
        

    def _retrie_multi_field_search(self, CONFIG, fields, output_fields):

        if CONFIG.vector_alg_dict[CONFIG.collection] == ReturnCode.ERROR:
            return return_dict(code=ReturnCode.ERROR, data='', message=f'{CONFIG.collection} 未在Mysql数据库中配置.')
        
        # idx
        idx = CONFIG.match_slice_id if CONFIG.dataType == 'FILE' else CONFIG.match_id_faq
        # 检索模版
        dataType = CONFIG.dataType
        # 向量模型
        emb_model_name = 'API' if CONFIG.IS_API else CONFIG.vector_alg_dict[CONFIG.collection]
        # 检索参数
        param = CONFIG.milvusParams
        # 原始结果数量
        if CONFIG.IF_RETRIVAL_ENHANCE or CONFIG.IF_RERANK:
            top_k_emb = CONFIG.topK * CONFIG.EMBEDDING_TOPN
                        
        result = self.multi_field_search(
                        collection_name = CONFIG.collection,    # 集合
                        partitions = CONFIG.partition_list,     # 分区列表
                        query = CONFIG.query,                   # 问题
                        top_k = top_k_emb,               # 返回数量
                        fields = fields,                 # 待检索向量字段
                        output_fields = output_fields,   # 输出字段
                        idx = idx,                       # 唯一标识字段
                        template = dataType,             # 检索模版
                        emb_model_name = emb_model_name, # embedding算法名称
                        param = param                    # 检索参数
                        )
                        
        result = json.loads(result)
                
        return result

        
    def _retrie_multi_match_search(self, CONFIG, fields, output_fields):

        # 检索模版
        dataType = CONFIG.dataType
        # 原始结果数量
        if CONFIG.IF_RETRIVAL_ENHANCE or CONFIG.IF_RERANK:
            top_k_es = CONFIG.topK * CONFIG.ES_TOPN
                            
        result = self.multi_match_search(
                        es_index = CONFIG.partition_list, # 索引列表
                        query = CONFIG.query,             # 问题
                        top_k = top_k_es,                 # top k
                        fields = fields,                  # 匹配字段列表
                        output_fields =output_fields,     # 输出字段
                        template = dataType               # 检索模版
                        )

        result = json.loads(result)   
        
        return result


    def _multi_hybrid_search(self, query, milvus_parms, es_parms, top_k, weight=0.4, overlap_boost=1.2):
        """
        混合检索
        
        """                                
        # ES 检索
        es_res = self.multi_match_search(
            es_index      = es_parms['partition_list'],
            query         = es_parms['query'],
            top_k         = es_parms['top_k'],
            fields        = es_parms['fields'],
            output_fields = es_parms['output_fields'],
            template      = es_parms['template']
            )

        # Milvus 检索
        milvus_res = self.multi_field_search(
            collection_name = milvus_parms['collection'],
            partitions = milvus_parms['partition_list'],
            query  = milvus_parms['query'],
            top_k  = milvus_parms['top_k'],
            idx    = milvus_parms['idx'],
            fields = milvus_parms['fields'],
            output_fields = milvus_parms['output_fields'],
            template = milvus_parms['template'],
            emb_model_name = milvus_parms['emb_model_name'],
            param  = milvus_parms['param'],
            )
        
        # 检查返回结果
        if json.loads(es_res)['retCode'] == ReturnCode.ERROR:
            raise ValueError(json.loads(es_res)['retMsg'])
        
        if json.loads(milvus_res)['retCode'] == ReturnCode.ERROR:
            raise ValueError(json.loads(es_res)['retMsg'])
            
        es_matches = json.loads(es_res)['retMap']
        vector_matches = json.loads(milvus_res)['retMap']
        
        # 开始混合检索结果
        vector_weight = 1 - weight  # 确保权重和为1
        combined_results = {}
        
        # 构建slice_id到结果的映射
        es_dict = {item['from']['sliceId']: item for item in es_matches}
        vector_dict = {item['from']['sliceId']: item for item in vector_matches}
        # 处理所有唯一的slice_id
        all_slice_ids = set(list(es_dict.keys()) + list(vector_dict.keys()))
        
        for slice_id in all_slice_ids:
            es_result = es_dict.get(slice_id)
            vector_result = vector_dict.get(slice_id)
            
            if es_result and vector_result:
                # 重叠结果 - 两种方法都检索到
                es_score = es_result['score']
                vector_score = vector_result['score']
                
                # 计算加权得分，并应用重叠提升
                combined_score = (weight * es_score + vector_weight * vector_score) * overlap_boost
                
                # 选择得分更丰富的结果作为基础
                base_result = es_result if es_score > vector_score else vector_result
                base_result = base_result.copy()  # 避免修改原始数据
                base_result['score'] = min(combined_score, 1.0)  # 确保不超过1.0
                
                combined_results[slice_id] = base_result
                
            elif es_result:
                # 仅ES检索到
                result = es_result.copy()
                result['score'] = result['score'] * weight
                combined_results[slice_id] = result
                
            elif vector_result:
                # 仅向量检索到
                result = vector_result.copy()
                result['score'] = result['score'] * vector_weight
                combined_results[slice_id] = result
        
        # 转换为列表并排序
        final_results = list(combined_results.values())
        final_results.sort(key=lambda x: x['score'], reverse=True)
        
        # 返回top_k结果
        return return_dict(code=ReturnCode.SUCESS, 
                           data=final_results[:top_k], 
                           message="sucess")
         
    
    def _retrie_multi_hybrid_search(self, CONFIG, es_fields, es_output_fields, milvus_fields, milvus_output_fields):

        if CONFIG.vector_alg_dict[CONFIG.collection] == ReturnCode.ERROR:
            return return_dict(code=ReturnCode.ERROR, data='', message=f'{CONFIG.collection} 未在Mysql数据库中配置.')
                
        milvus_parms = { 
            'collection' : CONFIG.collection,
            'partition_list' : CONFIG.partition_list,
            'query'  : CONFIG.query,
            'top_k'  : CONFIG.topK * CONFIG.EMBEDDING_TOPN if CONFIG.IF_RETRIVAL_ENHANCE or CONFIG.IF_RERANK else CONFIG.topK,
            'idx'    : CONFIG.match_slice_id if CONFIG.dataType == 'FILE' else CONFIG.match_id_faq,
            'fields' : milvus_fields,
            'output_fields' : milvus_output_fields,
            'template' : CONFIG.dataType,
            'emb_model_name' : 'API' if CONFIG.IS_API else CONFIG.vector_alg_dict[CONFIG.collection],
            'param' : CONFIG.milvusParams
            }
                        
        es_parms = {
            'partition_list' : CONFIG.partition_list,
            'query' : CONFIG.query,
            'top_k' : CONFIG.topK * CONFIG.ES_TOPN if CONFIG.IF_RETRIVAL_ENHANCE or CONFIG.IF_RERANK else CONFIG.topK,
            'fields' : es_fields,
            'output_fields' : es_output_fields,
            'template' : CONFIG.dataType
            }
        
        result = self._multi_hybrid_search(
                        query = CONFIG.query, 
                        milvus_parms= milvus_parms, 
                        es_parms = es_parms, 
                        top_k = CONFIG.topK,
                        weight = CONFIG.weight, 
                        overlap_boost=1.2
                        )
        
        result = json.loads(result)   
        
        return result













