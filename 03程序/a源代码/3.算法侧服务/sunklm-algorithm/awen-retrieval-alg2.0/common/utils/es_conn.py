#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from elasticsearch import Elasticsearch
from common.utils.log_utils import logger, return_dict
from common.conf.awen_schema import ReturnCode

logger = logger.bind(name="utils")


class EsClient:
    """
    提供 ES 文本检索方法
    
    1、修改原search方法，支持多字段检索功能；
    2、增加检索模版（通用模版 / 知识库模版 / 记忆体模版）
    
    """
    def __init__(self, ES_URL):
        """
        es引擎配置
        """
        self.client = Elasticsearch(ES_URL)
        
        logger.info(f"连接至 ES 数据库 {ES_URL}")
        
        # ES支持向量检索，可调用embedding模型API
        # if not IS_API:
        #     logger.info(f"调用向量化模型 {IS_API}")
        #     self.embedding_dict = LoadsEmbeddingModel().loads_model()


    def _query_body(self, query, top_k, match_text, match_weight=None):
        """
        创建ES查询语句（多字段检索）
        
        Args:
            query: str 检索文本
            top_k: int 查询结果最大返回数量
            match_text: list 目标字段 ['Col1','Col2','Col3']
            match_weight: dict 可对检索字段赋权重，{‘Col1’:3} 表示Col1字段的得分将放大3倍
        Returns:
            format后的ES查询语句
            
        Test:
            query = '测试问题'
            match_text = ['question','answer']
            match_weight = {'question': 3}
            top_k = 5
        """
        if not isinstance(match_weight, dict):
            match_weight = dict()
            
        # multi_match支持多字段检索
        query_body = {
            "query": {
                "multi_match": {
                    "query": query,
                    "fields": []
                    }
                },
            "size": top_k
            }
        
        # 添加目标字段
        fields = query_body['query']['multi_match']['fields']
        
        for text in match_text:
            # 获取字段对应的权重，默认为1（无权重）
            weight = match_weight.get(text, 1)
            
            if weight == 1:
                fields.append(text)
            else:
                fields.append(f"{text}^{weight}")
        
        return query_body


    def _res_format_rag(self, res, match_text, match_id, match_name):
        """
        ES 查询结果解析（知识库专用）
        
        """
        logger.info("[ES检索] ES检索解析 - 知识库")
        
        if isinstance(match_text, str):
            match_text = [match_text]
        
        result = list()
        for line in res['hits']['hits']:
            
            # 定义返回数据模版
            search_split = {
                "from": {"fileId": line['_source'][match_id], 
                         "fileName": line['_source'][match_name],
                         "sliceId": int(line['_id']), 
                         "sliceSource":"ES"
                         },
                # 知识库检索一般为单字段检索，match_text = ['slice']
                # 若果出现多字段情况，则将检索字段拼接输出
                "splitText": '\n'.join([line['_source'][i] for i in match_text]), 
                "score": line['_score']
                }
            # 结果保存
            result.append(search_split)
        
        # 排序  
        output = sorted(result, key=lambda k: k['score'], reverse=True)
        
        return output
                
    
    def multi_match_search(self, 
                           es_index, 
                           query, 
                           top_k, 
                           fields,
                           output_fields,
                           template = None,
                           match_weight=None,
                           **kwargs
                           ):
        """
        ES 多字段检索
        
        Args:
            es_index: list 索引列表
            query: str 检索文本
            top_k: int 查询结果最大返回数量
            match_text: list 目标字段 ['Col1','Col2','Col3']
            match_weight: dict 可对检索字段赋权重，{‘Col1’:3} 表示Col1字段的得分将放大3倍
            template: str 检索模版（ 'RAG' = 知识库, 'MEMO' = 记忆体, None = 通用 ）
        Returns:
            检索结果
            
        Test:
            es_index = 'memory_test_05'
            query = '信用卡申请条件'
            match_text = ['question','answer']
            match_weight = {'question': 3}
            top_k = 5
            template = 'MEMO'
        """
        
        try:
            logger.info(f"[ES检索] 索引 {es_index}")
            
            # 创建查询语句
            query_body = self._query_body(query, top_k, fields, match_weight)
            
            logger.info("[ES检索] ES检索开始")
            res = self.client.search(
                index=','.join(es_index),
                body=query_body
                )
            
            # 按 知识库 模版返回结果
            if template == 'FILE':
                
                match_id, match_name, match_text = output_fields
                  
                result = self._res_format_rag(res, match_text, match_id, match_name)
                logger.info(f"[ES检索] ES检索结果: 返回结果数量 {len(result)}")
                
                return return_dict(
                    code = ReturnCode.SUCESS, 
                    data = result, 
                    message="sucess")
            
        
        except Exception as e:
            return return_dict(code=ReturnCode.ERROR, data=[], message="es 检索异常"+ str(e))


                



if __name__ == "__main__":
       
    """
    import json 
    
    # 测试 记忆体
    ES_URL = "http://localhost:9200"
    test = EsClient(ES_URL)
    
    res = test.multi_match_search(
        es_index     = ['memory_test_05'],
        query        = '信用卡申请条件？以及审批时长？',
        match_text   = ['question','answer'],
        match_weight = {'question': 3},
        top_k        = 2,
        template     = 'MEMO'
        )
    json.loads(res)
    
    # 测试知识库
    ES_URL = "http://**********:9200"
    test = EsClient(ES_URL)
    
    res = test.multi_match_search(
        es_index     = ['1403'],
        query        = '信用卡申请条件？以及审批时长？',
        match_text   = ['slice'],
        top_k        = 2,
        template     = 'RAG',
        match_id     = 'file_id',
        match_name   = 'file_name'
        )
     
    json.loads(res)

    """















