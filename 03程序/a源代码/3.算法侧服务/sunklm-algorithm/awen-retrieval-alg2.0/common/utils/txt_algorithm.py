#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import jieba
import jieba.analyse
import math
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Union, Any
from common.utils.log_utils import logger
from common.llms.llm_chat import ModelAPI
from common.llms.prompt_template import ABSTRACT_TEMPLET_CN
from common.dict.dict_manager import JiebaDictManager

# 配置日志
logger = logger.bind(name="TextProcess")


class AbstractAlg():
    """
    摘要提取算法工具
    
    包含三种摘要提取方法：
    1. abstract_by_bm25: 使用BM25算法提取摘要
    2. abstract_by_text_rank: 使用TextRank算法提取摘要
    3. abstract_by_llm: 使用大模型提取摘要
    """
    
    def __init__(self):
        pass
    
    def _tokenize(self, text: str, cut_all=False) -> List[str]:
        """
        对文本进行分词处理
        
        Args:
            text: 待分词的文本
            cut_all: False = ['机器','学习']; True = ['机器学习','机器','学习']
            
        Returns:
            分词后的词语列表

        """
        try:
            return list(jieba.cut(text, cut_all))
        except Exception as e:
            logger.error(f"分词过程中发生异常: {str(e)}")
            return []
    
    def _bm25_fit(self, 
                  sentences: List[str], 
                  idf: Dict[str, float], 
                  avgdl: float
                  ) -> Tuple[Dict[str, float], float]:
        """
        训练BM25模型，计算IDF和平均句子长度
        
        Args:
            sentences: 句子列表，每个元素为字符串
            idf: 逆文档频率字典，将在方法中更新
            avgdl: 平均句子长度，将在方法中计算
            
        Returns:
            包含更新后的IDF字典和平均句子长度的元组
            
        """
        # 开始训练BM25模型
        if not sentences:
            raise ValueError("句子列表不能为空")
        
        try:
            # 对每个句子进行分词
            tokenized_sentences = [self._tokenize(sentence) for sentence in sentences]
            
            # 计算每个词在多少个句子中出现过
            doc_freq = defaultdict(int)
            for sentence in tokenized_sentences:
                unique_words = set(sentence)
                for word in unique_words:
                    doc_freq[word] += 1
            
            # 计算文档总数
            total_docs = len(sentences)
            
            # 计算每个词的逆文档频率(IDF)
            for word, freq in doc_freq.items():
                idf[word] = math.log((total_docs - freq + 0.5) / (freq + 0.5) + 1)
            
            # 计算平均句子长度
            if tokenized_sentences:
                avgdl = sum(len(sentence) for sentence in tokenized_sentences) / len(tokenized_sentences)
            else:
                avgdl = 1
                
            return idf, avgdl
        except Exception as e:
            logger.error(f"BM25模型训练过程中发生异常: {str(e)}")
            raise
    
    def _bm25_score_sentence(self, 
                             sentence: str, 
                             document_tokens: List[str], 
                             k1: float, 
                             b: float, 
                             idf: Dict[str, float], 
                             avgdl: float
                             ) -> float:
        """
        计算句子与文档的BM25相关性得分
        
        Args:
            sentence: 要评分的句子
            document_tokens: 整个文档的分词结果
            k1: BM25算法参数，控制词频饱和度
            b: BM25算法参数，控制文档长度影响
            idf: 逆文档频率字典
            avgdl: 平均句子长度
            
        Returns:
            句子的BM25得分
            
        """
        try:
            # 对句子进行分词
            sentence_tokens = self._tokenize(sentence)
            
            # 计算句子中每个词的词频
            sentence_freq = Counter(sentence_tokens)
            
            # 计算句子长度
            sentence_length = len(sentence_tokens)
            
            # 计算BM25得分
            score = 0.0
            for word in sentence_tokens:
                if word in idf and word in document_tokens:
                    # 计算词频因子
                    tf = sentence_freq[word]
                    tf_factor = (tf * (k1 + 1)) / (tf + k1 * (1 - b + b * sentence_length / avgdl))
                    
                    # 计算IDF因子
                    idf_factor = idf[word]
                    
                    # 累加得分
                    score += tf_factor * idf_factor
            
            return score
        except Exception as e:
            logger.error(f"计算BM25得分时发生异常: {str(e)}")
            return 0.0
    
    def abstract_by_bm25(self, 
                         text: str,
                         k1: float = 1.5, 
                         b: float = 0.7,
                         compression_ratio: float = 0.3, 
                         max_sentences: int = None
                         ) -> str:
        """
        使用BM25算法对输入文本进行摘要提取

        * 逆文档频率 (Inverse Document Frequency, IDF)：一个词在所有文档中出现的频率越高，其区分度越低，重要性越低
        * ./site-packages/jieba/analyse/idf.txt
        IsDF(w) = log((N - df(w) + 0.5) / (df(w) + 0.5) + 1)
        
        * 词频 (Term Frequency, TF)：一个词在文档中出现的频率越高，通常越重要
        TF(w) = f(w) / (f(w) + k1*(1 - b + b*(L/avgL)))
        
        BM25(w) = IDF(w) * TF(w)

        * N 是文档总数
        * df (w) 包含词 w 的短句数
        * f (w) 词 w 在当前短句中的频率
        * L 当前短句长度
        * avgL 平均短句长度
        * k1 可调参数
        * b  可调参数
        
        Args:
            text: 待处理的原始文本
            k1: BM25算法参数，控制词频饱和度，默认1.5
            b: BM25算法参数，控制文档长度影响，默认0.7
            compression_ratio: 压缩比例，0.3表示保留30%的句子，默认0.3
            max_sentences: 最大保留句子数，优先级高于compression_ratio，默认None
            
        Returns:
            提取的文本摘要
        """
        # logger.info("开始使用BM25算法提取摘要")
        if not text:
            logger.warning("输入文本为空，返回空摘要")
            return ""
        
        try:
            # 初始化参数
            idf = {}  # 逆文档频率(IDF)
            avgdl = 0  # 平均句子长度
            
            # 断句
            sentences = re.split(r'[。,，！!？?；;\s\n]', text)
            # 过滤空句
            sentences = [s for s in sentences if len(s) > 1]
            if not sentences:
                logger.warning("文本中未找到有效句子，返回空摘要")
                return ""
            
            # 分词
            document_tokens = self._tokenize(text)
            
            # 训练BM25模型
            idf, avgdl = self._bm25_fit(sentences, idf, avgdl)
            
            # 计算每个句子的得分
            scores = [(i, self._bm25_score_sentence(sentence, document_tokens, k1, b, idf, avgdl)) 
                     for i, sentence in enumerate(sentences)]
            
            # 根据得分排序
            scores.sort(key=lambda x: x[1], reverse=True)
            
            # 确定要保留的句子数量
            if max_sentences is not None:
                num_to_keep = min(max_sentences, len(sentences))
            else:
                num_to_keep = max(1, int(len(sentences) * compression_ratio))
            
            # 选择得分最高的句子的索引
            selected_indices = sorted([i for i, _ in scores[:num_to_keep]])
            
            # 构建压缩后的文本
            compressed_text = ''.join([sentences[i] + ' ' for i in selected_indices])
            
            return compressed_text
        except Exception as e:
            logger.error(f"BM25摘要提取过程中发生异常: {str(e)}")
            return ""
    
    def abstract_by_text_rank(self, 
                              text: str,
                              summary_length: int = 80,
                              d: float = 0.85,
                              max_iterations: int = 100,
                              min_delta: float = 1e-4
                              ) -> str:
        """
        使用TextRank算法对输入文本进行摘要提取
        
        Args:
            text: 待处理的原始文本
            summary_length: 目标摘要长度，默认80
            d: 阻尼系数，默认0.85
            max_iterations: 最大迭代次数，默认100
            min_delta: 收敛阈值，默认1e-4
            
        Returns:
            提取的文本摘要
        """
        # logger.info("开始使用TextRank算法提取摘要")
        try:
            # 1. 文本预处理：去除标点和特殊字符
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', ' ', text)
            
            # 2. 分词处理
            words = self._tokenize(text)
            words = [word for word in words if len(word) > 1]  # 过滤单字
            
            # 3. 构建词共现图 (滑动窗口法)
            window_size = 5
            graph = defaultdict(float)
            for i in range(len(words)):
                for j in range(i+1, min(i+window_size, len(words))):
                    word1, word2 = words[i], words[j]
                    graph[(word1, word2)] += 1
                    graph[(word2, word1)] += 1
            
            # 4. TextRank算法计算词权重
            word_weights = defaultdict(float)
            # 初始化权重
            for word in set(words):
                word_weights[word] = 1.0
        
            # 迭代计算权重
            for iteration in range(max_iterations):
                new_weights = word_weights.copy()
                graph_items = list(graph.items())
                
                for word in word_weights:
                    weight = 0
                    for (neighbor, _) in graph_items:
                        if neighbor == word:
                            for (_, co_occur_word) in graph_items:
                                if co_occur_word == word:
                                    continue
                                weight += graph.get((neighbor, co_occur_word), 0) * word_weights.get(co_occur_word, 0)
                    new_weights[word] = (1 - d) + d * (weight / len(word_weights) if len(word_weights) > 0 else 0)
                
                # 检查收敛性
                delta = sum(abs(new_weights.get(word, 0) - word_weights.get(word, 0)) for word in word_weights)
                if delta < min_delta:
                    break
                
                word_weights = new_weights
            
            # 5. 按权重排序并提取关键词
            sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
            keywords = [word for word, _ in sorted_words[:20]]  # 取前20个关键词
            
            # 6. 生成摘要：基于关键词匹配原文本句子
            sentences = re.split(r'[。,，！!？?；;\s\n]', text)
            sentences = [s for s in sentences if s]
            
            # 计算句子与关键词的匹配度
            sentence_scores = {}
            for sentence in sentences:
                sentence_words = self._tokenize(sentence)
                score = sum(1 for word in sentence_words if word in keywords)
                sentence_scores[sentence] = score
            
            # 按分数排序并生成摘要
            sorted_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)
            
            # 拼接摘要，控制长度
            summary = ""
            for sentence, _ in sorted_sentences:
                if len(summary) + len(sentence) <= summary_length:
                    summary += sentence + "。"
                else:
                    break
            
            return summary
        except Exception as e:
            logger.error(f"TextRank摘要提取过程中发生异常: {str(e)}")
            return ""
    
    def abstract_by_llm(self, text: str) -> str:
        """
        使用大模型提取关键摘要信息
        
        Args:
            text: 待处理的原始文本
            
        Returns:
            提取的文本摘要
        """
        # logger.info("开始使用大模型提取摘要")
        if not text:
            logger.warning("输入文本为空，返回空摘要")
            return ""
        
        try:
            # 基于本地部署的qwen大模型获取摘要
            prompt = ABSTRACT_TEMPLET_CN.format(text=text)
            llm = ModelAPI()
            abst = llm.generate(inputs=prompt)
            return abst["answer"]
        except Exception as e:
            logger.error(f"大模型摘要提取过程中发生异常: {str(e)}")
            return ""


class KeywordsAlg():
    """
    关键词提取算法工具
    
    包含一种关键词提取方法：
    1. abstract_by_keywords: 使用TF-IDF算法提取关键词
    """
    
    def __init__(self):
        pass
    
    def keywords_by_bm25(self, 
                            text: str, 
                            topK: int = 5, 
                            POS: Tuple[str, ...] = ('d', 'n', 'v'), 
                            output_format: str = 'str'
                            ) -> Union[str, List[str], List[Tuple[str, float]]]:
        """
        使用BM25算法提取文本中的关键词
        
        Args:
            text: 待处理的原始文本
            topK: 最大提取的关键词数量，默认5
            POS: 过滤词性，元组形式，默认('d', 'n', 'v') d-副词
            output_format: 输出格式，可选'str', 'list', 'weight'，默认'str'
            
        Returns:
            提取的关键词，格式由output参数决定
        """
        # logger.info("开始使用BM25算法提取关键词")
        if not text:
            logger.warning("输入文本为空，返回空关键词")
            if output_format == 'weight':
                return []
            elif output_format == 'str':
                return ""
            else:
                return []
        
        try:
            # 匹配中文、英文和数字的正则表达式
            pattern = re.compile(r'[\u4e00-\u9fa5a-zA-Z0-9]+')

            # 文本清洗
            all_words = pattern.findall(text)
            cleaned_text = ' '.join(all_words)
            
            # 关键词提取（TF-IDF）
            keywords = jieba.analyse.extract_tags(cleaned_text, 
                                                  topK=topK, 
                                                  withWeight=True, 
                                                  allowPOS=POS, 
                                                  withFlag=False
                                                  )
            if not keywords:
                logger.warning("未提取到关键词")
                if output_format == 'weight':
                    return []
                elif output_format == 'str':
                    return ""
                else:
                    return []
            
            if output_format == 'weight':
                # TF-IDF关键词提取完成，返回权重列表
                return keywords
            elif output_format == 'str':
                result = ' '.join([i[0] for i in keywords])
                # TF-IDF关键词提取完成，返回字符串
                return result
            else:
                result = [i[0] for i in keywords]
                # TF-IDF关键词提取完成，返回关键词列表
                return result
        except Exception as e:
            logger.error(f"BM25关键词提取过程中发生异常: {str(e)}")
            if output_format == 'weight':
                return []
            elif output_format == 'str':
                return ""
            else:
                return []


class TxtAlgorithm(JiebaDictManager, AbstractAlg, KeywordsAlg):
    """
    整合文本处理算法工具类
    
    abstract_by_text_rank - TextRank摘要
    abstract_by_bm25      - BM25摘要
    abstract_by_llm       - 大模型摘要
    keywords_by_bm25      - BM25提取关键词/词权重
    
    """
    def __init__(self, dict_directory=None):
        """初始化文本处理算法工具类"""
        self.dict_manager = JiebaDictManager()
        AbstractAlg.__init__(self)
        KeywordsAlg.__init__(self)
        logger.info("文本处理算法工具类初始化完成")
        
        # 加载自定义词典
        if dict_directory:
            self.dict_manager.load_dictionaries(dict_directory)



   
if __name__ == "__main__":
    
    # 测试代码
    ltext = """
发现账户异常交易，可以通过以下几种方式挂失：\n- **电话挂失**：立即拨打银行客服电话，
按照语音提示操作找到挂失相关服务，或者直接联系人工客服。向客服提供自己的卡号、身份证号、
姓名等身份信息，通过身份验证后即可办理临时挂失。电话挂失方便快捷，能在第一时间阻止可能
的进一步资金损失，适用于紧急情况。需注意的是，临时挂失通常有一定的有效期，一般为5天左右，
不同银行的规定可能略有不同。挂失时要记录好挂失编号，以便后续查询和处理。\n- **网上银行
或手机银行挂失**：如果已经开通了相关银行的网上银行或手机银行服务，可以登录相应平台进行
挂失。登录网上银行后，进入账户管理板块，寻找挂失服务选项，按照页面提示完成挂失流程；手机
银行的操作类似，通常在账户管理或安全中心等相关菜单中能找到挂失功能，按系统提示操作即可。
这种方式的优点是操作相对简便，且可以随时进行，不受时间和地点限制，但前提是要确保使用的网
络环境安全，防止个人信息泄露。\n- **柜台挂失**：携带本人有效身份证件，如身份证、护照等，
前往附近的银行网点。到网点后，向工作人员说明账户出现异常交易情况并要求挂失，工作人员会核
实身份信息，可能还会要求提供一些账户相关的其他信息，如预留手机号码、家庭住址等，确认无误
后会协助办理挂失手续。柜台挂失相对来说更为稳妥，能更全面地了解账户情况，同时也可以咨询工
作人员关于异常交易的处理建议，但可能需要花费一些时间前往银行网点并排队等待办理。

    """
    
    stext = "信用卡申请需要什么条件？多久能批下来？"
    
    tp = TxtAlgorithm("./src/dict")
    
    print( "TextRank 摘要: " )
    print( tp.abstract_by_text_rank(ltext) )
    
    print( "BM25 摘要: " )
    print( tp.abstract_by_bm25(ltext, compression_ratio=0.2) )
    
    print( "LLM 摘要: " )
    print( tp.abstract_by_llm(ltext) )
    
    print( "关键词提取: " )
    print( tp.keywords_by_bm25(stext, topK=3, output_format='weight') )


    
    
    
    
    
    

