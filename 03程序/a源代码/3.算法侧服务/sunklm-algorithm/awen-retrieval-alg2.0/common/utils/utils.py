#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import importlib.util
import inspect
import requests
from common.conf.awen_schema import HeadersCode



# softmax函数
def softmax(x):
    """
    Description: 计算softmax
    Args:
         x （list）: 数组或矩阵
    Returns:
         softmax后的数组或矩阵
    """
    e_x = np.exp(x - np.max(x))  # 减去最大值以避免数值溢出
    return e_x / e_x.sum(axis=0)  # 除以每行的和（对于二维数组）

# 归一化
def normalization(data):
    '''
      最大值：+10 * 10%
      最小值：6
    '''
    _max = 10
    _min = 6
    _prop = 0.1
    #_range = np.max(data) - np.min(data)
    for i in range(len(data)):
        data[i] = data[i] + _max  ## 防止最后归一化得分过小
    max_data = np.max(data) + np.max(data)*_prop
    #min_data = np.min(data)
    _range = max_data - _min
    result = (data - np.min(_min)) / _range
    return result

# 标准化
def standardization(data):
    mu = np.mean(data, axis=0)
    sigma = np.std(data, axis=0)
    return (data - mu) / sigma



def get_module_variables(module_path):
    """
    导入 Python 模块并获取其全局变量
    
    Args:
        module_path: 模块文件路径
    
    Returns:
        字典，包含变量名和对应的值
    """
    # 创建模块规范
    spec = importlib.util.spec_from_file_location("script_module", module_path)
    module = importlib.util.module_from_spec(spec)
    
    # 执行模块
    spec.loader.exec_module(module)
    
    # 获取模块的全局变量
    variables = {}
    for name, value in module.__dict__.items():
        # 过滤掉特殊属性和导入的模块
        if not name.startswith("__") and not inspect.ismodule(value):
            variables[name] = value
    
    return variables


def api_call(url,inputs):
    data =inputs
    response = requests.post(url, headers= HeadersCode.NORMAL_HEADERS, json=data)
    result = response.json()
    return result









