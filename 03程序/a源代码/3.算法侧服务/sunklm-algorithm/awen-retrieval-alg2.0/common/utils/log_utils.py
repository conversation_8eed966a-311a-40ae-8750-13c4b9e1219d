import logging
from logging.handlers import TimedRotatingFileHandler
import os
import json
import numpy

from datetime import datetime
from loguru import logger

def get_logger(log_file_path):
    """
    该函数为日志定义函数
    :param log_file_path: 日志保存路径
    :return:
    """

    logger = logging.getLogger('mylog')
    logger.setLevel(logging.DEBUG)

    # 创建TimedRotatingFileHandler，按天分割日志文件
    file_handler = TimedRotatingFileHandler(log_file_path, when='midnight',
                                            backupCount=30)
    file_handler.setLevel(logging.DEBUG)

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s '
                                  '- %(message)s')
    file_handler.setFormatter(formatter)

    # 添加文件处理程序
    logger.addHandler(file_handler)

    # 添加控制台处理程序，可以选择是否添加
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


class NpEncoder(json.JSONEncoder):
    """
    该类为对多种数据类型格式化，防止flask服务输出报错
    """
    def default(self, obj):
        if isinstance(obj, (numpy.int_, numpy.intc, numpy.intp, numpy.int8,
                            numpy.int16, numpy.int32, numpy.int64, numpy.uint8,
                            numpy.uint16, numpy.uint32, numpy.uint64)):
            return int(obj)
        elif isinstance(obj, (numpy.float_, numpy.float16, numpy.float32,
                              numpy.float64)):
            return float(obj)
        elif isinstance(obj, (numpy.ndarray,)):  # add this line
            return obj.tolist()  # add this line
        return json.JSONEncoder.default(self, obj)


def return_dict(code, data, message):
    """
    输出统一的算法服务格式
    :param code: 算法状态码
    :param data: 算法返回结果
    :param message: 算法报错信息，若无报错，为''
    :return:
    """
    return json.dumps({"retMsg": message, "retCode": code, "retMap": data},
                      cls=NpEncoder, indent=4)


# 自定义错误模块
class CustomException(Exception):
    """
    自定义算法错误类
    """
    def __init__(self, error_info):
        self.error_info = error_info

    def __str__(self):
        return self.error_info


def create_path(path):
    if not os.path.exists(path):
        os.makedirs(path)



log_name_list = ["awen-retrieval-alg","alg_utils"]
##日志模块
# logger.remove()
PWD = os.getcwd()
date = datetime.now().date()

## 文本检索日志
logs_save_dir = os.path.join(PWD, 'logs/{}_logs'.format(log_name_list[0]))
logger.add(os.path.join(logs_save_dir, f'save_{log_name_list[0]}_{date}.log'),
           rotation='00:00', retention='30 days',filter=lambda record: record["extra"].get("name") == "retrieval")

## util 日志
logs_save_dir = os.path.join(PWD, 'logs/{}_logs'.format(log_name_list[1]))
logger.add(os.path.join(logs_save_dir, f'save_{log_name_list[1]}_{date}.log'),
           rotation='00:00', retention='30 days',filter=lambda record: record["extra"].get("name") == "utils")
print(logs_save_dir)




