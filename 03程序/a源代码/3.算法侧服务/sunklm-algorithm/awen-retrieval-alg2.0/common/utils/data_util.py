import os
import re

## 读取测试文件
def recursive_listdir(path):
    files = os.listdir(path)
    file_list = []
    for file in files:
        file_path = os.path.join(path, file)
        if os.path.isfile(file_path):
            file_list.append(file_path)
    return file_list



def is_chinese(string):
    # 更全面的中文字符正则表达式模式
    chinese_chars = re.sub(r'[^\u4e00-\u9fff]', '', string)
    return chinese_chars


def get_data(rag_result):
    result = []
    for item in rag_result:
        line = item["splitText"]
        tmp_slice_list = line.split("。")
        for line in tmp_slice_list:
            if line != "":
                line = is_chinese(line)
                result.append(line)
    return result



