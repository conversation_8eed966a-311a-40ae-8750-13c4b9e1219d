#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pymilvus import connections, Collection, utility, db
from common.utils.log_utils import return_dict
from common.rag.embedding import LoadsEmbeddingModel
from common.conf.awen_schema import ReturnCode
from common.utils.log_utils import logger
import traceback
logger = logger.bind(name="utils")


class MilvusClient:
    
    def __init__(self, host, port, db_name):
        self.host = host
        self.port = port
        self.dbname = db_name
        self._connect()
        logger.info(f"连接至 Milvus 数据库 {self.host}:{self.port}/{self.dbname}")


    def _connect(self):
        connections.connect(db_name=self.dbname, host=self.host, port=self.port)
    
    def _using_database(self, db_name):
        db.using_database(db_name=db_name)
        
    def _list_collections(self):
        results = utility.list_collections()
        print("当前数据库中的集合:", results)
    
    def _query_embedding(self, query, emb_model_name='bge_m3'):
        """
        向量化嵌入方法

        Args:
            query: str 原始文本
            emb_model_name: str 向量模型
                API = 向量化模型服务
                bge_m3 = 本地模型文件
        
        """
        logger.info('[Milvus向量库] 开始问题向量化')
        
        # * 这里两种 con_vec 的格式不一样
        # API 返回 [[ ... ]]
        # 本地返回  [array([ ... ], dtype=float32)]
        if emb_model_name == 'API':
            # 使用API模型
            logger.info('[Milvus向量库] 向量化模型: API')
            con_vec = [LoadsEmbeddingModel().embedding_api(query)]
        else:
            # 使用本地模型
            logger.info(f'[Milvus向量库] 向量化模型: 本地模型 {emb_model_name}')
            embedding_dict = LoadsEmbeddingModel().loads_model()
            embedding = embedding_dict[emb_model_name]
            con_vec = [embedding.embedding_query(query)]
        
        logger.info('[Milvus向量库] 向量化完成')

        return con_vec
        

    def _res_format_rag_file(self, res, match_id, match_name, match_slice_id, match_text):
        """
        Milvus 查询结果解析（知识库 - 文件检索专用）
        
        """
        # 定义一个空的检索结果，检索报错时返回空的结果
        file_base_re = {"from": {"fileId": None,
                                 "fileName": "",
                                 "sliceId": None,
                                 "sliceSource":"Milvus"
                                 },
                        "splitText": "", 
                        "score": 0.0
                        }
        ret = []
        for result in res:
            if result.distance > 0:
                ret.append(
                    {"from": {"fileId": result.entity.get(match_id),
                              "fileName": result.entity.get(match_name),
                              "sliceId": result.entity.get(match_slice_id),
                              "sliceSource":"Milvus"
                              },
                     "splitText": result.entity.get(match_text), 
                     "score": result.distance
                     })
        # 返回结果
        if len(ret) > 0:
            return ret
        else:
            return file_base_re
                

    def _res_format_rag_faq(self, res, match_id, match_id_faq, match_name_faq, match_text_faq):
        """
        Milvus 查询结果解析（知识库 - 文件检索专用）
        
        """
        # 定义一个空的检索结果，检索报错时返回空的结果
        query_base_re = {"from": {"fileId": None, 
                                  "faqId": None
                                  }, 
                         "question": "", 
                         "answer":"", 
                         "score": 0.0
                         }
        ret = []
        for result in res:
            if result.distance > 0:
                ret.append(
                    {"from": {"fileId": result.entity.get(match_id), 
                              "faqId": result.entity.get(match_id_faq)
                              },
                     "question": result.entity.get(match_name_faq), 
                     "answer":result.entity.get(match_text_faq), 
                     "score": result.distance
                     })
        # 返回结果
        if len(ret) > 0:
            return ret
        else:
            return query_base_re
        
    def _multi_field_hybrid(self, 
                            multi_field_res, 
                            top_k, 
                            idx,
                            weights=None, 
                            overlap_boost=1.2,
                            template=None
                            ):
        """
        增强型混合排序算法，支持处理2个以上的检索结果源
    
        参数:
            multi_field_res: 包含多个检索结果列表的列表，每个子列表是一个检索结果(建议提前归一化)
            top_k: 最终返回结果数量
            weights: 各检索源的权重列表，默认为等权重
            overlap_boost: 重叠结果的提升系数，应用于所有重叠的结果
        
        返回:
            混合排序后的top_k结果
        
        """        
        num_sources = len(multi_field_res)
        if num_sources == 1:
            return multi_field_res[0]
        
        # 初始化权重（确保归一化）
        if weights is None:
            weights = [1.0 / num_sources] * num_sources
        else:
            if len(weights) != num_sources:
                raise ValueError("权重数量必须与检索源数量匹配")
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]
        
        # 为每个检索源结果添加来源标记
        marked_matches_list = []
        for source_idx, matches in enumerate(multi_field_res):
            marked_matches = []
            for item in matches:
                marked_item = item.copy()
                marked_item['_source_idx'] = source_idx  # 添加来源索引标记
                marked_matches.append(marked_item)
            marked_matches_list.append(marked_matches)
        
        # 构建slice_id到各检索源结果的映射
        source_dicts = []
        for marked_matches in marked_matches_list:
            source_dict = {item[idx]: item for item in marked_matches}
            source_dicts.append(source_dict)
        
        # 处理每个slice_id
        combined_results = {}
        for slice_id in set().union(*[d.keys() for d in source_dicts]):
            # 获取该slice_id在各检索源中的结果
            results = [source_dict.get(slice_id) for source_dict in source_dicts]
            valid_results = [res for res in results if res is not None]
            
            if not valid_results:
                continue
            
            # 计算加权得分
            combined_score = 0.0
            for res in valid_results:
                source_idx = res['_source_idx']  # 从标记中获取来源索引
                combined_score += res['distance'] * weights[source_idx]
            
            # 应用重叠提升
            if len(valid_results) > 1:
                combined_score *= (overlap_boost ** (len(valid_results) - 1))
            
            # 选择得分最高的结果作为基础
            base_result = max(valid_results, key=lambda x: x['distance']).copy()
            base_result['distance'] = min(combined_score, 1.0)
            combined_results[slice_id] = base_result
        
        # 排序并返回top_k
        return sorted(combined_results.values(), key=lambda x: x['distance'], reverse=True)[:top_k]


    # Milvus 多字段检索
    def multi_field_search(self,
                           collection_name,
                           partitions,
                           query,
                           top_k,
                           fields,
                           output_fields,
                           idx,
                           template = None,
                           emb_model_name = 'bge_m3',
                           param = None,
                           **kwargs
                           ):
        """
        Milvus 多(向量)字段检索
        * 不支持 向量+文本 的多字段检索，即只能指定单个/多个向量字段（FloatVector）进行检索
        * 对检索字段数量限制 <= 3个（综合考虑）
        * 删除了原方法中的 match_content参数：是否需要对摘要检索可通过指定 fields 字段来实现
        * 删除了原方法中的 data_type参数：文件与问答数据的字段区别通过参数实现
        
        Args:
            collection_name: str 需要加载的Milvusx集合
            partitions: list 待查询的分区列表
            query: str 检索文本
            top_k: int 查询结果最大返回数量
            fields: list 目标向量字段列表
            output_fields: list 输出字段列表
            idx: str 唯一标识字段
            emb_model_name: str 向量检索算法，BGE或M3E
            param: dict 检索配置字典
            template: str 检索模版（ 
                'FILE' = 知识库-文件, 
                'FAQ' = 知识库-问答
                ）
            
        Returns:
            检索结果
            
        Test:
            collection_name = 'memory_test_02'
            partitions = None
            query = '信用卡申请条件'
            top_k = 10
            fields = ['question_emb', 'answer_emb']
            output_fields = ['record_id', 'update_time_str', 'memory_score']
            emb_model_name = 'bge_m3'
            template = 'MEMO'
            param = {'metric_type': 'IP', 'params': {'nprobe': 10}}
        """
        
        logger.info('[Milvus向量库] 开始获取参数和模型')
        
        # 默认参数字典
        if param is None:
            param = {"metric_type": "IP", "params": {"nprobe": 10}}
            
        # 加载数据
        try:
            # 加载 MIlvus集合
            collection = Collection(collection_name)
            collection.load()
        except:
            logger.error(f"集合 {collection_name} 加载失败")
            errorMsg =  "collection failed load: {}".format(collection_name)
            return return_dict(code=ReturnCode.ERROR, data=[], message=errorMsg)
         
        # 向量化
        try:
            con_vec = self._query_embedding(query, emb_model_name)
        except:
            logger.error(f"向量化失败 {emb_model_name}")
            return return_dict(code=ReturnCode.ERROR, data=[], message=errorMsg)
        
        # 向量检索
        logger.info('[Milvus向量库] 开始进行向量检索')
        # Milvus数据库本身不支持多字段检索
        # 当检索字段为多个时，按顺序检索每个字段，再将检索结果混合
        # * 原则上可以对不同字段，使用不同参数检索（目前不支持这种场景，当前只支持对多个字段使用相同参数检索）
        # * ES的多字段检索使用的·multi_match·实现
        multi_field_res = list()
        for anns_field in fields:
            # 检索单一字段
            try:
                res = collection.search(
                    con_vec,
                    anns_field      = anns_field,
                    param           = param,
                    partition_names = partitions,
                    limit           = top_k,
                    output_fields   = output_fields
                    )
                
                multi_field_res.append(res[0])
                logger.info('[Milvus向量库] 向量检索结束。')

            except Exception as e:
                logger.error(f"向量检索失败 {e}")
                errorMsg =  "milvus retrival failed."
                return return_dict(code=ReturnCode.ERROR, data=[], message=errorMsg)
        
        # 对 multi_field_res 内的检索结果进行混合（多路召回方法）            
        res = self._multi_field_hybrid(multi_field_res, 
                                       top_k = top_k, 
                                       idx = idx,
                                       weights = None, 
                                       overlap_boost = 1.2,
                                       template = template
                                       )
        # 返回结果
        try:
            # 使用 知识库-文件 模版
            if template == 'FILE':
            
                match_id, match_name, match_text, match_slice_id  = output_fields
                  
                result = self._res_format_rag_file(res, match_id, match_name, match_slice_id, match_text)
                logger.info(f"[Milvus检索] Milvus检索结果: 返回结果数量 {len(result)}")
                return return_dict(
                    code=ReturnCode.SUCESS,
                    data=result, 
                    message="sucess")
        
            # 使用 知识库-问答 模版
            if template == 'FAQ':
                
                match_id, match_id_faq, match_name_faq, match_text_faq = output_fields
                result = self._res_format_rag_file(res, match_id, match_name, match_slice_id, match_text)
                logger.info(f"[Milvus检索] Milvus检索结果: 返回结果数量 {len(result)}")    
                return return_dict(
                    code=ReturnCode.SUCESS,
                    data=result, 
                    message="sucess")
                    
        # 异常时返回
        except Exception as e:
            logger.error(traceback.print_exc())
            logger.error(f"向量检索结果解析失败 {e}")
            errorMsg =  "milvus retrival results analysis failed."
            return return_dict(code=ReturnCode.ERROR, data=[], message=errorMsg)
    
        

if __name__ == "__main__":
    
    
    import json
    
    test = MilvusClient()
    
    # 测试 FILE
    res =  test.multi_field_search( 
        collection_name = 'kg_0vw3uc0w',
        partitions = ['20000078'],
        query = '行政复议受理',
        top_k = 5,
        fields = ['slice_embedding','slice_embedding','slice_embedding'],
        output_fields = ['slice_id','file_id','file_name','slice'],
        emb_model_name = 'bge_m3',
        param = None,
        template = 'RAG_FILE',
        match_id = 'file_id',
        match_name = 'file_name',
        match_slice_id = 'slice_id',
        match_text = 'slice'
        )
    
    json.loads(res)
    
    # 测试 FAQ
    res =  test.multi_field_search( 
        collection_name = 'kg_3bm4291x',
        partitions = ['1187','20000064'],
        query = '行政复议受理',
        top_k = 2,
        fields = ['faq_embedding','faq_embedding','faq_embedding','faq_embedding'],
        output_fields = ['file_id','faq_id','question','answer'],
        emb_model_name = 'API',
        param = None,
        template = 'RAG_FAQ',
        match_id = 'file_id',
        match_id_faq = 'faq_id',
        match_name_faq = 'question',
        match_text_faq = 'answer'
        )
    
    json.loads(res)


    test = MilvusClient(host='localhost',port=19530,db_name='default')
    
    # 测试 MEMO
    res =  test.multi_field_search( 
        collection_name = 'memory_test_02',
        partitions = None,
        query = '信用卡申请',
        top_k = 5,
        fields = ['question_emb','answer_emb'],
        output_fields = ['user_id','scene_id','session_id','update_time'],
        emb_model_name = 'bge_m3',
        param = None,
        template = 'MEMO',
        match_memo_id = 'record_id'
        )
    
    json.loads(res)
    
    
    
    