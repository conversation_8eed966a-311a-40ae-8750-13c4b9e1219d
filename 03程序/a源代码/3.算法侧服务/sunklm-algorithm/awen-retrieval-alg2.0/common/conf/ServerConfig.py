#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# 容器内参数

# 指向外部参数路径
config_dir = '/Users/<USER>/Desktop/server_config.py'

## 1、项目路径
#/home/<USER>/source/Awen/utils
current_path = os.path.dirname(os.getcwd()) 
parent_path = os.path.abspath('.')
# print(parent_path)
# 字典目录
dict_directory = "./common/dict"

## 2、模型参数
# 向量模型（本地）
EMBEDDING_MODEL_NAME = "bge_m3"
embedding_model_dict = {'bge_m3': os.path.join(parent_path,"model/vec_model/bge-m3")}
# 向量模型（API）
EMBEDDING_MODEL_BASEURL = "http://172.1.0.134:9020/v1/embeddings"
URL = "http://172.1.0.163:8011/Agent-alg/llm_chat"
# 向量模型使用的设备
DEVICE = 'cuda:0'

# 是否使用rerank模型api
IF_RERANK_API = False
# rerank模型（API）
RERANK_API_DICT = {"bge-reranker-v2-m3":{"url":"http://***********:9002/v1/rerank","model":"bge-reranker-v2-m3"}}
# rerank模型（本地）
RERANK_MODEL_DICT = {"bce-reranker-base_v1": parent_path+"/model/rerank_model/bce-reranker-base_v1",
                     "Conan-embedding-v1": parent_path+"/model/rerank_model/Conan-embedding-v1",
                     "jina-reranker-v2-base-multilingual":parent_path+"/model/rerank_model/jina-reranker-v2-base-multilingual",
                     "bge-reranker-v2-m3":parent_path+"/model/rerank_model/bge-reranker-v2-m3"}
# 默认rerank模型
DEFAULT_RERANK = "bge-reranker-v2-m3"

# 默认使用的模型
LLM_NAME = "Qwen2.5-72B-Instruct-GPTQ-Int4"
# 私有化部署的模型MAP
LLM_MAP = {
    "Qwen2.5-72B-Instruct-GPTQ-Int4": {
        "base_url": "http://172.1.0.134:9020/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode":0
        }
    }
# 最大输入长度
MAX_SEQ_LEN = 10000
# 最大输出长度：
MAX_OUT_LEN = 512
# 模型输出参数默认值
QUERY = "你好"
TEMPERATURE = 0.01
TOP_P = 0.01
TOP_K = 1
REWRITE = False
POSTPROCESS = False
STREAM = False
# 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
QUERY_PER_SECOND = 1
#输入窗口长度
CONTEXT_WINDOW = 50000


## 3、数据库参数
# ES
ES_URL = "http://**********:9200"

# Milvus
MILVUS_HOST = "***********"
MILVUS_PORT = 19530
MILVUS_DB   = 'KLM'

# Mysql
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@6888"
MYSQL_DATABASE = "AWEN_PRIMARY"

