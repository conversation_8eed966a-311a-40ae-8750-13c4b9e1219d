#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Thu Jul  3 16:10:51 2025

@author: yang<PERSON>handa
"""

from common.conf.ServerConfig import config_dir
from common.utils.utils import get_module_variables 

# 检索参数初始化
class PARAMS:
    
    def __init__(self):
        
        ## 读取最新的配置文件
        CONFIGS = get_module_variables(module_path=config_dir)
        
        ## 初始化参数

        ## 4、检索服务开关项配置
        # 是否使用向量模型API
        self.IS_API = CONFIGS.get('IS_API', False) 

        # 是否开启问题降噪
        self.IF_DENOISE = CONFIGS.get('IF_DENOISE', False) 

        # 是否启用检索增强
        self.IF_RETRIVAL_ENHANCE = CONFIGS.get('IF_RETRIVAL_ENHANCE', False)  

        # 是否启用检索结果压缩
        self.IF_COMPRESS = CONFIGS.get('IF_COMPRESS', False) 

        # 是否启用重排序
        self.IF_RERANK = CONFIGS.get('IF_RERANK', False) 

        # 当启用检索增强或重排序时，以下参数生效
        # 向量检索结果返回数量放倍数
        self.EMBEDDING_TOPN = CONFIGS.get('EMBEDDING_TOPN', 1) 
        # 文本检索结果返回数量放倍数
        self.ES_TOPN = CONFIGS.get('ES_TOPN', 1) 

        # 是否提取摘要
        # self.IF_ABSTRACT = CONFIGS.get('IF_ABSTRACT', False) 
        # 是否使用摘要检索
        self.IF_ABSTRACT_SEARCH = CONFIGS.get('IF_ABSTRACT_SEARCH', False) 
        # self.IF_CONTEXTUAL_RETRIEVAL = CONFIGS.get('IF_CONTEXTUAL_RETRIEVAL', False) 
        
        ## 5、检索服务参数配置

        # 文本检索默认值
        self.DEFAULT_TOPK = CONFIGS.get('DEFAULT_TOPK', 5) 
        self.DEFAULT_WEIGHT = CONFIGS.get('DEFAULT_WEIGHT', 0.5) 
        self.DEFAULT_THRESHOLD = CONFIGS.get('DEFAULT_THRESHOLD', 0) 
        self.DEFAULT_MATCHTYPE = CONFIGS.get('DEFAULT_MATCHTYPE') 
        self.DEFAULT_MATCHCONTENT = CONFIGS.get('DEFAULT_MATCHCONTENT') 

        # milvus及es的key值
        self.match_text = CONFIGS.get('match_text', 'slice') 
        self.match_id = CONFIGS.get('match_id', 'file_id') 
        self.match_name = CONFIGS.get('match_name', 'file_name') 
        self.match_slice_id = CONFIGS.get('match_slice_id', 'slice_id') 
        self.match_embedding = CONFIGS.get('match_embedding', 'slice_embedding') 
        self.match_abstract = CONFIGS.get('match_abstract', 'abstract') 
        self.match_abstract_embedding = CONFIGS.get('match_abstract_embedding', 'abstract_embedding') 

        self.match_text_faq = CONFIGS.get('match_text_faq', 'answer') 
        self.match_id_faq = CONFIGS.get('match_id_faq', 'faq_id') 
        self.match_name_faq = CONFIGS.get('match_name_faq', 'question') 
        self.match_embedding_faq = CONFIGS.get('match_embedding_faq', 'faq_embedding') 

        # 根据检索类型，定义目标字段与输出字段
        # 0、FILE - mltMatch (文件混合文本向量检索)
        # pass
        # 1、FILE - keyMatch (文件文本检索)
        self.target_file_keyMatch = CONFIGS.get('target_file_keyMatch', ['slice'])
        self.output_file_keyMatch = CONFIGS.get('output_file_keyMatch', ['file_id', 'file_name', 'slice']) 
        # 2、FILE - emdMatch (文件向量检索)
        self.target_file_emdMatch = CONFIGS.get('target_file_emdMatch', ['slice_embedding'])
        self.output_file_emdMatch = CONFIGS.get('output_file_emdMatch', ['file_id', 'file_name', 'slice', 'slice_id'])
        # 3、FILE - mltAbsMatch (文件向量混合摘要检索)
        self.target_file_mltAbsMatch = CONFIGS.get('target_file_mltAbsMatch', ['slice_embedding', 'abstract_embedding'])
        self.output_file_mltAbsMatch = CONFIGS.get('output_file_mltAbsMatch', ['file_id', 'file_name', 'slice', 'slice_id'])
        # 4、FILE - esMltMatch (文件文本混合摘要检索)
        self.target_file_esMltMatch = CONFIGS.get('target_file_esMltMatch', ['slice', 'abstract'])
        self.output_file_esMltMatch = CONFIGS.get('output_file_esMltMatch', ['file_id', 'file_name', 'slice'])
        # 5、FAQ - emdMatch (问答向量检索)
        self.target_faq_emdMatch = CONFIGS.get('target_faq_emdMatch', ['faq_embedding'])
        self.output_faq_emdMatch = CONFIGS.get('output_faq_emdMatch', ['file_id', 'faq_id', 'question', 'answer'])
        
      
        
       
        
       
        