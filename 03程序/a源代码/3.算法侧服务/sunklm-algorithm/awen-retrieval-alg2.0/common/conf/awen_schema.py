# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  awen_schema.py
   Description : 枚举值定义
   Author :  Arno
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

from enum import IntEnum
'''
参数返回的枚举值定义
'''
            
class TemplateCode():
    FILE_keyMatch    = 'FILE_keyMatch'
    FILE_emdMatch    = 'FILE_emdMatch'
    FILE_mltMatch    = 'FILE_mltMatch'
    FILE_mltAbsMatch = 'FILE_mltAbsMatch'
    FILE_esMltMatch  = 'FILE_esMltMatch'
    FAQ_emdMatch     = 'FAQ_emdMatch'
    
class ReturnCode(IntEnum):
    SUCESS = 0
    ERROR = 1

class ChatFormatCode():
    OPENAI_FORMAT = 'openai'
    GLM_FORMAT = 'glm'
    RAW_FORMAT = 'raw'


class HeadersCode():
    NORMAL_HEADERS = {'Content-Type': 'application/json'}


class CompanyCode():
    PRIVATE = 0 #私有化部署的模型的标志，company为：厂商名_private
    OPEN = 1 #开源部署的模型的标志，company为：厂商名_open

class SearchCode():
    DATA_FILE = 'FILE'
    DATA_FAQ = 'FAQ'

    MATCH_EMD = 'emdMatch'
    MATCH_KEY = 'keyMatch'
    MATCH_MLT = 'mltMatch'
    ES_MATCH_MLT ="esMltMatch"
    ABS_MATCH_MLT = "mltAbsMatch"
    MATCH_CONTENT_SPLIT = 'SPLIT'
    MATCH_CONTENT_ABSTRACT = 'ABSTRACT'
    MATCH_CONTENT_MIX = 'MIX'

class FileStatus(IntEnum):
    Unprocessed = 0,
    Structuring = 1,
    Structured_Successfully = 2,
    Structured_Failed = 3,
    Slicing = 4,
    Sliced_Successfully = 5,
    Sliced_Failed = 6,
    Vectorizing = 7,
    Vectorized_Successfully = 8,
    Vectorized_Failed = 9,

class SliceStatus(IntEnum):
    Pending_Vectorization =0,
    Vectorizing =1,
    Available_Retrieval =2,
    Vectorization_Failed =3,
    Pending_Insertion_ES =4,
    Insertion_ES_Successful =5,