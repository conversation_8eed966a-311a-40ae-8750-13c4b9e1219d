#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import jieba
from typing import List
from common.utils.log_utils import logger


class JiebaDictManager():
    """
    jieba词典管理工具
    """
    
    def __init__(self):
        """初始化词典管理器"""
        self.loaded_dictionaries = {}  # 存储已加载的词典文件路径
        self.dict_extensions = ['.txt', '.dict', '.dic']  # 支持的词典文件后缀
    
    def is_valid_dict_file(self, file_path: str) -> bool:
        """检查文件是否为有效的词典文件"""
        if not os.path.isfile(file_path):
            return False
            
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.dict_extensions
    
    def scan_directory(self, dir_path: str) -> List[str]:
        """
        扫描指定目录下的所有词典文件
        """
        if not os.path.exists(dir_path):
            raise ValueError(f"词典目录不存在: {dir_path}")
            
        if not os.path.isdir(dir_path):
            raise ValueError(f"{dir_path} 不是一个目录")
            
        dict_files = []
        for root, _, files in os.walk(dir_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_valid_dict_file(file_path):
                    dict_files.append(file_path)
        
        if not dict_files:
            logger.warning(f"目录 {dir_path} 下未找到有效的词典文件")
            
        return dict_files
    
    def load_dictionaries(self, dir_path: str, recursive: bool = True) -> int:
        """
        加载指定目录下的所有词典文件
        
        Args:
            dir_path: 词典目录路径
            recursive: 是否递归扫描子目录，默认True
            
        Returns:
            成功加载的词典数量
        """
        try:
            # 扫描词典文件
            dict_files = self.scan_directory(dir_path)
            
            # 加载词典文件
            loaded_count = 0
            for file_path in dict_files:
                try:
                    jieba.load_userdict(file_path)
                    self.loaded_dictionaries[file_path] = True
                    loaded_count += 1
                    logger.info(f"成功加载词典: {file_path}")
                except Exception as e:
                    logger.error(f"加载词典 {file_path} 失败: {e}")
            
            return loaded_count
            
        except Exception as e:
            logger.error(f"加载词典目录 {dir_path} 时发生错误: {e}")
            return 0
    
    def load_dictionary(self, file_path: str) -> bool:
        """
        加载单个词典文件
        
        Args:
            file_path: 词典文件路径
            
        Returns:
            是否加载成功
        """
        if not self.is_valid_dict_file(file_path):
            logger.warning(f"不是有效的词典文件: {file_path}")
            return False
            
        try:
            jieba.load_userdict(file_path)
            self.loaded_dictionaries[file_path] = True
            logger.info(f"成功加载词典: {file_path}")
            return True
        except Exception as e:
            logger.error(f"加载词典 {file_path} 失败: {e}")
            return False
    
    def get_loaded_dictionaries(self) -> List[str]:
        """获取所有已加载的词典文件路径"""
        return list(self.loaded_dictionaries.keys())




if __name__ == "__main__":
    # 创建词典管理器
    dict_manager = JiebaDictManager()
    
    # 定义词典目录（假设存在dict目录包含多个词典文件）
    dict_directory = "./common/dict"
    

    # 加载目录下的所有词典
    loaded_count = dict_manager.load_dictionaries(dict_directory)
    print(f"成功加载 {loaded_count} 个词典文件")
        
    # 查看已加载的词典
    loaded_dicts = dict_manager.get_loaded_dictionaries()
    print("\n已加载的词典文件:")
    for i, dict_path in enumerate(loaded_dicts, 1):
        print(f"{i}. {dict_path}")
        
    # 测试分词
    text = "区块链技术在金融领域的应用越来越广泛"
    print("\n使用加载的词典分词:")
    print(", ".join(jieba.cut(text)))
        
    
        
    
    
    
      