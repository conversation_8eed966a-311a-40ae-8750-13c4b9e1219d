#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
from retrieval.controller.RetrievalController import app

if __name__ == "__main__":

    print(r"""
     _                        _    _____  _                 
    / \    __ _   ___  _ __  | |_ |  ___|| |  ___ __      __
   / _ \  / _` | / _ \| '_ \ | __|| |_   | | / _ \\ \ /\ / /
  / ___ \| (_| ||  __/| | | || |_ |  _|  | || (_) |\ V  V / 
 /_/   \_\\__, | \___||_| |_| \__||_|    |_| \___/  \_/\_/  
          |___/                                                                        

    """, flush=True)
    port = sys.argv[1]
    app.run(host='0.0.0.0', port=port, debug=False)
    
    
    
    
    