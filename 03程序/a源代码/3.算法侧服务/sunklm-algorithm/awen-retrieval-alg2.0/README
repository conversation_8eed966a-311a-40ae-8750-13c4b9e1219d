
2.0功能更新：

1）动态参数配置：
   目录内 server_config.py 文件为动态参数，该脚本内参数修改保存后即生效。

   server_config.py 可放置在docker容器之外，通过 common.conf.ServerConfig.py 中的 config_dir 参数，声明文件路径。

2）检索功能优化：

   新增 对文件数据的向量化原文 + 向量化摘要的混合检索，通过参数 'matchType':'mltAbsMatch' 发起请求
   新增 对文件数据的原文 + 摘要的混合检索，通过参数 'matchType':'esMltMatch' 发起请求

   优化 文本检索方法，增加对多字段检索的支持
   优化 向量检索方法，增加对多字段检索的支持

   新增 输入问题文本降噪方法。通过动态参数开启
   新增 检索结果增强方法，通过BM25对输入问题提取关键词及词权重对检索结果中的score进行加权。通过动态参数开启
   新增 检索结果算法压缩方法，通过BM25算法对原始文本进行压缩。通过动态参数开启

3）其他功能优化：

   新增 文本处理算法类 （./common/utils/txt_algorithm.py）
   新增 对分词算法增加词典管理功能 （./commom/dict/）

   优化 代码结构与部分实现逻辑优化






