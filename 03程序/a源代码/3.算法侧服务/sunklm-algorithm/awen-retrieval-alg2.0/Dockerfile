FROM python:3.11-slim AS builder
WORKDIR /install

# 使用 Debian 12 (bookworm) 对应的清华镜像源
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list

# 安装构建依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        python3.11-dev \
        gcc \
        libffi-dev \
        libssl-dev \
        curl \
        ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir \
    -i https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    --prefix=/install \
    -r requirements.txt

# 最终镜像
FROM python:3.11-slim
WORKDIR /home/<USER>/retrieval_server

# 拷贝构建好的依赖
COPY --from=builder /install /usr/local