#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import time
import traceback
from flask import Flask, request

## 源码模块
from retrieval.service.RetrievalService import QueryMatch

from common.utils.log_utils import return_dict
from common.conf.awen_schema import ReturnCode
from common.utils.log_utils import logger
logger = logger.bind(name="retrieval")

## app
app = Flask(__name__)

@app.route('/RAG-alg/query_match', methods=['POST'])
def server():
    time_start = time.time()
    logger.info("-"*50)
    query_match = QueryMatch()
    try:
        params = json.loads(request.get_data(as_text=True))
        # 执行实际算法操作
        logger.info("[文本检索] ------query:{}------".format(params['query']))
        logger.info("[文本检索] 入参：" + str(params))
        result = query_match.query_match(params)
        result = json.loads(result)
        if result['retCode'] == ReturnCode.SUCESS:
            logger.info("[文本检索] 运行时间：{}----".format(time.time() - time_start))
            result = {'result':result['retMap']}
            logger.info("[文本检索] Rag result: {}".format(result))
            return return_dict(code=str(ReturnCode.SUCESS.value), data=result, message='success')
        else:
            return return_dict(code=str(ReturnCode.ERROR.value), data={}, message=result['retMsg'])
    except Exception as e:
        logger.error("[文本检索] 错误信息--------")
        logger.error(e)
        logger.error("{}".format(traceback.format_exc()))
        return return_dict(code=str(ReturnCode.ERROR.value), message='query match error.', data=traceback.format_exc())


