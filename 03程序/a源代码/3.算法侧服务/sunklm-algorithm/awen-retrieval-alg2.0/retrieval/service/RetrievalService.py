#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from common.conf.ServerConfig import dict_directory, DEFAULT_RERANK, \
     RERANK_MODEL_DICT, MILVUS_HOST, MILVUS_PORT, MILVUS_DB, ES_URL
from common.conf.awen_schema import ReturnCode, TemplateCode
from common.utils.es_conn import EsClient
from common.utils.milvus_conn import MilvusClient
from common.rag.retrieval import Retrival
from common.utils.db import POOL
from common.conf.config_init import PARAMS
from common.utils.log_utils import logger, return_dict
import traceback
import json
logger = logger.bind(name="retrieval")


class QueryMatch(
        
        MilvusClient,      # Milvus 向量检索工具
        EsClient,          # ES 文本检索工具
        Retrival           # 检索算法工具类
        
        ):
    """
    文本检索 - 功能类
    
    """
    
    def __init__(self):
        
        logger.info('[文本检索服务] 初始化服务...')
        
        # Milvus 链接
        MilvusClient.__init__(self, MILVUS_HOST, MILVUS_PORT, MILVUS_DB)
        # ES 链接
        EsClient.__init__(self, ES_URL)             
        # Mysql 链接
        self.mysql = POOL.connection()
        logger.info("建立 Mysql 连接池")
        
        # 初始化检索算法
        Retrival.__init__(self, dict_directory, DEFAULT_RERANK, RERANK_MODEL_DICT)
        logger.info('[文本检索服务] 初始化完成')

    def _params_resolver(self, CONFIG, params):
        """
        检索服务参数解析与校验
        
        """
        logger.info('[文本检索服务] 参数: {}'.format(params))
        # params 入参校验与参数解析
        try:
            # 1、定义检索模版
            # 根据 入参 dataType、matchType 确定检索模版
            # 检索模版：
            #  * FILE_keyMatch    - 文件数据 文本 检索
            #  * FILE_emdMatch    - 文件数据 向量 检索
            #  * FILE_mltMatch    - 文件数据 文本向量 混合检索
            #  * FILE_mltAbsMatch - 文件数据 向量 原文+摘要检索
            #  * FILE_esMltMatch  - 文件数据 文本 原文+摘要检索
            #  * FAQ_emdMatch     - 问答数据 向量 检索
            template = '_'.join([params.get('dataType'), params.get('matchType')])
            
            if template not in [att for att in dir(TemplateCode) if not att.startswith('__')]:
                logger.error(f"[文本检索服务] 无效参数 dataType:{params.get('dataType')} or matchType:{params.get('matchType')}")
                raise ValueError(f"请求参数异常: dataType:{params.get('dataType')} or matchType:{params.get('matchType')}")
            
            CONFIG.template  = template
            CONFIG.dataType  = params.get('dataType')
            CONFIG.matchType = params.get('matchType')
          
            # 2、必要参数解析与校验
            
            # 用户请求的问题
            CONFIG.query = params['query']
            # 知识类目
            CONFIG.partitions = params['partition']
            # 输出top-k个字段
            CONFIG.topK = int(params.get('topK', CONFIG.DEFAULT_TOPK))
            # 检索权重配置
            CONFIG.weight = float(params.get('weight', CONFIG.DEFAULT_WEIGHT))
            # 相似度阈值
            CONFIG.threshold = float(params.get('threshold', CONFIG.DEFAULT_THRESHOLD))
            
            # 3、可选参数解析
            # 是否进行rerank
            CONFIG.IF_RERANK = params.get('rerank', CONFIG.IF_RERANK)
            # 是否进行检索增强
            CONFIG.IF_RETRIVAL_ENHANCE = params.get('retrivalEnhance', CONFIG.IF_RETRIVAL_ENHANCE)
            # 向量检索参数
            CONFIG.milvusParams = params.get('milvusParams', {"metric_type": "IP", "params": {"nprobe": 10}})
            
            # 是否使用摘要检索
            # CONFIG.abstractSearch = params.get('abstractSearch', CONFIG.IF_ABSTRACT_SEARCH)

        except Exception as e:
            logger.error('[文本检索服务] 错误：'+repr(e))
            return return_dict(code=ReturnCode.ERROR, message='检索参数异常：' + str(e), data='')
        
        return CONFIG


    def _match_by_retrival_template(self, CONFIG):
        """
        针对不同检索模版进行召回

        """
        # 0、FILE - mltMatch (文件混合文本向量检索)
        if CONFIG.template == TemplateCode.FILE_mltMatch:
            result = self._retrie_multi_hybrid_search(
                            CONFIG,
                            es_fields = CONFIG.target_file_keyMatch,
                            es_output_fields = CONFIG.output_file_keyMatch,
                            milvus_fields = CONFIG.target_file_emdMatch,
                            milvus_output_fields = CONFIG.output_file_emdMatch
                            ) 
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        # 1、FILE - keyMatch (文件文本检索)
        if CONFIG.template == TemplateCode.FILE_keyMatch:
            result = self._retrie_multi_match_search(
                            CONFIG,
                            fields = CONFIG.target_file_keyMatch,                
                            output_fields = CONFIG.output_file_keyMatch
                            )
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        # 2、FILE - emdMatch (文件向量检索)
        if CONFIG.template == TemplateCode.FILE_emdMatch:
            result = self._retrie_multi_field_search( 
                            CONFIG,
                            fields = CONFIG.target_file_emdMatch,
                            output_fields = CONFIG.output_file_emdMatch
                            )
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        # 3、FILE - mltAbsMatch (文件向量混合摘要检索)
        if CONFIG.template == TemplateCode.FILE_mltAbsMatch:
            result = self._retrie_multi_field_search(
                            CONFIG,
                            fields = CONFIG.target_file_mltAbsMatch,
                            output_fields = CONFIG.output_file_mltAbsMatch
                            )
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        # 4、FILE - esMltMatch (文件文本混合摘要检索)
        if CONFIG.template == TemplateCode.FILE_esMltMatch:
            result = self._retrie_multi_match_search(
                            CONFIG,
                            fields = CONFIG.target_file_esMltMatch,                
                            output_fields = CONFIG.output_file_esMltMatch
                            )
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        # 5、FAQ - emdMatch (问答向量检索)
        if CONFIG.template == TemplateCode.FAQ_emdMatch:
            result = self._retrie_multi_field_search(
                            CONFIG,
                            fields = CONFIG.target_faq_emdMatch,
                            output_fields = CONFIG.output_faq_emdMatch
                            )
            if result['retCode'] == ReturnCode.ERROR:
                return return_dict(code=ReturnCode.ERROR, message=result['retMsg'], data='')
            result = result['retMap']
            
        return result
    
        
    def query_match(self, params):
        """
        知识库 检索 - 主逻辑
        
        """
        logger.info("[文本检索服务] 开始 ")
        
        # 参数初始化
        CONFIG = PARAMS()
        # 参数解析
        CONFIG = self._params_resolver(CONFIG, params)
        
        # 问题降噪
        if CONFIG.IF_DENOISE:
            CONFIG.query = self._search_query_denosie(CONFIG.query)
        
        matches_list = list()
        try:
            for database in CONFIG.partitions:

                logger.info("[文本检索服务] database：{}".format(database))
                # 为向量检索匹配向量化算法 {集合名称 : 向量化算法名称}
                CONFIG.vector_alg_dict = self._get_vector_alg(CONFIG.partitions[database])
                
                # 对每一个集合，单独进行检索操作
                for collection in CONFIG.partitions[database]:
                    
                    CONFIG.collection = collection
                    
                    # 获取该集合内所有待检索的分区列表
                    CONFIG.partition_list = [item.get('categoryCode') for item in CONFIG.partitions[database][collection] if item.get('type') == 1]
                    
                    # 检索召回
                    result = self._match_by_retrival_template(CONFIG)
                    
                    # 检索开关项
                    if float(CONFIG.threshold) >= 0:
                        result = self._filter_by_threshold(result, CONFIG.threshold)

                    if CONFIG.IF_RETRIVAL_ENHANCE:
                        logger.info("[RAG-文本检索算法] 开始检索增强")
                        result = self._retrival_enhance_by_weighted_keywords(CONFIG.query, result, CONFIG.dataType)
                            
                    if CONFIG.IF_RERANK:
                        logger.info(f"[RAG-文本检索算法] 进入rerank:候选集条数：{len(result)}")
                        result = self.compress_documents(result, CONFIG.query, CONFIG.topK)
                            
                    if CONFIG.IF_COMPRESS:
                        result = self._compress_search_results(result, CONFIG.dataType, alg_type= 'bm25')
                            
                    matches_list += result

                      
            if len(CONFIG.partitions) == 1:
                final_result = matches_list
            else:
                logger.info("[文本检索服务] 多database 排序.")
                sorted_list = sorted(matches_list, key=lambda x: x['score'], reverse=True)
                if len(sorted_list) > CONFIG.topK:
                    final_result = sorted_list[:CONFIG.topK]
                else:
                    final_result = sorted_list
            
            return return_dict(code=ReturnCode.SUCESS, data=final_result, message='')
                           
        except Exception as e:
            logger.error(traceback.print_exc())
            logger.error('[文本检索服务]  检索文本异常：'+ str(e))
            return return_dict(code=ReturnCode.ERROR, message='检索文本异常：' + str(e), data='')

     


if __name__ == "__main__":
    
    test = QueryMatch()

    # 测试 FILE 检索
    # 单 database 多 collection 多 partition
    params = {'topK': 10,
              'rerank': False,
              'partition': {
                           'KLM': {'kg_cz28tbp8': [{'categoryCode': '1212', 'type': 1}
                                                     ],
                                    'kg_2o3oqlq4': [{'categoryCode': '1241', 'type': 1},
                                                    {'categoryCode': '1242', 'type': 1},
                                                    {'categoryCode': '1243', 'type': 1}
                                                    ]
                                     }
                            },
              'matchType': 'mltMatch', # emdMatch keyMatch mltMatch mltAbsMatch esMltMatch
              'query': '公司利润分配与亏损弥补方案',
              'dataType': 'FILE', 'weight': '0.4', 'threshold': '0.2'}

    a = test.query_match(params)
    


    # 测试 FAQ
    params = {'topK': 10,
              'rerank': False,
              'partition': {
                           'KLM': {'kg_3bm4291x': [{'categoryCode': '1187', 'type': 1},
                                                   {'categoryCode': '20000064', 'type': 1}
                                                     ]
                                     }
                            },
              'matchType': 'emdMatch', # emdMatch keyMatch mltMatch mltAbsMatch esMltMatch
              'query': '请帮我尽量详细的分析一下2024年各机构业务情况，尽可能提供更多的参考信息',
              'dataType': 'FAQ', 
              'weight': '0.4', 
              'threshold': '0.2'
              }

    a = test.query_match(params)
    json.loads(a)


    
    
    
