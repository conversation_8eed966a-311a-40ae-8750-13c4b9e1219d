import json
from conf.server_config import PROJECT_PATH

from src.utils.service_call import api_call
from src.utils.data_util import recursive_listdir, is_chinese, get_data

## 根据文件读取测试数据
def auto_test(test_file_path,knowledge_param,url,top_k):
    #test_file_path为测试文件所在的当前目录
    file_list = recursive_listdir(test_file_path)
    result ={}
    #遍历每个文件
    for file in file_list:
        temp_score = 0
        filename = file.split('.')[0].split('/')[-1]
        test_list = []
        with open(file, "r") as f:
            for line in f.readlines():
                test_data = json.loads(line)
                test_list.append(test_data)
        # 遍历每个问题，计算每个测试案例召回得分
        for case in test_list:
            query = case["question"]       # 问题
            from_slice = str(case["from"])    # 来源切片
            from_file = case["article"]    # 来源文件
            # print(from_file)
            # print(from_slice)
            ## 对来源切片进行分句（按照句号）
            from_slice_list = []
            tmp_slice_list = from_slice.split("。")
            for line in tmp_slice_list:
                if line != "":
                    line = is_chinese(line)
                    from_slice_list.append(line)

            knowledge_param["query"] = query
            knowledge_param["topK"] = top_k
            rag_result = api_call(url, knowledge_param)["retMap"]["result"]
            rag_result = get_data(rag_result)
            common_elements = len(set(from_slice_list) & set(rag_result))
            if common_elements == 0:
                print("查询问题：",query)
                with open(PROJECT_PATH+'/logs/bad_case.txt','a') as f:
                    f.write("查询问题："+query+'\n')
                    f.write("参考片段：\n"+str(from_slice_list) + '\n')
                    f.write("检索片段：\n"+str(rag_result) + '\n')
            temp_score = temp_score + common_elements/len(from_slice_list)
        print(f"{filename}的得分{temp_score}")
        result[filename] = temp_score
    return test_list




if __name__=="__main__":
    test_file_path = "/home/<USER>/sunklm-algorithm/test/rag_test_data_1000"
    knowledge_param ={
            "dataType": "FILE", # 知识库类型，支持FILE、FAQ
            "partition": {
                "KLM": {
                    "kg_offee2uz": ["1339","1342","1344","1345","1346","1347","1348","1349","1350","1351","1352","1359"]
                }
            }, # 向量库位置，非类目用["1182"]，类目用[partition_dict[piece["category"]]] + mix_category(piece["category"], 2)，2为需要加入的混淆类目数量
            # collection为kg_dgkwwkwz，partition有1156定长、1166语义、1170递归
            # collection为kg_f27pzigw，partition有1182m3语义、1196m3定长、1198m3递归、1204m3ocr
            # collection为kg_1s06fuqy，partition为类目递归
            # collection为kg_fe82iwg2，partition为类目语义
            # collection为kg_zaosapkl，partition为es类目递归
            # collection为kg_6gagtjcx，partition为es类目递归
            # collection为kg_offee2uz，partition为可按类目分的，包含不同切片策略的
            "threshold": "0.5", # 阈值（未启用）
            "topK": 5, # topk
            "weight": "1", # 多路召回权重
            "matchType": "mltMatch", # 检索方法，支持emdMatch、keyMatch、mltMatch(milvus+es)、esMltMatch、mltAbsMatch（milvus多路）
            "abstractSearch": False, # 检索匹配内容
            "rerank": True # 是否进行rerank
        }
    url = "http://172.1.0.154:8013/RAG-alg/query_match"
    top_k = 2






