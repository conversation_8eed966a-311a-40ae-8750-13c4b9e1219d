#!/usr/bin/env python3
# -*- coding: utf-8 -*-


## 4、检索服务开关项配置
# 是否使用向量模型API
IS_API = True 



# 是否开启问题降噪
IF_DENOISE = True

# 是否启用检索增强
IF_RETRIVAL_ENHANCE = True 

# 是否启用检索结果压缩
IF_COMPRESS = True

# 是否启用重排序
IF_RERANK = True

# 当启用检索增强或重排序时，以下参数生效
# 向量检索结果返回数量放倍数
EMBEDDING_TOPN = 5
# 文本检索结果返回数量放倍数
ES_TOPN = 10

# 是否提取摘要
# IF_ABSTRACT = False
# 是否使用摘要检索
IF_ABSTRACT_SEARCH = False
# IF_CONTEXTUAL_RETRIEVAL = False


## 5、检索服务参数配置

# 文本检索默认值
DEFAULT_TOPK = 5
DEFAULT_WEIGHT = 0.5
DEFAULT_THRESHOLD = 0
DEFAULT_MATCHTYPE = "keyMatch"
DEFAULT_MATCHCONTENT = "SPLIT"

# milvus及es的key值
# Milvus 数据库字段 - FILE
match_slice_id= "slice_id"                        # 切片id union
match_id = "file_id"                              # 文件id
match_name = "file_name"                          # 文件名.后缀
match_text = "slice"                              # 切片文本
match_abstract = "abstract"                       # 摘要文本
match_embedding = "slice_embedding"               # 向量字段
match_abstract_embedding = "abstract_embedding"   # 向量字段

# Milvus 数据库字段 - FAQ
match_id_faq = "faq_id"                           # 问答id union
match_name_faq = "question"                       # 问题
match_text_faq = "answer"                         # 答案
match_embedding_faq = "faq_embedding"             # 向量字段

# ES 数据库字段
# file_id
# file_name
# slice
# abstract

# * FAQ 没有文本数据

# match_vector = "query_vector"

# 根据检索类型，定义目标字段与输出字段
# 0、FILE - mltMatch (文件混合文本向量检索)
# pass
# 1、FILE - keyMatch (文件文本检索)
target_file_keyMatch = [match_text]
output_file_keyMatch = [match_id, match_name, match_text]
# 2、FILE - emdMatch (文件向量检索)
target_file_emdMatch = [match_embedding]
output_file_emdMatch = [match_id, match_name, match_text, match_slice_id]
# 3、FILE - mltAbsMatch (文件向量混合摘要检索)
target_file_mltAbsMatch = [match_embedding, match_abstract_embedding]
output_file_mltAbsMatch = [match_id, match_name, match_text, match_slice_id]
# 4、FILE - esMltMatch (文件文本混合摘要检索)
target_file_esMltMatch = [match_text, match_abstract]
output_file_esMltMatch = [match_id, match_name, match_text]
# 5、FAQ - emdMatch (问答向量检索)
target_faq_emdMatch = [match_embedding_faq]
output_faq_emdMatch = [match_id, match_id_faq, match_name_faq, match_text_faq]


# search_fields_file = [match_text]
# search_fields_faq  = [match_name_faq, match_text_faq]
# output_fields_file = [match_id, match_name, match_text, match_slice_id]
# output_fields_faq  = [match_id, match_id_faq, match_name_faq, match_text_faq]

# emdMatch keyMatch mltMatch mltAbsMatch esMltMatch

## 6、大模型相关配置



