"""
-----------------------------------------------------
   File Name：  agent_api.py
   Description : agent算法服务
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
import sys
import traceback
from src.utils.log_utils import return_dict
from src.agent.agents.query_agent import AwenQueryAgent
from src.utils.awen_schema import ReturnCode
from flask import Flask, request, json, Response
from conf.server_config import LLM_NAME, LLM_MAP, QUERY, REWRITE, POSTPROCESS, STREAM
from src.utils.log_utils import logger
logger = logger.bind(name="awen-ag-process-alg")
robot_agent = Flask(__name__)


@robot_agent.route("/Agent-alg/agent", methods=["POST"])
def agent_predict():
    try:
        data = json.loads(request.get_data())
        logger.info(f"corpus generate request data {str(data)}", )
        for key in ["modelType", "baseUrl", "company", "deployMode"]:
            if key not in data:
                raise KeyError(f"必需参数{key}未传入")
        model_type = data.get("modelType")
        base_url = data.get("baseUrl")
        api_key = data.get("apiKey", LLM_MAP[LLM_NAME]["api_key"])
        history = data.get("history", [])
        query = data.get("query", QUERY)
        company = data.get("company")
        deploy_mode = data.get("deployMode")
        tool_list = data.get("toolList", [])
        agent_prompt = data.get("prompt", "")
        rewrite = data.get("rewrite", REWRITE)
        stream = data.get("stream", STREAM)
        postProcess = data.get("postProcess", POSTPROCESS)
        knowledgeInfo = data.get("knowledgeInfo", {"status": 0})
        faqInfo = data.get("faqInfo", None)
        mock = AwenQueryAgent(model_type=model_type,
                              api_key=api_key,
                              base_url=base_url,
                              company=company,
                              deploy_mode=deploy_mode)
        if stream:
            res = Response(mock.stream_query(agent_prompt, query, tool_list, faqInfo, knowledgeInfo, history, rewrite, postProcess),
                           mimetype="text/event-stream")
            return res
        else:
            result = mock.query(agent_prompt, query, tool_list, faqInfo, knowledgeInfo, history, rewrite, postProcess)
            return return_dict(str(ReturnCode.SUCESS.value),  result, "success")
    except Exception as e:
        logger.error(f"请求/generate/sentence接口出错{ str(traceback.format_exc())}")
        return return_dict("1", str(e), "error")

if __name__ == "__main__":
    print(r"""
         _                        _    _____  _                 
        / \    __ _   ___  _ __  | |_ |  ___|| |  ___ __      __
       / _ \  / _` | / _ \| '_ \ | __|| |_   | | / _ \\ \ /\ / /
      / ___ \| (_| ||  __/| | | || |_ |  _|  | || (_) |\ V  V / 
     /_/   \_\\__, | \___||_| |_| \__||_|    |_| \___/  \_/\_/  
              |___/                                                                        

        """, flush=True)
    port = sys.argv[1]
    robot_agent.run(host="0.0.0.0", port=port, debug=False)
