import mysql.connector
from conf.server_config import *
from src.utils.log_utils import *
##日志模块
from src.utils.log_utils import logger
logger = logger.bind(name="utils")


class MySQLClient:
    def __init__(self, host=MYSQL_HOST, port=MYSQL_PORT, user=MYSQL_USER, password=MYSQL_PASSWORD,
                 database=MYSQL_DATABASE):
        self.mysql_host = host
        self.mysql_port = port
        self.mysql_user = user
        self.mysql_password = password
        self.mysql_database = database

    def get_connect(self):
        """
        建立与MySQL数据库的连接
        """
        try:
            conn = mysql.connector.connect(
                host=self.mysql_host,
                port=self.mysql_port,
                user=self.mysql_user,
                password=self.mysql_password,
                database=self.mysql_database
            )
            logger.info("MySQL数据库连接成功")
            return conn
        except mysql.connector.Error as err:
            logger.infot(f"连接MySQL数据库失败: {err}")
