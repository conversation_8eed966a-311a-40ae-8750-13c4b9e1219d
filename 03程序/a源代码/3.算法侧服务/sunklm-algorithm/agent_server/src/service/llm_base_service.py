"""
-----------------------------------------------------
   File Name： llm_base_service.py
   Description : 文本改写
   Author :  mhxuan
   date： 2024/8/13 18:05
   Change Activity:  增加假设问题改写
-----------------------------------------------------
"""
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from src.llms import prompt_template
from src.llms.llm_chat import ModelAPI
from conf.server_config import LLM_NAME, LLM_MAP
REWRITER_PROMPT = ChatPromptTemplate.from_template(prompt_template.REWRITER_PROMPT)
HYDE_PROMPT = ChatPromptTemplate.from_template(prompt_template.HYDE_PROMPT)
CONTEXTUAL_PROMPT = ChatPromptTemplate.from_template(prompt_template.CONTEXTUAL_PROMPT1)
CONSISTENCY_PROMPT = ChatPromptTemplate.from_template(prompt_template.CONSISTENCY_PROMPT)
FAQ_PROMPT = ChatPromptTemplate.from_template(prompt_template.FAQ_PROMPT)
from src.utils.log_utils import logger
logger = logger.bind(name="llm_base_server")
# EVAL_PROMPT = ChatPromptTemplate.from_template()


class QueryLLMService:
    """
    Autor：mhxuan
    Description: 基于大模型实现的基础服务，目前实现问题改写、FAQ抽取、后处理服务

    Args:
        model_type (str): 模型类别
        base_url (str): 模型地址
        api_key (str): key
        company(str)：: 模型公司
    """

    def __init__(
            self,
            model_type: str = LLM_NAME,
            base_url: str = LLM_MAP[LLM_NAME]["base_url"],
            api_key: str = LLM_MAP[LLM_NAME]["api_key"],
            company: str = LLM_MAP[LLM_NAME]["company"],
            deploy_mode: int = LLM_MAP[LLM_NAME]["deploy_mode"],
    ):
        self.model_type = model_type
        self.base_url = base_url
        self.api_key = api_key
        self.company = company
        self.deploy_mode = deploy_mode
        self.llm = ModelAPI(model_type=self.model_type,
                            api_key=self.api_key,
                            company=self.company,
                            deploy_mode=self.deploy_mode,
                            base_url=self.base_url)
        self._output_parser = StrOutputParser()

    def rewrite(self, query: str, chat_history: list, prompt: str =REWRITER_PROMPT) -> str:
        if chat_history == "" or chat_history is None:
            chat_history = "Empty"
        format_query = prompt.invoke({"chat_history": chat_history, "current": query}).to_string()
        logger.info(f"问题改写prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite


    def hyde(self, query: str, prompt: str =HYDE_PROMPT) -> str:
        format_query = prompt.invoke({"query": query}).to_string()
        logger.info(f"假设问题改写prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite

    def contextual_retrieval(self, content: str, prompt: str =CONTEXTUAL_PROMPT) -> str:
        format_query = prompt.invoke({"chunk_content": content}).to_string()
        logger.info(f"上下文生成的prompt:\n\n{format_query}\n")
        rewrite = self.llm.generate(format_query)
        return rewrite


    def extract_faq(self, num: int, context: str, prompt: str =FAQ_PROMPT) -> str:
        if context == "" or context is None:
            context = "Empty"
        format_query = prompt.invoke({"context": context, "num": num}).to_string()
        logger.info(f"FAQ抽取的prompt:\n\n{format_query}\n")
        faq_result = self.llm.generate(format_query)
        return faq_result

    def consistency(self, answer: str, chat_history: list, prompt: str =CONSISTENCY_PROMPT) -> str:
        if chat_history == "" or chat_history is None:
            chat_history = "Empty"
        format_query = prompt.invoke({"answer": answer, "chat_history": chat_history}).to_string()
        logger.info(f"一致性检测的prompt:\n\n{format_query}\n")
        faq_result = self.llm.generate(format_query)
        return faq_result



if __name__ == '__main__':
   QS = QueryLLMService()
   query = "手机银行操作知识要点\n\n一、手机银行下载 \n\n1.苹果/安卓市场搜索并下载“吉林银行—手机银行”。\n\n2.门户网站、微信公众号、营业网点扫描二维码，根据手机选择安卓或苹果版本。\n\n" \
           "注意：\n\n1.安卓手机扫码下载手机银行出现报错或乱码：\n\n（1）确认下载地址或扫描二维码是否正确且为最新地址或二维码。\n\n（2）确认使用浏览器支持扫描二维码，建议用手机自带浏览器或百度。\n\n" \
           "2.三星手机无法通过扫码和公众号安装手机银行：\n\n（1）确认系统版本，手机银行支持最低的系统版本为安卓5.0 苹果9.0系统版本，低于此版本则无法使用新版手机银行。\n\n" \
           "（2）如系统版本达标后，则下载时请使用QQ、百度浏览器打开下载地址。\n\n二、手机银行开通、注册（需年满16周岁（含）以上）"
   result = QS.contextual_retrieval(query)
   print(result)
