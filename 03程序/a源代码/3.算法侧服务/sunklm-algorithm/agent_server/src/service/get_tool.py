#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
-----------------------------------------------------
   File Name：  get_tool.py
   Description : 插件信息获取
   Author :  Taiho
   date： 2024/8/13
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
from conf.server_config import API_TABLE
from src.utils.mysql_conn import *
import json
from src.utils.db import *


class GetToolInfo:
    """
    Autor：Taiho
    Description: 从数据库获取所有可用的插件的相关信息

    Args:

    """
    def __init__(self) -> None:
        self.tool_info = []

    def renew(self) -> None:
        """
            Autor：Taiho
            Description: 初始化插件的相关信息

            Args:

            Returns:
        """
        self.tool_info = []

    def __call__(self):
        """
        Autor：Taiho
        Description: 从数据库获取当前所有可用的插件的相关信息

        Args:

        Returns:
             插件信息的列表，列表结构如下：
                [{"插件ID":{"description":"插件功能描述",
                            "url":"api的url",
                            "headers":"api的headers",
                            "name":"插件名称"},
                            "parameters":{"参数1":{"description":"参数1的描述","type":"参数类型","default":"默认参数"},
                                          "参数2":{"description":"参数2的描述","type":"参数类型","default":"默认参数"},
                                          "参数3":{"description":"参数3的描述","type":"参数类型","default":"默认参数"}},
                            "required":['要求的必填参数'],##可为空
                            "type":"插件类型（api、函数）"}}]

        SQL语句查询参数注释：
            tool_id 插件ID
            url 插件调用地址
            headers 请求头
            tool_desc 知识空间描述
            tool_name 知识空间名称
            params 插件参数列表信息，Json格式，单个参数包括name(参数名)、type(参数类型string,int,datetime)、\
                   isInput(是否入参：1入参，0出参)、isRequired(是否必须：1是，0不是)、description(参数描述)、\
                   default(参数默认值)
            tool_type 插件类型：1 API，2 代码
            is_enable 是否可用：0 不可用，1 可用
        """
        # 从连接池获取连接
        conn = POOL.connection()
        # 创建游标对象
        cursor = conn.cursor(buffered=True)
        query = "SELECT tool_id, url, tool_desc, tool_name, input_data, tool_type, is_enable, headers FROM " + API_TABLE
        cursor.execute(query)
        logger.info(f'查询语句为{query}')
        conn.commit()
        infoList = cursor.fetchall()
        # 根据获得数据库数据，解析成下游任务所需格式
        for each_api_info in infoList:
            # 解析params
            each_api = {}
            paramerter = {}
            required = []
            parmas = json.loads(each_api_info[4])
            for item in parmas:
                paramerter[item["name"]] = {
                    "description": item["description"],
                    "type": item["type"],
                    "default": item.get('default', None)
                }
                if item["isRequired"] == 1:
                    required.append(item["name"])
            enable = True

            # 查看是否可用
            if each_api_info[6] == 0:
                enable = False

            # 处理headers（如果headers为空，添加默认headers的值
            headers = each_api_info[7]
            if not headers:
                headers = {
                    'Content-Type': 'application/json'
                }
            if isinstance(headers, str):
                headers = eval(headers)
            # 整合所有api信息
            each_api[each_api_info[0]] = {
                "url": each_api_info[1],
                "headers": headers,
                "description": each_api_info[2],
                "name": each_api_info[3],
                "parameters": paramerter,
                "required": required,
                "type": each_api_info[5],
                "enable": enable
            }

            # 整合结果添加到tool_info
            self.tool_info.append(each_api)
        return self.tool_info

