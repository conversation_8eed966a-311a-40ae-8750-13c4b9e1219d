# -*- coding=utf-8 -*-
"""
-----------------------------------------------------
   File Name：  llm_chat.py
   Description : 大模型推理服务，支持对接多种厂商的模型。提供了流式推理和非流式推理两种推理方式
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

import json
import requests
import traceback
from openai import OpenAI
from lagent.llms.base_api import BaseAPIModel
from src.utils.awen_schema import ReturnCode
from src.utils.awen_schema import ChatFormatCode, HeadersCode, CompanyCode
from lagent.llms.base_llm import LMTemplateParser
from conf.server_config import (MAX_SEQ_LEN, QUERY_PER_SECOND,
                                    LLM_MAP,LLM_NAME,
                                    MAX_OUT_LEN, TEMPERATURE, TOP_P, TOP_K)
from src.utils.log_utils import logger
from src.llms.meta_template import META_TEMPLATE
from typing import Dict, Tuple, Generator, List, Optional, Union
logger = logger.bind(name="LLM_predict")


class ModelAPI(BaseAPIModel):
    """
     Description: 模型推理封装器，可以调用不同供应商的开源模型。
     Args：
        model_type (str): 与OpenAI API兼容的模型类型。
        base_url(str)：OpenAI API的基础URL。例如："https://api.baichuan-ai.com/v1/chat/completions"
        或 "https://dashscope.aliyuncs.com/compatible-mode/v1"。
        api_key（str）：与OpenAI API兼容的模型API的api_key。
        company(str)：开源模型供应商，
        max_seq_len (int): 模型允许的最大序列长度。
        query_per_second (int): 在两次连续调用API之间允许的最大查询次数。默认为1。
        meta_template (Dict, 可选): 模型的元提示模板（如果需要的话），用于注入或封装任何元指令。
    """
    is_api: bool = True

    def __init__(self,
                 model_type: str = LLM_NAME,
                 max_seq_len: int = MAX_SEQ_LEN,
                 query_per_second: int = QUERY_PER_SECOND,
                 base_url: str = LLM_MAP[LLM_NAME]["base_url"],
                 api_key: str = LLM_MAP[LLM_NAME]["api_key"],
                 company: str = LLM_MAP[LLM_NAME]["company"],
                 deploy_mode: int = LLM_MAP[LLM_NAME]["deploy_mode"],
                 meta_template: Optional[Dict] = META_TEMPLATE):
        super().__init__(
            model_type=model_type,
            meta_template=meta_template,
            query_per_second=query_per_second)
        self.max_seq_len = max_seq_len
        self.company = company
        self.deploy_mode = deploy_mode
        self.model_type = model_type
        self.api_key = api_key
        self.url = base_url
        if self.api_key is None:
            headers = HeadersCode.NORMAL_HEADERS
        else:
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        self.template_parser = LMTemplateParser(meta_template)
        self.headers = headers
        self.context_window = self.max_seq_len

    def generate(self,
                 inputs: Union[List, str],
                 max_out_len: int = MAX_OUT_LEN,
                 history: list = None,
                 temperature: Optional[float] = TEMPERATURE,
                 top_p: Optional[float] = TOP_P,
                 top_k: Optional[int] = TOP_K,
                 **kwargs):
        return self._generate(inputs, max_out_len, history, temperature, top_p, top_k, **kwargs)

    def _generate(self,
                  inputs: Union[List, str],
                  max_out_len: int = MAX_OUT_LEN,
                  history: Optional[list] = None,
                  temperature: Optional[float] = TEMPERATURE,
                  top_p: Optional[float] = TOP_P,
                  top_k: Optional[int] = TOP_K,
                  **kwargs) -> str:
        """
        Description：根据输入的列表生成结果。
        Args：
            inputs (str 或 List): 一个字符串或PromptDict。PromptDict应该按照OpenCompass的API格式组织。
            temperature (float, 可选): 使用的采样温度。
            top_p (float, 可选): 生成过程中核采样方法的概率阈值，例如，当值为0.8时， 仅保留概率总和大于或等于0.8的
        最小可能标记集作为候选集。值范围为(0,1.0)，值越大，生成的随机性越高；值越小，生成的确定性越高。
            top_k：采样个数
            max_out_len (int): 输出的最大长度。

        Return：
            str: 生成的字符串。
        """
        max_out_len = min(
            max_out_len,
            self.context_window - self.get_token_len(str(inputs)) - 100)
        if max_out_len <= 0:
            logger.info("模型可生成的长度小于0，请检查总输入长度和最大输出长度值")
            raise ValueError(f"目前大模型仅支持{MAX_SEQ_LEN}长度输入,prompt输入长度为{len(str(inputs))}，导致模型可生成长度为0,请检查输入长度")
        try:
            data = \
                {
                    "model": self.model_type,
                    "stream": False,
                    "temperature": temperature,
                    "max_tokens":max_out_len,
                    "top_p": top_p
                }
            if not isinstance(inputs, list):
                inputs = eval(self._make_content(inputs, history))
            if self.deploy_mode == CompanyCode.PRIVATE:
                data["top_k"] = top_k
                data["messages"] = inputs
                print("传入的数据：", data)
                bc_res = requests.post(
                    self.url, headers=self.headers, json=data).json()
            elif self.deploy_mode == CompanyCode.OPEN:
                client = OpenAI(api_key=self.api_key, base_url=self.url)
                completion = client.chat.completions.create(model=self.model_type,
                                                            temperature=temperature,
                                                            top_p=top_p,
                                                            messages=inputs)
                bc_res = completion.dict()
            else:
                raise KeyError(f"参数conpany：{self.company}部署方式{self.deploy_mode}不符合规范，请检查")
            logger.info(f"---------大模型输入 start------\n\n{str(data)}\n---------大模型输入 end------")
            if "done_reason" in bc_res:
                answer = bc_res['message']['content']
                promptToken = bc_res['prompt_eval_count']
                completionTokens = bc_res['eval_count']
                totalTokens = promptToken + completionTokens
            else:
                answer = bc_res["choices"][0]["message"]["content"]
                promptToken = bc_res["usage"]["prompt_tokens"]
                completionTokens = bc_res["usage"]["completion_tokens"]
                totalTokens = bc_res["usage"]["total_tokens"]
            logger.info(f"---------大模型输出 start------\n\n{answer}\n---------大模型输出 end------")
            if answer is None or answer == '':
                raise KeyError(f"目前大模型仅支持{MAX_SEQ_LEN}长度输入,而输入的prompt长度为:{len(str(inputs))}，导致模型可生成长度为0,请检查输入长度")
            else:
                raw_response = {"answer": answer,
                                "modelType": self.model_type,
                                "temperature": temperature,
                                "topP": top_p,
                                "topK": top_k,
                                "promptToken": promptToken,
                                "completionTokens": completionTokens,
                                "totalTokens": totalTokens}

            return raw_response
        except requests.ConnectionError:
            raise requests.ConnectionError(f"服务地址为：{self.url}连接失败")

    def generate_stream(
            self,
            inputs: Union[List, str],
            max_out_len=MAX_OUT_LEN,
            history: Optional[list] = None,
            temperature: Optional[float]=TEMPERATURE,
            top_p: Optional[float]=TOP_P,
            top_k: Optional[int]=TOP_K,
    ) -> Generator:
        """
        Description:根据输入的列表生成流式结果。
        Args：
            inputs (str 或 List): 一个字符串或PromptDict。PromptDict应该按照OpenCompass的API格式组织。
            temperature (float, 可选): 使用的采样温度。
            top_p (float, 可选): 生成过程中核采样方法的概率阈值，例如，当值为0.8时， 仅保留概率总和大于或等于0.8的最小
            可能标记集作为候选集。值范围为(0,1.0)，值越大，生成的随机性越高；值越小，生成的确定性越高。
            top_k：采样个数
            max_out_len (int): 输出的最大长度。
        Return：
            generator: 生成的生成器
        """
        try:
            max_out_len = min(
                max_out_len,
                self.context_window - self.get_token_len(str(inputs)) - 100)
            if max_out_len <= 0:
                logger.info("模型可生成的长度小于0，请检查总输入长度和最大输出长度值")
                raise ValueError(f"prompt输入长度为{self.get_token_len(str(inputs))}，导致模型可生成长度为0")
            data = {"model": self.model_type, "stream": True, "temperature": temperature, "top_p": top_p,
                    "max_tokens": max_out_len}
            if not isinstance(inputs, list):
                inputs = eval(self._make_content(inputs, history))
            if self.deploy_mode == CompanyCode.PRIVATE:
                data["top_k"] = top_k
                data["messages"] = inputs
                print("传入参数：", data)
                response = requests.post(self.url, headers=self.headers, json=data, timeout=60, stream=True)
            elif self.deploy_mode == CompanyCode.OPEN:
                client = OpenAI(api_key=self.api_key, base_url=self.url)
                completion = client.chat.completions.create(model=self.model_type,
                                                            temperature=temperature,
                                                            top_p=top_p,
                                                            stream=True,
                                                            messages=inputs,
                                                            stream_options={"include_usage": False})
                response = completion.response
            else:
                raise KeyError(f"参数conpany：{self.company}的部署方式{self.deploy_mode}不符合规范，请检查")
            logger.info(f"---------大模型输入 start------\n\n{str(data)}\n---------大模型输入 end------")
            if response.status_code == 200:
                for line in response.iter_lines():
                    # 判断line为空
                    if line:
                        try:
                            line = line.decode("utf-8")
                        except:
                            line = line
                        line = line.strip("data").strip(": ")
                        if "created_at" in line:
                            if '"done_reason":"stop","done":true' in line:
                                data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                                        "retMap": {"answer": "Stream ended"}}
                                yield f"data:{json.dumps(data, ensure_ascii=False)}\n\n"
                                break
                            elif line and line[0] == "{" and line[-1] == "}":
                                line = json.loads(line)
                                res = line['message']['content']
                        else:
                            if line == "[DONE]":
                                data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success", "retMap": {"answer": "Stream ended"}}
                                yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"
                                break
                            elif line and line[0] == "{" and line[-1] == "}":
                                line = json.loads(line)
                                res = line["choices"][0]["delta"].get("content")
                        if res:
                            data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success", "retMap":{"answer": str(res)}}
                            yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"
                        else:
                            pass
            else:
                data = {"retCode": ReturnCode.ERROR.value, "retMsg": "error", "retMap": {"answer": f"{response.status_code}"}}
                yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"
        except Exception as e:
            logger.error(f"请求接口出错:{str(traceback.format_exc())}")
            data = {"retCode": ReturnCode.ERROR.value, "retMsg": "error", "retMap": {"answer": f"{e}"}}
            yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"


    def _make_content(
            self,
            query: str,
            history: List[Tuple[str, str]] = None,
            chat_format: str = ChatFormatCode.OPENAI_FORMAT,
    )-> str:
        """
        Description:历史封装和格式化，将用户的问题和历史信息封装成openai接口需要的输入格式，messages格式
        Args:
            query：用户输入的问题
            history：用户输入的历史信息
            chat_format：大模型支持的输入格式，openai接口格式:[{"role":"user","content":"你好"},{"role":"assistant","content":"你好"}]
        Return
           str：openai接口输入的格式
        """
        max_window_size = self.max_seq_len
        if history is None:
            history = []
        if chat_format == ChatFormatCode.OPENAI_FORMAT:
            messages = []
            current_context_size = 0
            for item in history:
                item_list = item.split(":")
                query_text = dict(role=item_list[0], content=':'.join(item_list[1:]))
                current_context_size += len(':'.join(item_list[1:]))
                if current_context_size < max_window_size:
                    messages.append(query_text)
                else:
                    break
            messages.append(dict(role="user", content=query))
            raw_text = str(messages)
        elif chat_format == ChatFormatCode.RAW_FORMAT:
            raw_text = query
        elif chat_format == ChatFormatCode.GLM_FORMAT:
            im_start, im_end = "<|im_start|>", "<|im_end|>"
            def _tokenize_str(role, content):
                return f"{role}\n{content}"

            for turn_query, turn_response in reversed(history):
                query_text = _tokenize_str("user", turn_query)
                response_text = _tokenize_str("assistant", turn_response)
                prev_chat = (
                    f"\n{im_start}{query_text}{im_end}\n{im_start}{response_text}{im_end}")
                current_context_size = (len(prev_chat))
                if current_context_size < max_window_size:
                    raw_text = prev_chat + raw_text
                else:
                    break
            raw_text += f"\n{im_start}user\n{query}{im_end}\n{im_start}assistant\n"
        else:
            raise NotImplementedError(f"Unknown chat format {chat_format!r}")
        return raw_text

    def get_token_len(self, prompt: str) -> int:
        """
        Description:获取分词后字符串的长度。目前只计算英文和中文字符。如果需要更精确的长度，鼓励用户重写此方法。
        Args:
            prompt (str): 输入字符串。
        Return:
            int: 输入分词的长度
        """
        return len(prompt)

