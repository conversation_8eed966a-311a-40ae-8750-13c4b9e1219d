# -*- coding=utf-8 -*-
"""
-----------------------------------------------------
   File Name：  meta_template.py
   Description : 模型的meta template
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

# The model"s meta prompt  template if needed
META_TEMPLATE = [
    dict(role="system", begin="<|im_start|>system\n", end="<|im_end|>\n"),
    dict(role="user", begin="<|im_start|>user\n", end="<|im_end|>\n"),
    dict(
        role="assistant",
        begin="<|im_start|>assistant\n",
        end="<|im_end|>\n",
        generate=True)
]


INTERNLM2_META = [
    dict(
        role="system",
        begin=dict(
            with_name="<|im_start|>system name={name}\n",
            without_name="<|im_start|>system\n",
            name={
                "interpreter": "<|interpreter|>",
                "plugin": "<|plugin|>",
            }),
        end="<|im_end|>\n",
    ),
    dict(
        role="user",
        begin=dict(
            with_name="<|im_start|>user name={name}\n",
            without_name="<|im_start|>user\n",
        ),
        end="<|im_end|>\n"),
    dict(
        role="assistant",
        begin=dict(
            with_name="<|im_start|>assistant name={name}\n",
            without_name="<|im_start|>assistant\n",
            name={
                "interpreter": "<|interpreter|>",
                "plugin": "<|plugin|>",
            }),
        end="<|im_end|>\n"),
    dict(
        role="environment",
        begin=dict(
            with_name="<|im_start|>environment name={name}\n",
            without_name="<|im_start|>environment\n",
            name={
                "interpreter": "<|interpreter|>",
                "plugin": "<|plugin|>",
            }),
        end="<|im_end|>\n"),
]
