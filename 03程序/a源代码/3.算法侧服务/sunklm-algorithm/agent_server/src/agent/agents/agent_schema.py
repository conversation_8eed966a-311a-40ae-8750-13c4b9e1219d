"""
-----------------------------------------------------
   File Name：  agent_scheme.py
   Description : 枚举值定义类
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
from enum import IntEnum


class ChatFormatCode:
    """
    openai:openai格式输入[{'role':'user','content':'你好'},{'role':'assistant','content':'你好。很高兴为您服务'}]
    glm：GLM格式输入\n<|im_start|>role\n你好<|im_end|>\n<|im_start|>assistant\n你好。很高兴为您服务<|im_end|>
    raw:不对query做任何处理
    """
    OPENAI_FORMAT = "openai"
    GLM_FORMAT = "glm"
    RAW_FORMAT = "raw"


class HeadersCode:
    """
    request请求头格式
    """
    NORMAL_HEADERS = {"Content-Type": "application/json"}


class CompanyCode:
    """
    私有化模型和开源模型请求的方式不同，所以通过conpany参数进行区分
    private：私有化部署的模型的标志，company为：厂商名_private
    open:开源部署的模型的标志，company为：厂商名_open
    """
    PRIVATE = "private"
    OPEN = "open"


class NodeTypeCode:
    """
    节点类型枚举：
    CALL_LLM：调用大模型
    CALL_PLUG:调用插件
    CALL_KNOWLEDGE:调用知识库
    """
    CALL_LLM = "调用LLM"
    CALL_PLUG = "调用插件"
    CALL_KNOWLEDGE = "调用知识库"
    CALL_FAQ = "调用FAQ库"


class KnowledgeCode(IntEnum):
    """
    知识库状态枚举：
    DISABLE:知识库禁用
    AUTO:知识库自动调用，在agent之前调用知识库
    DEMAND：知识库按需调用，让模型判断是否需要调用知识库
    """
    DISABLE = 0
    AUTO = 1
    DEMAND = 2



class PlugTypeCode():
    """
    插件调用类型
    API_PLUG：使用api调用的插件
    """
    API_PLUG = "Tool_Call"
