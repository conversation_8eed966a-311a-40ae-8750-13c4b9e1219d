"""
-----------------------------------------------------
   File Name：  query_agent.py
   Description : agent主流程,实现agent运行整个流程
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

import time
import json
from src.agent.agents.agent_util import format_llm_result,rag_structural
from src.agent.actions.tool_call import Tool_Call, date_process
from src.llms.llm_chat import ModelAPI
from src.llms.prompt_template import DEFAULT_TEMPLET_CN, DEFAULT_TEMPLET_EN, DEFAULT_TEMPLET_CN_STREAM, DEFAULT_TEMPLET_EN_STREAM
from src.agent.agents.react_awen import ReAct
from src.agent.agents.agent_schema import KnowledgeCode
from src.utils.awen_schema import ReturnCode
from src.service.llm_base_service import QueryLLMService
from lagent.actions import ActionExecutor
from conf.server_config import LLM_NAME, LLM_MAP
from src.agent.actions import open_tool
from src.utils.log_utils import logger
logger = logger.bind(name="awen-ag-process-alg")
QR = QueryLLMService()

class AwenQueryAgent:
    """
    Description:agent主流程模块
    Args:
        model_type (str): 与OpenAI API兼容的模型类型。
        base_url(str)：OpenAI API的基础URL。例如："https://api.baichuan-ai.com/v1/chat/completions"
        或 "https://dashscope.aliyuncs.com/compatible-mode/v1"。
        api_key（str）：与OpenAI API兼容的模型API的api_key。
        company(str)：开源模型供应商
    """
    def __init__(
            self,
            model_type: str = LLM_NAME,
            base_url: str = LLM_MAP[LLM_NAME]["base_url"],
            api_key: str = LLM_MAP[LLM_NAME]["api_key"],
            company: str = LLM_MAP[LLM_NAME]["company"],
            deploy_mode: int = LLM_MAP[LLM_NAME]["deploy_mode"]
    ):
        self.model_type = model_type
        self.base_url = base_url
        self.api_key = api_key
        self.company = company
        self.deploy_mode = deploy_mode
        self.llm = ModelAPI(model_type=self.model_type,
                            api_key=self.api_key,
                            company=self.company,
                            deploy_mode=self.deploy_mode,
                            base_url=self.base_url)

    def check_prompt(self, prompt: str, tool_list: list, stream: bool=None)->str:
        """
        Description：prompt校验，判断prompt是英文还是中文，然后拼接对应的固定输出格式
        Args:
            prompt：用户输入的prompt
            tool_list：用户使用的插件列表
        Return：
             str:校验拼接后的prompt
        """
        if stream:
            default_templte_cn = DEFAULT_TEMPLET_CN_STREAM
            default_templte_en = DEFAULT_TEMPLET_EN_STREAM
        else:
            default_templte_cn = DEFAULT_TEMPLET_CN
            default_templte_en = DEFAULT_TEMPLET_EN
        try:
            #language = detect(prompt)
            language = "zh-cn"
            if len(tool_list) == 0:
                prompt = prompt
            else:
                if language == "zh-cn":
                    prompt = prompt.strip("\n") + "\n" + default_templte_cn
                else:
                    prompt = prompt.strip("\n") + "\n" + default_templte_en
        except:
            prompt = prompt.strip("\n") + "\n" + DEFAULT_TEMPLET_EN
        return prompt

    def query(self, prompt: str, query: str, tool_list: list=[], faqInfo: dict=None, knowledgeInfo: dict={}, history: list=[],
              rewrite: bool=False, postProcess: bool=False):
        """
        Description：agent主函数非流式输出
        Args:
            prompt:  用户传入的完整的prompt
            query:   用户问题
            tool_list:  用户传入的插件id，和prompt包含的插件一致
            knowledgeInfo:  知识库相关参数信息
            history:  历史对话信息
            rewrite:  是否开启问题改写,
            postProcess：是否开启后处理模块
            stream:是否开启流式输出
        Return:
            dict:agent返回结果，和其中节点信息
        """
        tool_call = Tool_Call()
        tool_call.get_tool()
        action_list = list(tool_call.tool_detail.keys())
        # 插件map
        tool_maps = {}
        for action_name in action_list:
            if action_name in open_tool:
                tool_maps[open_tool[action_name].name] = open_tool[action_name]
            else:
                tool_maps[action_name] = tool_call
        # prompt校验，拼接固定的输出格式
        prompt = self.check_prompt(prompt, tool_list)
        logger.info(f"拼接后的prompt为：{prompt}")
        # 如果rewrite为true，开启问题改写，否则关闭
        if rewrite:
            raw_query = query
            query = QR.rewrite(raw_query, history)["answer"]
            logger.info(f"改写前问题：\n\n{raw_query},\n\n改写后问题：\n\n{query}")
        # TODO 后续实现后处理逻辑
        # 判断是否需要进行后处理， 如果postProcess为true，开启问题改写，否则关闭
        if postProcess:
            pass
        result = {}
        t0 = time.time()
        start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        node_result = []
        # 判断是否需要查询FAQ库
        if faqInfo:
            new_action_input = {}
            t3 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # 知识库调用参数解析
            tool_name = tool_call.id_action[faqInfo["id"]]
            params = date_process(faqInfo)
            params["query"] = query
            new_action_input["tool_name"] = tool_name
            new_action_input["tool_params"] = params
            # 插件执行
            t_k = time.time()
            res = tool_call(str(new_action_input))
            logger.info(f"rag检索耗时:{round(time.time()-t_k, 2)}s")
            faq_result =[]
            try:
                raw_result = res.result["text"]["retMap"]["result"]
                for item in raw_result:
                    if item["score"] >= faqInfo["threshold"]:
                        faq_result.append(item)
                    else:
                        pass
            except:
                raise KeyError("faq检索报错，请查看8013端口")
            logger.info(f"知识库faq检索执行结果为：{raw_result}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t3) * 1000, 0)
            if len(faq_result)>0:

                llm_temp_result = format_llm_result("调用FAQ库", node_use_time, node_start_time, node_end_time,
                                                    str(params), tool_name, str(faq_result), True)
                result["answer"] = faq_result[0]["answer"]
                result["type"] = "开始"
                result["name"] = "UserInput"
                result["modelType"] = self.model_type
                result["startTime"] = start_time
                result["endTime"] = node_end_time
                result["totalUseTime"] = node_use_time
                result["totalPromptToken"] = 0
                result["totalCompletionTokens"] = 0
                result["totalTotalTokens"] = 0
                result["status"] = "成功"
                result["nodeDetail"] = [llm_temp_result]
                return result
            else:
                llm_temp_result = format_llm_result("调用FAQ库", node_use_time, node_start_time, node_end_time,
                                                    str(params), tool_name, str(raw_result), True)
                node_result.append(llm_temp_result)
        # 知识库状态判断
        if knowledgeInfo["status"] == KnowledgeCode.DISABLE.value:
            new_tool_list = tool_list
        elif knowledgeInfo["status"] == KnowledgeCode.DEMAND.value:
            tool_list.append(knowledgeInfo["id"])
            new_tool_list = tool_list
        elif knowledgeInfo["status"] == KnowledgeCode.AUTO.value:
            new_action_input = {}
            new_tool_list = tool_list
            t3 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # 知识库调用参数解析
            tool_name = tool_call.id_action[knowledgeInfo["id"]]
            params = date_process(knowledgeInfo)
            params["query"] = query
            new_action_input["tool_name"] = tool_name
            new_action_input["tool_params"] = params
            # 插件执行
            t_k = time.time()
            res = tool_call(str(new_action_input))
            logger.info(f"rag检索耗时:{round(time.time()-t_k, 2)}s")
            raw_result = res.result["text"]
            # 知识库返回结果判断
            if raw_result["retCode"] == str(ReturnCode.SUCESS.value):
                # 知识库返回结果结构化解析
                rag_result = rag_structural(raw_result["retMap"]["result"])
                isSuccess = True
            elif raw_result["retCode"] == str(ReturnCode.ERROR.value):
                rag_result = None
                isSuccess = False
                logger.info(f"知识库检索异常，请查看知识库检索接口,返回状态为{raw_result['retCode']}")
            history.append(f"system:知识库返回的参考资料：【{rag_result}】")
            logger.info(f"知识库执行结果为：{rag_result}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t3) * 1000, 0)
            # 判断知识库返回结果是否为None
            if rag_result is None:
                output = ''
            else:
                output = str(raw_result["retMap"]["result"])
            # 保存插件结果
            llm_temp_result = format_llm_result("调用知识库", node_use_time, node_start_time, node_end_time,
                                                str(params), tool_name, output, isSuccess)
            node_result.append(llm_temp_result)
        else:
            raise KeyError(f"目前不支持知识库状态为：{knowledgeInfo['status']},请检查参数")
        # 获取用户输入的tool_list中的插件信息
        enable_plugin_action = []
        enable_plugin_id = {}
        for id in new_tool_list:
            try:
                enable_plugin_action.append(tool_maps[tool_call.id_action[id]])
                enable_plugin_id[tool_call.id_action[id]] = tool_maps[tool_call.id_action[id]]
            except:
                logger.info(f"用户配置的插件id{id}不在数据库或者不可用")
        logger.info(f"最终可用插件为：{enable_plugin_id.keys()}")
        chatbot = ReAct(llm=self.llm,
                        action_executor=ActionExecutor(actions=enable_plugin_action))
        agent_return, node_res = chatbot.chat(prompt, query, history, enable_plugin_id, knowledgeInfo)
        node_result = node_result + node_res
        end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        total_use_time = round((time.time() - t0) * 1000, 0)
        result["answer"] = agent_return.response
        result["type"] = "开始"
        result["name"] = "UserInput"
        result["modelType"] = self.model_type
        result["startTime"] = start_time
        result["endTime"] = end_time
        result["totalUseTime"] = total_use_time
        result["totalPromptToken"] = sum([item["nodePromptToken"] for item in node_result])
        result["totalCompletionTokens"] = sum([item["nodeCompletionTokens"] for item in node_result])
        result["totalTotalTokens"] = sum([item["nodeTotalTokens"] for item in node_result])
        result["status"] = "成功"
        result["nodeDetail"] = node_result
        return result

    def stream_query(self, prompt: str, query: str, tool_list: list=[], faqInfo: dict=None, knowledgeInfo: dict={}, history: list=[],
              rewrite: bool=False, postProcess: bool=False):
        """
        Description：agent主函数，流式输出
        Args:
            prompt:  用户传入的完整的prompt
            query:   用户问题
            tool_list:  用户传入的插件id，和prompt包含的插件一致
            knowledgeInfo:  知识库相关参数信息
            history:  历史对话信息
            rewrite:  是否开启问题改写,
            postProcess：是否开启后处理模块
        Return:
            dict:agent返回结果，和其中节点信息
        """
        tool_call = Tool_Call()
        tool_call.get_tool()
        action_list = list(tool_call.tool_detail.keys())
        # 插件map
        tool_maps = {}
        for action_name in action_list:
            if action_name in open_tool:
                tool_maps[open_tool[action_name].name] = open_tool[action_name]
            else:
                tool_maps[action_name] = tool_call
        # prompt校验，拼接固定的输出格式
        prompt = self.check_prompt(prompt, tool_list, stream=True)
        logger.info(f"拼接后的prompt为：{prompt}")
        # 如果rewrite为true，开启问题改写，否则关闭
        if rewrite:
            raw_query = query
            query = QR.rewrite(raw_query, history)["answer"]
            logger.info(f"改写前问题：\n\n{raw_query},\n\n改写后问题：\n\n{query}")
        # TODO 后续实现后处理逻辑
        # 判断是否需要进行后处理， 如果postProcess为true，开启问题改写，否则关闭
        if postProcess:
            pass
        result = {}
        t0 = time.time()
        start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        node_result = []
        if faqInfo:
            new_action_input = {}
            t3 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # 知识库调用参数解析
            tool_name = tool_call.id_action[faqInfo["id"]]
            params = date_process(faqInfo)
            params["query"] = query
            new_action_input["tool_name"] = tool_name
            new_action_input["tool_params"] = params
            print(new_action_input)
            # 插件执行
            t_k = time.time()
            res = tool_call(str(new_action_input))
            logger.info(f"rag检索耗时:{round(time.time()-t_k, 2)}s")
            faq_result =[]
            try:
                print("res",res)
                raw_result = res.result["text"]["retMap"]["result"]
                for item in raw_result:
                    if item["score"] >= faqInfo["threshold"]:
                        faq_result.append(item)
                    else:
                        pass
            except:
                raise KeyError("faq检索报错，请查看8013端口")
            logger.info(f"知识库faq检索执行结果为：{raw_result}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t3) * 1000, 0)
            if len(faq_result)>0:
                llm_temp_result = format_llm_result("调用FAQ库", node_use_time, node_start_time, node_end_time,
                                                    str(params), tool_name, str(faq_result), True)
                result["answer"] = faq_result[0]["answer"]
                result["type"] = "开始"
                result["name"] = "UserInput"
                result["modelType"] = self.model_type
                result["startTime"] = start_time
                result["endTime"] = node_end_time
                result["totalUseTime"] = node_use_time
                result["totalPromptToken"] = 0
                result["totalCompletionTokens"] = 0
                result["totalTotalTokens"] = 0
                result["status"] = "成功"
                result["nodeDetail"] = [llm_temp_result]
                data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                        "retMap": result}
                yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"
                return
            else:
                llm_temp_result = format_llm_result("调用FAQ库", node_use_time, node_start_time, node_end_time,
                                                    str(params), tool_name, str(raw_result), True)
                node_result.append(llm_temp_result)
        # 知识库状态判断
        if knowledgeInfo["status"] == KnowledgeCode.DISABLE.value:
            new_tool_list = tool_list
        elif knowledgeInfo["status"] == KnowledgeCode.DEMAND.value:
            tool_list.append(knowledgeInfo["id"])
            new_tool_list = tool_list
        elif knowledgeInfo["status"] == KnowledgeCode.AUTO.value:
            new_action_input = {}
            new_tool_list = tool_list
            t3 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # 知识库调用参数解析
            tool_name = tool_call.id_action[knowledgeInfo["id"]]
            params = date_process(knowledgeInfo)
            params["query"] = query
            new_action_input["tool_name"] = tool_name
            new_action_input["tool_params"] = params
            # 插件执行
            t_k = time.time()
            res = tool_call(str(new_action_input))
            logger.info(f"rag检索耗时:{round(time.time()-t_k, 2)}s")
            raw_result = res.result["text"]
            # 知识库返回结果判断
            if raw_result["retCode"] == str(ReturnCode.SUCESS.value):
                # 知识库返回结果结构化解析
                rag_result = rag_structural(raw_result["retMap"]["result"])
                isSuccess = True
            elif raw_result["retCode"] == str(ReturnCode.ERROR.value):
                rag_result = None
                isSuccess = False
                logger.info(f"知识库检索异常，请查看知识库检索接口,返回状态为{raw_result['retCode']}")
            history.append(f"system:知识库返回的参考资料：【{rag_result}】")
            logger.info(f"知识库执行结果为：{rag_result}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t3) * 1000, 0)
            # 判断知识库返回结果是否为None
            if rag_result is None:
                output = ''
            else:
                output = str(raw_result["retMap"]["result"])
            # 保存插件结果
            llm_temp_result = format_llm_result("调用知识库", node_use_time, node_start_time, node_end_time,
                                                str(params), tool_name, output, isSuccess)
            node_result.append(llm_temp_result)
        else:
            raise KeyError(f"目前不支持知识库状态为：{knowledgeInfo['status']},请检查参数")
        # 获取用户输入的tool_list中的插件信息
        enable_plugin_action = []
        enable_plugin_id = {}
        for id in new_tool_list:
            try:
                enable_plugin_action.append(tool_maps[tool_call.id_action[id]])
                enable_plugin_id[tool_call.id_action[id]] = tool_maps[tool_call.id_action[id]]
            except:
                logger.info(f"用户配置的插件id{id}不在数据库或者不可用")
        logger.info(f"最终可用插件为：{enable_plugin_id.keys()}")
        chatbot = ReAct(llm=self.llm,
                        action_executor=ActionExecutor(actions=enable_plugin_action),
                        debug=True)
        agent_generator = chatbot.stream_chat(prompt, query, history, enable_plugin_id, knowledgeInfo)
        result_content = ""
        for resp in agent_generator:
            if isinstance(resp, dict) and "node_result" in resp:
                node_res = resp['node_result']
                node_result = node_result + node_res
                end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                total_use_time = round((time.time() - t0) * 1000, 0)
                result["answer"] = result_content
                result["type"] = "开始"
                result["name"] = "UserInput"
                result["modelType"] = self.model_type
                result["startTime"] = start_time
                result["endTime"] = end_time
                result["totalUseTime"] = total_use_time
                result["totalPromptToken"] = sum([item["nodePromptToken"] for item in node_result])
                result["totalCompletionTokens"] = sum([item["nodeCompletionTokens"] for item in node_result])
                result["totalTotalTokens"] = sum([item["nodeTotalTokens"] for item in node_result])
                result["status"] = "成功"
                result["nodeDetail"] = node_result
                data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                        "retMap": result}
                print('nodel_detail', result)
                data_end = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                        "retMap": {"answer": "Stream ended"}}
                yield f"data:{json.dumps(data_end,ensure_ascii=False)}\n\n"
                yield f"data:{json.dumps(data,ensure_ascii=False)}\n\n"
            else:
                result_content = result_content+eval(resp.strip("data:"))["retMap"]["answer"]
                yield resp
                #time.sleep(1)



if __name__=="__main__":
    params = {
                "stream": False,
                "query": "",
                "company": "qwen_private",
                "modelType": "qwen2.5-72b-instruct-gptq-int4",
                "baseUrl": "http://***********:9997/v1/chat/completions",
                "toolList": [],
                "knowledgeInfo": {"status": 0}
            }
    prompt = "现在的时间是202407。\n\n 你只能使用所获得的信息，不要产生获得信息外的内容。如果所给的信息不足或者插件无法调用，请直接说明“信息不足无法回答”或者“无法调用插件”，不要根据自己所知道知识进行猜测回答。你能获得到的工具如下：\n\n[{'name': 'PythonInterpreter', 'description': '若需要用python解释器来执行Python代码解决问题时，调用此插件', 'parameters': {'command': {'description': '可执行的python函数', 'type': 'str', 'default': None}}}]\n你是一个可以调用外部工具的助手，负责计划下一步是使用工具还是做出总结。\n如果客户的问题涉及'兴趣小组管理'或者‘生成式人工智能’相关问题时，请先查询faq库。\n 如果客户的问题涉及'项目'、'平台'或者'服务'相关问题时，请先查询FILE知识库。"
    query = "帮我计算下1到100的和"
    knowldgeInfo = params["knowledgeInfo"]
    faqInfo = params.get("faqInfo", None)
    history =[]
    tool_list =["3"]
    aq = AwenQueryAgent()
    result = aq.query(prompt, query, tool_list, faqInfo, knowldgeInfo)
    print(result)




