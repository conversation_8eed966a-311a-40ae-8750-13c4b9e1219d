"""
-----------------------------------------------------
   File Name：  react_awen.py
   Description : agent中的react实现模块
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

import copy
import time
from lagent.actions import ActionExecutor
from lagent.llms.base_api import BaseAPIModel
from lagent.llms.base_llm import BaseModel
from lagent.agents.base_agent import BaseAgent
from conf.server_config import *
from src.utils.log_utils import logger
from src.llms import prompt_template
from src.agent.agents.agent_schema import NodeTypeCode, PlugTypeCode
from src.agent.agents.agent_util import format_llm_result, format_history, date_process
from typing import Dict, List, Tuple, Union, Generator
from lagent.schema import ActionReturn, ActionStatusCode, AgentReturn
from src.llms import prompt_template
from langchain_core.prompts import ChatPromptTemplate
logger = logger.bind(name="awen-ag-process-alg")
INFORMATION_INTEGRATION = ChatPromptTemplate.from_template(prompt_template.INFORMATION_INTEGRATION)

class ReActProtocol:
    """
    Description：ReAct提示的包装器，它管理来自LLM的响应，并以ReAct格式生成所需的提示。
    Args:
        thought (dict)：思维模式的信息
        action (dict)：动作模式的信息
        action_input (dict)：动作输入模式的信息
        response (dict)：响应模式的信息
        finish (dict)：完成模式的信息
        call_protocol (str)：ReAct的格式
        force_stop (str)：强制LLM生成响应的提示
    """

    def __init__(self,
                 thought: dict = THOUGHT,
                 action: dict = ACTION,
                 action_input: dict = ACTION_INPUT,
                 response: dict = RESPONSE,
                 finish: dict = FINISH,
                 force_stop: str = FORCE_STOP_PROMPT_EN) -> None:
        self.call_protocol = ""
        self.force_stop = force_stop
        self.thought = thought
        self.action = action
        self.action_input = action_input
        self.response = response
        self.finish = finish

    def format(self,
               prompt,
               inner_step: List[Dict],
               action_names: list,
               force_stop: bool = False) -> list:
        """
        Description: 生成React格式的prompt
        Args:
            chat_history : 之前运行的历史日志。
            inner_step: 当前运行中的日志。
            action_executor: 动作管理器，用于执行动作。
            force_stop : 是否强制代理在预定义轮次内给出响应。
        Returns:
            List[Dict]: React格式的prompt
        """
        self.call_protocol = prompt
        call_protocol = self._format(
            prompt=self.call_protocol,
            action_names=action_names,
            thought=self.thought["begin"],
            action=self.action["begin"],
            action_input=self.action_input["begin"],
            response=self.response["begin"],
            finish=self.finish["begin"],
        )
        formatted = [dict(role="user", content=call_protocol)]
        formatted += inner_step
        if force_stop:
            formatted.append(dict(role="user", content=self.force_stop))
        return formatted

    def _format(self, prompt: str, action_names: list, thought: str, action: str, action_input: Union[str, Dict],
                response: str, finish: str):
        prompt = prompt.replace("{action_names}", str(action_names)). \
            replace("{thought}", thought).replace("{action}", action). \
            replace("{action_input}", action_input).replace("{response}", response). \
            replace("{finish}", finish)
        return prompt

    def parse(
            self,
            message: str,
            action_executor: ActionExecutor,
    ) -> Tuple[str, str, str]:
        """
        Description:以ReAct格式解析动作返回值。
        Args：
            message (str): LLM以ReAct格式返回的响应。
            action_executor (ActionExecutor): 动作执行器，用于
            提供无动作/完成动作的名称。
            返回值：
            元组：返回值是一个包含以下内容的元组：
            - thought (str): 包含当前步骤LLM的思考内容。
            - action (str): 包含LLM计划执行的动作。
            - action_input (str): 包含当前动作所需的动作输入。
        """
        import re
        def _pattern_search(pattern, text):
            if re.search(pattern, text):
                return pattern
            if re.search(pattern.capitalize(), text):
                return pattern.capitalize()
            if re.search(pattern.title(), text):
                return pattern.title()
            if re.search(pattern.upper(), text):
                return pattern.upper()
            if re.search(pattern.lower(), text):
                return pattern.lower()
            return pattern

        # "Thought:"
        thought_pattern = _pattern_search(self.thought["begin"], message)
        # "Action:"
        action_pattern = _pattern_search(self.action["begin"], message)
        # "Action Input:"
        action_input_pattern = _pattern_search(self.action_input["begin"], message)
        # "Observation:"
        response_pattern = _pattern_search(self.response["begin"], message)
        # "Final Answer:"
        finsh_pattern = _pattern_search(self.finish["begin"], message)
        thought = message.split(action_pattern)[0].split(thought_pattern)[-1].split(finsh_pattern)[0]
        match_status = 0
        if finsh_pattern in message:
            match_status = 1
        action_regex = f"{action_pattern}(.*?)\n"
        args_regex = f"{action_input_pattern}(.*)"
        action_match = re.findall(action_regex, message)
        action = None
        action_input = ""
        if not action_match:
            match_status = 2 - match_status
        else:
            action = action_match[-1]

            def is_x(a):
                return a == "_"

            def is_pha(a):
                return a.isalpha()

            action = "".join(filter(lambda x: is_x(x) or is_pha(x), action))  # 只保留英文函数名
            arg_match = re.findall(args_regex, message, re.DOTALL)  # 解析参数
            if not arg_match:
                match_status = 2 - match_status
            action_input = arg_match[-1]

            def _dump_response(action_input: str):
                """除去LLM模型自己生成的插件结果"""
                return action_input.split(response_pattern)[0]

            action_input = _dump_response(action_input)
            # 判断action_input是否为空
            try:
                action_input = date_process(action_input)
            except:
                pass
        if not action or not action.replace("_", "").isalpha():
            if "Final Answer:" in message:
                final_answer = message.split("Final Answer:")[-1].replace("<|im_end|>", "")
            elif "Action Input:" in message:
                final_answer = message.split("Action Input:")[-1].replace("<|im_end|>", "")
            else:
                final_answer = message.replace("<|im_end|>", "")
            return thought, action_executor.finish_action.name, final_answer
        if match_status == 0:
            """normal case"""
            return thought, action.strip(), str(action_input)
        elif match_status == 1:
            """if final answer in message"""
            if action is not None and len(action.strip()) > 1:
                """maybe llm generate some fake response"""
                return thought, action.strip(), str(action_input)
            else:
                final_answer = message.split(finsh_pattern)[-1]
                return thought, action_executor.finish_action.name, final_answer
        else:
            return thought, action_executor.no_action.name, ""

    def format_response(self, action_return: ActionReturn) -> str:
        """
        Description：格式化当前步骤的最终响应。
        Args:
            action_return (ActionReturn): 当前插件返回结果
        Returns:
            str: 当前步骤的最终结果
        """
        if action_return.state == ActionStatusCode.SUCCESS:
            response = str(action_return.result["text"])
        else:
            response = action_return.errmsg
        return self.response["begin"] + response + self.response["end"]


class ReAct(BaseAgent):
    """
    Description：React实现类
    Args：
        llm (BaseModel 或 BaseAPIModel)：一个可以聊天并作为后端的LLM（大型语言模型）服务。
        action_executor (ActionExecutor)：一个动作执行器，用于管理所有动作及其响应。
        protocol (ReActProtocol)：一个包装器，用于生成提示并解析来自LLM/动作的响应。
        max_turn (int)：LLM生成可由ReAct协议成功解析的计划的最大尝试次数。默认为4。
    """

    def __init__(self,
                 llm: Union[BaseModel, BaseAPIModel],
                 action_executor: ActionExecutor,
                 protocol: ReActProtocol = ReActProtocol(),
                 max_turn: int = MAX_TURN,  # 最大轮次
                 debug=True) -> None:
        self.max_turn = max_turn
        self.debug = debug
        super().__init__(
            llm=llm, action_executor=action_executor, protocol=protocol)

    def chat(self, prompt: str, query: str, history: list, enable_plugin_id: dict, knowledgeInfo: dict) -> AgentReturn:
        """
           Description：React主函数
           Args:
               prompt:  用户传入的完整的prompt
               query:   用户问题
               enable_plugin_id:  可用的插件map
               knowledgeInfo:  知识库相关参数信息
               history:  历史对话信息
           Return:
               dict: agent返回结果，和其中节点信息
        """
        node_result = []
        self._inner_history = format_history(history)
        self._inner_history.append(dict(role="user", content=query))
        agent_return = AgentReturn()
        default_response = DEFAULT_RESPONSE
        for turn in range(self.max_turn):
            logger.info(f"Inner step:\n  {self._inner_history}")
            t1 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            llm_prompt = self._protocol.format(
                prompt=prompt,
                inner_step=self._inner_history,
                action_names=list(enable_plugin_id.keys()),
                force_stop=(turn == self.max_turn - 1))
            # func_call
            logger.info(f"********func_call prompt:\n{llm_prompt}\n*********")
            raw_response = self._llm.generate_from_template(llm_prompt, MAX_OUT_LEN)
            try:
                response = raw_response["answer"]
            except:
                raise KeyError(f"大模型请求报错，错误信息为：{raw_response}")
            logger.info(f"********func_call model response:\n{response}\n*********")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t1) * 1000, 0)
            self._inner_history.append(
                dict(role="assistant", content=response))
            # 开始解析action的名称和入参：
            thought, action, action_input = self._protocol.parse(
                response, self._action_executor)
            logger.info(f"*****func_call结果:\n  Thought: {thought}\n  Action: {action}\n "
                        f"Action Input: {action_input}*****")
            output = str({"thought": thought, "action": action, "action_input": action_input})
            isSuccess = True
            llm_temp_result = format_llm_result(NodeTypeCode.CALL_LLM, node_use_time,
                                                node_start_time, node_end_time,
                                                str(llm_prompt), action, output, isSuccess, raw_response)
            node_result.append(llm_temp_result)
            # 插件执行
            t2 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_type = NodeTypeCode.CALL_PLUG
            try:
                # 判断func-call抽取的action是否是可用插件
                if action in enable_plugin_id:
                    # 如果插件为api调用的插件。api插件统一注册，所有的插件请求一个函数，所以需要传入插件名称
                    if enable_plugin_id[action].name == PlugTypeCode.API_PLUG:
                        new_action_input = {"tool_name": action}
                        # 知识库插件参数需要二次封装
                        if action == knowledge_name:
                            node_type = NodeTypeCode.CALL_KNOWLEDGE
                            knowledgeInfo["query"] = query
                            new_action_input["tool_params"] = knowledgeInfo
                        else:
                            new_action_input["tool_params"] = eval(action_input)
                        action_return: ActionReturn = self._action_executor(enable_plugin_id[action].name,
                                                                            str(new_action_input))
                    else:
                        # 非api调用的插件，独立注册，可以直接调用
                        action_return: ActionReturn = self._action_executor(action, action_input)
                else:
                    # 当插件为FinishAction时
                    if action == self._action_executor.finish_action.name:
                        action_return: ActionReturn = self._action_executor(action, action_input)
                    else:
                        raise KeyError(f"action:{action}不在可用插件内，请检查参数")
                isSuccess = True
            except:
                action_return: ActionReturn = self._action_executor(self._action_executor.invalid_action.name,
                                                                    "插件请求失败，请检查插件调用状态状态")
                isSuccess = False
            action_return.thought = thought
            agent_return.actions.append(action_return)
            logger.info(f"****{action}插件执行结果:\n  Agent return: {agent_return}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t2) * 1000, 0)
            # 判断插件结果是否返回为空
            if action_return.result == None:
                output = action_return.args["text"]
            else:
                output = str({"api_result": action_return.result["text"]})
            # 保存插件结果
            llm_temp_result = format_llm_result(node_type, node_use_time,
                                                node_start_time,
                                                node_end_time, str(action_input),
                                                action, output, isSuccess)
            node_result.append(llm_temp_result)
            # 若插件类型为InvalidAction或者FinishAction，结束循环
            if action_return.type == self._action_executor.finish_action.name:
                agent_return.response = action_return.result["text"]
                break
            self._inner_history.append(
                dict(role="system",
                     content=self._protocol.format_response(action_return)))
        else:
            agent_return.response = default_response
        agent_return.inner_steps = copy.deepcopy(self._inner_history)
        logger.info(f"agent最终回复：{agent_return.response}")
        return agent_return, node_result

    def stream_chat(self, prompt: str, query: str, history: list, enable_plugin_id: dict,
                    knowledgeInfo: dict) -> Generator:
        """
       Description：React主函数流式输出
       Args:
           prompt:  用户传入的完整的prompt
           query:   用户问题
           enable_plugin_id:  可用的插件map
           knowledgeInfo:  知识库相关参数信息
           history:  历史对话信息
       Return:
           dict: agent返回结果，和其中节点信息
                """
        node_result = []
        self._inner_history = format_history(history)
        self._inner_history.append(dict(role="user", content=query))
        agent_return = AgentReturn()
        # 若超过最大轮次没有结果结果后的默认回复
        default_response = DEFAULT_RESPONSE
        for turn in range(self.max_turn):
            logger.info(f"Inner step:\n  {self._inner_history}")
            t1 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            llm_prompt = self._protocol.format(
                prompt=prompt,
                inner_step=self._inner_history,
                action_names=list(enable_plugin_id.keys()),
                force_stop=(turn == self.max_turn - 1))
            # func_call prompt
            logger.info(f"********func_call prompt:\n{llm_prompt}\n*********")
            # 调用模型进行func-call
            raw_response = self._llm.generate_from_template(llm_prompt, MAX_OUT_LEN)
            try:
                response = raw_response["answer"]
            except:
                raise KeyError(f"大模型请求报错，错误信息为：{raw_response}")
            logger.info(f"********func_call model response:\n{response}\n*********")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t1) * 1000, 0)
            # 模型结果写入历史信息
            self._inner_history.append(
                dict(role="assistant", content=response))
            # 开始解析action的名称和入参
            thought, action, action_input = self._protocol.parse(
                response, self._action_executor)
            logger.info(f"*****func_call结果:\n  Thought: {thought}\n  Action: {action}\n "
                        f"Action Input: {action_input}*****")
            output = str({"thought": thought, "action": action, "action_input": action_input})
            isSuccess = True
            llm_temp_result = format_llm_result(NodeTypeCode.CALL_LLM, node_use_time,
                                                node_start_time, node_end_time,
                                                str(llm_prompt), action, output, isSuccess, raw_response)
            node_result.append(llm_temp_result)
            # 插件执行
            t2 = time.time()
            node_start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_type = NodeTypeCode.CALL_PLUG
            try:
                # 判断func-call抽取的action是否是可用插件
                if action in enable_plugin_id:
                    # 如果插件为api调用的插件。api插件统一注册，所有的插件请求一个函数，所以需要传入插件名称
                    if enable_plugin_id[action].name == PlugTypeCode.API_PLUG:
                        new_action_input = {"tool_name": action}
                        # 知识库插件参数需要二次封装
                        if action == knowledge_name:
                            node_type = NodeTypeCode.CALL_KNOWLEDGE
                            knowledgeInfo["query"] = query
                            new_action_input["tool_params"] = knowledgeInfo
                        else:
                            new_action_input["tool_params"] = eval(action_input)
                        action_return: ActionReturn = self._action_executor(enable_plugin_id[action].name,
                                                                            str(new_action_input))
                    else:
                        # 非api调用的插件，独立注册，可以直接调用
                        action_return: ActionReturn = self._action_executor(action, action_input)
                else:
                    # 当插件为FinishAction时
                    if action == self._action_executor.finish_action.name:
                        break
                    else:
                        raise KeyError(f"action:{action}不在可用插件内，请检查参数")
                isSuccess = True
            except:
                action_return: ActionReturn = self._action_executor(self._action_executor.invalid_action.name,
                                                                    "插件请求失败，请检查插件调用状态状态")
                isSuccess = False
            logger.info(f"****{action}插件执行结果:\n  Agent return: {agent_return}")
            node_end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            node_use_time = round((time.time() - t2) * 1000, 0)
            # 判断插件结果是否返回为空
            if action_return.result is None:
                output = action_return.args["text"]
            else:
                output = str({"api_result": action_return.result["text"]})
            llm_temp_result = format_llm_result(node_type, node_use_time,
                                                node_start_time,
                                                node_end_time, str(action_input),
                                                action, output, isSuccess)
            node_result.append(llm_temp_result)
            # 判断是否结束循环
            if action_return.type == self._action_executor.finish_action.name:
                break
            self._inner_history.append(
                dict(role="system",
                     content=self._protocol.format_response(action_return)))
        else:
            self._inner_history.append(dict(role="system", content=default_response))
        agent_return.inner_steps = copy.deepcopy(self._inner_history)
        # 信息整合
        format_query = INFORMATION_INTEGRATION.invoke({"query": query, "chat_history": self._inner_history}).to_string()
        logger.info(f"信息整合的prompt:\n\n{format_query}\n")
        agent_generate = self._llm.generate_stream(format_query)
        for resp in agent_generate:
            temp_result = eval(resp.strip("data:"))["retMap"]["answer"]
            if temp_result == "Stream ended":
                yield {"node_result": node_result}
                break
            else:
                yield resp
                #time.sleep(1)

