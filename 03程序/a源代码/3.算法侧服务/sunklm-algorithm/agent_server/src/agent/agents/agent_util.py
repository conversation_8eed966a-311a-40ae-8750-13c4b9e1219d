"""
-----------------------------------------------------
   File Name：  agent_util.py
   Description : agent工具类函数
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
import pandas as pd
from conf.server_config import default_params
from typing import Optional, Union, Dict
from src.agent.agents.agent_schema import NodeTypeCode
import re

def get_month_diff(start_date: str, end_date: str)->int:
    """
    Description：计算两个月份之间的月份差
    Args:
        start_date:计算的开始月份，
        end_date：计算的结束月份
    Return：
        返回两个月份之间的月份差
    """
    start_date = pd.to_datetime(start_date + "01")
    end_date = pd.to_datetime(end_date + "01")
    month_diff = (end_date.year - start_date.year)*12+(end_date.month - start_date.month) + 1
    return month_diff


def format_llm_result(node_type: str,
                      node_use_time: Optional[float],
                      start_time: str,
                      end_time: str,
                      inputs: Union[str, list],
                      action: str,
                      output: Union[str, list],
                      isSuccess: bool,
                      raw_response: dict=None)->dict:
    """
      Description：agent节点结果格式化
        Args:
            node_type:节点类型
            node_use_time：节点耗时（毫秒）
            start_time：节点开始时间
            end_time：节点结束时间
            inputs： 节点请求参数
            action：节点插件名称
            raw_response：大模型func-call返回结果
        Return：
            dict：agent节点格式化后的dict
    """
    result = {}
    if node_type == NodeTypeCode.CALL_LLM:
        result["nodeType"] = node_type
        result["nodeName"] = raw_response["modelType"]
        result["nodePromptToken"] = raw_response["promptToken"]
        result["nodeCompletionTokens"] = raw_response["completionTokens"]
        result["nodeTotalTokens"] = raw_response["totalTokens"]
        result["temperature"] = raw_response["temperature"]
        result["topP"] = raw_response["topP"]
    elif node_type == NodeTypeCode.CALL_PLUG:
        result["nodeType"] = node_type
        result["nodeName"] = action
        result["nodePromptToken"] = 0
        result["nodeCompletionTokens"] = 0
        result["nodeTotalTokens"] = 0
        result["temperature"] = None
        result["topP"] = None
    elif node_type == NodeTypeCode.CALL_KNOWLEDGE:
        result["nodeType"] = node_type
        result["nodeName"] = action
        result["nodePromptToken"] = 0
        result["nodeCompletionTokens"] = 0
        result["nodeTotalTokens"] = 0
        result["temperature"] = None
        result["topP"] = None
    elif node_type == NodeTypeCode.CALL_FAQ:
        result["nodeType"] = node_type
        result["nodeName"] = action
        result["nodePromptToken"] = 0
        result["nodeCompletionTokens"] = 0
        result["nodeTotalTokens"] = 0
        result["temperature"] = None
        result["topP"] = None
    else:
        raise KeyError(f"节点类型为：{node_type}未提供，请检查传入的参数")
    result["input"] = inputs
    result["nodeUseTime"] = node_use_time
    result["nodeStartTime"] = start_time
    result["nodeEndTime"] = end_time
    result["output"] = output
    result["isSuccess"] = isSuccess
    return result


def format_history(history: list)->list:
    """
    Description:历史信息解析，格式话历史信息
    Args:
       history:用户传入的历史信息
    Return:
        list：符合openai接口输入的messages格式
    """
    result = []
    for item in history:
        item_list = item.split(":")
        query_text = dict(role=item_list[0], content=str(item_list[1:]))
        result.append(query_text)
    return result


def rag_structural(split_list: list)-> str:
    """
        Description:知识库返回信息封装
        Args:
           split_list:待封装的知识库信息
        Return:
            str：封装好的str
        """
    image_url_pattern = r'<img\s+[^>]*?utils="([^"]*)"'
    result = '\n\n'
    for i, split in enumerate(split_list):
        text = split["splitText"]
        match = re.search(image_url_pattern, text)
        if match:
            temp_word = '<img utils="'+match.group(1)+'" alt="Image" />'
            text = text.replace(temp_word, '')
        text_list = text.split("\n")
        title = ""
        for tmp in text_list:
            if tmp != "":
                title = tmp
                break
        content = text[text.find(title)+len(title):]
        result = result + "参考知识片段" + str(i + 1) + "；" + title + "\n\n" + content.strip()
    return result


def get_tool_name(though: str, enable_plugin_id: dict)-> str:
    """
    Description: 获取工具名称
    Args:
       though:模型输出的思考
       enable_plugin_id:插件map
    Return:
        str：插件名称
    """
    result = None
    for tool_name in enable_plugin_id.values():
        if tool_name in though:
            result = tool_name
            return result
            break
        else:
            pass
    return result


def exit_element(target_list: list, raw_list2: list)-> bool:
    """
    Description:  判断一个列表中的元素是否都在另一个列表中
    Args:
       target_list:目标列表
       raw_list2:原列表
    Return:
        str：插件名称
    """
    result = True
    # 判断list1中的元素是否都在list2中。
    for element in target_list:
        if element not in raw_list2:
            if element not in default_params:
                result = False
                break
    return result


def date_process(data: Union[str, Dict])->Dict:

    """
    Description:  将str格式的数据转成json
    Args:
       data:原始数据
    Return:
        dict：json格式的数据
    """
    deal_res = {}
    if isinstance(data, dict):
        deal_res = data
    else:
        stack = []
        flag = False
        deal_res = ''
        for s in data:
            if s == "{":
                if not flag:
                    flag = True
                stack.append("{")
            if s == "}" and stack:
                stack.pop()
            if flag:
                deal_res += s
            if not stack and flag:
                break
        deal_res = eval(deal_res)
    return deal_res




