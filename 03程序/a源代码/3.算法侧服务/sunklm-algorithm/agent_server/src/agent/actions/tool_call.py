"""
-----------------------------------------------------
   File Name：  tool_call.py
   Description : api类工具插件调用类
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

from typing import Optional, Union
import requests
from lagent.actions import BaseAction
from lagent.schema import ActionReturn, ActionStatusCode
from conf.server_config import APITOOL_DESCRIPTION
from src.service.get_tool import GetToolInfo
from src.agent.agents.agent_util import date_process
from src.agent.agents.agent_util import exit_element
from src.utils.log_utils import logger
logger = logger.bind(name="awen-ag-process-alg")

class Tool_Call(BaseAction):
    """
    Description：该类为工具注册调用类，可以调用的插件类型为api调用
    Args：
        description (str): 行动的描述。默认为APITOOL_DESCRIPTION。
        name (str, 可选): 行动的名称。如果为None，则名称默认为类名。默认为None。
        enable (bool, 可选): 是否启用该行动。默认为True。
        disable_description (str, 可选): 当行动被禁用时，行动的描述。默认为None。
    """
    def __init__(
            self,
            description: str = APITOOL_DESCRIPTION,
            name: Optional[str] = None,
            enable: bool = True,
            disable_description: Optional[str] = None
    ) -> None:
        super().__init__(description, name, enable, disable_description)
        self.tool_detail = {}
        self.id_action = {}

    def get_tool(self)->None:
        """
        Description：解析数据库获取的插件信息
        """
        TOOL_API = GetToolInfo().__call__()
        for item in TOOL_API:
            # 遍历数据库返回的插件信息，
            for key, value in item.items():
                name = value["name"]
                self.id_action[str(key)] = name
                self.tool_detail[name] = {}
                self.tool_detail[name]["headers"] = value["headers"]
                self.tool_detail[name]["url"] = value["url"]
                self.tool_detail[name]["description"] = value["description"]
                self.tool_detail[name]["enable"] = value["enable"]
                self.tool_detail[name]["parameters"] = value["parameters"]
                self.tool_detail[name]["required"] = value["required"]
        return

    def __call__(self, params: str) -> ActionReturn:
        """
         Description：解析插件参数，调用插件，封装插件结果
         Args:
            params:包含插件名、插件参数的json转成的str
        Return:
            ActionReturn:插件返回的信息
        """
        logger.info(f"插件請求參數{params}")
        # 参数解析
        params = eval(params)
        tool_name = params.get("tool_name", "")
        tool_params = params.get("tool_params", {})
        requried_params = self.tool_detail[tool_name]["required"]
        # 参数校验：将func-call抽取的参数和数据库插件的必填参数进行校验
        if not exit_element(requried_params, list(tool_params.keys())):
            raise AssertionError(f"插件参数错误，请仔细检查参数信息，必要参数为{requried_params}，传入的参数为：{tool_params}")
        # 定义插件返回格式
        tool_return = ActionReturn(
            url=self.tool_detail[tool_name]["url"],
            args=tool_params,
            type=tool_name,
            state=ActionStatusCode.SUCCESS
        )
        status_code, response = self._search(tool_name, tool_params)
        # 插件返回状态进行判断解析
        if status_code == -1:
            tool_return.errmsg = response
            tool_return.result = dict(text="API internal error, check log for more detail.")
            tool_return.state = ActionStatusCode.API_ERROR
        elif status_code == 200:
            tool_return.result = dict(text=response)
            tool_return.state = ActionStatusCode.SUCCESS
        else:
            tool_return.errmsg = str(status_code)
            tool_return.result = dict(text="API internal error, check log for more detail.")
            tool_return.state = ActionStatusCode.API_ERROR
        return tool_return

    def _search(self, tool_name: str, tool_params: dict) -> tuple:
        """
         Description：根据插件名称和插件参数，通过request请求插件
         Args:
            tool_name：插件名称，
            tool_params：插件参数
        Return:
            dict:插件返回的结果
        """
        raw_response = ""
        try:
            status_code = 200
            tool_url = self.tool_detail[tool_name]["url"]
            tool_headers = self.tool_detail[tool_name]["headers"]
            # 请求插件的参数格式校验
            if isinstance(tool_params, dict):
                params = tool_params
            else:
                params = date_process(tool_params)
            # request请求插件，获取插件返回
            raw_response = requests.post(url=tool_url, headers=tool_headers, json=params).json()
        except Exception as e:
            logger.info(f"{tool_name}插件调用失败 . {e}")
            ex = Exception(f"{tool_name}插件服务请求报错，报错信息{e}")
            raise ex
        return status_code, raw_response



