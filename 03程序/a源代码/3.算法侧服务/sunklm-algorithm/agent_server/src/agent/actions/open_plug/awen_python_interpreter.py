"""
-----------------------------------------------------
   File Name：  awen_python_interpreter.py
   Description : python解释器类，可以执行python代码
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""

import copy
import io
from contextlib import redirect_stdout
from typing import Any, Optional
from func_timeout import FunctionTimedOut, func_set_timeout
from lagent.actions.base_action import BaseAction
from lagent.schema import ActionReturn, ActionStatusCode


class GenericRuntime:
    GLOBAL_DICT = {}
    LOCAL_DICT = None
    HEADERS = []

    def __init__(self):
        self._global_vars = copy.copy(self.GLOBAL_DICT)
        self._local_vars = copy.copy(
            self.LOCAL_DICT) if self.LOCAL_DICT else None

        for c in self.HEADERS:
            self.exec_code(c)

    def exec_code(self, code_piece: str) -> None:
        exec(code_piece, self._global_vars)

    def eval_code(self, expr: str) -> Any:
        return eval(expr, self._global_vars)


DEFAULT_DESCRIPTION = """用来执行Python代码。代码必须是一个函数，
函数名必须得是 "solution"，代码对应你的思考过程。代码实例格式如下：
```python
# import 依赖包
import xxx
def solution():
    # 初始化一些变量
    variable_names_with_real_meaning = xxx
    # 步骤一
    mid_variable = func(variable_names_with_real_meaning)
    # 步骤 x
    mid_variable = func(mid_variable)
    # 最后结果
    final_answer =  func(mid_variable)
    return final_answer
```"""


class PythonInterpreter(BaseAction):
    """
    Description：一个Python执行器，能够执行Python脚本
    Args：
        description (str): 行动的描述。默认为DEFAULT_DESCRIPTION。
        answer_symbol (str, 可选): 来自LLM（大型语言模型）的答案标识符
        answer_expr (str, 可选): Python脚本中答案函数的名称。默认为"solution()"。
        answer_from_stdout (boolean): 执行结果是否来自标准输出（stdout）。
        name (str, 可选): 行动的名称。如果为None，则名称默认为类名。默认为None。
        enable (bool, 可选): 是否启用该行动。默认为True。
        disable_description (str, 可选): 当行动被禁用时，行动的描述。默认为None。
        timeout (int): Python脚本执行的最大等待时间（单位：秒）。
    """

    def __init__(self,
                 description: str = DEFAULT_DESCRIPTION,
                 answer_symbol: Optional[str] = None,
                 answer_expr: Optional[str] = "solution()",
                 answer_from_stdout: bool = False,
                 name: Optional[str] = None,
                 enable: bool = True,
                 disable_description: Optional[str] = None,
                 timeout: int = 20) -> None:
        super().__init__(description, name, enable, disable_description)

        self.answer_symbol = answer_symbol
        self.answer_expr = answer_expr
        self.answer_from_stdout = answer_from_stdout
        self.timeout = timeout

    def __call__(self, command: str) -> ActionReturn:
        self.runtime = GenericRuntime()
        try:
            tool_return = func_set_timeout(self.timeout)(self._call)(command)
        except FunctionTimedOut as e:
            tool_return = ActionReturn(url=None, args=None, type=self.name)
            tool_return.errmsg = repr(e)
            tool_return.state = ActionStatusCode.API_ERROR
        return tool_return

    def _call(self, command: str) -> ActionReturn:
        tool_return = ActionReturn(url=None, args=None, type=self.name)
        try:
            if "```python" in command:
                command = command.split("```python")[1].split("```")[0]
            if "```py" in command:
                command = command.split("```py")[1].split("```")[0]
            elif "```" in command:
                command = command.split("```")[1].split("```")[0]
            tool_return.args = dict(text="```python\n" + command + "\n```")
            command = command.split("\n")

            if self.answer_from_stdout:
                program_io = io.StringIO()
                with redirect_stdout(program_io):
                    self.runtime.exec_code("\n".join(command))
                program_io.seek(0)
                res = program_io.readlines()[-1]
            elif self.answer_symbol:
                self.runtime.exec_code("\n".join(command))
                res = self.runtime._global_vars[self.answer_symbol]
            elif self.answer_expr:
                self.runtime.exec_code("\n".join(command))
                res = self.runtime.eval_code(self.answer_expr)
            else:
                self.runtime.exec_code("\n".join(command[:-1]))
                res = self.runtime.eval_code(command[-1])
        except Exception as e:
            tool_return.errmsg = repr(e)
            tool_return.type = self.name
            tool_return.state = ActionStatusCode.API_ERROR
            return tool_return
        try:
            tool_return.result = dict(text=str(res))
            tool_return.state = ActionStatusCode.SUCCESS
        except Exception as e:
            tool_return.errmsg = repr(e)
            tool_return.type = self.name
            tool_return.state = ActionStatusCode.API_ERROR
        return tool_return
