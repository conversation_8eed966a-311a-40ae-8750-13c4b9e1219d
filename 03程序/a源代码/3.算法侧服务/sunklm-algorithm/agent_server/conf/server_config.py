import os

PWD = os.path.dirname(os.getcwd())

# API所在表格名称
API_TABLE = "xj_tool_info_tb"

# mysql配置
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@123"
MYSQL_DATABASE = "AWEN_PRIMARY"

#默认参数
default_params = ["topK", "matchType", "threshold", "weight"]

# 默认使用的模型
LLM_NAME = "Qwen2.5-72B-Instruct"
# 私有化部署的模型MAP
LLM_MAP = {
    "Qwen2.5-72B-Instruct": {
        "base_url": "http://**********:9997/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0}}

# 最大输入长度
MAX_SEQ_LEN = 10000

# 最大输出长度：
MAX_OUT_LEN = 512

# 模型输出参数默认值
QUERY = "你好"
TEMPERATURE = 0.01
TOP_P = 0.01
TOP_K = 1
REWRITE = False
POSTPROCESS = False
STREAM = False
# 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
QUERY_PER_SECOND = 1

# 工具测试
# the prompt to force LLM to generate response
FORCE_STOP_PROMPT_EN = """You should directly give results
 based on history information."""

# agent 最大轮次
MAX_TURN = 3

#输入窗口长度
CONTEXT_WINDOW = 50000

# 当超出agent最大轮次时，模型托底回复
DEFAULT_RESPONSE = "Sorry that I cannot answer your question."

# 知识库插件名称
knowledge_name = "recallKnowledge_file"


#APITOOL_DESCRIPTION
APITOOL_DESCRIPTION = """
The tool to get api respose of query input
Input format
{
   "params":{ "tool_name": name of api,
    "tool_params": The parameters of the API plugin are in the format of a dictionary }
}
"""


"""
func-call结果解析和sunklm-algorithm/resources/template/prompt_template.py所选择的输出模板绑定
thought (dict): the information of thought pattern
action (dict): the information of action pattern
action_input (dict): the information of action_input pattern
response (dict): the information of response pattern
finish (dict): the information of finish pattern
"""
THOUGHT = dict(role="THOUGHT", begin="Thought:", end="\n", belong="assistant")
ACTION = dict(role="ACTION", begin="Action:", end="\n")
ACTION_INPUT = dict(role="ARGS", begin="Action Input:", end="\n")
RESPONSE = dict(role="RESPONSE", begin="Observation:", end="\n")
FINISH = dict(role="FINISH", begin="Final Answer:", end="\n")



