import sys
import tempfile
import requests
import os
import importlib.util
from common.utils.LogUtils import logger

loaded_tools = {}


def load_tools(mcp, url: str, save_dir: str = None):
    """从 URL 下载 .py 文件并加载为工具模块"""
    try:
        if not save_dir:
            raise ValueError("save_dir cannot be empty")
        filename = os.path.basename(url)
        tool_path = os.path.join(save_dir, filename)
        response = requests.get(url)
        response.raise_for_status()
        with open(tool_path, "wb") as f:
            f.write(response.content)

        module_name = os.path.splitext(filename)[0]
        spec = importlib.util.spec_from_file_location(module_name, tool_path)
        mod = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mod)
        loaded_tools[module_name] = mod

        if hasattr(mod, "register") and callable(mod.register):
            mod.register(mcp)

        logger.info(f"Tool {module_name} registered from URL.")
        return module_name
    except Exception as e:
        logger.error(f"Failed to load tool from {url}: {e}")
        raise


def unload_tools(mcp, tool_names: list, save_dir: str = None):
    """卸载指定工具模块"""
    for module_name in tool_names:
        mod = loaded_tools.pop(module_name, None)
        if mod:
            if hasattr(mod, "unregister") and callable(mod.unregister):
                mod.unregister(mcp)
            if module_name in sys.modules:
                sys.modules.pop(module_name, None)
            logger.info(f"Tool {module_name} unloaded.")
        # 删除本地文件
        if not save_dir:
            raise ValueError("save_dir cannot be empty")
        file_path = os.path.join(save_dir, f"{module_name}.py")
        if os.path.exists(file_path):
            os.remove(file_path)
