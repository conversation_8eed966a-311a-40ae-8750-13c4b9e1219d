import json
import traceback
import re
from func_timeout import func_set_timeout, FunctionTimedOut
from langchain_core.prompts import Chat<PERSON>romptTemplate
from common.utils.ResultUtis import ReturnCode,return_dict
from common.utils.LogUtils import logger
from common.constants.PromptConstants import PromptConstants
from common.utils.ModelUtils import ModelAPI
from common.utils.MinioUtils import MinioClient
from common.conf.ServerConfig import LLM_NAME, LLM_MAP, OVERLAP, MATRIX_PATH
from typing import List, Dict


llm = ModelAPI(model_type=LLM_NAME,
               api_key=LLM_MAP[LLM_NAME]["api_key"],
               company=LLM_MAP[LLM_NAME]["company"],
               deploy_mode=LLM_MAP[LLM_NAME]["deploy_mode"],
               base_url=LLM_MAP[LLM_NAME]["base_url"])

QR = ModelAPI(model_type=LLM_NAME,
              api_key=LLM_MAP[LLM_NAME]["api_key"],
              company=LLM_MAP[LLM_NAME]["company"],
              deploy_mode=LLM_MAP[LLM_NAME]["deploy_mode"],
              base_url=LLM_MAP[LLM_NAME]["base_url"])


def register(mcp):
    @mcp.tool()
    def code_executor(command: str, input: dict, timeout: int = 20, url=None, headers=None):
        '''
            代码执行器，执行python代码
            入参：
            - command: str - 代码源码
            - input: dict - 代码入参 必填
            - timeout： int - 超时设置，非必需
        '''
        try:
            logger.info(f"[code_executor] 入参: command={command},input={input},timeout={input}, headers={headers}, url={url}")
            new_command = command + "\narg = " + repr(json.dumps(input))

            def _run_code():
                local_vars = {"__builtins__": __builtins__}
                exec(new_command, local_vars)
                return eval("solution(arg)", local_vars)

            # 加入超时机制
            result = func_set_timeout(timeout)(_run_code)()
            logger.info(f"[code_executor] 返回值: result={result}")
            return return_dict(data={"result": result}, code=0, message="success")
        except FunctionTimedOut:
            logger.error(f"代码执行超时（>{timeout}秒）")
            return return_dict(data={"error": f"代码执行超时（>{timeout}秒）"}, code=1, message="error")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")


    @mcp.tool()
    def read_minio_file_data(fileUrl: str, url=None, headers=None):
        '''
            minio文件读取
            入参：
            - fileUrl: str - 文件路径
        '''
        try:
            logger.info(f"[read_minio_file_data] 入参: fileUrl={fileUrl}, headers={headers}, url={url}")
            if fileUrl is None or fileUrl == '':
                return {}
            result = MinioClient.getFileInfo(fileUrl)
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")


    @mcp.tool()
    def extract_faq(faq_num: int, context: str, faq_context_length: int, url=None, headers=None) -> str:
        """
        从给定的文本中抽取FAQ问答对。

        参数:
            faq_num (int): 每个文本块需要抽取的FAQ数量
            context (str): 需要抽取FAQ的源文本
            faq_context_length (int): 每个文本块的长度

        返回:
            str: 包含问答对的结果字典
        """
        logger.info(
            f"[extract_faq] 入参: faq_num={faq_num},context={context},faq_context_length={faq_context_length},url={url},headers={headers}")
        try:
            # 将文本分割成重叠的块
            slices = []
            if len(context) <= faq_context_length:
                slices.append(context)
            else:
                start = 0
                text_length = len(context)
                while start < text_length:
                    end = start + faq_context_length
                    slices.append(context[start:end])
                    start += faq_context_length

            # 移除空的文本块
            valid_slices = [s for s in slices if s]
            logger.info(f"有效的文本块数量: {len(valid_slices)}")

            # 从每个文本块中抽取FAQ
            all_questions = []
            all_answers = []
            FAQ_PROMPT = ChatPromptTemplate.from_template(PromptConstants.FAQ_PROMPT2)

            for chunk in valid_slices:
                try:
                    format_query = FAQ_PROMPT.invoke({"num": faq_num, "input": chunk}).to_string()
                    logger.info(f"FAQ抽取的prompt:\n\n{format_query}\n")
                    faq_result = llm.generate(format_query)

                    if isinstance(faq_result, dict):
                        faq_result = json.dumps(faq_result, ensure_ascii=False)

                    # 使用正则表达式提取问题和答案
                    pattern1 = r'<question>(.*?)</question>'
                    chunk_questions = re.findall(pattern1, faq_result, re.DOTALL)

                    pattern2 = r'<answer>(.*?)</answer>'
                    chunk_answers = re.findall(pattern2, faq_result, re.DOTALL)

                    # 确保问答对数量相等
                    min_length = min(len(chunk_questions), len(chunk_answers))
                    chunk_questions = chunk_questions[:min_length]
                    chunk_answers = chunk_answers[:min_length]

                    all_questions.extend(chunk_questions)
                    all_answers.extend(chunk_answers)
                except Exception as e:
                    logger.error(f"处理文本块时出错: {str(e)}")
                    continue
            result = {"questions": all_questions, "answers": all_answers}
            logger.info(f"[extract_faq] 返回值: result={result}")
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def question_rewrite(query: str, history: list, url=None, headers=None):
        '''
            问题改写插件
            入参：
            - query: str - 当前用户输入问题
            - history: list - 聊天历史记录
        '''
        try:
            logger.info(f"[question_rewrite] 入参: query={query}, history={history}, headers={headers}, url={url}")
            if history == "" or history is None:
                history = "Empty"
            REWRITER_PROMPT = ChatPromptTemplate.from_template(PromptConstants.REWRITER_PROMPT)
            prompt = str(REWRITER_PROMPT)
            prompt = prompt + f'"chat_history": {history}, "current": {query}'
            rewrite = QR.generate(inputs=prompt)
            logger.info(f"[question_rewrite] 返回值: {rewrite}")
            return return_dict(data={"rewrite": rewrite}, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def product_matrix(query: str, url=None, headers=None):
        """
        产品矩阵，查询产品处于公司的位置
        入参：
        - query: str - 输入文本
        """
        logger.info(f"[product_matrix] 入参: query={query}, headers={headers}, url={url}")
        try:
            path = MATRIX_PATH
            logger.info(f"MATRIX_PATH {path}")

            with open(path, 'r', encoding='utf-8') as f:
                product_dict = json.load(f)

            template = "{product_name}在产品矩阵中的位置属于{product_division}-{product_category}，目前属于{product_grade}。"
            result = ""

            for key in product_dict:
                if key in query:
                    result = template.format(
                        product_name=key,
                        product_division=product_dict[key]['事业群名称'],
                        product_category=product_dict[key]['产品类别'],
                        product_grade=product_dict[key]['产品等级']
                    )
                    query = query.replace(key, '-' + key)
                    break

            if not result:
                result = "产品矩阵中未包含该产品。"

            logger.info(f"[product_matrix] 返回值: result={result}")
            return return_dict(data={"matrix": result}, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")
        

    @mcp.tool()
    def multi_turn_optimize_route(query: str, history: list, url=None, headers=None):
        """
        多轮对话优化接口

        入参示例:
        {
        "query":   "他明天还会下雨吗？",
        "history": [
            "user:今天天气怎么样？",
            "assistant:{\"output\": \"今天天气晴朗，气温在20到25度之间。\"}",
            ...
        ]
        }

        出参示例:
        {
        "summary": "用户先询问今天天气，助手回答今日晴朗、气温 20–25 ℃；随后用户追问明天是否会下雨。"
        }
        """
        try:
            logger.info(f"[question_rewrite] 入参: query={query}, history={history}, headers={headers}, url={url}")
            if history == "" or history is None:
                history = "Empty"

            # ---- 将最新 query 追加为 user 消息 ----
            history.append({"role": "user", "content": query})
            
            def _extract_answer(resp):
                """兼容不同返回格式：{'output': ...} | {'answer': ...} | {'text': ...} | str"""
                if isinstance(resp, dict):
                    for key in ("output", "answer", "text"):
                        if key in resp and resp[key]:
                            return resp[key]
                    return ""
                return str(resp) if isinstance(resp, str) else ""


            def _summarize(hist: List[Dict[str, str]]) ->  str:
                if not hist:
                    return ""
                text = "\n".join([f"{m['role']}: {m['content']}" for m in hist])
                prompt = ChatPromptTemplate.from_template(PromptConstants.SUMMARIZE_PROMPT)
                prompt_str = prompt.format(dialogue=text)
                resp = llm.generate(inputs=prompt_str)
                logger.info(f"[multi_turn_optimize] 返回值: {resp}")
                return _extract_answer(resp).strip()
            
            summary_str = _summarize(history)

            return return_dict(data={"summary": summary_str}, code=0, message="success")

        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")