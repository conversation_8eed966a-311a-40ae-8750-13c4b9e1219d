import hashlib
import time
import requests
import json
from common.utils.LogUtils import logger
from common.conf.ServerConfig import qichacha_appkey, qichacha_secretkey

def register(mcp):
    @mcp.tool()
    def qichacha_bidding_details(bidding_id: str, url: str, headers=None):
        """
        企查查招投标详情查询
        入参：
        - bidding_id: str - 列表内提供的bidding_id
        """
        appkey = qichacha_appkey
        secretkey = qichacha_secretkey
        logger.info(f"[qichacha_bidding_details] 入参: bidding_id={bidding_id}, headers={headers}, url={url}")
        # 企查查接口鉴权
        timestamp = str(int(time.time() * 1000))
        source = (appkey + secretkey + timestamp).lower()
        # 使用MD5进行消息摘要计算
        token = hashlib.md5(source.encode()).hexdigest()
        headers = {
            'Content-Type': 'application/json',
            'token': token,
            "timestamp": timestamp,
            "appkey": appkey
        }
        url = url + bidding_id
        try:
            response = requests.get(
                url=url,
                headers=headers,
                timeout=30
            ).content
            response = json.loads(str(response, 'utf-8'))
            ret = {
                "status": response["status"],
                "result": response["result"],
                "message": response["message"]
            }
            logger.info(f"[qichacha_bidding_details] 返回值: ret={ret}")
            return ret
        except Exception as e:
            logger.error(f"Error Response qichacha_bidding_details: {e}")

    @mcp.tool()
    def qichacha_website(keyword: str, url: str, headers=None):
        """
        企查查网站备案域名查询
        入参：
        - keyword: str - 查询关键词
        """
        appkey = qichacha_appkey
        secretkey = qichacha_secretkey
        logger.info(f"[qichacha_website] 入参: keyword={keyword}, headers={headers}, url={url}")
        # 企查查接口鉴权
        timestamp = str(int(time.time() * 1000))
        source = (appkey + secretkey + timestamp).lower()
        # 使用MD5进行消息摘要计算
        token = hashlib.md5(source.encode()).hexdigest()
        headers = {
            'Content-Type': 'application/json',
            'token': token,
            "timestamp": timestamp,
            "appkey": appkey
        }
        url = url + keyword
        try:
            response = requests.get(
                url=url,
                headers=headers,
                timeout=30
            ).content
            response = json.loads(str(response, 'utf-8'))
            ret = {
                "status": response["status"],
                "result": response["result"],
                "message": response["message"]
            }
            logger.info(f"[qichacha_website] 返回值: ret={ret}")
            return ret
        except Exception as e:
            logger.error(f"Error Response qichacha_patent_list: {e}")

    @mcp.tool()
    def qichacha_ic_info(keyword: str, url: str, headers=None):
        """
        企查查企业全维度信息查询
        入参：
        - keyword: str - 查询关键词
        """
        appkey = qichacha_appkey
        secretkey = qichacha_secretkey
        logger.info(f"[qichacha_ic_info] 入参: keyword={keyword}, headers={headers}, url={url}")
        # 企查查接口鉴权
        timestamp = str(int(time.time() * 1000))
        source = (appkey + secretkey + timestamp).lower()
        # 使用MD5进行消息摘要计算
        token = hashlib.md5(source.encode()).hexdigest()
        headers = {
            'Content-Type': 'application/json',
            'token': token,
            "timestamp": timestamp,
            "appkey": appkey
        }
        url = url + keyword
        try:
            response = requests.get(
                url=url,
                headers=headers,
                timeout=30
            ).content
            response = json.loads(str(response, 'utf-8'))
            ret = {
                "status": response["status"],
                "result": response["result"],
                "message": response["message"]
            }
            logger.info(f"[qichacha_ic_info] 返回值: ret={ret}")
            return ret
        except Exception as e:
            logger.error(f"Error Response qichacha_ic_info: {e}")

    @mcp.tool()
    def qichacha_patent_list(keyword: str, url: str, headers=None):
        """
        企查查专利列表查询
        入参：
        - keyword: str - 查询关键词
        """
        appkey = qichacha_appkey
        secretkey = qichacha_secretkey
        logger.info(f"[qichacha_patent_list] 入参: keyword={keyword}, headers={headers}, url={url}")
        # 企查查接口鉴权
        timestamp = str(int(time.time() * 1000))
        source = (appkey + secretkey + timestamp).lower()
        # 使用MD5进行消息摘要计算
        token = hashlib.md5(source.encode()).hexdigest()
        headers = {
            'Content-Type': 'application/json',
            'token': token,
            "timestamp": timestamp,
            "appkey": appkey
        }
        url = url + keyword
        try:
            response = requests.get(
                url=url,
                headers=headers,
                timeout=30
            ).content
            response = json.loads(str(response, 'utf-8'))
            ret = {
                "status": response["status"],
                "result": response["result"],
                "message": response["message"]
            }
            logger.info(f"[qichacha_patent_list] 返回值: ret={ret}")
            return ret
        except Exception as e:
            logger.error(f"Error Response qichacha_patent_list: {e}")

    @mcp.tool()
    def qichacha_bidding_list(keyword: str, year_int: int, url: str, headers=None):
        """
        企查查招投标列表查询
        入参：
        - keyword: str - 查询关键词
        - year_int: int - 年份
        """
        appkey = qichacha_appkey
        secretkey = qichacha_secretkey
        logger.info(f"[qichacha_bidding_list] 入参: keyword={keyword}, year_int={year_int}, headers={headers}, url={url}")
        try:
            # 企查查接口鉴权
            timestamp = str(int(time.time() * 1000))
            source = (appkey + secretkey + timestamp).lower()
            token = hashlib.md5(source.encode()).hexdigest()
            headers = {
                'Content-Type': 'application/json',
                'token': token,
                'timestamp': timestamp,
                'appkey': appkey
            }
            full_url = f"{url}{keyword}&year={year_int}"
            response = requests.get(url=full_url, headers=headers, timeout=30).content
            data = json.loads(response.decode('utf-8'))
            ret = {
                "status": data.get("status"),
                "result": data.get("result"),
                "message": data.get("message")
            }
            logger.info(f"[qichacha_bidding_list] 返回值: ret={ret}")
            return ret
        except Exception as e:
            logger.error(f"Error Response qichacha_bidding_list: {e}")
