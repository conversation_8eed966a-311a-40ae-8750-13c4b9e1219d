import os
import json
import logging
import warnings
import requests
import docx
import openpyxl
import xlrd
import pandas as pd
import pptx
import mistune
from bs4 import BeautifulSoup
import traceback
from func_timeout import func_set_timeout, FunctionTimedOut
import mysql.connector
import sqlparse
from pdfminer.high_level import extract_text
from pdfminer.pdfpage import PDFTextExtractionNotAllowed
from common.utils.LogUtils import logger
from common.utils.MinioUtils import MinioClient
from common.conf.ServerConfig import MINOBUCKET, PDF_OCR_URL
from common.utils.ResultUtis import ReturnCode, return_dict
import time
from common.conf.ServerConfig import *


def validate_sql(sql: str) -> tuple:
    """
    验证SQL语句的合法性
    返回: (是否合法, 错误信息)
    """
    try:
        # 格式化SQL语句
        formatted_sql = sqlparse.format(sql, reindent=True, keyword_case='upper')
        # 解析SQL语句
        parsed = sqlparse.parse(formatted_sql)
        if not parsed:
            return False, "SQL语句为空"
        # 获取语句类型
        stmt = parsed[0]
        if stmt.get_type() not in ['SELECT', 'SHOW']:
            return False, "仅支持SELECT和SHOW语句"
        return True, ""
    except Exception as e:
        return False, f"SQL语法错误: {str(e)}"


def register(mcp):
    """
    注册所有工具函数到 mcpProtocol
    """
    minio_client = MinioClient().get_connect()

    @mcp.tool()
    def http_function(input: dict, url: str, headers: dict, method: str):
        '''
            http插件
        '''
        logger.info(f"[http_function] 入参: input={input}, headers={headers}, url={url}, method={method}")
        try:
            if method.upper() == "GET":
                response = requests.get(
                    url=url,
                    params=input,
                    headers=headers,
                    timeout=30
                )
            elif method.upper() == "POST":
                response = requests.post(
                    url=url,
                    json=input,
                    headers=headers,
                    timeout=30
                )
            else:
                return {"error": f"不支持的HTTP方法: {method}"}

            result = json.loads(response.content.decode('utf-8'))
            logger.info(f"[http_function] 返回值: {result}")
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def asr_function(
            asrPath: str,
            modelType: str,
            url: str,
            headers: dict):
        """
        ASR (Automatic Speech Recognition) - Convert speech to text
        Args:
            asrPath: str - Path to audio file
            modelType: str - Model type to use
            url: str - Base URL for the API
            headers: dict - Request headers
        Returns:
            dict with transcription result or error
        """
        logger.info(
            f"[asr_function] Input params: asrPath={asrPath}, modelType={modelType}, url={url}")

        local_path = None
        try:
            # Create temp directory if it doesn't exist
            os.makedirs(ASR_PATH, exist_ok=True)

            # Download audio file with timeout
            response = requests.get(asrPath, timeout=30)
            response.raise_for_status()

            # Save to temp file with unique name
            local_path = os.path.join(ASR_PATH, f"temp_{os.path.basename(asrPath)}")
            with open(local_path, "wb") as f:
                f.write(response.content)

            # Process audio file
            with open(local_path, "rb") as audio_file:
                files = {
                    'file': ('audio.mp3', audio_file, 'audio/mpeg'),
                    'model': (None, modelType),
                    'response_format': (None, 'json')
                }
                response = requests.post(
                    url=f"{url}/audio/transcriptions",
                    headers=headers,  # 使用传入的headers参数
                    files=files,
                )
                response.raise_for_status()
                completion = response.json()

                # 修正这里：根据实际API响应结构获取文本
                text = completion.get('text', '')  # 假设API返回的JSON中有text字段
                result = {"text": text}

            logger.info(f"[asr_function] Result: {result}")
            return return_dict(data=result, code=0, message="success")

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download audio file: {str(e)}")
            return return_dict(data={"error": "Failed to download audio file"}, code=1, message=str(e))

        except Exception as e:
            logger.error(f"Unexpected error: {traceback.format_exc()}")
            return return_dict(data={"error": str(e)}, code=1, message="error")

        finally:
            # Clean up temp file
            if local_path and os.path.exists(local_path):
                try:
                    os.remove(local_path)
                    logger.info(f"Removed temp file: {local_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temp file {local_path}: {str(e)}")

    @mcp.tool()
    def tts_function(
            modelType: str,
            query: str,
            url: str,
            headers: dict):
        """
        tts文本转语音
        入参：
        - modelType: str - 模型名字
        - url: str - 接口
        - query: str - 需要转语音的文本
        """
        logger.info(
            f"[tts_function] 入参: modelType={modelType}, query={query}, url={url}, headers={headers}")
        try:
            file_name = str(int(time.time()))
            tts_path = MINIO_PATH + file_name + '.mp3'
            local_path = f"/tmp/{file_name}.mp3"
            inputs = {
                "input": query,
                "voice": VOICE,
                "model": modelType,
            }
            response = requests.post(
                url=f"{url}/audio/speech",
                headers={'contentType': 'application/json; charset=UTF-8'},
                json=inputs)
            if response.status_code != 200:
                error_msg = f"API请求失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return return_dict(data={"error": error_msg}, code=1, message="error")
            # 保存音频文件到本地
            with open(local_path, 'wb') as f:
                f.write(response.content)
            # 上传到MinIO
            if os.path.exists(local_path):
                with open(local_path, 'rb') as audio_file:
                    minio_client.put_object(
                        MINOBUCKET,
                        tts_path,
                        audio_file,
                        length=-1,
                        part_size=100 * 1024 * 1024
                    )
                # 上传成功后删除本地文件
                os.remove(local_path)
                logger.info(f"音频文件已上传到MinIO: {tts_path}")
            logger.info(f"[tts_function] 返回值: ret={tts_path}")
            return return_dict(data={"audio_url": f"{ENDPOINT}/{MINOBUCKET}/{tts_path}"}, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def search_bc_internet(q: str, url: str, headers: dict):
        '''
            博查联网搜索插件
            入参：
            - q: str - 查询关键词
        '''
        logger.info(f"[search_bc_internet] 入参: q={q}, headers={headers}, url={url}")
        try:
            input_data = {
                "q": q,
                "summary": True
            }
            # 转发请求
            response = requests.post(
                url=url,
                json=input_data,
                headers=headers,
                timeout=30
            )
            result = json.loads(str(response.content, 'utf-8'))
            logger.info(f"[search_bc_internet] 返回值: {response}")
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def search_bing_internet(q: str, url: str, headers: dict):
        '''
            Bing联网搜索插件
            入参：
            - q: str - 查询关键词
            - url: http://172.1.0.154:8081/search
        '''
        logger.info(f"[search_bing_internet] 入参: {q}, headers={headers}, url={url}")
        try:
            input_data = {
                "q": q,
                "format": "json"
            }
            # 转发请求
            response = requests.post(
                url=url,
                json=input_data,
                headers=headers,
                timeout=30
            )
            # 返回结果
            logger.info(f"[search_bing_internet] 返回值: {response}")
            result = json.loads(response.content)
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def ocr_parse_file(file_path: str,
                       ocr_on_figures: bool,
                       url: str = PDF_OCR_URL,
                       headers: dict = {'Content-Type': 'application/json'}):
        '''
            OCR文档解析工具，处理PDF文件并进行OCR识别。
            入参：
            - file_path: str - minio文件路径
            - ocr_on_figures: bool - 是否对图片进行OCR
            - url: str - 接口URL，默认为PDF_OCR_URL
            - headers: dict - 请求头，默认为{'Content-Type': 'application/json'}
        '''
        # Remove ENDPOINT and MINOBUCKET from file_path if present
        file_path = file_path.replace(f"http://{ENDPOINT}/{MINOBUCKET}", "") if file_path else ""
        logger.info(
            f"[ocr_parse_filfe] 入参: file_path={file_path}, "
            f"ocr_on_figures={ocr_on_figures}, headers={headers}, url={url}")

        try:
            input_data = {
                "url": file_path,
                "bucket": MINOBUCKET,
                "perform_ocr_on_figures": ocr_on_figures
            }

            # 发送请求到OCR处理接口
            response = requests.post(
                url=url,
                json=input_data,
                headers=headers,
                timeout=30
            )

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"[ocr_parse_file] 请求失败: status_code={response.status_code}")
                return return_dict(data={"error": f"Request failed with status code: {response.status_code}"}, code=1,
                                   message="error")

            result = response.json()
            logger.info(f"[ocr_parse_file] 返回值: result={result}")

            # 检查处理结果
            if not result.get("success"):
                error_msg = result.get('message', 'Unknown error')
                logger.error(f"[ocr_parse_file] 处理失败: message={error_msg}")
                return return_dict(data={"error": f"Processing failed: {error_msg}"}, code=1, message="error")

            # 获取处理后的文件路径
            if not result.get("minio_chunks_path"):
                logger.error("[ocr_parse_file] 响应中未包含minio_chunks_path")
                return return_dict(data={"error": "Response does not contain minio_chunks_path"}, code=1,
                                   message="error")
            result["minio_chunks_path"] = f"http://{ENDPOINT}/{result['minio_chunks_path']}"
            logger.info(f"[ocr_parse_file] 返回值: ret={result}")
            logger.info(return_dict(data=result, code=0, message="success"))
            return return_dict(data=result, code=0, message="success")

        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def parse_file(file_path=None, headers=None) -> dict:
        """
        解析文件内容
        Args:
            file_path: 文件路径
        Returns:
            dict: {success: bool, message: str, result: str}
        Supported file types:
            - PDF (.pdf)
            - Word (.docx)
            - Excel (.xlsx, .xls)
            - Text (.txt)
            - Markdown (.md)
            - HTML (.html)
            - PowerPoint (.pptx)
            - CSV (.csv)
            - JSON (.json)
        """
        # 从url中提取bucket_name和object_name
        if file_path:
            if not file_path.startswith(f"http://{ENDPOINT}/{MINOBUCKET}"):
                return return_dict(data={"error": "Invalid URL"}, code=1, message="error")
            object_name = file_path.replace(f"http://{ENDPOINT}/{MINOBUCKET}/", "")
            bucket_name = MINOBUCKET
        logger.info(
            f"[parse_file] 入参: object_name={object_name},bucket_name={bucket_name},headers={headers}")
        try:
            parser = DocumentParser(minio_client, bucket_name, object_name)
            result = parser.parse()
            logger.info(f"[parse_file] 返回值: ret={result}")
            return return_dict(data=result, code=0, message="success")
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            return return_dict(data={"error": str(e)}, code=1, message="error")

    @mcp.tool()
    def sql_executor(sql: str,
                     host: str,
                     port: int,
                     user: str,
                     password: str,
                     database: str,
                     timeout: int = 20,
                     url=None,
                     headers=None):
        '''
            MySQL执行器，执行SQL查询语句
            入参：
            - sql: str - SQL查询语句(仅支持SELECT和SHOW)
            - host: str - MySQL主机地址
            - port: int - MySQL端口号
            - user: str - 用户名
            - password: str - 密码
            - database: str - 数据库名
            - timeout: int - 查询超时时间(秒)，默认20秒
        '''
        logger.info(
            f"[sql_executor] 入参: sql={sql}, host={host}, port={port}, "
            f"user={user}, database={database}, timeout={timeout}")

        # SQL语法检查
        is_valid, error_msg = validate_sql(sql)
        if not is_valid:
            return return_dict(data={"error": error_msg}, code=1, message="error")

        try:
            def _execute_sql():
                # 建立数据库连接
                connection = mysql.connector.connect(
                    host=host,
                    port=port,
                    user=user,
                    password=password,
                    database=database,
                    charset='utf8mb4'
                )
                try:
                    with connection.cursor(dictionary=True) as cursor:
                        cursor.execute(sql)
                        result = cursor.fetchall()
                        return result
                finally:
                    connection.close()

            # 添加超时机制
            result = func_set_timeout(timeout)(_execute_sql)()

            logger.info(f"[sql_executor] 返回值: result={result}")
            return return_dict(data=result, code=0, message="success")

        except FunctionTimedOut:
            return return_dict(data={"error": f"SQL执行超时（>{timeout}秒）"}, code=1, message="error")

        except mysql.connector.Error as e:
            return return_dict(data={"error": f"数据库错误: {str(e)}"}, code=1, message="error")

        except Exception as e:
            logger.error(traceback.format_exc())
            return return_dict(data={"error": str(e)}, code=1, message="error")


logging.getLogger("pdfminer").setLevel(logging.WARNING)
warnings.filterwarnings('ignore')


class DocumentParser:
    def __init__(self, minio_client, bucket_name: str, object_name: str):
        """
        初始化文档解析器
        Args:
            minio_client: MinIO客户端实例
            bucket_name: MinIO bucket名称
            object_name: 对象名称/文件名
        """
        self.minio_client = minio_client
        self.bucket_name = MINOBUCKET
        self.object_name = object_name
        self.temp_dir = "/tmp"

    def download_file(self):
        """从MinIO下载文件到临时目录"""
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)

        temp_path = os.path.join(self.temp_dir, self.object_name)
        try:
            self.minio_client.fget_object(self.bucket_name, self.object_name, temp_path)
            return temp_path
        except Exception as e:
            logger.error(f"从MinIO下载文件失败: {str(e)}")
            raise

    def parse_pdf(self, file_path: str) -> str:
        """解析PDF文件内容"""
        try:
            return extract_text(file_path)
        except PDFTextExtractionNotAllowed:
            logger.error("PDF文档不允许提取文本")
            raise
        except Exception as e:
            logger.error(f"PDF解析失败: {str(e)}")
            raise

    def parse_docx(self, file_path: str) -> str:
        """解析DOCX文件内容"""
        doc = docx.Document(file_path)
        full_text = []

        # 解析段落
        for para in doc.paragraphs:
            if para.text.strip():
                full_text.append(para.text)

        # 解析表格
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text)
                if row_text:
                    full_text.append('\t'.join(row_text))

        return '\n'.join(full_text)

    def parse_excel(self, file_path: str) -> str:
        """解析Excel文件内容(xlsx/xls)"""
        if file_path.endswith('.xlsx'):
            wb = openpyxl.load_workbook(file_path)
        else:
            wb = xlrd.open_workbook(file_path)

        df = pd.read_excel(file_path)
        return df.to_json(orient='records', force_ascii=False)

    def parse_text(self, file_path: str) -> str:
        """解析文本文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

    def parse_markdown(self, file_path: str) -> str:
        """解析Markdown文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            return mistune.Markdown()(content)

    def parse_html(self, file_path: str) -> str:
        """解析HTML文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            soup = BeautifulSoup(f, 'html.parser')
            return soup.get_text()

    def parse_pptx(self, file_path: str) -> str:
        """解析PPTX文件内容"""
        prs = pptx.Presentation(file_path)
        text_parts = []

        for slide_num, slide in enumerate(prs.slides, 1):
            text_parts.append(f"=== 幻灯片 {slide_num} ===")

            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    text_parts.append(shape.text.strip())

                if shape.has_table:
                    for row in shape.table.rows:
                        row_text = [cell.text.strip() for cell in row.cells if cell.text.strip()]
                        if row_text:
                            text_parts.append('\t'.join(row_text))

            if slide.has_notes_slide and slide.notes_slide.notes_text_frame.text.strip():
                text_parts.append(f"备注：{slide.notes_slide.notes_text_frame.text.strip()}")

        return '\n\n'.join(text_parts)

    def parse_csv(self, file_path: str) -> str:
        """解析CSV文件内容"""
        df = pd.read_csv(file_path)
        return df.to_json(orient='records', force_ascii=False)

    def parse_json(self, file_path: str) -> str:
        """解析JSON文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return json.dumps(data, ensure_ascii=False, indent=4)

    def parse(self) -> str:
        """
        解析文档内容
        Returns:
            str: 解析出的文本内容
        """
        try:
            # 从MinIO下载文件
            temp_file = self.download_file()

            # 根据文件扩展名选择解析方法
            ext = os.path.splitext(self.object_name)[1].lower()

            parser_map = {
                '.pdf': self.parse_pdf,
                '.docx': self.parse_docx,
                '.xlsx': self.parse_excel,
                '.xls': self.parse_excel,
                '.txt': self.parse_text,
                '.md': self.parse_markdown,
                '.html': self.parse_html,
                '.pptx': self.parse_pptx,
                '.csv': self.parse_csv,
                '.json': self.parse_json
            }

            if ext not in parser_map:
                raise ValueError(f"不支持的文件类型: {ext}")

            content = parser_map[ext](temp_file)

            # 删除临时文件
            os.remove(temp_file)

            return content

        except Exception as e:
            logger.error(f"文档解析失败: {str(e)}")
            raise



