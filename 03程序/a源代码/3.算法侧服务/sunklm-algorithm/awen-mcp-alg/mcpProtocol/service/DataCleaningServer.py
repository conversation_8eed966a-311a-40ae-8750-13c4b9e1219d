import re
from minio.error import S3Error
import hashlib
import requests
import hanlp
import datetime
from pathlib import Path
import tempfile
import os
import pandas as pd
from docx import Document
from bs4 import BeautifulSoup
import json
from common.conf.ServerConfig import FILE_Convert_URL, FILE_IMAGEREC_URL, PDF_OCR_URL, MINOBUCKET
from common.utils.LogUtils import logger
from common.utils.MinioUtils import MinioClient
from common.utils.MysqlUtils import MySQLClient
from common.utils.ResultUtis import ReturnCode, return_dict



class FileConverter:
    """
    Helper class for converting files to .txt format.
    """
    @staticmethod
    def convert_to_txt(file_path, file_name):
        """
        Convert the given file to .txt format, return new file path and local txt file path.
        Args:
            file_path: str, minio file path (with leading slash)
            file_name: str, original file name
        Returns:
            (new_object_path, local_txt_path, content_type)
        Raises:
            Exception if conversion fails.
        """
        client = MinioClient().get_connect()
        suffix = Path(file_path).suffix.lower()
        object_path = file_path.lstrip("/")
        orig_file_name = Path(file_name).stem
        new_file_name = orig_file_name + ".txt"
        # .txt files: just copy
        with tempfile.TemporaryDirectory() as tmp_dir:
            local_file = os.path.join(tmp_dir, Path(object_path).name)
            txt_file = os.path.join(tmp_dir, new_file_name)
            # Download original file
            client.fget_object(MINOBUCKET, object_path, local_file)
            if suffix == ".txt":
                # Already txt, just copy
                with open(local_file, "r", encoding="utf-8", errors="ignore") as f_in, \
                        open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(f_in.read())
            elif suffix == ".docx":
                doc = Document(local_file)
                with open(txt_file, "w", encoding="utf-8") as f_out:
                    for para in doc.paragraphs:
                        f_out.write(para.text + "\n")
            elif suffix in {".xls", ".xlsx"}:
                df = pd.read_excel(local_file)
                df.to_csv(txt_file, index=False, sep="\t", encoding="utf-8")
            elif suffix == ".csv":
                df = pd.read_csv(local_file)
                df.to_csv(txt_file, index=False, sep="\t", encoding="utf-8")
            elif suffix == ".json":
                with open(local_file, "r", encoding="utf-8") as f_in:
                    data = json.load(f_in)

                # flatten json to text
                def flatten_json(y):
                    out = []

                    def flatten(x, name=''):
                        if type(x) is dict:
                            for a in x:
                                flatten(x[a], f'{name}{a}_')
                        elif type(x) is list:
                            for i, a in enumerate(x):
                                flatten(a, f'{name}{i}_')
                        else:
                            out.append(f"{name[:-1]}: {x}")

                    flatten(y)
                    return "\n".join(out)

                txt = flatten_json(data)
                with open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(txt)
            elif suffix == ".html":
                with open(local_file, "r", encoding="utf-8") as f_in:
                    soup = BeautifulSoup(f_in.read(), "html.parser")
                # Get all text
                text = soup.get_text(separator="\n")
                with open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(text)
            elif suffix == ".md":
                with open(local_file, "r", encoding="utf-8", errors="ignore") as f_in, \
                        open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(f_in.read())
            elif suffix == ".doc":
                # Try to convert .doc to .docx first using doc2docx service
                resp = requests.post(FILE_Convert_URL, json={
                    "bucket": MINOBUCKET,
                    "url": object_path,
                    "output_format": "docx"
                })
                resp.raise_for_status()
                result = resp.json()
                if not result.get("success"):
                    raise Exception(result.get("message", "Unknown error"))
                converted_path = result.get("converted_path", file_path)
                # Download converted docx and extract text
                conv_object_path = converted_path.lstrip("/")
                conv_file = os.path.join(tmp_dir, Path(conv_object_path).name)
                client.fget_object(MINOBUCKET, conv_object_path, conv_file)
                doc = Document(conv_file)
                with open(txt_file, "w", encoding="utf-8") as f_out:
                    for para in doc.paragraphs:
                        f_out.write(para.text + "\n")
            elif suffix == ".pdf":
                # Use OCR service to extract text
                resp = requests.post(PDF_OCR_URL, json={
                    "bucket": MINOBUCKET,
                    "url": object_path,
                    "perform_ocr_on_figures": True
                })
                resp.raise_for_status()
                result = resp.json()
                if not result.get("success"):
                    raise Exception(result.get("message", "OCR failed"))
                chunks_path = result["minio_chunks_path"].split("/", 1)[1]
                chunk_object_name = Path(chunks_path).name
                chunk_local_path = os.path.join(tmp_dir, chunk_object_name)
                client.fget_object(MINOBUCKET, chunks_path, chunk_local_path)
                with open(chunk_local_path, "r", encoding="utf-8") as f_in, \
                        open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(f_in.read())
            elif suffix in {".png", ".jpg", ".jpeg"}:
                # Use OCR service to extract text
                resp = requests.post(PDF_OCR_URL, json={
                    "bucket": MINOBUCKET,
                    "url": object_path,
                    "perform_ocr_on_figures": True
                })
                resp.raise_for_status()
                result = resp.json()
                if not result.get("success"):
                    raise Exception(result.get("message", "OCR failed"))
                chunks_path = result["minio_chunks_path"].split("/", 1)[1]
                chunk_object_name = Path(chunks_path).name
                chunk_local_path = os.path.join(tmp_dir, chunk_object_name)
                client.fget_object(MINOBUCKET, chunks_path, chunk_local_path)
                with open(chunk_local_path, "r", encoding="utf-8") as f_in, \
                        open(txt_file, "w", encoding="utf-8") as f_out:
                    f_out.write(f_in.read())
            elif suffix in {".pptx", ".ppt"}:
                # Not supported for now
                raise Exception("ppt/pptx to txt not supported")
            elif suffix in {".zip", ".rar"}:
                raise Exception("压缩包无法直接转换为txt")
            else:
                raise Exception(f"不支持的文件类型: {suffix}")
            # Save to /unstructed_result/new_file_name
            new_object_path = str(Path("unstructed_result") / new_file_name)
            return new_object_path, txt_file, "text/plain"


class Desensitizer:
    """
    使用 HanLP NER 模型 + 正则表达式，对文本进行脱敏处理。
    """
    _ner_model = None

    @classmethod
    def get_model(cls):
        if cls._ner_model is None:
            model_path = Path(__file__).parent.parent.parent / 'hanlp_cache' / 'ner' / 'msra'
            root_path = Path(__file__).parent.parent.parent / 'hanlp_cache'
            cls._ner_model = hanlp.load(str(model_path), root=str(root_path))
        return cls._ner_model

    # 正则脱敏规则
    regex_rules = [
        (re.compile(r'(?<!\d)(1[3-9]\d{9})(?!\d)'), lambda m: m.group(1)[:3] + "****" + m.group(1)[-4:]),  # 手机号
        (re.compile(r'\b(\d{6})\d{8}(\w{4})\b'), lambda m: m.group(1) + "********" + m.group(2)),  # 身份证
        (re.compile(r'\b([a-zA-Z0-9_.+-]+)@([a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)\b'),
         lambda m: m.group(1)[:2] + "***@" + m.group(2)),  # 邮箱
    ]

    @classmethod
    def desensitize_text(cls, text):
        """
        对文本执行 NER + 正则脱敏
        """
        try:
            entities = cls.get_model()(text, tasks=['ner'])['ner/msra']
        except Exception as e:
            entities = []

        if entities:
            for word, label in sorted(entities, key=lambda x: -len(x[0])):
                if label in ('PERSON', 'ORGANIZATION', 'LOCATION'):
                    text = re.sub(re.escape(word), '*' * len(word), text)

        for pattern, repl in cls.regex_rules:
            text = pattern.sub(repl, text)

        return text

    @classmethod
    def desensitize_ocr_result(cls, result):
        """
        对 OCR 识别结果中的文本内容进行脱敏处理。
        """
        if result and isinstance(result[0], dict) and "page" in result[0]:
            for page in result:
                page["contents"] = [
                    {"text": cls.desensitize_text(item["text"])} for item in page.get("contents", [])
                ]
            return result
        else:
            return [{"text": cls.desensitize_text(item["text"])} for item in result]


class DataCleaner:
    @staticmethod
    def deduplicate(file_paths):
        """
        去重方法：从 MinIO 下载每个文件，通过内容哈希判断是否重复，删除重复文件，仅保留唯一内容。
        :param file_paths: 文件信息字典列表，每个字典包含 filePath, fileId, categoryCode, fileName, kgId
        :return: 去重后的文件信息字典列表（按文件内容）
        """

        client = MinioClient().get_connect()

        seen_hashes = set()
        unique = []

        with tempfile.TemporaryDirectory() as tmp_dir:
            for file_info in file_paths:
                file_path = file_info["filePath"]
                file_id = int(file_info["fileId"])
                object_path = file_path.lstrip('/')
                local_file = os.path.join(tmp_dir, os.path.basename(object_path))
                try:
                    client.fget_object(MINOBUCKET, object_path, local_file)
                    with open(local_file, 'rb') as f:
                        content = f.read()
                    file_hash = hashlib.md5(content).hexdigest()
                    if file_hash in seen_hashes:
                        client.remove_object(MINOBUCKET, object_path)
                        logger.info(f"重复文件已删除: {object_path}")
                        # 删除数据库记录
                        conn = None
                        try:
                            conn = MySQLClient().get_connect()
                            with conn.cursor() as cursor:
                                sql = "DELETE FROM xj_kg_file_info_tb WHERE file_id = %s"
                                cursor.execute(sql, (int(file_info["fileId"]),))
                            conn.commit()
                        except Exception as e:
                            logger.error(f"数据库记录删除失败: {e}")
                        finally:
                            if conn:
                                conn.close()
                        continue
                    seen_hashes.add(file_hash)
                    new_file_path = file_path
                    # 保留文件不再进行数据库UPDATE操作
                    # unique中添加保留的唯一文件
                    unique.append({
                        "filePath": new_file_path,
                        "fileId": file_id,
                        "categoryCode": file_info["categoryCode"],
                        "fileName": os.path.basename(new_file_path),
                        "kgId": file_info["kgId"]
                    })
                except S3Error as e:
                    logger.error(f"文件处理失败: {object_path}, 错误: {e}")
                    continue
        return unique

    @staticmethod
    def validate_and_fix_encoding(file_paths):
        """
        校验并修正编码方法：从 MinIO 下载每个 file_path 对应的文件，检查是否为 UTF-8 编码，
        如果不是则尝试用 GBK 读取并转为 UTF-8 保存。记录错误信息并返回校验通过和失败的路径。
        :param file_paths: 文件信息字典列表，每个字典包含 filePath, fileId, categoryCode, fileName, kgId
        :return: (校验通过的文件信息字典列表, 校验失败路径及错误信息字典列表)
        """
        client = MinioClient().get_connect()
        valid = []
        errors = []
        with tempfile.TemporaryDirectory() as tmp_dir:
            for file_info in file_paths:
                file_path = file_info["filePath"]
                file_id = int(file_info["fileId"])
                object_path = file_path.lstrip('/')
                local_file = os.path.join(tmp_dir, os.path.basename(object_path))
                need_update = False
                try:
                    client.fget_object(MINOBUCKET, object_path, local_file)
                except S3Error as e:
                    logger.error(f"下载失败: {object_path}, 错误: {e}")
                    errors.append({'filePath': file_path, 'error': f'Download failed: {e}',
                                   **{k: file_info[k] for k in ('fileId', 'categoryCode', 'fileName', 'kgId')}})
                    continue
                try:
                    with open(local_file, 'rb') as f:
                        content = f.read()
                    content.decode('utf-8')
                    logger.info(f"文件编码为 UTF-8: {object_path}")
                    need_update = False
                except UnicodeDecodeError:
                    try:
                        with open(local_file, 'r', encoding='gbk', errors='ignore') as f:
                            content_text = f.read()
                        with open(local_file, 'w', encoding='utf-8') as f:
                            f.write(content_text)
                        logger.info(f"文件编码从 GBK 转换为 UTF-8: {object_path}")
                        need_update = True
                    except Exception as e:
                        logger.error(f"编码转换失败: {object_path}, 错误: {e}")
                        errors.append({'filePath': file_path, 'error': f'Encoding conversion failed: {e}',
                                       **{k: file_info[k] for k in ('fileId', 'categoryCode', 'fileName', 'kgId')}})
                        continue
                new_file_path = file_path
                # 只有内容被修改时才更新数据库
                if need_update:
                    conn = None
                    try:
                        conn = MySQLClient().get_connect()
                        with conn.cursor() as cursor:
                            sql = """
                                UPDATE xj_kg_file_info_tb
                                SET file_path = %s, file_name = %s, update_time = %s, file_size = %s, file_format = %s
                                WHERE file_id = %s
                            """
                            now = datetime.datetime.now()
                            cursor.execute(sql, (
                                new_file_path,
                                os.path.basename(new_file_path),
                                now,
                                os.path.getsize(local_file) // 1024,
                                Path(new_file_path).suffix[1:],
                                int(file_info["fileId"])
                            ))
                        conn.commit()
                    except Exception as e:
                        logger.error(f"数据库更新失败: {e}")
                    finally:
                        if conn:
                            conn.close()
                valid.append({
                    "filePath": new_file_path,
                    "fileId": file_id,
                    "categoryCode": file_info["categoryCode"],
                    "fileName": os.path.basename(new_file_path),
                    "kgId": file_info["kgId"]
                })
        return valid, errors

    @staticmethod
    def doc2docx(file_paths):
        """
        将 .doc 文件转换为 .docx 文件，调用外部服务进行格式转换。
        :param file_paths: .doc 文件信息字典列表
        :return: (转换成功后的文件信息字典列表, 错误信息字典列表)
        """
        converted = []
        errors = []
        # 初始化 Minio 客户端
        client = MinioClient().get_connect()

        for file_info in file_paths:
            file_path = file_info["filePath"]
            file_id = int(file_info["fileId"])
            need_update = False
            if not file_path.endswith('.doc'):
                new_file_path = file_path
                need_update = False
            else:
                object_path = file_path.lstrip('/')
                try:
                    resp = requests.post(FILE_Convert_URL, json={
                        "bucket": MINOBUCKET,
                        "url": object_path,
                        "output_format": "docx"
                    })
                    if resp.status_code != 200:
                        raise Exception(f"Service error: {resp.status_code}, {resp.text}")
                    result = resp.json()
                    if not result.get("success"):
                        raise Exception(result.get("message", "Unknown error"))
                    converted_path = result.get("converted_path", file_path)
                    new_file_path = converted_path
                    need_update = True
                except Exception as e:
                    errors.append({'filePath': file_path, 'error': f"Conversion failed: {e}",
                                   **{k: file_info[k] for k in ('fileId', 'categoryCode', 'fileName', 'kgId')}})
                    continue
            # 只有.doc且转换成功时才更新数据库
            if need_update:
                conn = None
                try:
                    conn = MySQLClient().get_connect()
                    with conn.cursor() as cursor:
                        sql = """
                            UPDATE xj_kg_file_info_tb
                            SET file_path = %s, file_name = %s, update_time = %s, file_size = %s, file_format = %s
                            WHERE file_id = %s
                        """
                        now = datetime.datetime.now()
                        # 增加下载转换后文件到本地，获取文件大小
                        new_object_path = new_file_path.lstrip("/")
                        tmp_dir = tempfile.gettempdir()
                        local_file = os.path.join(tmp_dir, os.path.basename(new_file_path))
                        try:
                            client.fget_object(MINOBUCKET, new_object_path, local_file)
                            file_size_kb = os.path.getsize(local_file) // 1024
                        except Exception as e:
                            logger.warning(f"下载转换后文件失败，回退为0KB: {e}")
                            file_size_kb = 0
                        cursor.execute(sql, (
                            new_file_path,
                            os.path.basename(new_file_path),
                            now,
                            file_size_kb,
                            Path(new_file_path).suffix[1:],
                            int(file_info["fileId"])
                        ))
                        if os.path.exists(local_file):
                            os.remove(local_file)
                    conn.commit()
                except Exception as e:
                    logger.error(f"数据库更新失败: {e}")
                finally:
                    if conn:
                        conn.close()
            converted.append({
                "filePath": new_file_path,
                "fileId": file_id,
                "categoryCode": file_info["categoryCode"],
                "fileName": os.path.basename(new_file_path),
                "kgId": file_info["kgId"]
            })
        return converted, errors

    @staticmethod
    def anonymize(file_paths):
        updated = []
        client = MinioClient().get_connect()
        with tempfile.TemporaryDirectory() as tmp_dir:
            for file_info in file_paths:
                file_path = file_info["filePath"]
                file_id = int(file_info["fileId"])
                suffix = Path(file_path).suffix.lower()
                object_path = file_path.lstrip("/")
                file_name = Path(object_path).name
                new_file_name = Path(file_name).stem + "_masked.txt"
                new_object_path = Path(object_path).parent / new_file_name

                local_file = os.path.join(tmp_dir, file_name)
                masked_file = os.path.join(tmp_dir, new_file_name)

                try:
                    if suffix == ".docx":
                        client.fget_object(MINOBUCKET, object_path, local_file)
                        doc = Document(local_file)
                        for para in doc.paragraphs:
                            para.text = Desensitizer.desensitize_text(para.text)
                        masked_file = os.path.join(tmp_dir, Path(file_name).stem + "_masked.docx")
                        doc.save(masked_file)
                        new_object_path = Path(object_path).parent / Path(masked_file).name
                        content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

                    elif suffix in {".pdf", ".png", ".jpg", ".jpeg", ".pptx", ".ppt"}:
                        resp = requests.post(PDF_OCR_URL, json={
                            "bucket": MINOBUCKET,
                            "url": object_path,
                            "perform_ocr_on_figures": True
                        })
                        resp.raise_for_status()
                        result = resp.json()
                        if not result.get("success"):
                            raise Exception(result.get("message", "Unknown error"))
                        chunks_path = result["minio_chunks_path"].split("/", 1)[1]
                        chunk_object_name = Path(chunks_path).name
                        chunk_local_path = os.path.join(tmp_dir, chunk_object_name)
                        client.fget_object(MINOBUCKET, chunks_path, chunk_local_path)
                        with open(chunk_local_path, "r", encoding="utf-8") as f:
                            content = f.read()
                        with open(masked_file, "w", encoding="utf-8") as f:
                            f.write(Desensitizer.desensitize_text(content))

                        # 判断chunks_path和object_path是否在同一目录
                        if Path(chunks_path).parent != Path(object_path).parent:
                            new_object_path = Path(object_path).parent / new_file_name
                        else:
                            new_object_path = Path(chunks_path)

                        # 按新逻辑: 若chunks_path与object_path目录不一致，则上传脱敏文件至原始目录，并删除chunks_path文件
                        if str(new_object_path) != chunks_path:
                            client.fput_object(
                                MINOBUCKET,
                                str(new_object_path),
                                masked_file,
                                content_type="text/plain"
                            )
                            client.remove_object(MINOBUCKET, chunks_path)
                        else:
                            client.fput_object(
                                MINOBUCKET,
                                chunks_path,
                                masked_file,
                                content_type="text/plain"
                            )
                        content_type = "text/plain"

                    elif suffix in {".xls", ".xlsx", ".csv"}:
                        client.fget_object(MINOBUCKET, object_path, local_file)
                        df = pd.read_excel(local_file) if suffix != ".csv" else pd.read_csv(local_file)
                        for col in df.select_dtypes(include="object").columns:
                            df[col] = df[col].astype(str).apply(Desensitizer.desensitize_text)
                        df.to_csv(masked_file, index=False, encoding="utf-8")
                        new_object_path = Path("unstructed_result") / new_file_name
                        content_type = "text/csv"

                    elif suffix == ".json":
                        client.fget_object(MINOBUCKET, object_path, local_file)

                        def walk_json(data):
                            if isinstance(data, dict):
                                return {k: walk_json(v) for k, v in data.items()}
                            elif isinstance(data, list):
                                return [walk_json(i) for i in data]
                            elif isinstance(data, str):
                                return Desensitizer.desensitize_text(data)
                            return data

                        with open(local_file, "r", encoding="utf-8") as f:
                            json_data = json.load(f)
                        masked_data = walk_json(json_data)
                        with open(masked_file, "w", encoding="utf-8") as f:
                            json.dump(masked_data, f, ensure_ascii=False, indent=2)
                        new_object_path = Path("unstructed_result") / new_file_name
                        content_type = "application/json"

                    elif suffix == ".html":
                        client.fget_object(MINOBUCKET, object_path, local_file)
                        with open(local_file, "r", encoding="utf-8") as f:
                            soup = BeautifulSoup(f.read(), "html.parser")
                        for tag in soup.find_all(text=True):
                            if tag.strip():
                                tag.replace_with(Desensitizer.desensitize_text(tag))
                        with open(masked_file, "w", encoding="utf-8") as f:
                            f.write(str(soup))
                        new_object_path = Path("unstructed_result") / new_file_name
                        content_type = "text/html"

                    elif suffix in {".md", ".txt", ".doc"}:
                        client.fget_object(MINOBUCKET, object_path, local_file)
                        with open(local_file, "r", encoding="utf-8", errors="ignore") as f:
                            content = f.read()
                        with open(masked_file, "w", encoding="utf-8") as f:
                            f.write(Desensitizer.desensitize_text(content))
                        new_object_path = Path("unstructed_result") / new_file_name
                        content_type = "text/plain"

                    else:
                        raise ValueError(f"不支持的文件类型: {suffix}")

                    # 只有非pdf/png/jpg/jpeg/pptx/ppt分支需要上传（这些在分支内已上传）
                    if suffix not in {".pdf", ".png", ".jpg", ".jpeg", ".pptx", ".ppt"}:
                        client.fput_object(
                            MINOBUCKET,
                            str(new_object_path),
                            masked_file,
                            content_type=content_type
                        )
                    # 数据库更新
                    conn = None
                    try:
                        conn = MySQLClient().get_connect()
                        with conn.cursor() as cursor:
                            sql = """
                                UPDATE xj_kg_file_info_tb
                                SET file_path = %s, file_name = %s, update_time = %s, file_size = %s, file_format = %s
                                WHERE file_id = %s
                            """
                            now = datetime.datetime.now()
                            cursor.execute(sql, (
                                "/" + str(new_object_path),
                                os.path.basename(str(new_object_path)),
                                now,
                                os.path.getsize(masked_file) // 1024,
                                Path(str(new_object_path)).suffix[1:],
                                int(file_info["fileId"])
                            ))
                        conn.commit()
                    except Exception as e:
                        logger.error(f"数据库更新失败: {e}")
                    finally:
                        if conn:
                            conn.close()
                    updated.append({
                        "filePath": "/" + str(new_object_path),
                        "fileId": file_id,
                        "categoryCode": file_info["categoryCode"],
                        "fileName": os.path.basename(str(new_object_path)),
                        "kgId": file_info["kgId"]
                    })

                except Exception as e:
                    logger.error(f"脱敏处理失败: {file_path}, 错误: {e}")
                    continue

        return updated

    @staticmethod
    def pdf2docx(file_paths):
        """
        使用 OCR + docx 重建方式，将 PDF 文件转换为可编辑的 DOCX。
        对每个 PDF，调用图像识别服务获取文本内容，并生成 DOCX 文件上传回 MinIO。
        :param file_paths: 文件信息字典列表
        :return: (转换成功后的文件信息字典列表, 错误信息列表)
        """
        converted = []
        errors = []
        for file_info in file_paths:
            file_path = file_info["filePath"]
            file_id = int(file_info["fileId"])
            need_update = False
            if not file_path.endswith(".pdf"):
                new_file_path = file_path
                need_update = False
            else:
                object_path = file_path.lstrip("/")
                try:
                    # 发送识别请求
                    resp = requests.post(FILE_IMAGEREC_URL, json={
                        "bucket": MINOBUCKET,
                        "url": object_path,
                    }, timeout=120)
                    if resp.status_code != 200:
                        raise Exception(f"识别服务异常: {resp.status_code}, {resp.text}")
                    result = resp.json()
                    if not result.get("success"):
                        raise Exception(result.get("message", "识别失败"))

                    import docx
                    doc = docx.Document()

                    results = result.get("results", [])
                    # 多页 PDF
                    if isinstance(results, list) and results and "page" in results[0]:
                        for page in results:
                            for item in page.get("contents", []):
                                doc.add_paragraph(item["text"])
                    else:  # 单页或图片识别结果
                        for item in results:
                            doc.add_paragraph(item["text"])

                    # 保存到临时文件
                    tmp_dir = tempfile.gettempdir()
                    output_name = Path(file_path).stem + ".docx"
                    output_path = os.path.join(tmp_dir, output_name)
                    doc.save(output_path)

                    # 计算文件大小（KB）
                    file_size_kb = os.path.getsize(output_path) // 1024

                    # 上传至 MinIO
                    client = MinioClient().get_connect()

                    new_object_path = object_path.replace(".pdf", ".docx")
                    client.fput_object(
                        MINOBUCKET,
                        new_object_path,
                        output_path,
                        content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    )

                    new_file_path = "/" + new_object_path
                    need_update = True
                except requests.exceptions.Timeout:
                    errors.append({
                        "filePath": file_path,
                        "error": "识别服务超时",
                        **{k: file_info[k] for k in ('fileId', 'categoryCode', 'fileName', 'kgId')}
                    })
                except Exception as e:
                    errors.append({"filePath": file_path, "error": str(e),
                                   **{k: file_info[k] for k in ('fileId', 'categoryCode', 'fileName', 'kgId')}})
                    continue
                finally:
                    if 'output_path' in locals() and os.path.exists(output_path):
                        os.unlink(output_path)
            if need_update:
                conn = None
                try:
                    conn = MySQLClient().get_connect()
                    with conn.cursor() as cursor:
                        sql = """
                            UPDATE xj_kg_file_info_tb
                            SET file_path = %s, file_name = %s, update_time = %s, file_size = %s, file_format = %s
                            WHERE file_id = %s
                        """
                        now = datetime.datetime.now()
                        cursor.execute(sql, (
                            new_file_path,
                            os.path.basename(new_file_path),
                            now,
                            file_size_kb if need_update else 0,
                            Path(new_file_path).suffix[1:],
                            int(file_info["fileId"])
                        ))
                    conn.commit()
                except Exception as e:
                    logger.error(f"数据库更新失败: {e}")
                finally:
                    if conn:
                        conn.close()
            converted.append({
                "filePath": new_file_path,
                "fileId": file_id,
                "categoryCode": file_info["categoryCode"],
                "fileName": os.path.basename(new_file_path),
                "kgId": file_info["kgId"]
            })
        return converted, errors

    @staticmethod
    def validate_file_types(file_paths):
        """
        校验文件类型是否为允许的扩展名，不合法的从 MinIO 中删除并从列表中移除。
        :param file_paths: 文件信息字典列表
        :return: 合法文件信息字典列表
        """
        allowed_exts = {".xls", ".xlsx", ".pdf", ".docx", ".doc", ".txt", ".png", ".jpg", ".jpeg", ".csv", ".json",
                        ".html", ".md", ".pptx", ".ppt", ".zip", ".rar"}
        client = MinioClient().get_connect()
        filtered = []
        for file_info in file_paths:
            file_path = file_info["filePath"]
            file_id = int(file_info["fileId"])
            if any(file_path.endswith(ext) for ext in allowed_exts):
                new_file_path = file_path
                # 合法类型，仅保留，不更新数据库
                filtered.append({
                    "filePath": new_file_path,
                    "fileId": file_id,
                    "categoryCode": file_info["categoryCode"],
                    "fileName": os.path.basename(new_file_path),
                    "kgId": file_info["kgId"]
                })
            else:
                try:
                    object_path = file_path.lstrip("/")
                    client.remove_object(MINOBUCKET, object_path)
                    logger.info(f"已删除不支持的文件类型: {file_path}")
                    # 非法类型，删除数据库记录
                    conn = None
                    try:
                        conn = MySQLClient().get_connect()
                        with conn.cursor() as cursor:
                            sql = "DELETE FROM xj_kg_file_info_tb WHERE file_id = %s"
                            cursor.execute(sql, (int(file_info["fileId"]),))
                        conn.commit()
                    except Exception as e:
                        logger.error(f"数据库更新失败: {e}")
                    finally:
                        if conn:
                            conn.close()
                except Exception as e:
                    logger.warning(f"删除文件失败: {file_path}, 错误: {e}")
        return filtered

    @staticmethod
    def all2txt(file_paths):
        """
        Convert all files in file_paths to .txt and upload to MinIO, update DB.
        :param file_paths: list of dicts with filePath, fileId, categoryCode, fileName, kgId
        :return: list of dicts with new file info
        """
        client = MinioClient().get_connect()
        result = []
        for file_info in file_paths:
            file_path = file_info["filePath"]
            file_id = int(file_info["fileId"])
            file_name = file_info["fileName"]
            try:
                # Convert to txt
                new_object_path, txt_file, content_type = FileConverter.convert_to_txt(file_path, file_name)
                # Upload to MinIO
                client.fput_object(
                    MINOBUCKET,
                    new_object_path,
                    txt_file,
                    content_type=content_type
                )
                # Update DB
                conn = None
                try:
                    conn = MySQLClient().get_connect()
                    with conn.cursor() as cursor:
                        sql = """
                            UPDATE xj_kg_file_info_tb
                            SET file_path = %s, file_name = %s, update_time = %s, file_size = %s, file_format = %s
                            WHERE file_id = %s
                        """
                        now = datetime.datetime.now()
                        file_size_kb = os.path.getsize(txt_file) // 1024
                        cursor.execute(sql, (
                            "/" + new_object_path,
                            os.path.basename(new_object_path),
                            now,
                            file_size_kb,
                            "txt",
                            file_id
                        ))
                    conn.commit()
                except Exception as e:
                    logger.error(f"数据库更新失败: {e}")
                finally:
                    if conn:
                        conn.close()
                result.append({
                    "filePath": "/" + new_object_path,
                    "fileId": file_id,
                    "categoryCode": file_info["categoryCode"],
                    "fileName": os.path.basename(new_object_path),
                    "kgId": file_info["kgId"]
                })
            except Exception as e:
                logger.error(f"all2txt转换失败: {file_path}, 错误: {e}")
                continue
        return result


def register(mcp):
    """
    将 DataCleaner 的方法注册为 MCP 工具
    """

    @mcp.tool()
    def file_clean_tool(filePaths: list,cleanType:str):
        """
        文档处理工具
        入参
        - filePaths 文件地址
        - cleanType 清洗类型
        """
        logger.info(f"[文档处理] 入参: cleanType:{cleanType},filePaths:{filePaths}")
        try:
            result = None
            if "deduplicate" == cleanType:
                # 去重
                result = DataCleaner.deduplicate(filePaths)
            if "validate_encoding" == cleanType:
                # 编码校验
                result, _ = DataCleaner.validate_and_fix_encoding(filePaths)
            if "anonymize" == cleanType:
                # 去隐私
                result = DataCleaner.anonymize(filePaths)
            if "validate_file_type" == cleanType:
                # 文件类型校验接口
                result = DataCleaner.validate_file_types(filePaths)

            return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
        except  Exception as e:
            logger.error(f"[文档处理] 异常: {e}")
            return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")

    @mcp.tool()
    def file_format_tool(filePaths: list, cleanType: str):
        """
        文件转换工具
        入参
        - filePaths 文件地址
        - cleanType 清洗类型
        """
        logger.info(f"[文件转换工具] 入参: cleanType:{cleanType},filePaths:{filePaths}")
        try:
            result = None
            if "doc2docx" == cleanType:
                # doc2docx
                result = DataCleaner.doc2docx(filePaths)
            if "pdf2docx" == cleanType:
                # pdf2docx
                result, _ = DataCleaner.pdf2docx(filePaths)
            if "all2txt" == cleanType:
                result = DataCleaner.all2txt(filePaths)

            return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
        except  Exception as e:
            logger.error(f"[文件转换工具] 异常: {e}")
            return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")

    #
    # @mcp.tool()
    # def deduplicate_tool(filePaths: list):
    #     logger.info(f"[deduplicate_tool] 入参: {filePaths}")
    #     try:
    #         result = DataCleaner.deduplicate(filePaths)
    #         return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[deduplicate_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def validate_encoding_tool(filePaths: list):
    #     logger.info(f"[validate_encoding_tool] 入参: {filePaths}")
    #     try:
    #         valid, _ = DataCleaner.validate_and_fix_encoding(filePaths)
    #         return return_dict(data=valid, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[validate_encoding_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def doc2docx_tool(filePaths: list):
    #     logger.info(f"[doc2docx_tool] 入参: {filePaths}")
    #     try:
    #         converted, _ = DataCleaner.doc2docx(filePaths)
    #         return return_dict(data=converted, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[doc2docx_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def anonymize_tool(filePaths: list):
    #     logger.info(f"[anonymize_tool] 入参: {filePaths}")
    #     try:
    #         result = DataCleaner.anonymize(filePaths)
    #         return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[anonymize_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def pdf2docx_tool(filePaths: list):
    #     logger.info(f"[pdf2docx_tool] 入参: {filePaths}")
    #     try:
    #         converted, _ = DataCleaner.pdf2docx(filePaths)
    #         return return_dict(data=converted, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[pdf2docx_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def validate_file_type_tool(filePaths: list):
    #     logger.info(f"[validate_file_type_tool] 入参: {filePaths}")
    #     try:
    #         result = DataCleaner.validate_file_types(filePaths)
    #         return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[validate_file_type_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
    #
    # @mcp.tool()
    # def all2txt(filePaths: list):
    #     logger.info(f"[validate_file_type_tool] 入参: {filePaths}")
    #     try:
    #         result = DataCleaner.all2txt(filePaths)
    #         return return_dict(data=result, code=str(ReturnCode.SUCESS.value), message="success")
    #     except Exception as e:
    #         logger.error(f"[validate_file_type_tool] 异常: {e}")
    #         return return_dict(data=[], code=str(ReturnCode.FAIL.value), message=f"执行失败: {e}")
