from contextlib import contextmanager
import random
import string
import os
import requests
import json, traceback
from common.utils.DbUtils import PO<PERSON>
from common.utils.MysqlUtils import MySQLClient
from common.utils.SnowflakeUtils import SnowflakeUtils
from common.utils.LogUtils import logger
from common.utils.ResultUtis import ReturnCode, return_dict
from common.utils.SftpUtils import SFTPClient
from common.conf.ServerConfig import FILE_FORMAR_URL, FILE_SPLIT_URL, FILE_LAYOUT_URL, FILE_IMAGEREC_URL, FILE_TABLEREC_URL, MINOBUCKET

snowflake = SnowflakeUtils(machine_id=1)


class KnowService:
    def __init__(self):
        conn = POOL.connection()
        self.conn = conn

    @contextmanager
    def _get_cursor(self):
        cursor = self.conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()

    def insert_kg_vector_slice_conf(self, fileIds, kgIds, categoryCodes, userId, isDeepProcess, isFaqExtract) -> None:
        try:
            with self._get_cursor() as cursor:
                for i in range(len(fileIds)):
                    # Check if record exists
                    checkSql = """
                        SELECT conf_id FROM xj_kg_vector_slice_conf_tb
                        WHERE file_id = %s
                    """
                    cursor.execute(checkSql, (fileIds[i],))
                    exists = cursor.fetchone()

                    if exists:
                        # Update existing record
                        updateSql = """
                            UPDATE xj_kg_vector_slice_conf_tb
                            SET kg_id = %s, slice_conf_id = %s, vector_id = %s, category_code = %s,
                                create_user_id = %s, is_deep_process = %s, is_faq_extract = %s,
                                is_abstract_extract = %s, is_meta_extract = %s
                            WHERE file_id = %s
                        """
                        cursor.execute(updateSql, (
                            kgIds[i],
                            1,  # slice_conf_id
                            2,  # default vector_id
                            categoryCodes[i],
                            userId,
                            isDeepProcess,
                            isFaqExtract,
                            0,  # default is_abstract_extract
                            0,  # default is_meta_extract
                            fileIds[i]
                        ))
                    else:
                        # Insert new record
                        insertSql = """
                            INSERT INTO xj_kg_vector_slice_conf_tb
                            (conf_id, kg_id, file_id, slice_conf_id, vector_id, category_code, create_user_id,
                             is_deep_process, is_faq_extract, is_abstract_extract, is_meta_extract)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        conf_id = snowflake.generate_id()
                        cursor.execute(insertSql, (
                            conf_id,
                            kgIds[i],
                            fileIds[i],
                            1,  # slice_conf_id
                            2,  # default vector_id
                            categoryCodes[i],
                            userId,
                            isDeepProcess,
                            isFaqExtract,
                            0,  # default is_abstract_extract
                            0  # default is_meta_extract
                        ))
                self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to insert/update vector slice configuration: {str(e)}")

    def update_kg_vector_slice_conf(self, fileId, is_abstract_extract, slice_conf_id, is_meta_extract) -> None:
        try:
            with self._get_cursor() as cursor:
                # 查询 知识空间名称
                updateSql = """
                    UPDATE xj_kg_vector_slice_conf_tb
                    SET is_abstract_extract = %s, is_meta_extract = %s,slice_conf_id = %s
                    WHERE file_id = %s
                """
                cursor.execute(updateSql, (is_abstract_extract, is_meta_extract, slice_conf_id, fileId))
                self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to update vector slice configuration: {str(e)}")

    def insert_kg_slice_conf(self, createUser, sliceName, algoCode, slice_param, sliceDesc="", projectId=None) -> None:
        try:
            with self._get_cursor() as cursor:
                # 查询 知识空间名称
                slice_conf_id = snowflake.generate_id()
                insertSql = """
                    INSERT INTO xj_kg_slice_conf_tb
                    (slice_conf_id, slice_name, algo_code, slice_desc, slice_param, create_user_id, is_delete, project_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insertSql,
                               (slice_conf_id, sliceName, algoCode, sliceDesc, slice_param, createUser, 0, projectId))
                self.conn.commit()
                return slice_conf_id
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to insert slice configuration: {str(e)}")

    def update_kg_file_info_enable(self, fileId, is_enable) -> None:
        try:
            with self._get_cursor() as cursor:
                # 查询 知识空间名称
                updateSql = """
                    UPDATE xj_kg_file_info_tb
                    SET is_enable = %s
                    WHERE file_id = %s
                """
                cursor.execute(updateSql, (is_enable, fileId))
                self.conn.commit()
        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to update file info: {str(e)}")

    def update_kg_file_status(self, fileId, fileStatus) -> None:
        try:
            with self._get_cursor() as cursor:
                # First check current file status
                checkSql = """
                    SELECT file_status
                    FROM xj_kg_file_info_tb
                    WHERE file_id = %s
                """
                cursor.execute(checkSql, (fileId,))
                current_status = cursor.fetchone()

                if current_status and current_status[0] == 3:
                    # Only update if current status is 3
                    updateSql = """
                        UPDATE xj_kg_file_info_tb
                        SET file_status = %s
                        WHERE file_id = %s
                    """
                    cursor.execute(updateSql, (fileStatus, fileId))
                    self.conn.commit()
                else:
                    raise Exception("文档结构化未成功")

        except Exception as e:
            self.conn.rollback()
            raise Exception(f"Failed to update file status: {str(e)}")

    def get_know_info(self, kgName: str, categoryName: str, userId: str) -> None:
        """
            query = "SELECT category_level FROM xj_kg_category_info_tb WHERE category_code = %s"
        self.cursor.execute(query, (category_code,))
        """
        try:
            res = {
                "kgCode": "",
                "categoryCode": ""
            }
            with self._get_cursor() as cursor:
                # 查询 知识空间名称
                queryKgSpaceSql = "SELECT kg_id,kg_code,kg_name,create_user_id FROM  xj_kg_space_info_tb where kg_name =  %s and create_user_id =  %s"
                cursor.execute(queryKgSpaceSql, (kgName, userId))
                # 获取知识空间信息
                result = cursor.fetchone()
                if result is None:
                    # 不存在 创建一个 KgCode
                    kgId = snowflake.generate_id()
                    kgCode = self.generate_code("kg_")
                    # 添加知识空间数据
                    insertKgSpace = """
                                    INSERT INTO xj_kg_space_info_tb
                                    (kg_id, kg_name, kg_code, kg_type, vector_id, create_user_id, update_user_id, \
                                     kg_desc, is_delete)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) \
                                    """
                    cursor.execute(insertKgSpace, (kgId, kgName, kgCode, "1", "2", userId, userId, "", 0))
                    self.conn.commit()
                    # 不存在，新建一个categoryCode
                    categoryId = snowflake.generate_id()

                    # 查询上一个类目code
                    queryCate = """
                                select category_code
                                from xj_kg_category_info_tb \
                                where category_level = 1 \
                                order by category_code desc limit 0,1 \
                                """
                    cursor.execute(queryCate)
                    category = cursor.fetchone()
                    if category is None:
                        categoryCode = "1000"
                    else:
                        categoryCode = str(int(category[0]) + 1)

                    insertCategory = """
                                     INSERT INTO xj_kg_category_info_tb
                                     (category_id, kg_id, category_code, category_name, category_type, category_level, \
                                      parent_category_id, create_user_id, is_delete)
                                     VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) \
                                     """
                    cursor.execute(insertCategory, (categoryId, kgId, categoryCode, categoryName, "1", 1, 0, userId, 0))
                    self.conn.commit()

                    res['kgCode'] = kgCode
                    res['categoryCode'] = categoryCode
                else:
                    # 存在返回kgCode
                    kgId = result[0]
                    res['kgCode'] = result[1]
                    # 如果存在，根据空间查询类目是否存在
                    # 查询 知识空间下是否存在类目
                    queryCategorySql = "SELECT category_id,category_code,category_name,kg_id FROM  xj_kg_category_info_tb where category_name =  %s and kg_id =  %s"
                    cursor.execute(queryCategorySql, (categoryName, kgId))
                    categoryRes = cursor.fetchone()
                    if categoryRes is None:
                        categoryId = snowflake.generate_id()
                        queryCate = """
                                    select category_code
                                    from xj_kg_category_info_tb \
                                    where category_level = 1 \
                                    order by category_code desc limit 0,1 \
                                    """
                        cursor.execute(queryCate)
                        category = cursor.fetchone()
                        if category is None:
                            categoryCode = "1000"
                        else:
                            categoryCode = str(int(category[0]) + 1)

                        insertCategory = """
                                         INSERT INTO xj_kg_category_info_tb
                                         (category_id, kg_id, category_code, category_name, category_type, \
                                          category_level,
                                          parent_category_id, create_user_id, is_delete)
                                         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) \
                                         """
                        cursor.execute(insertCategory,
                                       (categoryId, kgId, categoryCode, categoryName, "1", 1, 0, userId, 0))
                        self.conn.commit()
                        res['categoryCode'] = categoryCode
                    else:
                        res['categoryCode'] = categoryRes[1]

            return res
        except Exception as e:
            logger.error(f"get_know_file_sql 异常:{e}")
            return None

    def get_know_file_sql(self, categoryCode: str, userId: str, fileList: list) -> None:
        """
            query = "SELECT category_level FROM xj_kg_category_info_tb WHERE category_code = %s"
        self.cursor.execute(query, (category_code,))
        """
        logger.info(
            f"get_know_file_sql 参数 categoryCode:{categoryCode},userId:{userId},fileList:{fileList}")
        res = []
        try:

            for file in fileList:
                fileId = snowflake.generate_id()
                fileName = file.get("fileName")
                fileSize = file.get("fileSize")
                filePath = file.get("filePath")
                _, ext = os.path.splitext(fileName)
                fileFormat = ext[1:].lower()

                insertFile = """INSERT INTO xj_kg_file_info_tb(file_id, category_code, file_name, file_size, file_status, file_format, file_path, import_type,is_enable, create_user_id, is_delete, is_auto_process) \
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""

                sql = insertFile % (fileId, categoryCode, f"'{fileName}'", max(1, fileSize // 1024), 0,
                                    f"'{fileFormat}'", f"'{filePath}'", 0, 1, userId, 0, 0)
                res.append(sql)
            return res
        except Exception as e:
            logger.error(f"get_know_file_sql异常:{e}")
            return res

    def exec_know_file_sql(self, host: str, port: str, user: str, password: str, database: str, fileList: list) -> None:
        """
            执行sql
        """
        logger.info(
            f"exec_know_file_sql 参数 host:{host},port:{port},user:{user},password:{password},database:{database},sqlList:{fileList}")
        conn = None
        cursor = None
        try:
            mysql = MySQLClient(host, port, user, password, database)
            conn = mysql.get_connect()
            # 创建游标对象，用于执行SQL查询
            cursor = conn.cursor(buffered=True)
            for sql in fileList:
                try:
                    cursor.execute(sql)
                    conn.commit()
                    logger.info(f"执行sql成功:{sql}")
                except Exception as e:
                    logger.info(f"执行sql失败:{sql},异常:{e}")
            return True
        except Exception as e:
            logger.info(f"连接数据库异常:{e}")
            return False
        finally:
            cursor.close()
            conn.close()

    def get_know_file(self) -> None:
        """
            query = "SELECT category_level FROM xj_kg_category_info_tb WHERE category_code = %s"
        self.cursor.execute(query, (category_code,))
        """
        res = []
        try:
            with self._get_cursor() as cursor:
                queryFileList = """
                                SELECT kfi.file_id, \
                                       kfi.category_code, \
                                       kfi.file_name, \
                                       kfi.file_path, \
                                       kci.kg_id
                                FROM xj_kg_file_info_tb kfi \
                                         LEFT JOIN xj_kg_category_info_tb kci ON kfi.category_code = kci.category_code
                                WHERE kfi.is_auto_process = 0
                                  and kfi.file_status = 0 \
                                """
                cursor.execute(queryFileList)
                # 获取知识空间信息
                result = cursor.fetchall()
                for row in result:
                    data = {
                        "fileId": row[0],
                        "categoryCode": row[1],
                        "fileName": row[2],
                        "filePath": row[3],
                        "kgId": row[4]
                    }
                    res.append(data)
            return res
        except Exception as e:
            logger.error(f"get_know_file异常:{e}")
            return res

    def generate_code(self, prefix: str):
        # 生成随机部分（8位小写字母+数字）
        random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return prefix + random_part



    def img_rec(self,results:list):
        """
                   文档-图片文字识别
                   入参：
                   - results: list - 版式识别结果
                """
        try:
            # 获取并验证结果数据
            if not results:
                raise ValueError("结果列表不能为空")

            # 提取所有类型为 figure 的完整信息
            figures = [
                {
                    "type": item["type"],
                    "bbox": item["bbox"],
                    "score": item["score"],
                    "page": item["page"],
                    "roi_image_url": item["roi_image_url"]
                }
                for item in results
                if item.get("type") == "figure"
            ]

            if not figures:
                return return_dict(
                    str(ReturnCode.SUCESS.value),
                    {"message": "未找到figure类型的图片"},
                    "success"
                )

            url = FILE_IMAGEREC_URL
            headers = {
                "Content-Type": "application/json"
            }

            # 处理所有figure图片
            for figure in figures:
                payload = {
                    "url": figure["roi_image_url"],
                    "bucket": MINOBUCKET
                }

                logger.info(f"file img_rec request data {json.dumps(payload)}")
                response = requests.post(url, headers=headers, json=payload)

                if response.status_code != 200:
                    logger.error(f"图片识别请求失败: {figure['roi_image_url']}")
                    continue

                response_json = response.json()

                # 如果识别成功，提取所有文本内容
                if response_json.get("success") and response_json.get("results"):
                    texts = [
                        result["text"]
                        for result in response_json["results"]
                    ]
                    # 将提取的文本添加到figure信息中
                    figure["texts"] = texts

            return return_dict(
                str(ReturnCode.SUCESS.value),
                figures,
                "success"
            )

        except Exception as e:
            logger.error(f"请求/Tool-alg/img_rec接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(traceback.format_exc()), "error")

    def table_rec(self,results: list):
        """
           文档-表格/表头识别
           入参：
           - results: list - 版式识别结果
        """
        try:
            # 获取并验证结果数据
            if not results:
                raise ValueError("结果列表不能为空")

            # 提取所有类型为 table 的完整信息
            tables = [
                {
                    "type": item["type"],
                    "bbox": item["bbox"],
                    "score": item["score"],
                    "page": item["page"],
                    "roi_image_url": item["roi_image_url"]
                }
                for item in results
                if item.get("type") == "table"
            ]

            if not tables:
                return return_dict(
                    str(ReturnCode.SUCESS.value),
                    {"message": "未找到table类型的图片"},
                    "success"
                )

            url = FILE_TABLEREC_URL
            headers = {
                "Content-Type": "application/json"
            }

            # 处理所有table图片
            for table in tables:
                payload = {
                    "url": table["roi_image_url"],
                    "bucket": MINOBUCKET
                }

                logger.info(f"file table_rec request data {json.dumps(payload)}")
                response = requests.post(url, headers=headers, json=payload)

                if response.status_code != 200:
                    logger.error(f"表格识别请求失败: {table['roi_image_url']}")
                    continue

                response_json = response.json()

                # 只提取HTML内容
                if response_json.get("success") and response_json.get("results"):
                    html_contents = []
                    for result in response_json["results"]:
                        if "html" in result:
                            html_contents.append(result["html"].get("html", ""))
                    table["html"] = html_contents

            return return_dict(
                str(ReturnCode.SUCESS.value),
                tables,
                "success"
            )
        except Exception as e:
            logger.error(f"请求/Tool-alg/table_rec接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(traceback.format_exc()), "error")

# MCP 工具注册
def register(mcp):
    """
    将 KnowService 的方法注册为 MCP 工具
    """

    @mcp.tool()
    def know_sftp_tool(host:str,  username:str, password:str, remoteDir:str):
        """
            SFTP连接插件
            入参：
            - host: str - 服务器地址
            - username: str - 用户名
            - password: str - 密码
            - remoteDir: str - 远程目录地址
        """
        try:
            # 配置SFTP连接信息
            sftp_config = {
                "host": host,
                "port": 22,
                "username": username,
                "password": password
            }

            # 创建SFTP客户端
            client = SFTPClient(**sftp_config)

            if client.connect():
                try:
                    # 下载整个目录
                    res = client.download_directory_to_minio(remote_dir=remoteDir)
                    return return_dict(str(ReturnCode.SUCESS.value), res, "success")
                except Exception as e:
                    logger.error(f"SFTP操作失败: {str(e)}")
                    return return_dict(str(ReturnCode.ERROR.value), str(e), "error")
                finally:
                    client.disconnect()
        except Exception as e:
            logger.error(f"请求/sftp_tool接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(e), "error")

    @mcp.tool()
    def know_space_info(kgName:str,categoryName:str,userId:str):
        """
            获取知识知识库信息
            入参：
            - kgName: str - 知识空间名称
            - categoryName: str - 知识类目名称
            - userId: str - 用户ID
        """
        try:
            know = KnowService()
            res = know.get_know_info(kgName, categoryName, userId)

            # 返回结果 {"kgCode":"kg_3p2qqif1","categoryCode":"1302"}
            return return_dict(str(ReturnCode.SUCESS.value), res, "success")
        except Exception as e:
            logger.error(f"请求/get_know_info接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(e), "error")

    @mcp.tool()
    def know_file_sql_get(knowInfo:dict,fileList:list,userId:str):
        """
           获取知识知识库信息
           入参：
           - knowInfo: dict - 知识空间信息
           - fileList: list - 文件列表
           - userId: str - 用户IDs
        """
        try:
            categoryCode = knowInfo.get("categoryCode")
            know = KnowService()
            res = know.get_know_file_sql(categoryCode, userId, fileList)
            return return_dict(str(ReturnCode.SUCESS.value), res, "success")
        except Exception as e:
            logger.error(f"请求/import_know_file接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(e), "error")

    @mcp.tool()
    def know_file_sql_exec(host:str,port:str,user:str,password:str,database:str,sqlList:list):
        """
           执行SQL语句
           入参：
           - host: str - 服务器地址
           - port: str - 端口
           - user: str - 用户名
           - password: str - 密码
           - database: str - 数据库
           - sqlList: str - sql列表

       """
        try:
            know = KnowService()
            res = know.exec_know_file_sql(host, port, user, password, database, sqlList)
            return return_dict(str(ReturnCode.SUCESS.value), res, "success")
        except Exception as e:
            logger.error(f"请求/get_know_file接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(e), "error")

    @mcp.tool()
    def know_pending_file():
        """
           获取知识库中待处理的文件列表
        """
        try:
            know = KnowService()
            res = know.get_know_file()
            return return_dict(str(ReturnCode.SUCESS.value), res, "success")
        except Exception as e:
            logger.error(f"请求/get_know_file接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(e), "error")

    @mcp.tool()
    def know_file_parse(filesList:list,isDeepProcess:int,isFaqExtract:int,userId:str):
        """
           知识文件-结构化处理
           入参：
           - filesList: list - 文件列表
           - isDeepProcess: int - 是否深度处理，0：不处理，1：处理
           - isFaqExtract: int 是否抽取FAQ，0：不处理，1：处理
           - userId: str - 用户id
        """
        try:
            files_data = filesList or []
            fileIds = [str(file["fileId"]) for file in files_data]
            logger.info(f"fileIds {str(fileIds)}")
            kgIds = [file["kgId"] for file in files_data]
            categoryCodes = [file["categoryCode"] for file in files_data]
            userId = userId or 1
            isDeepProcess = isDeepProcess or 0
            isFaqExtract = isFaqExtract or 0
            know = KnowService()
            know.insert_kg_vector_slice_conf(fileIds, kgIds, categoryCodes, userId, isDeepProcess, isFaqExtract)

            url = FILE_FORMAR_URL
            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, json={"filesId": fileIds}, headers=headers)
            response_json = response.json()
            if response_json.get("retCode") != '0':
                raise Exception(f"请求失败，返回信息：{response_json.get('retMsg')}")
            return return_dict(str(ReturnCode.SUCESS.value), fileIds, "success")
        except Exception as e:
            logger.error(f"请求/Tool-alg/file_parse接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(traceback.format_exc()), "error")

    @mcp.tool()
    def know_file_split(filesId:list,isAbstractExtract:int,isMetaExtract:int,sliceName:str,algoCode:str,slice_param:dict,userId:str):
        """
           知识文件-文件切片
           入参：
           - filesId: list - 文件ID列表
           - isAbstractExtract: int - 是否抽取摘要，0：不处理，1：处理s
           - isMetaExtract: int 是否抽取元数据，0：不处理，1：处理
           - sliceName: str 切片算法
           - algoCode: str 切片算法代码
           - slice_param: str 切片算法
           - userId: str - 用户id
        """
        try:
            filesId = filesId or []
            logger.info(f"file_split是{filesId}")
            isAbstractExtract = isAbstractExtract or 0
            isMetaExtract = isMetaExtract or 0
            sliceName = sliceName or "fixedlength_spliter"
            algoCode = algoCode or "fixedlength_spliter"
            slice_param = str(slice_param) or '{"spliter_size":500}'
            userId = userId or 1
            know = KnowService()
            for fileId in filesId:
                know.update_kg_file_status(fileId, 5)
                slice_conf_id = know.insert_kg_slice_conf(userId, sliceName, algoCode, slice_param)
                know.update_kg_vector_slice_conf(fileId, isAbstractExtract, slice_conf_id, isMetaExtract)
            url = FILE_SPLIT_URL
            headers = {
                "Content-Type": "application/json"
            }
            response = requests.post(url, json={"filesId": filesId}, headers=headers)
            response_json = response.json()
            if response_json.get("retCode") != '0':
                raise Exception(f"请求失败，返回信息：{response_json.get('retMsg')}")
            for fileId in filesId:
                know.update_kg_file_info_enable(fileId, 0)
            return return_dict(str(ReturnCode.SUCESS.value), filesId, "success")
        except Exception as e:
            logger.error(f"请求/Tool-alg/file_split接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(traceback.format_exc()), "error")

    @mcp.tool()
    def file_layout_handle(url:list):
        """
           获取文档版式信息
           入参：
           - url: list - 文件列表
        """
        try:
            file_path = url
            url = FILE_LAYOUT_URL
            headers = {
                "Content-Type": "application/json"
            }

            payload = json.dumps({
                "url": file_path,
                "bucket": MINOBUCKET
            })
            logger.info(f"file layout request data {str(payload)}")
            response = requests.request("POST", url, headers=headers, data=payload)
            response_json = response.json()
            return response_json
        except Exception as e:
            logger.error(f"请求/Tool-alg/layout接口出错{str(traceback.format_exc())}")
            return return_dict(str(ReturnCode.ERROR.value), str(traceback.format_exc()), "error")


    @mcp.tool()
    def file_rec_handle(results:list,type:str):
        """
           文档版式分析
           入参：
           - result:list -版式结果
           - type: str -类型
        """
        know = KnowService()
        if "img_rec" == type:
            return know.img_rec(results)
        if "table_rec" == type:
            return know.table_rec(results)

        return return_dict(str(ReturnCode.ERROR.value), "未知的type", "error")
