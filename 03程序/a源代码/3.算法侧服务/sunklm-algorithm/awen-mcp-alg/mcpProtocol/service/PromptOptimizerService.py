import re
import traceback
from typing import Optional, Dict

import aiohttp
import asyncio
import json
from common.utils.LogUtils import logger
from common.utils.ResultUtis import ReturnCode, return_dict
from common.constants.PromptConstants import PromptConstants
from common.conf.ServerConfig import MODEL_URL, OPTIMIZE_PATH
from common.utils.JsonUtils import validate_and_optimize_json


def register(mcp):
    """
    将提示词优化服务注册为MCP工具
    """

    @mcp.tool()
    def optimize_prompt_tool(
            prompt: str,
            optimization_type: int = 1,
            feedback: str = "",
            expectation: str = "",
            requirements: str = ""
    ):
        """
        MCP工具封装：提示词优化服务
        参数:
            prompt: 原始提示词
            optimization_type: 优化类型(1-自动优化/2-反馈优化/3-自定义优化)
            feedback: 用户反馈（用于反馈优化）
            expectation: 期望改进方向（用于反馈优化）
            requirements: 自定义优化需求
        """
        try:
            optimizer = PromptOptimizer()
            result = asyncio.run(optimizer.optimize_prompt(
                prompt=prompt,
                feedback=feedback,
                expectation=expectation,
                requirements=requirements,
                optimization_type=optimization_type
            ))
            return return_dict(
                data=result,
                code=str(ReturnCode.SUCESS.value),
                message="success"
            )
        except Exception as e:
            logger.error(f"[PromptOptimizeTool] 异常: {e}")
            return return_dict(
                data={},
                code=str(ReturnCode.FAIL.value),
                message=f"提示词优化失败: {str(e)}"
            )


class PromptOptimizer:
    """
    提示词优化服务核心类
    """

    def __init__(self):
        self.prompt_templates = self._load_prompt_templates()
        self.config = self._load_config(OPTIMIZE_PATH)

    def _load_config(self, config_path: str) -> dict:
        """加载优化配置"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                file_content = f.read()
            return validate_and_optimize_json(file_content)
        except FileNotFoundError:
            logger.error("配置文件未找到")
            raise RuntimeError("配置文件未找到")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise RuntimeError(f"加载配置文件失败: {e}")

    def _load_prompt_templates(self) -> dict:
        """加载提示词模板"""
        return {
            key: value for key, value in PromptConstants.__dict__.items()
            if not key.startswith('__') and isinstance(value, str) and key.startswith("PROMPT_")
        }

    def calculate_similarity(self, prompt: str, template: str) -> float:
        """计算提示词与模板的相似度"""
        if not prompt or not template:
            return 0.0
        prompt_words = set(prompt.strip().split())
        template_words = set(template.strip().split())
        common = prompt_words & template_words
        union = prompt_words | template_words
        return len(common) / (len(union) + 1e-6)

    def match_best_template(self, prompt: str) -> dict:
        """匹配最佳模板"""
        best_match = None
        highest_score = 0.0

        for name, template in self.prompt_templates.items():
            score = self.calculate_similarity(prompt, template)
            if score > highest_score:
                highest_score = score
                best_match = {
                    "template_name": name,
                    "template_content": template,
                    "similarity_score": round(score, 4)
                }

        return best_match or {
            "template_name": "DEFAULT_TEMPLATE",
            "template_content": PromptConstants.PROMPT_OPTIMIZER_TEMPLATE,
            "similarity_score": 0.0
        }

    async def _call_model(self, payload: dict) -> str:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                        MODEL_URL,
                        headers=self.config["model_config"]["headers"],
                        json=payload,
                        timeout=120
                ) as response:
                    if response.status != 200:
                        raise Exception(f"HTTP错误 {response.status}")

                    response_text = await response.text()
                    if not response_text:
                        raise ValueError("模型返回了空响应内容")

                    # 增加响应内容校验
                    try:
                        response_data = json.loads(response_text)
                        if not isinstance(response_data.get("retMap"), dict):
                            raise ValueError("模型返回格式异常: retMap缺失或非字典类型")
                    except json.JSONDecodeError:
                        raise ValueError("模型返回非JSON格式")

                    return response_text
        except Exception as e:
            logger.error(f"模型调用失败: {str(traceback.format_exc())}")
            raise

    def _process_model_response(self, response: str) -> dict:
        """处理模型响应"""
        try:
            data = json.loads(response)
            if data.get("retCode") != "0":
                raise Exception(data.get("retMsg", "模型返回错误"))
            return data.get("retMap", {})
        except json.JSONDecodeError:
            raise Exception("模型返回非JSON格式")

    def optimize_prompt(
            self,
            prompt: str,
            optimization_type: int = 1,
            feedback: str = "",
            expectation: str = "",
            requirements: str = "",
            templates: Optional[Dict[str, str]] = None
    ) -> Dict:
        """
        核心优化方法（单次调用同时获取优化结果和建议问题）
        Args:
            prompt: 原始提示词
            optimization_type: 优化类型(1-自动优化/2-反馈优化/3-自定义优化)
            feedback: 用户反馈
            expectation: 期望改进方向
            requirements: 自定义优化需求
            templates: 可选模板字典
        Returns:
            包含优化结果和建议问题的标准格式字典
        """
        try:
            # 1. 匹配模板（如果有提供）
            template_info = {}
            if templates:
                template_info = self.match_best_template(prompt, templates)

            # 2. 构造带问题生成要求的查询内容
            query_map = {
                1: f"""优化以下提示词并生成3个相关建议问题:
                    原始提示词:{prompt}
                    要求:
                    1. 输出优化后的提示词
                    2. 生成3个引导用户深入思考的问题
                    格式要求:
                    <optimized_prompt>
                    优化结果
                    </optimized_prompt>
                    <suggested_questions>
                    1. 问题1
                    2. 问题2
                    3. 问题3
                    </suggested_questions>""",
                2: f"""根据用户反馈优化提示词并生成建议问题:
                    原始提示词:{prompt}
                    反馈:{feedback}
                    期望:{expectation}
                    要求同上""",
                3: f"""根据自定义需求优化提示词并生成建议问题:
                    原始提示词:{prompt}
                    需求:{requirements}
                    要求同上"""
            }
            query = query_map.get(optimization_type, query_map[1])

            if template_info.get("template_content"):
                query = f"参考模板进行优化:\n模板:{template_info['template_content']}\n{query}"

            # 3. 调用模型（单次调用）
            model_response = self._call_model(
                payload={
                    "query": query,
                    "temperature": 0.7,
                    "max_tokens": 1024
                }
            )

            # 4. 解析响应内容
            if not model_response:
                raise ValueError("模型返回了空响应")

                # 解析响应内容（添加更严格的验证）
            optimized_prompt = self._extract_between_tags(model_response, "optimized_prompt")
            questions_text = self._extract_between_tags(model_response, "suggested_questions")

            if not optimized_prompt:  # 如果提取失败，使用原始提示词
                optimized_prompt = prompt
                logger.warning("未能从模型响应中提取优化后的提示词，使用原始提示词作为默认值")

            # 处理建议问题
            suggested_questions = [
                                      q.strip()
                                      for q in (questions_text or "").split('\n')
                                      if q.strip()
                                  ][:3]  # 确保最多3个问题

            if not suggested_questions:  # 默认问题
                suggested_questions = [
                    "如何进一步完善这个提示词的具体技术细节？",
                    "是否需要添加更多实例说明？",
                    "这篇文章的目标读者是谁？需要调整技术深度吗？"
                ]

            return {
                "retCode": "0",
                "retMsg": "success",
                "retMap": {
                    "answer": optimized_prompt,
                    "suggested_questions": suggested_questions
                }
            }


        except Exception as e:

            logger.error(f"提示词优化失败: {str(traceback.format_exc())}")

            return {
                "retCode": "1",
                "retMsg": f"提示词优化失败: {str(e)}",
                "retMap": {
                    "answer": prompt,  # 保留原始提示词
                    "suggested_questions": [
                        "如何手动改进这个提示词？",
                        "需要添加哪些具体的技术细节？",
                        "结果长度要求如何满足？"
                    ]
                }
            }

    def _extract_between_tags(self, text: str, tag_name: str) -> str:
        """从文本中提取指定标签间的内容"""
        pattern = re.compile(f"<{tag_name}>(.*?)</{tag_name}>", re.DOTALL)
        match = pattern.search(text)
        return match.group(1).strip() if match else ""

    def _build_query(
            self,
            template: str,
            prompt: str,
            feedback: str,
            expectation: str,
            requirements: str,
            optimization_type: int
    ) -> str:
        """构建查询内容"""
        query_map = {
            1: template.format(prompt=prompt),
            2: f"{template}\n用户反馈:{feedback}\n期望:{expectation}",
            3: f"{template}\n自定义要求:{requirements}"
        }
        return query_map.get(optimization_type, query_map[1])

    async def _call_optimization_service(self, *args):
        """带重试机制的模型调用"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = await self._call_model(*args)
                return self._validate_response(response)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(1)

    def _validate_response(self, response: str) -> dict:
        """响应验证"""
        try:
            data = json.loads(response)
            if not isinstance(data.get("retMap"), dict):
                data["retMap"] = {
                    "answer": "",
                    "suggested_questions": []
                }
            return data
        except json.JSONDecodeError:
            return {
                "retCode": "1",
                "retMsg": "Invalid response format",
                "retMap": None
            }

    def _ensure_questions(self, text: str) -> list:
        """确保始终有3个建议问题"""
        questions = self._extract_questions(text) or [
            "如何改进这个提示词的专业性？",
            "需要补充哪些技术细节？",
            "是否需要调整内容结构？"
        ]
        return questions[:3]  # 确保不超过3个


