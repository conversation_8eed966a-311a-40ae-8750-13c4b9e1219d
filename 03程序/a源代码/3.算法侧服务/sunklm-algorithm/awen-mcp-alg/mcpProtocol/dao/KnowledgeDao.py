from common.utils.DbUtils import POOL
from common.utils.LogUtils import logger

class KnowledgeDao:

    # 获取切片配置
    def get_kg_vector_slice_conf(self,  fileId):
        """
        获取可用工具列表。
        """
        logger.info(f"【Dao】【获取文件的切片配置ID】")
        try:
            # 数据库连接
            conn = POOL.connection()
            with conn.cursor() as cursor:
                checkSql = """
                           SELECT conf_id \
                           FROM xj_kg_vector_slice_conf_tb
                           WHERE file_id = %s \
                           """
                cursor.execute(checkSql, (fileId,))
                exists = cursor.fetchone()
                return exists
        except Exception as e:
            logger.error(f"【Dao】【获取文件的切片配置ID】失败：{e}")
            return False

    # 更新切片配置
    def update_kg_vector_slice_conf(self,  kgId,categoryCode,userId,isDeepProcess,isFaqExtract,fileId):
        """
        获取可用工具列表。
        """
        logger.info(f"【Dao】【获取文件的切片配置ID】")
        try:
            # 数据库连接
            conn = POOL.connection()
            with conn.cursor() as cursor:
                updateSql = """
                            UPDATE xj_kg_vector_slice_conf_tb
                            SET kg_id               = %s, \
                                slice_conf_id       = %s, \
                                vector_id           = %s, \
                                category_code       = %s,
                                create_user_id      = %s, \
                                is_deep_process     = %s, \
                                is_faq_extract      = %s,
                                is_abstract_extract = %s, \
                                is_meta_extract     = %s
                            WHERE file_id = %s \
                            """
                cursor.execute(updateSql, (
                    kgId,
                    1,  # slice_conf_id
                    2,  # default vector_id
                    categoryCode,
                    userId,
                    isDeepProcess,
                    isFaqExtract,
                    0,  # default is_abstract_extract
                    0,  # default is_meta_extract
                    fileId
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"【Dao】【获取文件的切片配置ID】失败：{e}")
            return False

    # 新增切片配置
    # def insert_kg_vector_slice_conf(self, kgId, categoryCode, userId, isDeepProcess, isFaqExtract, fileId):
    #     """
    #     获取可用工具列表。
    #     """
    #     logger.info(f"【Dao】【获取文件的切片配置ID】")
    #     try:
    #         # 数据库连接
    #         conn = POOL.connection()
    #         with conn.cursor() as cursor:
    #             insertSql = """
    #                         INSERT INTO xj_kg_vector_slice_conf_tb
    #                         (conf_id, kg_id, file_id, slice_conf_id, vector_id, category_code, create_user_id,
    #                          is_deep_process, is_faq_extract, is_abstract_extract, is_meta_extract)
    #                         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) \
    #                         """
    #             conf_id = snowflake.generate_id()
    #             cursor.execute(insertSql, (
    #                 conf_id,
    #                 kgIds[i],
    #                 fileIds[i],
    #                 1,  # slice_conf_id
    #                 2,  # default vector_id
    #                 categoryCodes[i],
    #                 userId,
    #                 isDeepProcess,
    #                 isFaqExtract,
    #                 0,  # default is_abstract_extract
    #                 0  # default is_meta_extract
    #             ))
    #             return True
    #     except Exception as e:
    #         logger.error(f"【Dao】【获取文件的切片配置ID】失败：{e}")
    #         return False