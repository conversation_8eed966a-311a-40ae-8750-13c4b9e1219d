/home/<USER>/envs/mcp/lib/python3.11/site-packages/PyPDF2/__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.
  warnings.warn(
2025-07-11 02:08:01,209 - app - INFO - Tool InternalService registered successfully. - [system]
2025-07-11 02:08:01,277 - app - ERROR - Failed to load tool PromptOptimizerService: cannot import name 'OPTIMIZE_PATH' from 'common.conf.ServerConfig' (/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-mcp-alg/common/conf/ServerConfig.py) - [system]
2025-07-11 02:08:01,285 - app - INFO - Tool QccServer registered successfully. - [system]
2025-07-11 02:08:01,557 - app - INFO - Tool ExternalService registered successfully. - [system]
2025-07-11 02:08:01,579 - mysql.connector - INFO - package: mysql.connector.plugins - [system]
2025-07-11 02:08:01,580 - mysql.connector - INFO - plugin_name: caching_sha2_password - [system]
2025-07-11 02:08:01,580 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin - [system]
2025-07-11 02:08:01,607 - app - ERROR - Failed to load tool KnowService: 1045 (28000): Access denied for user 'SUNKLM'@'***********' (using password: YES) - [system]
2025-07-11 02:08:01,624 - app - INFO - Tool DataCleaningServer registered successfully. - [system]
INFO:     Started server process [3611144]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 98] error while attempting to bind on address ('0.0.0.0', 9082): address already in use
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
