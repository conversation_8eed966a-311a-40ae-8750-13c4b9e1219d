2025-07-11 02:08:01,209 - app - INFO - Tool InternalService registered successfully. - [system]
2025-07-11 02:08:01,277 - app - ERROR - Failed to load tool PromptOptimizerService: cannot import name 'OPTIMIZE_PATH' from 'common.conf.ServerConfig' (/home/<USER>/dev_flow_kxj,lhk/03程序/a源代码/3.算法侧服务/sunklm-algorithm/awen-mcp-alg/common/conf/ServerConfig.py) - [system]
2025-07-11 02:08:01,285 - app - INFO - Tool QccServer registered successfully. - [system]
2025-07-11 02:08:01,557 - app - INFO - Tool ExternalService registered successfully. - [system]
2025-07-11 02:08:01,579 - mysql.connector - INFO - package: mysql.connector.plugins - [system]
2025-07-11 02:08:01,580 - mysql.connector - INFO - plugin_name: caching_sha2_password - [system]
2025-07-11 02:08:01,580 - mysql.connector - INFO - AUTHENTICATION_PLUGIN_CLASS: MySQLCachingSHA2PasswordAuthPlugin - [system]
2025-07-11 02:08:01,607 - app - ERROR - Failed to load tool KnowService: 1045 (28000): Access denied for user 'SUNKLM'@'172.1.0.154' (using password: YES) - [system]
2025-07-11 02:08:01,624 - app - INFO - Tool DataCleaningServer registered successfully. - [system]
