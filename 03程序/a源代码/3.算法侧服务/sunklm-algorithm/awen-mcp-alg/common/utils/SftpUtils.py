import paramiko
import os
import stat
from minio import Minio
from pathlib import Path
from common.conf.ServerConfig import ENDPOIN<PERSON>,ACCESS_KEY,SECRET_KEY,MINOBUCKET,SFTP_UPLOAD_PATH
from common.utils.LogUtils import logger
from common.utils.ResultUtis import return_dict


class SFTPClient:
    def __init__(self, host, port, username, password=None, key_path=None):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.key_path = key_path
        self.ssh_client = None
        self.sftp = None

    def connect_minio(self):
        """
        创建Minio客户端连接
        """
        return Minio(
            endpoint=ENDPOINT,
            access_key=ACCESS_KEY,
            secret_key=SECRET_KEY,
            secure=False
        )

    def connect(self):
        """连接到SFTP服务器"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            if self.key_path:
                private_key = paramiko.RSAKey.from_private_key_file(self.key_path)
                self.ssh_client.connect(
                    hostname=self.host,
                    port=self.port,
                    username=self.username,
                    pkey=private_key
                )
            else:
                self.ssh_client.connect(
                    hostname=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password
                )

            self.sftp = self.ssh_client.open_sftp()
            logger.info(f"SFTP连接成功,host:{self.host},username:{self.username},password:{self.password}")
            return True
        except Exception as e:
            logger.error(f"连接SFTP服务器失败,host:{self.host},username:{self.username},password:{self.password}")
            return False

    def disconnect(self):
        """断开连接"""
        if self.sftp:
            self.sftp.close()
        if self.ssh_client:
            self.ssh_client.close()
        logger.info(f"SFTP连接关闭,host:{self.host},username:{self.username},password:{self.password}")

    def download_file(self, remote_path, local_path, show_progress=False):
        """下载单个文件"""
        try:
            if show_progress:
                self._download_with_progress(remote_path, local_path)
            else:
                self.sftp.get(remote_path, local_path)
            print(f"文件下载成功: {remote_path} -> {local_path}")
            return True
        except Exception as e:
            print(f"下载文件时出错: {str(e)}")
            return False

    def download_directory(self, remote_dir, local_dir):
        """下载整个目录"""
        try:
            os.makedirs(local_dir, exist_ok=True)

            for item in self.sftp.listdir(remote_dir):
                remote_path = os.path.join(remote_dir, item)
                local_path = os.path.join(local_dir, item)

                try:
                    if stat.S_ISDIR(self.sftp.stat(remote_path).st_mode):
                        self.download_directory(remote_path, local_path)
                    else:
                        self.sftp.get(remote_path, local_path)
                        print(f"下载文件: {remote_path} -> {local_path}")
                except Exception as e:
                    print(f"下载 {remote_path} 时出错: {str(e)}")

            return True
        except Exception as e:
            print(f"下载目录时出错: {str(e)}")
            return False

    def download_directory_to_minio(self, remote_dir):
        """下载整个目录"""
        fileList = []
        try:
            # 创建minio连接
            minioClient = self.connect_minio()
            for item in self.sftp.listdir(remote_dir):
                remote_path = os.path.join(remote_dir, item)
                try:
                    # 使用SFTP的文件对象直接上传到MinIO
                    with self.sftp.open(remote_path, 'rb') as file_obj:
                        file_stat = self.sftp.stat(remote_path)
                        file_size = file_stat.st_size
                        file_name = Path(remote_path).name
                        minio_object_path = SFTP_UPLOAD_PATH + file_name
                        logger.info(f"文件 file_name:{file_name},remote_path:{remote_path}")
                        # 检查MinIO中是否已存在该文件
                        try:
                            minioClient.stat_object(
                                bucket_name=MINOBUCKET,
                                object_name=minio_object_path
                            )
                            # 如果文件已存在，跳过上传
                            logger.info(f"文件已存在于MinIO，跳过上传: {minio_object_path}")
                            continue
                        except Exception as e:
                            # 文件不存在，继续上传流程
                            minioClient.put_object(
                                bucket_name=MINOBUCKET,
                                object_name=minio_object_path,
                                data=file_obj,
                                length=file_size
                            )
                            file = {
                                "fileName": file_name,
                                "fileSize": file_size,
                                "filePath": minio_object_path
                            }
                            fileList.append(file)
                            logger.info(f"上传文件到MinIO: {remote_path} -> {minio_object_path}")

                except Exception as e:
                    logger.error(f"下载 {remote_path} 时出错: {str(e)}")

            return fileList
        except Exception as e:
            logger.error(f"下载目录时出错: {str(e)}")
            return False

    def _download_with_progress(self, remote_path, local_path):
        """带进度显示的文件下载"""
        file_size = self.sftp.stat(remote_path).st_size
        progress = 0

        def print_progress(transferred, to_be_transferred):
            nonlocal progress
            new_progress = int(transferred / file_size * 100)
            if new_progress != progress:
                progress = new_progress
                print(f"下载进度: {progress}%", end='\r')

        self.sftp.get(remote_path, local_path, callback=print_progress)
        print("\n下载完成")


# 使用示例
if __name__ == "__main__":
    # 配置SFTP连接信息
    sftp_config = {
        "host": "***********",
        "port": 22,
        "username": "root",
        "password": "Xydxnj240422#",  # 或使用密钥认证
        # "key_path": "/path/to/private/key"
    }

    # 创建SFTP客户端
    client = SFTPClient(**sftp_config)

    if client.connect():
        try:
            # 下载单个文件
            # client.download_file(
            #     remote_path="/data/volumes/minio/data/sunklm-dev/front-api-file/bin.wang/0b1c8656-87c5-483f-b344-db8b48c6db58.mp3",
            #     local_path="/Users/<USER>/Downloads/0b1c8656-87c5-483f-b344-db8b48c6db58.mp3",
            #     show_progress=True
            # )

            # 下载整个目录
            res = client.download_directory_to_minio(
                remote_dir="/data/volumes/minio/data/sunklm-dev/front-api-file/bin.wang"
            )
            print(res)
        finally:
            client.disconnect()
