# logger.py
import logging
from logging.handlers import RotatingFileHandler
import os
from pathlib import Path
import sys
from datetime import datetime
from common.conf.ServerConfig import ServerConfig

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class RequestIdFilter(logging.Filter):
    """添加request_id到日志记录"""

    def filter(self, record):
        record.request_id = getattr(record, 'request_id', 'system')
        return True


def setup_logging():
    """配置全局日志"""
    # 创建日志目录
    log_dir = BASE_DIR / "logs"
    os.makedirs(log_dir, exist_ok=True)

    # 主日志文件路径（添加日期）
    current_date = datetime.now().strftime("%Y-%m-%d")
    log_file = log_dir / f"{ServerConfig.SERVICE_NAME}_{current_date}.log"

    # 创建格式化器
    text_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s - [%(request_id)s]'
    )

    # 创建处理器 - 控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(text_formatter)

    # 创建处理器 - 文本文件 (增大到20MB)
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=1024 * 1024 * 20,  # 20MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(text_formatter)

    # 添加过滤器
    request_filter = RequestIdFilter()
    console_handler.addFilter(request_filter)
    file_handler.addFilter(request_filter)

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # 禁用uvicorn访问日志（避免重复日志）
    logging.getLogger("uvicorn.access").handlers.clear()


# 初始化日志配置
setup_logging()

# 创建全局日志实例
logger = logging.getLogger("app")


def get_logger(name=None):
    """获取日志记录器"""
    return logger.getChild(name) if name else logger