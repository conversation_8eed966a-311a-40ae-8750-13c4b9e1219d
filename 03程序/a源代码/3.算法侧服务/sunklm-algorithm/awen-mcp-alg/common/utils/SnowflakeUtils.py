import time

class SnowflakeUtils:
    def __init__(self, machine_id, epoch=None):
        """
        初始化雪花ID生成器

        :param machine_id: 机器ID (0-1023)
        :param epoch: 自定义纪元时间戳 (默认为2020-01-01)
        """
        self.machine_id = machine_id & 0x3FF  # 限制在0-1023范围内
        self.sequence = 0

        # 默认纪元时间戳 (2020-01-01 00:00:00 UTC)
        self.epoch = epoch if epoch else 1577836800000  # 毫秒级时间戳

        # 时间戳、机器ID、序列号的位数分配
        self.timestamp_bits = 41
        self.machine_id_bits = 10
        self.sequence_bits = 12

        # 最大序列号
        self.max_sequence = -1 ^ (-1 << self.sequence_bits)

        # 上次生成ID的时间戳
        self.last_timestamp = -1

    def _current_timestamp(self):
        """获取当前时间戳 (毫秒)"""
        return int(time.time() * 1000)

    def generate_id(self):
        """生成雪花ID"""
        timestamp = self._current_timestamp()

        # 如果时钟回拨，抛出异常
        if timestamp < self.last_timestamp:
            raise Exception(f"时钟回拨! 当前时间: {timestamp}, 上次时间: {self.last_timestamp}")

        # 如果是同一毫秒内
        if timestamp == self.last_timestamp:
            self.sequence = (self.sequence + 1) & self.max_sequence
            if self.sequence == 0:
                # 序列号用完，等待下一毫秒
                timestamp = self._wait_next_millis(self.last_timestamp)
        else:
            self.sequence = 0

        self.last_timestamp = timestamp

        # 生成ID
        return ((timestamp - self.epoch) << (self.machine_id_bits + self.sequence_bits)) | \
               (self.machine_id << self.sequence_bits) | \
               self.sequence

    def _wait_next_millis(self, last_timestamp):
        """等待下一毫秒"""
        timestamp = self._current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._current_timestamp()
        return timestamp