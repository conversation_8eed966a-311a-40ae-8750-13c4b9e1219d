# -*- coding: UTF-8 -*-
"""
---------------------------------------
File Name ：sunklm-algorithm 
Description    ：MinioUtils.py.py
Author  ：cyq
Date    ：2025/2/28 11:17 
Change Activity: (更新内容)
---------------------------------------
"""
import base64
import json
import os
from urllib.parse import urlparse
import PyPDF2
import docx
import pandas as pd
import requests
from minio import Minio
from io import BytesIO
from common.conf.ServerConfig import ENDPOINT, ACCESS_KEY, SECRET_KEY


class MinioClient:
    def __init__(self, endpoint=ENDPOINT, access_key=ACCESS_KEY, secret_key=SECRET_KEY):
        """
        初始化Minio连接
        :param endpoint: Minio服务的端点URL
        :param access_key: 访问密钥
        :param secret_key: 秘密密钥
        """
        self.endpoint = endpoint
        self.access_key = access_key
        self.secret_key = secret_key

    def get_connect(self):
        """
        创建Minio客户端连接
        """
        return Minio(
            endpoint=self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=False
        )

    def getFileInfo(self: str):
        try:
            reqList = []
            for item in self.split(','):
                # 获取url文件后缀
                parsed_url = urlparse(item)
                Path = parsed_url.path
                fileSuffix = Path.split('.')[-1]
                # 获取url文件二进制内容
                response = requests.get(item)
                # 解析获取文件内容
                fileContent = readFile(BytesIO(response.content), fileSuffix)
                data = {
                    "fileName": os.path.basename(Path),
                    "fileSuffix": fileSuffix,
                    "fileContent": str(fileContent),
                    "fileUrl": item
                }
                reqList.append(data)
            return reqList
        except Exception as err:
            print(err)


# 读取对应后缀文件的内容
def readFile(binary_data, suffix: str):
    # 初始化返回变量
    contentStr = ""
    match suffix:
        case "xlsx" | "xls":
            try:
                contentXlsx = []
                # 转成文件对象
                dataXlsx = pd.read_excel(binary_data)
                for indexXlsx, rowXlsx in dataXlsx.iterrows():
                    contentXlsx.append(rowXlsx.to_dict())
                contentStr = str(contentXlsx)
            except Exception as err:
                print('解析xlsx异常:')
                print(err)
        case "docx" | "doc":
            try:
                contentDocx = []
                document = docx.Document(binary_data)
                for para in document.paragraphs:
                    contentDocx.append(para.text)
                contentStr = str(contentDocx)
            except Exception as err:
                print('解析docx或doc异常:')
                print(err)
        case "csv":
            try:
                contentCsv = []
                dataCsv = pd.read_csv(binary_data)
                for indexCsv, rowCsv in dataCsv.iterrows():
                    contentCsv.append(rowCsv)
                contentStr = str(contentCsv)
            except Exception as err:
                print('解析csv异常:')
                print(err)
        case "txt":
            try:
                dataCsv = pd.read_table(binary_data)
                contentStr = str(dataCsv)
            except Exception as err:
                print('解析txt异常:')
                print(err)
        case "pdf":
            try:
                contentPdf = []
                reader = PyPDF2.PdfReader(binary_data)
                num_pages = len(reader.pages)
                for page in range(num_pages):
                    text = reader.pages[page].extract_text()
                    contentPdf.append(text)
                contentStr = str(contentPdf)
            except Exception as err:
                print('解析pdf异常:')
                print(err)
        case "png" | "jpeg" | "jpg" | "mp4":
            try:
                base64_data = base64.b64encode(binary_data)
                base64_string = base64_data.decode('utf-8')
                contentStr = str(base64_string)
            except Exception as err:
                print('获取文件base64异常:')
                print(err)
        case _:
            print('暂不支持该文件类型转换')
    return contentStr


if __name__ == '__main__':
    fileUrl = 'http://172.1.6.188:9000/sunklm-dev/agentToolFile/1/812df485-95b0-4e4d-8307-15f8fbbc45b0.xlsx'
    reqJson = MinioClient.getFileInfo(fileUrl)
    print(reqJson)
