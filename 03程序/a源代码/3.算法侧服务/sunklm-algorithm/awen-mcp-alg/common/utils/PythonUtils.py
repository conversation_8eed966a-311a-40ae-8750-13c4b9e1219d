"""
-----------------------------------------------------
   File Name：  awen_python_interpreter.py
   Description : python解释器类，可以执行python代码
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""
import copy
from typing import Any, Optional
from func_timeout import func_set_timeout
from common.utils.LogUtils import logger

class GenericRuntime:
    GLOBAL_DICT = {}
    LOCAL_DICT = None
    HEADERS = []

    def __init__(self):
        self._global_vars = copy.copy(self.GLOBAL_DICT)
        self._local_vars = copy.copy(
            self.LOCAL_DICT) if self.LOCAL_DICT else None

        for c in self.HEADERS:
            self.exec_code(c)

    def exec_code(self, code_piece: str) -> None:
        print('KKKKKKKK', code_piece)
        exec(code_piece, self._global_vars)

    def eval_code(self, expr: str) -> Any:
        return eval(expr, self._global_vars)


DEFAULT_DESCRIPTION = """用来执行Python代码。代码必须是一个函数，
函数名必须得是 "solution"，代码对应你的思考过程。代码实例格式如下：
```python
# import 依赖包
import xxx
def solution(arg):
    # 初始化一些变量
    variable_names_with_real_meaning = arg
    # 步骤一
    mid_variable = func(variable_names_with_real_meaning)
    # 步骤 x
    mid_variable = func(mid_variable)
    # 最后结果
    final_answer =  func(mid_variable)
    return final_answer
```"""


class PythonInterpreter():
    """
    Description：一个Python执行器，能够执行Python脚本
    Args：
        description (str): 行动的描述。默认为DEFAULT_DESCRIPTION。
        answer_symbol (str, 可选): 来自LLM（大型语言模型）的答案标识符
        answer_expr (str, 可选): Python脚本中答案函数的名称。默认为"solution()"。
        answer_from_stdout (boolean): 执行结果是否来自标准输出（stdout）。
        name (str, 可选): 行动的名称。如果为None，则名称默认为类名。默认为None。
        enable (bool, 可选): 是否启用该行动。默认为True。
        disable_description (str, 可选): 当行动被禁用时，行动的描述。默认为None。
        timeout (int): Python脚本执行的最大等待时间（单位：秒）。
    """

    def __init__(self,
                 answer_expr: Optional[str] = "solution(arg)",
                 timeout: int = 20) -> None:
        self.answer_expr = answer_expr
        self.timeout = timeout

    def __call__(self, command: str):
        self.runtime = GenericRuntime()
        tool_return = func_set_timeout(self.timeout)(self._call)(command)
        return tool_return

    def _call(self, command: str):
        print('1111', command)
        self.runtime.exec_code(command)
        res = self.runtime.eval_code(self.answer_expr)
        return res


if __name__ == "__main__":
    py_tool = PythonInterpreter()
    params = {"command": """
# -*- coding: utf-8 -*-
import json
import sys

def solution(arg):
  param = json.loads(arg)
  context = param.get("verify_data")
  json_text_step1 = context[context.find("json\\n") + 5:]
  json_text_final = json_text_step1[:len(json_text_step1) - 4]
  verify_data = json.loads(json_text_final)

  # 请在下面填写代码逻辑

  ret = {
    "query": ", ".join(list(map(lambda e: e[0], filter(lambda x: x[1] == 0, verify_data.items()))))
  }
  print(ret)
  return json.dumps(ret) 
""",
              "input": '{"verify_data":"<think>\\n好，我现在需要分析用户提供的对话内容，判断是否提供了有效的信息。首先，对话中李三一开始就说了她的名字是李三，但客服称呼她为李女士，这可能是个错误，不过这不影响信息检测。\\n\\n接下来，客服询问电话服务密码，李三说她忘了，所以电话服务密码这里没有提供正确的信息，应该是0。\\n\\n然后，客服询问身份证号码，李三提供了身份证号码，虽然被隐藏了，但根据对话内容，她确实给出了身份证号码，所以身份证号码应该是1。\\n\\n接下来，客服询问亲属姓名，李三回答说是王宽或者陈璇，客服让她确认具体是哪个，李三确认是陈璇。因此，亲属姓名也提供了正确的信息，应该是1。\\n\\n然后，客服询问公司名称，李三第一次说单位是天虹汽车，客服让她确认完整名称，李三补充说是广州市天虹汽车股份有限公司。因此，公司名称也提供了正确的信息，应该是1。\\n\\n最后，关于电邮地址，整个对话中没有提到李三提供电子邮箱地址，所以电邮地址应该是0。\\n\\n总结一下，电话服务密码0，身份证号码1，亲属姓名1，公司名称1，电邮地址0。\\n</think>\\n\\n```json\\n{\\n  \\"电话服务密码\\": 0,\\n  \\"身份证号码\\": 1,\\n  \\"亲属姓名\\": 1,\\n  \\"公司名称\\": 1,\\n  \\"电邮地址\\": 0\\n}\\n```"}'
}

    command = params['command']
    input = params['input']
    new_command = command+"\narg = "+ repr(input)
    #print(new_command)
    tool_return = py_tool(new_command)


