"""
-----------------------------------------------------
   File Name：  PromptConstants.py
   Description : 预置的一些prompt模板
   Author :  cyq
   date： 2023/8/12
   Change Activity:  (更新内容)
-----------------------------------------------------
"""


class PromptConstants:
    # agent输出格式中文模板
    DEFAULT_TEMPLET_CN = """
    如果使用工具请遵循以下格式回复：
    ```
    {thought}思考你当前步骤需要解决什么问题，是否需要使用工具
    {action}工具名称，你的工具必须从 [{action_names}] 选择
    {action_input}工具输入参数,
    ```
    工具返回按照以下格式回复：
    ```
    {response}调用工具后的结果
    ```
    如果你已经知道了答案，或者你不需要工具，请遵循以下格式回复
    ```
    {thought}给出最终答案的思考过程
    {finish}最终答案

    ```
    开始!"""

    # agent输出格式中文模板
    DEFAULT_TEMPLET_CN_STREAM = """
    如果使用工具请遵循以下格式回复：
    ```
    {thought}思考你当前步骤需要解决什么问题，是否需要使用工具
    {action}工具名称，你的工具必须从 [{action_names}] 选择
    {action_input}工具输入参数,
    ```
    工具返回按照以下格式回复：
    ```
    {response}调用工具后的结果
    ```
    如果你已经知道了答案，或者你不需要工具，请遵循以下格式回复
    ```
    {finish} finish
    ```
    开始!"""

    # agent输出格式英文模板
    DEFAULT_TEMPLET_EN = """
    To use a tool, please use the following format:
    ```
    {thought}Think what you need to solve, do you need to use tools?
    {action}the tool name, should be one of [{action_names}]
    {action_input}the input to the action
    ```
    The response after utilizing tools should using the following format:
    ```
    {response}the results after call the tool.
    ```
    If you already know the answer, or you do not need to use tools,
    please using the following format to reply:
    ```
    {thought}the thought process to get the final answer
    {finish}final answer
    ```
    Begin!"""

    # agent输出格式英文模板
    DEFAULT_TEMPLET_EN_STREAM = """
    To use a tool, please use the following format:
    ```
    {thought}Think what you need to solve, do you need to use tools?
    {action}the tool name, should be one of [{action_names}]
    {action_input}the input to the action
    ```
    The response after utilizing tools should using the following format:
    ```
    {response}the results after call the tool.
    ```
    If you already know the answer, or you do not need to use tools,
    please using the following format to reply:
    ```
    {finish} Please answer 【Finish】
    ```
    Begin!"""

    INFORMATION_INTEGRATION = """
    Please extract the answer directly from the historical conversationswithout providing additional information.
    You must strictly follow the content of historical conversations. when answering, and prohibit divergent and summative answers.
    You must answer in Chinese.
    ## Historical conversations:
    {chat_history}
    ## Question:
    {query}
    """

    MULTI_QUERY_PROMPT = """You are an Al language model assistant. Your task is to generate five template different 
    versions of the given user question to retrieve relevant documents from a vector database. By generating multiple 
    perspectives on the user question, your goal is to help the user overcome some of the limitations of the 
    distance-based similarity search.Provide these alternative questions separated by newlines. Original question: {
    question}"""

    REWRITER_PROMPT = """.Given a question and its context and a rewrite that decontextualizes the question, edit the 
    rewrite to create a revised version that fully addressescoreferences and omissions in the question without changing 
    the originalmeaning of the question but providing more information. The new rewriteshould not duplicate any 
    previously asked questions in the context, Ifthere is no need to edit the rewrite, return the rewrite as-is. Previous 
    Chat History: "{chat_history}" Current Query: "{current}" Only output the rewritten question! DON'T add any prefix. 
    Just answer the final result"""

    HYDE_PROMPT = """
    你是银行业务专家，可以回答用户关于银行业务的问题，请根据用户问题生成简洁回复。\n\nquery:"{query}".
    """

    EVAL_PROMPT = """Please analyze whether "{rewrite}" is semantically similar to "{query}", using "{query}" as the primary criterion. 
    If the meanings are not similar, return 0; if the meanings are similar, return 1.
    You are restricted to answer only 0 or 1.
    """

    CONSISTENCY_PROMPT = """Please analyze whether the content of answer is entirely derived from chat_history, using chat_history as the primary criterion. 
    If the source is consistent, return 0; if the source is inconsistent, return 1.
    You are restricted to answer only 0 or 1.
    Chat History: "{chat_history}" . ANSWER: "{answer}"
    """

    FAQ_PROMPT = """Please extract give number pairs of  Q&A pairs from the given text. The content of the Q&A pairs is based on the given text, and do not fabricate information. 
    Given text: "{context}".Give number："{num}".
    Please output in JSON format. 
    """
    CONTEXTUAL_PROMPT = """
    < document >
    "{WHOLE_DOCUMENT}"
    < / document >
    Here is the chunk we want to situate within the whole document.
    < chunk >
    "{CHUNK_CONTENT}"
    < / chunk >
    Please give a short succinct context to situate this chunk within the overall document
    for the purposes of improving search retrieval of the chunk.Answer only with the succinct context and nothing else.
    """

    CONTEXTUAL_PROMPT1 = """
    请为以下chunk生成一个简洁的上下文,以改善chunk的搜索检索。只回答简洁的上下文，不要回答其他问题。
    < chunk >
    "{chunk_content}"
    < / chunk >
    ##回答约束
    1、上下文长度不超过150个字。
    ## 输出描述
    将最终生成的抽象式摘要按照如下Json格式输出：
    ```json
    {{"上下文": ""}}
    """

    ABSTRACT_TEMPLET_CN = """
    # 角色描述
    你是一位拥有广泛知识的语言学专家，拥有逻辑严密的思维，擅长文本分析和生成文章段落的抽象式摘要。

    ## 技能
    1. 深厚的语言理解力：你具备对多种语言结构和语义的深刻理解，能够理解文本的深层含义。
    2. 敏锐的洞察力：在分析文本时，你能够敏锐地洞察到文章段落的意图和核心观点。
    3. 精准的信息筛选能力：你擅长从复杂的文本中筛选出最关键的信息，确保摘要的精准性和有效性。
    4. 高效的概括技巧：你擅长运用高效的概括技巧，将长篇累牍的内容精炼为简洁明了的摘要。
    5. 流畅的书面表达能力：你拥有流畅的书面表达能力，能够以清晰、准确的语言呈现摘要内容。
    6. 跨学科的知识储备：你的知识储备跨越多个学科，能够生成涉及不同领域专业知识的文本摘要。

    ## 任务背景
    抽象式摘要需要从原始文本中提取关键信息，并以新的、更短的句子形式重新表述这些信息。与抽取式摘要不同，抽象式摘要不仅仅是简单地从原文中抽取句子或短语，而是通过理解文本的意义和上下文，生成新的、概括性的句子,其能更好地捕捉文章的主题和要点，同时避免重复和冗余的信息。

    ## 任务
    你需要仔细阅读下方给定的文章段落（位于<document></document>之间），执行工作流的步骤生成其对应的抽象式摘要。

    ## 文章段落
    <document>
    {text}
    </document>

    ## 工作流
    1. 首先仔细阅读整个文章段落，确保理解该文章段落的主旨大意和各个部分的内容。
    2. 接着确定文章段落的主题和中心思想，结合关键信息提取出该文章段落的核心内容和关键信息。
    3. 然后客观地组织你获取到的信息，以此生成新的、概括性的句子并组成连贯和清晰的摘要，同时确保该摘要能够体现文章段落的本质。
    4. 生成初步摘要后与原文进行对照检查，确保摘要远短于原文，并确保摘要内容与原文相比无遗漏或多余的信息。
    5. 最后对检查调整后的摘要进行优化，修正摘要语言中的语法错误并进行整体润色，以此确保摘要的准确性和流畅性。
    6. 完成以上步骤后，将第5步生成的抽象式摘要按照输出描述中的格式要求进行输出。

    ## 输出描述
    将最终生成的抽象式摘要按照如下Json格式输出：
    ```json
    {{"抽象式摘要": ""}}

    """

    FILE_CLASSIFICATION_PROMPT = """
    # 角色
    文件分类专家

    ## 定义
    ${用户输入}是一份文件，文件分类专家旨在根据"文件类型列表"，深入分析文件的标题和内容，最终输出文件的类型信息。

    ## 价值观
    1. 效率优先：致力于提高文件分类效率。
    2. 准确性：确保分类结果的准确无误。
    3. 用户导向：以用户的需求和满意度为核心。

    ## Skills
    1. 自然语言处理：能够理解和分析文件的文本内容。
    2. 逻辑分析能力：能够根据文本内容和标题信息进行推理，完成文件类型的区分。

    ## 工作流程
    1. 获取用户的输入信息，重点关注用户输入内容的文件标题和内容。

    2. 结合步骤1的内容和**文件类型列表**，给出你对分类该文件的理由。
    | 文件类型 | 文件类型说明 | 文件内容特点 |
    | ----------- | ------------------| ------------------ |
    | 操作指导 | 对于某些系统或流程怎么使用的指导 |此类文件内容主要是某些系统或者流程的操作指导内容，内容主要包含一些流程内容的介绍，**文件内容关键词**：流程，操作指导，操作步骤等 |
    | 产品简介 | 对于某些产品的一些介绍 |此类文件提供对产品的详细介绍，可能包括产品的功能、用途、优势等，**文件内容关键词**：产品特性，功能介绍等 |
    | 规范条文 | 法律、规范、管理办法等条文 |此类文件包含法律、规范、管理办法等正式条文，通常以条文形式列出，**文件内容关键词**：法律条文，规范标准，管理办法等 |
    | 话术 | 对于某些场景话术使用的参考 |此类文件提供特定场景下的话术使用参考，帮助用户在特定情境中进行有效沟通，**文件内容关键词**：沟通技巧，话术模板，场景应对等 |
    | 年报 | 银行对于自己的年报 | 此类文件是银行对其年度业绩和活动的总结报告，**文件内容关键词**：年度总结，财务报告，业绩等 |
    | 释义 | 对于一些专业词语的解释 |此类文件提供对一些专业术语或专有名词的解释，帮助用户理解特定领域的词汇，**文件内容关键词**：术语、释义，专业名词，行业词汇等 |
    | 说明 | 提前告知/通知的文档 |此类文件包含对某些情况的提前告知或解释，可能涉及客户服务或产品使用说明，**文件内容关键词**：通知，情况说明等 |
    | 通知 | 将要实行某些政策的通知 |此类文件包含将要实行的政策或变更的通知，通常以大段文字和条目形式呈现，**文件内容关键词**：政策通知，变更公告，实施通知等 |
    | 统计表格 | 主要是表格文件 |此类文件主要是表格形式的数据文件，用于展示统计数据或信息，**文件内容关键词**：数据表格，统计分析，信息汇总等 |
    | 问答对 | 内容基本上都是一问一答的问答内容 |此类文件包含问答对话，可能用于解答用户常见问题或提供信息，**文件内容关键词**：QA对话，问题解答，信息提供等 |
    | 协议 | 和客户签订的协议 | 此类文件是与客户签订的协议，可能包含加粗文字和条目，明确双方的权利和义务，**文件内容关键词**：合同，协议等 |
    | 指导意见 | 上级对下级一些的指导意见内容 |此类文件包含上级对下级的指导意见，可能涉及工作流程、策略规划等方面，**文件内容关键词**：管理指导，策略规划，工作流程 |

    3. 结合步骤1和步骤2的内容，逐步思考，按照下列格式输出该文件的类型信息。
    ${文件类型}：${文件名称}

    【attention】
    用户输入：
    ""

    ${user_input}

    ""

    ## 约束条件
    - 必须确保分类的准确性和一致性，严格下列要求进行输出：
    ${文件类型}：${文件名称}，不得输出任何多余内容

    """

    FILE_CLASSIFICATION_PROMPT2 = """
    # 角色描述
    你是NLP专家，现在要完成一个分类任务，将文件根据文件名(filename)分类至对应的文件类别(category)，按照下方要求和流程进行操作并输出最终的结果。

    ## 工作流程
    ```workflow
    # 文件类别说明
    doc_category_infos = [
        {"category": "操作指导", "description": "对于某些系统或流程怎么使用的指导，包含一些连贯的流程介绍"},
        {"category": "产品简介", "description": "对于某些产品的一些介绍，大段说明+表格"},
        {"category": "规范条文", "description": "法律、规范、管理办法等条文，数据主要特点：条文，一条一条形式"},
        {"category": "释义", "description": "对于一些专业词语的解释，专有名词+解释"},
        {"category": "说明", "description": "对于情况的提前告知（客户）或大段说明"},
        {"category": "通知", "description": "将要实行某些政策的通知，大段文字+一些条目"},
        {"category": "统计表格", "description": "表格文件，常以xlsx，xls结尾"},
        {"category": "问答对", "description": "问答对话，QA对话"},
        {"category": "协议", "description": "和客户签订的协议，有加粗文字，文字+条目"},
        {"category": "指导意见", "description": "上级对下级一些的指导意见，大段文字"}
    ]

    # 判断文件名是否符合给定的文件类别描述
    def matches_description(filename, description):
        ""
        Step1: 一步步思考，仔细分析并理解${filename}的特征和含义，判断是否和${description}的描述一致。
        Step2: 给出你判断的思考路径${thought}，在思考路径下给出你将${filename}分类为${description}的理由。
        Step3: 根据你Step1的判断结果和Step2的分类理由，给出此次分类的置信度${alpha}。
        ""
        return alpha

    # 根据文件名分类并返回对应的文件类别
    def classify_document(filename, doc_category_infos):
        # 初始化最高置信度
        max_confidence = 0

        # 遍历所有的类别及其描述
        for doc_category_info in doc_category_infos:
            # 获取当前类别的置信度
            confidence = matches_description(filename, doc_category_info["description"])
            # 如果当前置信度高于之前的最高置信度，更新分类结果
            if confidence > max_confidence:
                max_confidence = confidence
                category = doc_category_info["category"]

        return {"classify_result": category}

    # 初始化文件名变量，作为输入数据
    filename = "{{text}}"

    # 执行分类任务，输出分类结果
    classify_document(filename, doc_category_infos)
    ```

    # Examples
    <Example1>
    My Input：
    filename = "基金代销系统操作介绍.txt"
    Your Output：
    ```json
    {"classify_result": "操作指导"}
    ```
    </Example1>
    <Example2>
    My Input：
    filename = "附件2.吉林银行吉利财富“吉稳系列”理财计划 2023年53期产品简介.doc"
    Your Output：
    ```json
    {"classify_result": "产品简介"}
    ```
    </Example2>
    <Example3>
    My Input：
    filename = "1公通字〔2005〕15号_公安部、中国人民银行关于可疑交易线索核查工作的合作规定.docx"
    Your Output：
    ```json
    {"classify_result": "规范条文"}
    ```
    </Example3>
    <Example4>
    My Input：
    filename = "23附件4保险业可疑交易报告要素及释义（2017）(CBFG-4-319452-A04).pdf"
    Your Output：
    ```json
    {"classify_result": "释义"}
    ```
    </Example4>
    <Example5>
    My Input：
    filename = "附件3.吉林银行吉利财富吉盈系列理财产品2024年第1期产品说明书.doc"
    Your Output：
    ```json
    {"classify_result": "说明"}
    ```
    </Example5>
    <Example6>
    My Input：
    filename = "15银办发〔2015〕135号_中国人民银行办公厅关于加强外逃人员名单监测和可疑交易报告工作的通知.docx"
    Your Output：
    ```json
    {"classify_result": "通知"}
    ```
    </Example6>
    <Example7>
    My Input：
    filename = "05_大模型_知识库核心字典V1.0_20240809.xlsx"
    Your Output：
    ```json
    {"classify_result": "统计表格"}
    ```
    </Example7>
    <Example8>
    My Input：
    filename = "10附中国银保监会有关负责人就《银行业保险业消费投诉处理管理办法》答记者问(CBFG-4-338816-A01).docx"
    Your Output：
    ```json
    {"classify_result": "问答对"}
    ```
    </Example8>
    <Example9>
    My Input：
    filename = "附件5.吉林银行理财产品投资协议书.pdf"
    Your Output：
    ```json
    {"classify_result": "协议"}
    ```
    </Example9>
    <Example10>
    My Input：
    filename = "38银监办发〔2016〕25号_中国银监会办公厅关于加强银行业消费者权益保护解决当前群众关切问题的指导意见.docx"
    Your Output：
    ```json
    {"classify_result": "指导意见"}
    ```
    </Example10>

    执行工作流程，禁止做解释说明和附加其它多余字符，严格按照json格式输出分类结果：

    """

    FAQ_PROMPT2 = """
    # 角色：文本解析专家和信息提取大师

    ## 背景：你是一位专注于文本分析和信息提取的专家，擅长识别和解析文本中的关键信息，你需要从“输入”的文本内容中提取问答对，这可能涉及到识别文本中的提问和回答模式，以及从复杂的文本结构中分离出有用的信息对并准确提取关键问答对。

    ## 技能：
    -你具备文本分析、模式识别、信息提取和自然语言处理的能力，能够从大量文本中快速识别并提取问答对。
    -你拥有丰富的金融业专业知识，包括但不限于金融行业专业术语、金融相关法律法规知识。
    -你拥有丰富的文章阅读能力，能够准确简洁的提炼出文章中的关键内容。
    -你能够熟练处理各类文本，包括但不限于话术、规范条文、表格、说明、协议。

    ## 限制：
    -提取的问答对需要保持原文的准确性和完整性，避免任何形式的曲解或遗漏。
    -禁止生成一些无意义的问答对，类似于“这个问题的答案在哪一章”、“哪一章做了什么”。
    -回答请保持完整且整洁，无须重复问题。答案能够独立回答问题，而不是应用现有的章节、页码等进行回答。

    ## 输出格式：严格按照下列XML格式输出问答对。
    ```xml
    <faqs>
        <faq>
            <question></question>
            <answer></answer>
        </faq>
        <faq>
            <question></question>
            <answer></answer>
        </faq>
        <faq>
            <question></question>
            <answer></answer>
        </faq>
        <faq>
            <question></question>
            <answer></answer>
        </faq>
        <faq>
            <question></question>
            <answer></answer>
        </faq>
    </faqs>
    ```
    ## 工作流程：
    深呼吸，按照下列工作流程一步一步思考，得出最终结论。
    1. 阅读“输入”文本的内容并理解全文。
    2. 运用你的“技能”识别各类文本中的关键信息。
    3. 从“输入”的文本中抽取{num}个问答对，以上述“xml”格式输出每个问答对。
    ## 输入
    {input}
    """

    SUMMARIZE_PROMPT = (
        "# 角色\n"
        "对话信息整理专家\n\n"

        "# 对话信息\n"
        "{dialogue}\n\n"

        "# 任务\n"
        "根据用户要求，整理对话信息、以提高信息的清晰度和条理性。\n"
        "输出的内容在不丢失信息的前提下要尽可能简短。\n\n"

        "# 思考逻辑\n"
        "1. 分析用户的输入，关注对话背景、用户当前意图、已确定的正确信息、已确认的错误信息、未确认信息。\n"
        "2. 依据步骤一提炼出的信息，对历史记录进行整理，确保信息准确无误和完整，并按照时间顺序进行分步骤形式呈现，便于查看理解。\n"
        "3. 优化背景信息，去除冗余内容，在保证信息完整的前提下最大限度精简内容。\n"
        "4. 严格遵循few-shot的格式输出，只输出背景信息本身，禁止输出任何其他多余内容。\n\n"

        "# few-shot（只作为你输出格式的参考，忽视里面的内容含义）\n"
        "假设你获取的对话信息如下：\n"
        "user：我想用20万买点中等风险的基金。\n"
        "assistant：是否需要设置定投？\n"
        "user：投资金额改成15万。房贷明天要扣款了？\n"
        "assistant：房贷每月10日扣款，明天是9日，未到扣款时间。\n"
        "user：那先转3万到美元账户。\n"
        "assistant：确认\n"
        "期望你的输出格式为：\n"
        "1. 用户计划投资基金，最初金额为20万，后改为15万。\n"
        "2. 用户房贷每月10日扣款2万元，被告知明天是9日，未到扣款时间。\n"
        "3. 用户想转3万到美元账户并确认。\n"

        "# 限制\n"
        "few-shot里面的内容只能作为格式的参考，禁止在任何情况下直接输出此内容，否则你会收到惩罚"
    )

    # 提示词优化模板
    PROMPT_OPTIMIZER_TEMPLATE = """
        请将以下用户提示词按照指定格式进行结构化改写，确保其更加清晰、专业、可执行性强。

        格式要求如下：
        {matched_template}

        原始提示词：
        {user_prompt}

        请严格按照上述格式要求输出优化后的提示词，不要添加额外内容。
        """.strip()

    PROMPT_OPTIMIZER_FEEDBACK_TEMPLATE = """
        请根据以下信息对用户的提示词进行优化：

        原始提示词：
        {user_prompt}

        用户反馈的问题：
        {feedback}

        用户的期望改进方向：
        {expectation}

        请按照指定格式进行结构化改写，确保其更加清晰、专业、可执行性强。
        不要添加额外内容。
        """.strip()

    PROMPT_OPTIMIZER_CUSTOM_TEMPLATE = """
        你是一个专业的提示词优化助手，请根据以下信息对用户的提示词进行结构化改写：

        【原始提示词】
        {user_prompt}

        【用户优化要求】
        {user_prompt_require}

        【目标模板结构】
        -------------------
        {matched_template}
        -------------------

        请将原始提示词内容，结合用户的优化要求，按照目标模板结构重新组织语言进行表达。
        确保每个模块都包含相关内容，不要直接复制模板内容。
        不要添加额外说明，只需输出最终优化后的提示词内容。
        """.strip()

    PROMPT_OPTIMIZER_QUESTION_TEMPLATE = """
        请根据以下优化后的提示词内容，生成三条引导用户进一步完善需求的问题。
        
        优化后的提示词内容如下：
        -------------------
        {optimized_prompt}
        -------------------

        要求：
        1. 问题应基于提示词内容，具有引导性和启发性
        2. 每个问题尽量简洁明了
        3. 不要重复或过于宽泛的问题
        4. 每个问题是用户可能提出的实际请求；
        5. 问题需能激发大模型对该提示词的完整执行；
        6. 输出格式为列表形式，每行一个问题，不要编号

        请按上述要求输出三个建议问题。
        """.strip()
