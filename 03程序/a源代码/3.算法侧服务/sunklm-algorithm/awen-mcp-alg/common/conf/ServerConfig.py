import os

current_path = os.path.dirname(os.getcwd())  # /home/<USER>/source/Awen/utils


# 应用配置
class ServerConfig():
    # 服务名称
    SERVICE_NAME: str = "awen-mcpProtocol-alg"


parent_path = os.path.dirname(current_path)
PDF_OCR_URL = "http://***********:9011/process"
FILE_IMAGEREC_URL = "http://***********:9011/image_rec"
FILE_Convert_URL = "http://***********:9011/fileformatconvert"
FILE_FORMAR_URL = "http://***********:9083/RAG-alg/file_format"
FILE_SPLIT_URL = "http://***********:9083/RAG-alg/text_split"
FILE_LAYOUT_URL = "http://***********:9011/layout"
FILE_TABLEREC_URL = "http://***********:9011/table_rec"
# mino配置
ENDPOINT = "172.1.6.188:9000"
# ACCESS_KEY = config['ACCESS_KEY']
# SECRET_KEY = config['SECRET_KEY']
ACCESS_KEY = 'minioadmin'
SECRET_KEY = 'minioadmin'
MINOBUCKET = "sunklm-dev"
SFTP_UPLOAD_PATH = "/sftp/"

PWD = os.path.dirname(os.getcwd())

# 默认参数
default_params = ["topK", "matchType", "threshold", "weight"]

# 默认使用的模型
LLM_NAME = "qwen2.5-72b-instruct-gptq-int4"
# 私有化部署的模型MAP
LLM_MAP = {
    "qwen2.5-72b-instruct-gptq-int4": {
        "base_url": "http://172.1.0.134:9020/v1/chat/completions",
        "api_key": "not used actually",
        "company": "qwen",
        "deploy_mode": 0}}

# 最大输入长度
MAX_SEQ_LEN = 10000

# 最大输出长度：
MAX_OUT_LEN = 512

# 模型输出参数默认值
QUERY = "你好"
TEMPERATURE = 0.01
TOP_P = 0.01
TOP_K = 1
REWRITE = False
POSTPROCESS = False
STREAM = False
# 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
QUERY_PER_SECOND = 1

# 工具测试
# the prompt to force LLM to generate response
FORCE_STOP_PROMPT_EN = """You should directly give results
 based on history information."""

# agent 最大轮次
MAX_TURN = 3

# 输入窗口长度
CONTEXT_WINDOW = 50000

# 大模型参数计算默认参数
CALC_RAM = "ram"
CALC_PARAMS = "params"
CALC_QUANTIZATION_LEVEL = 16
CALC_OS_RAM = 2
CALC_CONTEXT_WINDOW = 2048

# mysql配置
MYSQL_HOST = "**********"
MYSQL_PORT = 3306
MYSQL_USER = "SUNKLM"
MYSQL_PASSWORD = "Sunyard@123"
MYSQL_DATABASE = "AWEN_PRIMARY"

MATRIX_PATH = os.path.join(parent_path,
                           'sunklm-algorithm/awen-mcp-alg/resources/product-matrix.json')  # "/home/<USER>/source/Awen/resources/product-matrix.json"

# 企查查
qichacha_appkey = "2612edaf7dbb46eeb8d815f527cb7376"
qichacha_secretkey = "4ehzBAj48fTQMRBSKzAGX9PUS3SDVnwu"

BC_INTERNET_URL = "https://api.bochaai.com/v1/web-search"
qichacha_patent_list_URL = "http://api.singdata.cn/api/v1/ip/patent/info/list?keyword="
qichacha_website_URL = "http://api.singdata.cn/api/v1/ip/website/bykeyword?keyword="
qichacha_ic_info_URL = "http://api.singdata.cn/api/v1/ic/info/full?keyword="
qichacha_bidding_details_URL = "http://api.singdata.cn/api/v1/biz/bidding/details?id="
qichacha_bidding_list_URL = "http://api.singdata.cn/api/v1/biz/bidding/list?id="
MODEL_URL = "http://***********:8011/Agent-alg/llm_chat"
workflow_tool_URL = "http://***********:8083/workflow/execWorkflowTool"
BING_INTERNET_URL = "http://***********:8081/search"
KNOWLEDGE_SEARCH_URL = "http://***********:8013/RAG-alg/query_match"

MCP_TOOL_WORKERS = 10

OVERLAP = 100  # 重叠的字符数

Port = 9082

parent_path = os.path.abspath('')
ASR_PATH = os.path.join(parent_path, "logs/asr_logs")

# # tts语音种类
VOICE = '中文女'
# 生成的tts语音存放的minio地址
MINIO_PATH = "agentToolFile/13/"
