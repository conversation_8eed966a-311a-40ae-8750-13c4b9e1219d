import os
import sys
import importlib.util
from mcp.server.fastmcp import FastMCP

sys.path.append("../../")
from common.utils.LogUtils import logger
import asyncio
from concurrent.futures import ThreadPoolExecutor
import atexit
from common.conf.ServerConfig import MCP_TOOL_WORKERS,Port
from mcpProtocol.api.ApiManger import load_tools, unload_tools


MCP_SERVER_NAME = "MCP_Server"

# 保留已加载工具模块的引用
loaded_tools = {}

def load_external_tools(mcp, tools_dirs=None):
    if tools_dirs is None:
        tools_dirs = ["./mcpProtocol/service"]
    for tools_dir in tools_dirs:
        if not os.path.exists(tools_dir):
            continue

        for filename in os.listdir(tools_dir):
            if filename.endswith(".py"):
                module_name = filename[:-3]
                file_path = os.path.join(tools_dir, filename)

                spec = importlib.util.spec_from_file_location(module_name, file_path)
                mod = importlib.util.module_from_spec(spec)
                try:
                    spec.loader.exec_module(mod)
                    # 记录已加载的模块以供执行
                    loaded_tools[module_name] = mod
                    if hasattr(mod, "register") and callable(mod.register):
                        mod.register(mcp)
                        logger.info(f"Tool {module_name} registered successfully.")
                    else:
                        logger.warning(f"Module {module_name} does not have a callable 'register(mcpProtocol)' function.")
                except Exception as e:
                    logger.error(f"Failed to load tool {module_name}: {e}")


def get_mcp(port: int = None):
    if port is None:
        import argparse
        parser = argparse.ArgumentParser()
        parser.add_argument("--port", type=int, default=8019)
        args = parser.parse_args()
        port = args.port
    return FastMCP(MCP_SERVER_NAME, port=port)


def start_mcp_server(port: int):
    mcp = get_mcp(port=port)
    load_external_tools(mcp, ["./mcpProtocol/service", "./resources"])

    # 初始化线程池以执行工具任务
    global executor
    executor = ThreadPoolExecutor(max_workers=MCP_TOOL_WORKERS)
    atexit.register(lambda: executor.shutdown(wait=True))

    @mcp.custom_route("/load_tools", methods=["POST"])
    async def load_tools_route(body: dict):
        url = body.get("url")
        if not url:
            return {"error": "Missing 'url' parameter"}
        try:
            module = load_tools(mcp, url, save_dir="./resources")
            return {"status": "loaded", "module": module}
        except Exception as e:
            logger.error(f"Error loading tool: {e}")
            return {"error": str(e)}

    @mcp.custom_route("/unload_tools", methods=["POST"])
    async def unload_tools_route(body: dict):
        tools = body.get("tool_names")
        if not isinstance(tools, list):
            return {"error": "Missing or invalid 'tool_names' list"}
        try:
            unload_tools(mcp, tools, save_dir="./resources")
            return {"status": "unloaded", "modules": tools}
        except Exception as e:
            logger.error(f"Error unloading tools: {e}")
            return {"error": str(e)}

    @mcp.prompt()
    def es_prompt(index: str) -> str:
        """创建用于索引分析的提示词"""
        return f"""You are an elite Elasticsearch expert with deep knowledge of search engine architecture, data indexing strategies, and performance optimization. Please analyze the index '{index}' considering:
        - Index settings and mappings
        - Search optimization opportunities
        - Data modeling improvements
        - Potential scaling considerations
        """

    @mcp.tool()
    async def run_tool(tool_name: str, **params):
        """
        通用工具入口：在模块中找到被 @mcpProtocol.tool 装饰的函数，并用参数执行。
        """
        if tool_name not in loaded_tools:
            return {"error": f"Tool {tool_name} is not loaded"}
        mod = loaded_tools[tool_name]

        func = None
        for attr in dir(mod):
            obj = getattr(mod, attr)
            if (
                callable(obj)
                and getattr(obj, "__wrapped__", None) is not None
                and attr == tool_name
            ):
                func = obj
                break

        loop = asyncio.get_event_loop()
        try:
            # 使用线程池异步执行函数并传入参数
            result = await loop.run_in_executor(executor, lambda: func(**params))
            return {"result": result}
        except Exception as e:
            logger.error(f"[run_tool] {tool_name} error: {e}")
            return {"error": str(e)}

    # 启动服务
    mcp.run(transport='sse')


if __name__ == "__main__":
    port = Port
    start_mcp_server(port)
