#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from pymilvus import Collection, MilvusException
from elasticsearch import helpers
from datetime import datetime
from pprint import pprint
import json
import time
import uuid
import numpy as np
from functools import reduce
from collections import OrderedDict
from typing import List, Dict, Any, Optional, Union
import os
import sys
import logging

# 将当前目录添加到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from common.conf.db_config import db_config, get_memory_db_config
from common.utils.log_utils import logger
from common.utils.embedding import EmbeddingService
from common.utils.mysql_conn import MySQLClient
from common.utils.milvus_conn import MilvusClient
from common.utils.es_conn import EsClient
from common.utils.abstract_text_rank import abstract_by_txt_rank

# 导入 ModelAPI
try:
    from common.utils.ModelUtils import ModelAPI
except ImportError:
    # 如果导入失败，定义一个备用的 ModelAPI 类
    class ModelAPI:
        def __init__(self, base_url=None, api_key=None):
            logger.warning("使用备用的 ModelAPI 实现")
            self.base_url = base_url or "http://localhost:8000"
            self.api_key = api_key or "fallback_key"
        
        def generate(self, query, temperature=0.01, max_tokens=512):
            logger.warning(f"为查询生成模拟响应: {query[:50]}...")
            return {
                "status": "success",
                "answer": "这是一个模拟的回答。在实际应用中，这里将是模型生成的回答。",
                "tokens": {
                    "prompt_tokens": len(query) // 2,
                    "completion_tokens": 50,
                    "total_tokens": len(query) // 2 + 50
                }
            }


class MemorySave:
    """
    记忆存储管理类，负责将问答数据存储到Elasticsearch、Milvus和MySQL中
    提供完整的记忆存储流程，包括文本存储、向量生成与存储、统计信息初始化
    """

    def __init__(self, db_config: Dict[str, Any], memory_type: str = "SHORT_TERM"):
        """
        初始化记忆存储服务，建立与各数据库的连接
        
        Args:
            db_config: 数据库配置信息，包含ES、Milvus和MySQL的连接参数
            memory_type: 记忆类型，"SHORT_TERM" 或 "LONG_TERM"，默认为短期记忆
        """
        try:
            # 根据记忆类型获取对应的数据库配置
            self.memory_type = memory_type
            memory_db_config = get_memory_db_config(memory_type)
            
            # 从配置中获取索引和集合名称
            self.es_index = memory_db_config['ES']['index_name']
            self.milvus_collection = memory_db_config['MILVUS']['collection_name']
            self.mysql_stats_table = memory_db_config['MYSQL']['stats_table']
            
            self.ES_CONFIG = memory_db_config['ES']
            self.MILVUS_CONFIG = memory_db_config['MILVUS']
            
            # 连接Elasticsearch，添加异常处理
            logger.info("正在连接Elasticsearch...")
            self.es = EsClient().get_connect()
            logger.info("Elasticsearch连接成功")
            
            # 连接Milvus
            logger.info("正在连接Milvus...")
            MilvusClient()._connect()
            self.collection = Collection(name=self.milvus_collection)
            logger.info(f"成功连接到Milvus，集合: {self.milvus_collection}")
            
            # 连接MySQL
            logger.info("正在连接MySQL...")
            self.mysql = MySQLClient().get_connect()
            logger.info("MySQL连接成功")
            
            # 加载向量化服务
            logger.info("正在加载向量化服务...")
            self.embedding_service = EmbeddingService()
            logger.info("向量化服务加载成功")
            
        except Exception as e:
            logger.error(f"初始化MemorySave失败: {str(e)}")
            raise

    def _validate_inputs(self, question: str, answer: str, user_id: str, 
                         agent_id: str, session_id: str) -> None:
        """
        校验输入参数的合法性
        
        Args:
            question: 问题内容
            answer: 答案内容
            user_id: 用户ID
            agent_id: 智能体ID
            session_id: 会话ID
            
        Raises:
            ValueError: 当输入参数不符合要求时抛出
        """
        # 检查输入是否为字符串类型
        for param, name in [(question, "question"), (answer, "answer"), 
                           (user_id, "user_id"), (agent_id, "agent_id"), 
                           (session_id, "session_id")]:
            if not isinstance(param, str):
                raise ValueError(f"参数 {name} 必须为字符串类型，当前类型: {type(param)}")
        
        # 检查输入是否为空
        if not question.strip():
            raise ValueError("问题内容不能为空")
        if not answer.strip():
            raise ValueError("答案内容不能为空")
        if not user_id.strip():
            raise ValueError("用户ID不能为空")
        if not agent_id.strip():
            raise ValueError("智能体ID不能为空")
        if not session_id.strip():
            raise ValueError("会话ID不能为空")

    def _creart_memory_body(self, memory_id: str, question: str, answer: str, 
                           user_id: str, agent_id: str, session_id: str, 
                           ntime: int, ntime_str: str, memory_score: int = 1) -> Dict[str, Any]:
        """
        创建记忆数据体，包含记忆的基本信息
        
        Args:
            memory_id: 记忆唯一ID
            question: 问题内容
            answer: 答案内容
            user_id: 用户ID
            agent_id: 智能体ID
            session_id: 会话ID
            ntime: 时间戳（毫秒）
            memory_score: 记忆分数，默认为1
            
        Returns:
            包含记忆基本信息的字典
        """
        try:
            memory_body = {
                "memory_id": memory_id,
                "question": question,
                "answer": answer,
                "user_id": user_id,
                "agent_id": agent_id,
                "session_id": session_id,
                "create_time": ntime,
                "update_time": ntime,
                "create_time_str": ntime_str,
                "update_time_str": ntime_str,
                "memory_score": memory_score
            }
            logger.debug(f"创建记忆数据体: {memory_id}")
            return memory_body
        except Exception as e:
            logger.error(f"创建记忆数据体失败: {str(e)}")
            raise

    def _memory_embedding(self, memory_body: Dict[str, Any]) -> Dict[str, Any]:
        """
        对问题和答案进行向量化处理
        
        Args:
            memory_body: 包含问题和答案的记忆数据体
            
        Returns:
            包含向量化结果的记忆数据体
            
        Raises:
            Exception: 向量化过程中出现异常时抛出
        """
        try:
            logger.info(f"开始对记忆进行向量化处理: {memory_body['memory_id']}")
            
            # 生成问题向量
            question_vec = self.embedding_service.embedding_query(memory_body["question"])
            memory_body["question_vec"] = np.array(question_vec, dtype=np.float32).tolist()
            
            # 生成答案向量
            answer_vec = self.embedding_service.embedding_query(memory_body["answer"])
            memory_body["answer_vec"] = np.array(answer_vec, dtype=np.float32).tolist()
            
            logger.info(f"记忆向量化完成: {memory_body['memory_id']}, "
                        f"问题向量维度: {len(memory_body['question_vec'])}, "
                        f"答案向量维度: {len(memory_body['answer_vec'])}")
            return memory_body
        except Exception as e:
            logger.error(f"记忆向量化失败: {memory_body['memory_id']}, 错误: {str(e)}")
            raise

    def _es_add_memory(self, memory_body: Dict[str, Any]) -> None:
        """
        将记忆文本数据存入Elasticsearch
        
        Args:
            memory_body: 包含记忆信息的字典
            
        Raises:
            Exception: 存储过程中出现异常时抛出
        """
        try:
            # 构建Elasticsearch文档
            es_doc = {
                "question": memory_body["question"],
                "answer": memory_body["answer"],
                "user_id": memory_body["user_id"],
                "agent_id": memory_body["agent_id"],
                "session_id": memory_body["session_id"],
                "create_time": memory_body["create_time"] * 1000,
                "update_time": memory_body["update_time"] * 1000,
                "create_time_str": memory_body["create_time_str"][:10],
                "update_time_str": memory_body["update_time_str"][:10],
                "search_count": 1,
                "memory_score": memory_body["memory_score"]
            }
            
            # 插入数据到Elasticsearch
            self.es.index(index=self.es_index, id=memory_body["memory_id"], document=es_doc)
            logger.info(f"Elasticsearch存储记忆成功: {memory_body['memory_id']}")

        except Exception as e:
            logger.error(f"存储记忆到Elasticsearch时发生未知错误: {memory_body['memory_id']}, 错误: {str(e)}")
            raise

    def _milvus_add_memory(self, memory_body: Dict[str, Any]) -> None:
        """
        将记忆向量数据存入Milvus
        
        Args:
            memory_body: 包含记忆向量信息的字典
            
        Raises:
            Exception: 存储过程中出现异常时抛出
        """
        try:
            # 构建Milvus文档
            milvus_doc = {
                "memory_id": memory_body["memory_id"],
                "question_emb": memory_body["question_vec"],
                "answer_emb": memory_body["answer_vec"],
                "user_id": memory_body["user_id"],
                "agent_id": memory_body["agent_id"],
                "session_id": memory_body["session_id"],
                "create_time": memory_body["create_time"],
                "update_time": memory_body["update_time"],
                "create_time_str": memory_body["create_time_str"],
                "update_time_str": memory_body["update_time_str"],
                "memory_score": memory_body["memory_score"]
            }
            
            # 插入数据到Milvus
            self.collection.insert(milvus_doc)
            logger.info(f"Milvus存储记忆成功: {memory_body['memory_id']}")
        except MilvusException as e:
            logger.error(f"Milvus存储记忆失败: {memory_body['memory_id']}, 错误码: {e.code}, 错误信息: {e.message}")
            raise
        except Exception as e:
            logger.error(f"存储记忆到Milvus时发生未知错误: {memory_body['memory_id']}, 错误: {str(e)}")
            raise

    def _mysql_memory_init(self, memory_body: Dict[str, Any]) -> None:
        """
        在MySQL中初始化记忆统计信息
        
        Args:
            memory_body: 包含记忆信息的字典
            
        Raises:
            Exception: 初始化过程中出现异常时抛出
        """
        try:
            # 转换时间戳为可读格式
            ntime = memory_body["create_time"]
            time_str = datetime.fromtimestamp(ntime).strftime('%Y-%m-%d %H:%M:%S')
            
            # 构建SQL语句
            sql_insert = f"""
            INSERT INTO {self.mysql_stats_table} 
            (memory_id, create_time, update_time, recall_stas, recall_stas_txt, recall_stas_vct, recall_stas_mlt)
            VALUES ("{memory_body["memory_id"]}", "{time_str}", "{time_str}", 0, 0, 0, 0)
            """
            
            # 执行SQL语句
            with self.mysql.cursor() as cursor:
                cursor.execute(sql_insert)
            self.mysql.commit()
            logger.info(f"MySQL初始化记忆统计信息成功: {memory_body['memory_id']}")
        except Exception as e:
            logger.error(f"MySQL初始化记忆统计信息失败: {memory_body['memory_id']}, 错误: {str(e)}")
            self.mysql.rollback()  # 发生异常时回滚事务
            raise

    def memory_save(self,
                    question: str,
                    answer: str, 
                    user_id: str, 
                    agent_id: str, 
                    session_id: str) -> Optional[str]:
        """
        记忆存储主函数，协调完成记忆的存储流程
        
        Args:
            question: 问题内容
            answer: 答案内容
            user_id: 用户ID
            agent_id: 智能体ID
            session_id: 会话ID
            
        Returns:
            记忆ID，如果存储过程中发生异常则返回None
        """
        memory_id = None
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 输入参数校验
            self._validate_inputs(question, answer, user_id, agent_id, session_id)
            logger.info("输入参数校验通过")
            
            # 生成时间戳
            ntime = int(time.time())
            ntime_str = datetime.fromtimestamp(ntime).strftime("%Y-%m-%d %H:%M:%S")

            # 生成统一记忆ID
            memory_id = str(uuid.uuid1())
            logger.info(f"生成记忆ID: {memory_id}")
            
            # 生成记忆数据体
            memory_body = self._creart_memory_body(
                memory_id, question, answer, user_id, agent_id, session_id, ntime, ntime_str
            )
            
            # ES文本存储
            self._es_add_memory(memory_body)
            
            # 记忆向量化
            memory_body = self._memory_embedding(memory_body)
            
            # Milvus向量存储
            self._milvus_add_memory(memory_body)
            
            # MySQL初始化
            self._mysql_memory_init(memory_body)
            
            # 记录结束时间和耗时
            end_time = time.time()
            logger.info(f"记忆存储完成，耗时: {(end_time - start_time) * 1000:.2f}ms, 记忆ID: {memory_id}")
            return memory_id
            
        except ValueError as ve:
            logger.error(f"记忆存储参数错误: {str(ve)}")
        except Exception as e:
            logger.error(f"记忆存储过程中发生异常: {str(e)}")
        return None


class MemoryRecall:
    """
    记忆检索服务类，提供基于文本和向量的混合检索功能，支持多源数据融合与检索结果处理
    """
    def __init__(self, db_config: Dict[str, Any], memory_type: str = "SHORT_TERM"):
        """
        初始化记忆存储服务，建立与各数据库的连接
        
        Args:
            db_config: 数据库配置信息，包含ES、Milvus和MySQL的连接参数
            memory_type: 记忆类型，"SHORT_TERM" 或 "LONG_TERM"，默认为短期记忆
        """        
        try:
            # 根据记忆类型获取对应的数据库配置
            self.memory_type = memory_type
            memory_db_config = get_memory_db_config(memory_type)
            
            # 从配置中获取索引和集合名称
            self.es_index = memory_db_config['ES']['index_name']
            self.milvus_collection = memory_db_config['MILVUS']['collection_name']
            self.mysql_stats_table = memory_db_config['MYSQL']['stats_table']
            self.ES_CONFIG = memory_db_config['ES']
            self.MILVUS_CONFIG = memory_db_config['MILVUS']
            
            # 连接Elasticsearch，添加异常处理
            logger.info("正在连接Elasticsearch...")
            self.es = EsClient().get_connect()
            if not self.es:
                raise ConnectionError("Elasticsearch连接对象为空")
            logger.info("Elasticsearch连接成功")
            
            # 连接Milvus
            logger.info("正在连接Milvus...")
            MilvusClient()._connect()
            self.collection = Collection(name=self.milvus_collection)
            self.collection.load()
            logger.info(f"成功连接到Milvus，集合: {self.milvus_collection}")
            
            # 连接MySQL
            logger.info("正在连接MySQL...")
            self.mysql = MySQLClient().get_connect()
            if not self.mysql or not self.mysql.is_connected():
                raise ConnectionError("MySQL连接失败或未连接")
            logger.info("MySQL连接成功")
            
            # 加载向量化服务
            logger.info("正在加载向量化服务...")
            self.embedding_service = EmbeddingService()
            logger.info("向量化服务加载成功")
            
        except Exception as e:
            logger.error(f"初始化MemoryRecall失败: {str(e)}", exc_info=True)
            # 释放已连接的资源
            self._release_resources()
            raise
    
    def _release_resources(self):
        """释放已获取的数据库资源"""
        try:
            if self.collection:
                self.collection.release()
                logger.info(f"释放Milvus集合: {self.milvus_collection}")
            if self.mysql and self.mysql.is_connected():
                self.mysql.close()
                logger.info("关闭MySQL连接")
        except Exception as e:
            logger.warning(f"释放资源时发生异常: {str(e)}")
    
    def _get_recall_model_parms(self, model_id: str) -> Dict[str, Any]:
        """
        获取记忆模型参数
        
        Args:
            model_id: 记忆体模型ID，用于从数据库获取对应的检索参数
            
        Returns:
            包含模型参数的字典
            
        Raises:
            Exception: 当数据库查询失败或结果格式不正确时抛出
        """
        
        sql = f"""
        SELECT parms FROM memo_retrieval_model_parms_info
        WHERE model_id = "{model_id}";
        """
        
        try:
            if not self.mysql or not self.mysql.is_connected():
                raise ConnectionError("MySQL未连接，无法获取模型参数")
                
            cursor = self.mysql.cursor()
            cursor.execute(sql)
            records = cursor.fetchone()
            cursor.close()
            
            if not records:
                raise ValueError(f"未找到模型ID为 {model_id} 的参数记录")
                
            return json.loads(records[0])
        except Exception as e:
            logger.error(f"获取模型参数失败，model_id: {model_id}, 错误: {str(e)}", exc_info=True)
            raise
    
    def _get_recall_sapce(self, s: Optional[str], l: Optional[List[str]]) -> List[str]:
        """
        获得检索空间，合并单个元素和列表元素并去重，保持顺序
        
        Args:
            s: 单个检索空间元素，如单个用户ID或智能体ID
            l: 检索空间元素列表，如用户ID列表或智能体ID列表
            
        Returns:
            去重后的检索空间列表，保持元素添加顺序
        """
        recall_space = []
        
        if s is not None:
            recall_space.append(s)
        if l is not None and isinstance(l, list):
            recall_space.extend(l)
    
        # 使用OrderedDict去重并保持顺序
        return list(OrderedDict.fromkeys(recall_space))
    
    def _get_es_query_body(self, query: str, recall_space: Dict[str, List[str]], parms: Dict[str, Any], use_and_logic: bool = False) -> Dict[str, Any]:
        """
        构建ES查询逻辑，ES检索逻辑在数据层进行
        
        Args:
            query: 用户查询问题文本
            recall_space: 检索空间字典，包含user、scene、session三个维度的检索ID列表
            parms: 检索参数，包含TOP_N、各种因子开关及参数
            use_and_logic: 是否使用AND逻辑组合检索条件，默认为False(使用OR逻辑)
            
        Returns:
            符合Elasticsearch DSL格式的查询体字典，包含查询逻辑、过滤条件和权重因子
        """
        query_body = {
            "query": {
                "function_score": {
                    "query": {
                        "bool": {
                            # 必须匹配的条件
                            "must": [{
                                "multi_match": {
                                    "query": query,
                                    "fields": ["question^3", "answer"],
                                    "type": "phrase",
                                    "slop": 3
                                }
                            }],
                            # 应该匹配的条件
                            "should": [],
                            "minimum_should_match": 0
                        }
                    },
                    # 设置因子为检索结果加权
                    "functions": [],
                    "max_boost": 10,
                    "score_mode": "multiply",
                    "boost_mode": "multiply"
                }
            },
            "size": parms["TOP_N"]
        }

        # 根据条件确定使用must还是should
        filter_type = "must" if use_and_logic else "should"
        query_filter = query_body["query"]["function_score"]["query"]["bool"][filter_type]
        
        # 如果使用OR逻辑，设置最小匹配数为1
        if not use_and_logic and filter_type == "should":
            query_body["query"]["function_score"]["query"]["bool"]["minimum_should_match"] = 1
        
        # 添加用户ID过滤条件
        if len(recall_space['user']) > 0:
            query_filter.append(
                {"terms": {"user_id": recall_space['user']}}
            )
        
        # 添加智能体ID过滤条件
        if len(recall_space['scene']) > 0:
            query_filter.append(
                {"terms": {"agent_id": recall_space['scene']}}
            )
        
        # 添加会话ID过滤条件
        if len(recall_space['session']) > 0:
            query_filter.append(
                {"terms": {"session_id": recall_space['session']}}
            )
            
        # 添加权重因子
        query_function = query_body["query"]["function_score"]["functions"]
       
        if parms["TIME_FACTOR"]:
            query_function.append({
                "gauss": {
                    "update_time_str": {
                        "origin": parms["TIME_FACTOR_PARMS"]["ORIGIN"].lower(),
                        "scale": parms["TIME_FACTOR_PARMS"]["SCALE"].lower(),
                        "decay": parms["TIME_FACTOR_PARMS"]["DECAY"]
                    }
                }
            })
           
        if parms["WEIGHT_FACTOR"]:
            query_function.append({
                "field_value_factor": {
                    "field": "search_count",
                    "factor": parms["WEIGHT_FACTOR_PARMS"]["FACTOR"],
                    "modifier": parms["WEIGHT_FACTOR_PARMS"]["MODE"].lower(),
                    "missing": 1
                }
            })
       
        if parms["SCORE_FACTOR"]:
            query_function.append({
                "field_value_factor": {
                    "field": "memory_score",
                    "factor": parms["SCORE_FACTOR_PARMS"]["FACTOR"],
                    "modifier": parms["SCORE_FACTOR_PARMS"]["MODE"].lower(),
                    "missing": 1
                }
            })
        
        return query_body
    
    
    def _es_memory_recall(self, es_query_body: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        执行Elasticsearch文本检索
        
        Args:
            es_query_body: 构建好的ES查询体
            
        Returns:
            检索结果列表，每个元素包含memory_id、score、question、answer和source字段
            
        Raises:
            Exception: 当ES查询失败时抛出
        """
        try:
            if not self.es:
                raise ConnectionError("Elasticsearch客户端未初始化")
                
            response = self.es.search(index=self.es_index, body=es_query_body)
            
            result = []
            for line in response['hits']['hits']: 
                result.append({
                    "memory_id": line['_id'],
                    "score": line['_score'],
                    "question": line['_source']['question'],
                    "answer": line['_source']['answer'],
                    "source": 'es'
                })
            
            return result
        except Exception as e:
            logger.error(f"ES文本检索失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _check_question_embedding(self, query_vector: Optional[List[float]], n: int = 1024) -> bool:
        """
        检查问题向量化结果是否有效
        
        Args:
            query_vector: 问题向量化结果列表
            n: 期望的向量维度，默认为1024
            
        Returns:
            向量是否有效的布尔值
        """
        if query_vector is None:
            return False
        if not isinstance(query_vector, list):
            return False
        return len(query_vector) == n

    def _question_embedding(self, query: str) -> List[float]:
        """
        对问题进行向量化处理
        
        Args:
            query: 用户问题文本
            
        Returns:
            问题的向量化表示，维度为1024的浮点列表
            
        Raises:
            Exception: 当向量化服务调用失败时抛出
        """
        try:
            if not self.embedding_service:
                raise ValueError("向量化服务未初始化")
                
            # 生成问题向量
            question_vec = self.embedding_service.embedding_query(query)
            return np.array(question_vec, dtype=np.float32).tolist()
        except Exception as e:
            logger.error(f"问题向量化失败，query: {query}, 错误: {str(e)}", exc_info=True)
            raise
    
    def _milvus_memory_recall(self, 
                              query_vector: List[float], 
                              recall_space,
                              use_and_logic = True,
                              n: int = 500) -> tuple:
        """
        执行Milvus向量检索
        
        Args:
            query_vector: 问题向量化结果
            n: 检索返回的最大结果数，默认为500
            
        Returns:
            包含四个列表的元组：memory_ids(记忆ID)、distances(距离)、memory_scores(记忆分数)、update_times(更新时间)
            
        Raises:
            Exception: 当Milvus检索失败时抛出
        """
        try:
            if not self.collection:
                raise ConnectionError("Milvus集合未初始化")
                
            # 搜索参数
            search_params = {
                "metric_type": "IP",
                "params": {"ef": 512}
            }
            
            output_fields = ["memory_id", "user_id", "agent_id", "session_id", "update_time", "memory_score"]
            
            # 构建过滤条件表达式
            conditions = []

            user_ids = recall_space['user']
            agent_ids = recall_space['scene']
            session_ids = recall_space['session']
            # 添加 user_id 条件
            if user_ids:
                user_condition = f"user_id in {user_ids}"
                conditions.append(user_condition)

            # 添加 agent_id 条件
            if agent_ids:
                scene_condition = f"agent_id in {agent_ids}"
                conditions.append(scene_condition)

            # 添加 session_id 条件
            if session_ids:
                session_condition = f"session_id in {session_ids}"
                conditions.append(session_condition)

            # 组合条件
            if conditions:
                if use_and_logic:
                    # 使用 AND 逻辑组合条件
                    expr = " && ".join(conditions)
                else:
                    # 使用 OR 逻辑组合条件
                    expr = " || ".join(conditions)
            else:
                # 没有条件时，expr 为 None
                expr = None

            # Debug: print filter expression and schema
            logger.info(f"Milvus filter expr: {expr}")
            try:
                logger.info(f"Milvus collection schema: {[field.name + ':' + str(field.dtype) for field in self.collection.schema.fields]}")
            except Exception as e:
                logger.warning(f"Could not print Milvus schema: {e}")

            # 执行搜索
            self.collection.load()
            results = self.collection.search(
                data=[query_vector],
                anns_field="question_emb",  # 用户问题向量字段
                param=search_params,
                limit=n,
                expr=expr,                  # 应用检索空间过滤
                output_fields=output_fields
            )
            hits = results[0]

            def get_entity_field(entity, field, default=None):
                try:
                    return entity[field]
                except (KeyError, TypeError):
                    pass
                try:
                    return getattr(entity, field)
                except AttributeError:
                    pass
                return default

            memory_ids = [get_entity_field(hit.entity, 'memory_id', hit.id) for hit in hits]
            distances = [hit.distance for hit in hits]
            memory_scores = [get_entity_field(hit.entity, 'memory_score', 1.0) for hit in hits]
            update_times = [get_entity_field(hit.entity, 'update_time', int(time.time())) for hit in hits]
            
            return memory_ids, distances, memory_scores, update_times
        except Exception as e:
            logger.error(f"Milvus向量检索失败，错误: {str(e)}", exc_info=True)
            raise
        finally:
            # 释放集合资源
            try:
                if self.collection:
                    self.collection.release()
            except Exception as e:
                logger.warning(f"释放Milvus集合时发生异常: {str(e)}")
    
    def _factor_gauss_decay(self, values: List[float], short_parms: Dict[str, Any], col: str = "TIME_FACTOR") -> List[float]:
        """
        时间因子: 实现高斯衰退函数
        
        Args:
            values: 时间值列表（时间戳）
            short_parms: 短时检索参数
            col: 因子类型，默认为时间因子
            
        Returns:
            高斯衰退后的得分列表，与输入列表长度一致
            
        Raises:
            Exception: 当参数解析或计算失败时抛出
        """
        try:
            # 启用时间因子
            if short_parms[col]:
                
                # 衰退起点
                origin = short_parms[f"{col}_PARMS"]["ORIGIN"].lower()
                
                if origin in ("now", "0d"):
                    origin = time.time()
                else:
                    try:
                        days = int(origin[:-1])
                        origin = time.time() - days * 3600 * 24
                    except Exception as e:
                        raise ValueError(f"时间原点格式错误: {origin}") from e
                
                # 衰退范围
                scale = short_parms[f"{col}_PARMS"]["SCALE"].lower()
                try:
                    scale = int(scale[:-1])
                except Exception as e:
                    raise ValueError(f"时间尺度格式错误: {scale}") from e
                
                # 缓冲范围
                offset = short_parms[f"{col}_PARMS"]["DECAY"]
                
                # 计算与原点的时间差（天）
                day_diffs = np.array([(origin - v) / 3600 / 24 for v in values], dtype=float)
                distances = np.abs(day_diffs)
            
                # 处理offset缓冲范围
                within_offset = distances <= offset
                distances[within_offset] = 0
            
                # 高斯衰退计算
                decay_scores = np.exp(-(distances ** 2) / (2 * (scale ** 2)))
                decay_scores[within_offset] = 1.0
            
                return decay_scores.tolist()
            
            else:
                # 不启用时间因子，权重赋1
                return [1.0] * len(values)
        except Exception as e:
            logger.error(f"高斯衰退因子计算失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _factor_weight(self, values: List[float], short_parms: Dict[str, Any], col: str) -> List[float]:
        """
        对列表元素应用指定的数学变换函数进行因子加工
        
        Args:
            values: 输入数值列表
            short_parms: 短时检索参数
            col: 因子类型，如权重因子或评分因子
            
        Returns:
            变换后的得分列表，与输入列表长度一致
            
        Raises:
            Exception: 当参数解析或计算失败时抛出
        """
        try:
            # 启用因子
            if short_parms[col]:
                
                # 缩放因子
                factor = short_parms[f"{col}_PARMS"]["FACTOR"]
                
                # 变换函数
                modifier = short_parms[f"{col}_PARMS"]["MODE"].lower()

                # 检查modifier函数
                valid_modifiers = {
                    'log': np.log,
                    'log1p': np.log1p,
                    'log2p': lambda x: np.log2(x + 1),
                    'ln': np.log,
                    'ln1p': np.log1p,
                    'ln2p': lambda x: np.log(x + 1) / np.log(2),
                    'square': lambda x: x ** 2,
                    'sqrt': np.sqrt,
                    'reciprocal': lambda x: 1.0 / x if x != 0 else 0
                }
                
                if modifier not in valid_modifiers:
                    raise ValueError(f"不支持的变换函数: {modifier}")
                
                # 获取对应的变换函数
                mode = valid_modifiers[modifier]
                
                # 向量化计算提高效率
                values_np = np.array(values, dtype=float)
                scaled_values = values_np * factor
                
                weighted_scores = mode(scaled_values)
    
                return weighted_scores.tolist()
            
            else:
                # 不启用因子，权重赋1
                return [1.0] * len(values)
        except Exception as e:
            logger.error(f"权重因子计算失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _factor_weight_from_mysql(self, memory_ids: List[str], short_parms: Dict[str, Any], col: str, ftr: str) -> List[float]:
        """
        从Mysql获取记忆特征，并进行因子加工
        
        Args:
            memory_ids: 记忆ID列表
            short_parms: 短时检索参数
            col: 因子类型，如评分因子
            ftr: 数据库中的特征字段名，如recall_stas
            
        Returns:
            因子加工后的得分列表，与输入列表长度一致
            
        Raises:
            Exception: 当数据库查询或因子计算失败时抛出
        """
        try:
            # 启用因子
            if short_parms[col]:
                
                # 按memory_id取检索计数
                if not memory_ids:
                    return [1.0]
                
                me0 = reduce(lambda x, y: f"{x},'{y}'" if x else f"'{y}'", memory_ids, "")
                sql = f"SELECT memory_id, {ftr} FROM {self.mysql_stats_table} WHERE memory_id in ({me0});"

                # 执行SQL语句       
                with self.mysql.cursor() as cursor:
                    cursor.execute(sql)
                    results = cursor.fetchall()
                
                # 现在将mysql查询返回的结果，按照memory_ids排序输出
                id_value_dict = dict(results)
                results = [id_value_dict.get(id_, 0) for id_ in memory_ids]
                
                # 检查结果列表长度是否与memory_ids匹配
                if len(results) != len(memory_ids):
                    logger.warning(f"MySQL返回的结果数量({len(results)})与请求的ID数量({len(memory_ids)})不匹配，使用默认值填充")
                    # 确保结果与memory_ids长度一致
                    results = [id_value_dict.get(id_, 0) for id_ in memory_ids]
                
                # 对返回结果应用加权函数
                return self._factor_weight(results, short_parms, col)

            else:
                # 不启用因子，权重赋1
                return [1.0] * len(memory_ids)
        except Exception as e:
            logger.error(f"从MySQL获取特征并计算因子失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _sort_by_product_numpy(self,
                               memory_ids: List[str],
                               distances: List[float],
                               time_factor: List[float],
                               weight_factor: List[float],
                               score_factor: List[float],
                               short_parms: Dict[str, Any]) -> tuple:
        """
        基于numpy向量化计算，按多因子乘积排序
        
        Args:
            memory_ids: 记忆ID列表
            distances: 距离列表
            time_factor: 时间因子列表
            weight_factor: 权重因子列表
            score_factor: 评分因子列表
            short_parms: 短时检索参数，包含TOP_N
            
        Returns:
            包含排序后的记忆ID列表和对应乘积得分列表的元组
            
        Raises:
            Exception: 当输入列表长度不一致或计算失败时抛出
        """
        try:
            # 检查输入列表长度一致性
            lists = [memory_ids, distances, time_factor, weight_factor, score_factor]
            if not all(len(l) == len(memory_ids) for l in lists):
                raise ValueError("输入列表长度不一致")
                
            # 转换为numpy数组
            distances_np = np.array(distances)
            time_factor_np = np.array(time_factor)
            weight_factor_np = np.array(weight_factor)
            score_factor_np = np.array(score_factor)
            memory_ids_np = np.array(memory_ids)
            
            # 向量化计算乘积，距离作为相似度（距离越小相似度越高）
            product = distances_np * time_factor_np * weight_factor_np * score_factor_np
    
            # 按乘积降序排序的索引
            sorted_indices = np.argsort(-product)  # -product表示降序排序
    
            # 根据索引排序
            sorted_id = memory_ids_np[sorted_indices].tolist()
            sorted_product = product[sorted_indices].tolist()
            
            return sorted_id[:short_parms["TOP_N"]], sorted_product[:short_parms["TOP_N"]]
        except Exception as e:
            logger.error(f"多因子乘积排序失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _get_data_by_ids(self, id_list: Optional[List[str]] = None, sc_list: Optional[List[float]] = None) -> List[Dict[str, Any]]:
        """
        根据records id 获取数据
        
        Args:
            id_list: 记忆ID列表
            sc_list: 对应的得分列表，与id_list顺序一致
            
        Returns:
            包含记忆详细信息的字典列表，每个字典包含memory_id、score、question、answer和source字段
            
        Raises:
            Exception: 当ES查询失败时抛出
        """
        try:
            if not id_list or len(id_list) == 0:
                return []
                
            mget_params = {
                'ids': id_list
            }
            
            resutls = self.es.mget(index=self.es_index, body=mget_params)
            
            output = list()
            if sc_list:
                id_to_sc = dict(zip(id_list, sc_list))
            else:
                id_to_sc = {id_: 1.0 for id_ in id_list}
                
            for res in resutls["docs"]:
                temp = dict()
                if res["found"]:
                    temp["memory_id"] = res["_id"]
                    temp["score"] = id_to_sc.get(temp["memory_id"], 1.0)
                    temp["question"] = res["_source"]["question"]
                    temp["answer"] = res["_source"]["answer"]
                    temp["source"] = 'emd'
                    output.append(temp)
            
            return output
        except Exception as e:
            logger.error(f"根据ID获取数据失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _hybrid_merge_results(self, 
                              es_results: List[Dict[str, Any]], 
                              emd_results: List[Dict[str, Any]], 
                              short_parms: Dict[str, Any], 
                              es_weight: float = 0.5, 
                              overlap_boost: float = 1.2) -> List[Dict[str, Any]]:
        """
        合并并排序 Elasticsearch 和 Embedding 模型的搜索结果
        
        Args:
            es_results: Elasticsearch 搜索结果列表
            emd_results: Embedding 模型搜索结果列表
            short_parms: 短时检索参数，包含TOP_N
            es_weight: Elasticsearch 结果权重，范围 [0,1]，默认为0.5
            overlap_boost: 重叠结果的增强系数，默认为1.2
            
        Returns:
            排序后的结果列表，包含最终得分和来源信息，按得分降序排列，最多返回TOP_N条结果
        """
        try:
            top_n = short_parms['TOP_N']
            # 创建字典存储结果，键为memory_id
            es_dict = {item['memory_id']: item for item in es_results}
            emd_dict = {item['memory_id']: item for item in emd_results}
        
            # 存储合并后的结果
            merged_results = []
        
            # 处理重叠的memory_id
            for memory_id in set(es_dict.keys()) & set(emd_dict.keys()):
                es_item = es_dict[memory_id]
                emd_item = emd_dict[memory_id]
            
                # 计算混合得分
                mixed_score = overlap_boost * (es_item['score'] * es_weight + (1 - es_weight) * emd_item['score'])
                final_score = min(mixed_score, 1.0)  # 确保得分不超过1
            
                # 添加到结果列表
                merged_results.append({
                    'memory_id': memory_id,
                    'score': final_score,
                    'question': es_item.get('question', emd_item.get('question', '')),
                    'answer': es_item.get('answer', emd_item.get('answer', '')),
                    'source': 'hybrid'
                })
        
            # 处理仅在ES结果中的memory_id
            for memory_id in set(es_dict.keys()) - set(emd_dict.keys()):
                es_item = es_dict[memory_id]
            
                # 计算ES得分
                final_score = es_item['score'] * es_weight
            
                # 添加到结果列表
                merged_results.append({
                    'memory_id': memory_id,
                    'score': final_score,
                    'question': es_item.get('question', ''),
                    'answer': es_item.get('answer', ''),
                    'source': 'es'
                })
        
            # 处理仅在Embedding结果中的memory_id
            for memory_id in set(emd_dict.keys()) - set(es_dict.keys()):
                emd_item = emd_dict[memory_id]
            
                # 计算Embedding得分
                final_score = (1 - es_weight) * emd_item['score']
            
                # 添加到结果列表
                merged_results.append({
                    'memory_id': memory_id,
                    'score': final_score,
                    'question': emd_item.get('question', ''),
                    'answer': emd_item.get('answer', ''),
                    'source': 'emd'
                })
        
            # 根据最终得分降序排序
            sorted_results = sorted(merged_results, key=lambda x: x['score'], reverse=True)
            
            return sorted_results[:top_n]
        except Exception as e:
            logger.error(f"混合结果合并排序失败，错误: {str(e)}", exc_info=True)
            raise
    
    def _memory_compress(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对记忆结果进行压缩，提取关键信息
        
        Args:
            results: 完整的记忆检索结果列表
            
        Returns:
            压缩后的记忆结果列表，每个元素包含简化的question、answer、memory_id和score
        """
        compress_results = list()
        
        for item in results:
            temp = dict()
            # 压缩方法：取前10个字符
            temp['question_cps'] = abstract_by_txt_rank(item['question'], summary_length=80) if item.get('question') else ""
            temp['answer_cps'] = abstract_by_txt_rank(item['answer'], summary_length=80) if item.get('answer') else ""
            temp['memory_id'] = item['memory_id']
            temp['score'] = item['score']
            compress_results.append(temp)
        
        return compress_results
            
    def _memory_rewrite(self, compress_results: List[Dict[str, Any]]) -> str:
        """
        重写PROMPT，整合压缩后的记忆信息
        
        Args:
            compress_results: 压缩后的记忆结果列表
            
        Returns:
            重写后的PROMPT文本，包含历史问答关键信息和指令要求
        """
        short_memo = ""
        
        for i, item in enumerate(compress_results, 1):
            question = item.get('question_cps', '')
            answer = item.get('answer_cps', '')
            
            short_memo += f"历史对话 {i} 中，用户提问: {question} ; 模型回答: {answer}; \n"
        
        template = f"""
        【用户历史问答中的关键信息】
        {short_memo}
        【指令要求】​
        请结合上述历史问答中的关键信息，理解用户提问的背景、意图和需求，以 [具体风格，如专业严谨 / 通俗易懂] 的语言，提供全面且有针对性的回答。
        若历史问答中有相关信息，需整合进回答中进行关联拓展；若无相关信息，依据专业知识和逻辑作答。
        """
        
        return template
        
    def _update_recall_stats(self, results: List[Dict[str, Any]], MYSQL: bool = True, ES: bool = True):
        """
        更新检索计数，记录记忆被检索的次数
        
        Args:
            results: 检索结果列表
            MYSQL: 是否更新MySQL，默认为True
            ES: 是否更新ES，默认为True
            
        Raises:
            Exception: 当数据库更新失败时抛出
        """
        try:
            # 更新时间
            update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            now_timestamp = int(time.time() * 1000)
            now_date = update_time[:10]
            
            # Mysql更新模版
            SQL_UPDATE = f"""
            UPDATE {self.mysql_stats_table}
            SET update_time='{{}}', recall_stas=IFNULL(recall_stas,0)+1, {{}}=IFNULL({{}},0)+1
            WHERE memory_id in ({{}})
            ;
            """
            
            # ES更新模版
            ES_UPDATE = {
                "script": {
                    "source": "ctx._source.search_count += 1; ctx._source.update_time = params.now; ctx._source.update_time_str = params.nowstr",
                    "params": {"now": now_timestamp,
                               "nowstr": now_date
                               }
                }
            }

            # 分类需要更新的记忆ID
            es_update = [item['memory_id'] for item in results if item['source'] == 'es']
            emd_update = [item['memory_id'] for item in results if item['source'] == 'emd']
            hybrid_update = [item['memory_id'] for item in results if item['source'] == 'hybrid']
            
            if MYSQL:
                if len(es_update) > 0:
                    me0 = reduce(lambda x, y: f"{x},'{y}'" if x else f"'{y}'", es_update, "")
                    # 执行SQL语句
                    with self.mysql.cursor() as cursor:
                        cursor.execute(SQL_UPDATE.format(update_time, 'recall_stas_txt', 'recall_stas_txt', me0))
                        self.mysql.commit()
                    logger.info("记忆检索计数更新 MYSQL TXT")
                    
                if len(emd_update) > 0:
                    me0 = reduce(lambda x, y: f"{x},'{y}'" if x else f"'{y}'", emd_update, "")
                    # 执行SQL语句
                    with self.mysql.cursor() as cursor:
                        cursor.execute(SQL_UPDATE.format(update_time, 'recall_stas_vct', 'recall_stas_vct', me0))
                        self.mysql.commit()
                    logger.info("记忆检索计数更新 MYSQL VCT")
                    
                if len(hybrid_update) > 0:
                    me0 = reduce(lambda x, y: f"{x},'{y}'" if x else f"'{y}'", hybrid_update, "")
                    # 执行SQL语句
                    with self.mysql.cursor() as cursor:
                        cursor.execute(SQL_UPDATE.format(update_time, 'recall_stas_mlt', 'recall_stas_mlt', me0))
                        self.mysql.commit()
                    logger.info("记忆检索计数更新 MYSQL MLT")
                    
            if ES and self.es:
                # 构建批量更新操作
                actions = [
                    {
                        "_op_type": "update",  # 操作类型为更新
                        "_index": self.es_index,   # 目标索引
                        "_id": doc_id,          # 文档ID
                        "_source": ES_UPDATE  # 更新内容
                    }
                    for doc_id in es_update
                ]

                try:
                    response = helpers.bulk(self.es, actions)
                    logger.info(f"成功更新 {response[0]} 个ES文档的检索计数")
                except Exception as e:
                    logger.error(f"批量更新ES检索计数失败: {str(e)}", exc_info=True)
        except Exception as e:
            logger.error(f"更新检索计数失败，错误: {str(e)}", exc_info=True)
            raise
    
    def memory_recall(self, 
                      query: str, 
                      model_id: str,

                      user_id: Optional[Union[str, List[str]]] = None, 
                      agent_id: Optional[Union[str, List[str]]] = None, 
                      session_id: Optional[Union[str, List[str]]] = None,

                      query_vector: Optional[List[float]] = None,  

                      ast_user_id: Optional[List[str]] = None, 
                      ast_agent_id: Optional[List[str]] = None, 
                      ast_session_id: Optional[List[str]] = None,
                      
                      use_and_logic: bool = False
                      
                      ) -> Optional[tuple]:
        """
        统一召回接口，支持文本检索、向量检索和混合检索模式
        
        记忆检索的流程：
        1）定义检索空间；2）算法召回历史记忆内容；3）压缩并将记忆注入提示词
        
        一、记忆检索空间：
        
        记忆空间包含3个维度（用户、场景、会话），记忆检索时可将不同空间内的记忆进行关联。
        支持n对n的关系 - 可以有多个用户关联到多个场景和多个会话
        
        ps：可以将其他用户的记忆与当前用户关联；也可将当前场景内所有用户的记忆进行关联；
        

        二、记忆召回算法：
        
        根据输入的问题query，在历史问答数据中检索，取回相似性（S）最高的 top n 的问题与答案。
        
        相似性（S）的计算公式：
        S = Standard( Simility * ( a * TimeDecay ) * ( b * Freq ) * ( c * Score ) )
        
        Simility : 基于文本与向量相似度计算的结果（如文本检索中BM2.5算法计算的相似度与向量检索中的内积相似度）
        TimeDecay : 时间衰减因子，模拟记忆衰减曲线，记忆更新时间越久，则 TimeDecay 越低，可实现对新老记忆的权重调整。
        Freq : 检索频率权重因子，当记忆被召回的次数越高，则 Freq 越高
        Score : 记忆评分权重因子，使用大模型对问答内容打分，具体且明确则 Score 更高，通用或模糊则 Score 低
        a b c : 控制因子的影响度的参数（缩放因子）
        Standard : 标准化函数
        
        * 控制单一因子的影响力上限 : 
            1）通过函数变换实现（_factor_weight方法）；
            2）通过直接限制计算结果上下限。
        * 停用因子：
            1）通过配置参数启用/停用因子；
            2）停用时，因子返回结果 = 1；
        
        三、记忆压缩与注入：
        
        压缩：记忆召回结果为问题-答案文本，通过文本处理算法进行简化提取关键信息的操作。
        
        注入：将压缩后的记忆添加进大模型提示词中，或当作历史上下文参数使用。
        
        -----------------------------------------------------------------------
        
        Args:
            query: 用户问题文本
            model_id: 记忆体模型ID，用于取检索模型参数
            user_id: 用户ID或用户ID列表，支持传入单个字符串或字符串列表
            agent_id: 可选，智能体ID或智能体ID列表，支持传入单个字符串或字符串列表
            session_id: 可选，会话ID或会话ID列表，支持传入单个字符串或字符串列表
            query_vector: 可选，向量化的用户问题，若未提供则自动生成
            ast_user_id: 可选，额外的用户ID列表，可关联其中用户的记忆
            ast_agent_id: 可选，额外的智能体ID列表，可关联其场景中其他用户的记忆
            ast_session_id: 可选，额外的会话ID列表，可关联其会话中用户的记忆
            use_and_logic: 可选，是否使用AND逻辑组合检索条件，默认为False(使用OR逻辑)
            
        Returns:
            包含记忆改写后的Prompt文本和压缩结果的元组，用于后续的回答生成
            
        Raises:
            Exception: 当检索过程中发生任何异常时抛出
            
        TEST:
            query = "银行卡密码忘记了怎么重置？"
            model_id = "MEMO_21918"
            user_id = ["customer01", "customer02"]
            agent_id = ["ZNKF", "BKRF"]
            session_id = ["customer01_001", "customer02_001"]
            query_vector = None
            
        """
        try:
            # 记录开始时间
            start_time = time.time()
            logger.info(f"开始检索: 【query】{query} 【model_id】{model_id}")
            
            # 定义检索空间（检索空间 = 用户 ∪ 场景 ∪ 会话）
            recall_space = dict()
            
            # 将单个ID和列表ID统一处理
            def normalize_ids(id_value, additional_ids=None):
                result = []
                # 处理主ID参数 - 可能是字符串或列表
                if id_value is not None:
                    if isinstance(id_value, str):
                        result.append(id_value)
                    elif isinstance(id_value, list):
                        result.extend(id_value)
                
                # 处理额外ID列表
                if additional_ids is not None and isinstance(additional_ids, list):
                    result.extend(additional_ids)
                
                # 使用OrderedDict去重并保持顺序
                return list(OrderedDict.fromkeys(result))
            
            # 统一处理三个维度的ID
            recall_space['user'] = normalize_ids(user_id, ast_user_id)
            recall_space['scene'] = normalize_ids(agent_id, ast_agent_id)
            recall_space['session'] = normalize_ids(session_id, ast_session_id)
            
            # 获取模型参数
            model_parms = self._get_recall_model_parms(model_id)
            
            # 模型参数解析
            # 启用短时检索
            if model_parms['SHORT_TERM']:
                # 使用短期记忆数据库
                if self.memory_type != "SHORT_TERM":
                    logger.info(f"切换到短期记忆数据库进行检索")
                    # 重新初始化短期记忆数据库连接
                    memory_db_config = get_memory_db_config("SHORT_TERM")
                    self.es_index = memory_db_config['ES']['index_name']
                    self.milvus_collection = memory_db_config['MILVUS']['collection_name']
                    self.mysql_stats_table = memory_db_config['MYSQL']['stats_table']
                    self.ES_CONFIG = memory_db_config['ES']
                    self.MILVUS_CONFIG = memory_db_config['MILVUS']
                    self.memory_type = "SHORT_TERM"
                logger.info("启用短时检索...")
                
                # 获取短时检索参数
                short_parms = model_parms['SHORT_TERM_MODEL']
                
                # 初始化检索结果
                short_recall = list()
                
                # 使用文本检索
                if short_parms['RETRIEVAL_TYPE'] in ('MLT', 'TXT'): 
                    logger.info("开始进行文本检索...")
                    
                    # 生成es查询逻辑（包含模型逻辑）
                    es_query_body = self._get_es_query_body(query, recall_space, short_parms, use_and_logic)
                    # es查询结果（模型加权后）
                    es_results = self._es_memory_recall(es_query_body)
                    
                    # 检查是否有结果返回
                    if not es_results:
                        logger.warning("ES文本检索未返回任何结果")
                    
                    logger.info("文本检索完成")
                    
                # 使用向量检索
                if short_parms['RETRIEVAL_TYPE'] in ('MLT', 'EMD'):
                    logger.info("开始进行向量检索...")
                    
                    # 1、问题向量化
                    # 校验query_vector是否入参
                    if not self._check_question_embedding(query_vector):
                        query_vector = self._question_embedding(query)
                    logger.info("向量化完成...")
                    
                    # 2、milvus查询结果（原始结果）
                    memory_ids, distances, memory_scores, update_times = self._milvus_memory_recall(
                        query_vector, 
                        recall_space,
                        use_and_logic=use_and_logic
                    )
                    
                    # 检查是否有结果返回
                    if not memory_ids:
                        logger.warning("Milvus向量检索未返回任何结果")
                        emd_results = []  # 设置为空列表
                    else:
                        # 3、计算时间衰减因子
                        time_factor = self._factor_gauss_decay(update_times, short_parms, "TIME_FACTOR")
                        # 4、计算权重因子
                        weight_factor = self._factor_weight_from_mysql(memory_ids, short_parms, 'SCORE_FACTOR', 'recall_stas')
                        # 5、计算评分因子
                        score_factor = self._factor_weight(memory_scores, short_parms, "SCORE_FACTOR")
                        # 6、对原始结果计算模型加权得分
                        emd_memory_id, emd_score = self._sort_by_product_numpy(memory_ids, distances, time_factor, weight_factor, score_factor, short_parms)
                        # 7、根据memory_id去es取回原始文本
                        emd_results = self._get_data_by_ids(emd_memory_id, emd_score)
                    logger.info("向量检索完成...")
                    
                # 混合检索结果
                if short_parms['RETRIEVAL_TYPE'] == 'MLT':
                    logger.info("开始进行混合检索...")
                    
                    # 1、混合排序
                    short_recall = self._hybrid_merge_results(es_results, emd_results, short_parms)
                    # 2、记忆压缩
                    compress_results = self._memory_compress(short_recall)
                    # 3、记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("混合检索完成...")
                    # 4、更新检索计数
                    self._update_recall_stats(short_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                # 文本检索结果
                elif short_parms['RETRIEVAL_TYPE'] == 'TXT':
                    short_recall = es_results
                    # 记忆压缩
                    compress_results = self._memory_compress(short_recall)
                    # 记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("文本检索完成...")
                    # 更新检索计数
                    self._update_recall_stats(short_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                # 向量检索结果
                elif short_parms['RETRIEVAL_TYPE'] == 'EMD':
                    short_recall = emd_results
                    # 记忆压缩
                    compress_results = self._memory_compress(short_recall)
                    # 记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("向量检索完成...")
                    # 更新检索计数
                    self._update_recall_stats(short_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                else:
                    raise ValueError(f"不支持的检索类型: {short_parms['RETRIEVAL_TYPE']}")
                
                # 记录结束时间和耗时
                end_time = time.time()
                logger.info(f"记忆检索完成，耗时: {(end_time - start_time) * 1000:.2f}ms")
                
                return template, compress_results
            
            # 长时记忆检索
            if model_parms.get('LONG_TERM', False):
                # 使用长期记忆数据库
                if self.memory_type != "LONG_TERM":
                    logger.info(f"切换到长期记忆数据库进行检索")
                    # 重新初始化长期记忆数据库连接
                    memory_db_config = get_memory_db_config("LONG_TERM")
                    self.es_index = memory_db_config['ES']['index_name']
                    self.milvus_collection = memory_db_config['MILVUS']['collection_name']
                    self.mysql_stats_table = memory_db_config['MYSQL']['stats_table']
                    self.ES_CONFIG = memory_db_config['ES']
                    self.MILVUS_CONFIG = memory_db_config['MILVUS']
                    self.memory_type = "LONG_TERM"
                logger.info("启用长时检索...")
                
                # 获取短时检索参数
                long_parms = model_parms['LONG_TERM_MODEL']
                
                # 初始化检索结果
                long_recall = list()
                
                # 使用文本检索
                if long_parms['RETRIEVAL_TYPE'] in ('MLT', 'TXT'): 
                    logger.info("开始进行文本检索...")
                    
                    # 生成es查询逻辑（包含模型逻辑）
                    es_query_body = self._get_es_query_body(query, recall_space, long_parms, use_and_logic)
                    # es查询结果（模型加权后）
                    es_results = self._es_memory_recall(es_query_body)
                    
                    # 检查是否有结果返回
                    if not es_results:
                        logger.warning("ES文本检索未返回任何结果")
                    
                    logger.info("文本检索完成")
                    
                # 使用向量检索
                if long_parms['RETRIEVAL_TYPE'] in ('MLT', 'EMD'):
                    logger.info("开始进行向量检索...")
                    
                    # 1、问题向量化
                    # 校验query_vector是否入参
                    if not self._check_question_embedding(query_vector):
                        query_vector = self._question_embedding(query)
                    logger.info("向量化完成...")
                    
                    # 2、milvus查询结果（原始结果）
                    memory_ids, distances, memory_scores, update_times = self._milvus_memory_recall(
                        query_vector, 
                        recall_space,
                        use_and_logic=use_and_logic
                    )
                    
                    # 检查是否有结果返回
                    if not memory_ids:
                        logger.warning("Milvus向量检索未返回任何结果")
                        emd_results = []  # 设置为空列表
                    else:
                        # 3、计算时间衰减因子
                        time_factor = self._factor_gauss_decay(update_times, long_parms, "TIME_FACTOR")
                        # 4、计算权重因子
                        weight_factor = self._factor_weight_from_mysql(memory_ids, long_parms, 'SCORE_FACTOR', 'recall_stas')
                        # 5、计算评分因子
                        score_factor = self._factor_weight(memory_scores, long_parms, "SCORE_FACTOR")
                        # 6、对原始结果计算模型加权得分
                        emd_memory_id, emd_score = self._sort_by_product_numpy(memory_ids, distances, time_factor, weight_factor, score_factor, long_parms)
                        # 7、根据memory_id去es取回原始文本
                        emd_results = self._get_data_by_ids(emd_memory_id, emd_score)
                    logger.info("向量检索完成...")
                    
                # 混合检索结果
                if long_parms['RETRIEVAL_TYPE'] == 'MLT':
                    logger.info("开始进行混合检索...")
                    
                    # 1、混合排序
                    long_recall = self._hybrid_merge_results(es_results, emd_results, long_parms)
                    # 2、记忆压缩
                    compress_results = self._memory_compress(long_recall)
                    # 3、记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("混合检索完成...")
                    # 4、更新检索计数
                    self._update_recall_stats(long_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                # 文本检索结果
                elif long_parms['RETRIEVAL_TYPE'] == 'TXT':
                    long_recall = es_results
                    # 记忆压缩
                    compress_results = self._memory_compress(long_recall)
                    # 记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("文本检索完成...")
                    # 更新检索计数
                    self._update_recall_stats(long_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                # 向量检索结果
                elif long_parms['RETRIEVAL_TYPE'] == 'EMD':
                    long_recall = emd_results
                    # 记忆压缩
                    compress_results = self._memory_compress(long_recall)
                    # 记忆注入
                    template = self._memory_rewrite(compress_results)
                    logger.info("向量检索完成...")
                    # 更新检索计数
                    self._update_recall_stats(long_recall, MYSQL=True, ES=True)
                    logger.info("更新检索计数...")
                    
                else:
                    raise ValueError(f"不支持的检索类型: {long_parms['RETRIEVAL_TYPE']}")
                
                # 记录结束时间和耗时
                end_time = time.time()
                logger.info(f"记忆检索完成，耗时: {(end_time - start_time) * 1000:.2f}ms")
                
                return template, compress_results
            
            # 记录结束时间和耗时
            end_time = time.time()
            logger.info(f"记忆检索完成，耗时: {(end_time - start_time) * 1000:.2f}ms")
            
        except Exception as e:
            logger.error(f"记忆检索过程中发生异常: {str(e)}", exc_info=True)
            raise
        finally:
            # 释放Milvus集合（如果已加载）
            try:
                if self.collection:
                    pass
                    # self.collection.release()
                    logger.info(f"释放Milvus集合: {self.milvus_collection}")
            except Exception as e:
                logger.warning(f"释放Milvus集合失败: {str(e)}")
        
        return None, None



if __name__ == "__main__":
    
    # 测试代码

    # import pandas as pd
    # test_data = pd.read_excel(r"C:\Users\<USER>\Desktop\Frank Job\信雅达\星舰project\agentflow\03程序\a源代码\3.算法侧服务\sunklm-algorithm\MemoryService_V1.0\测试数据2.xlsx")

    # 实例
    # MemoSave = MemorySave(db_config)
      
    
    """
    # 清空测试数据 并初始化数据库
    from common.conf.db_config import MILVUS_CONFIG, field_mapping
    from common.conf.db_config import ES_CONFIG, es_index_parm
    
    # 删除 ES索引
    es = EsClient().get_connect()
    es.indices.delete(index="memory_test_05")
    
    # 重建 ES索引
    es = EsClient()
    es._do_create_index(index_name="memory_test_05")
    
    
    # 删除 Milvus集合
    mil = MilvusClient()
    mil.do_drop_col("memory_test_02")
    
    # 重建 Milvus集合
    mil.do_create_col("memory_test_02", description="测试用问答集合", colType="QA_RECORDS")
    
    """
    
    
    # for i in range(len(test_data)):
        
    #     temp = test_data.loc[i,].to_dict()
        
    #     MemoSave.memory_save(
    #         question   = temp['question'],
    #         answer     = temp['answer'],
    #         user_id    = temp['user_id'],
    #         agent_id   = temp['agent_id'],
    #         session_id = temp['session_id']
    #         )
     
        
    # 测试记忆检索功能
    test = MemoryRecall(db_config, memory_type="SHORT_TERM")
    
    # 测试单用户对单场景单会话
    single_user_single_scene_single_session_result, _ = test.memory_recall(
        query="银行卡密码忘记了怎么重置？",
        model_id="MEMO_21918",
        user_id="C10001",
        agent_id="XYK",
        session_id="XYK_001",
        query_vector=None
    )

    # 测试多用户对多场景多会话（OR逻辑）
    multi_user_multi_scene_multi_session_or_result, _ = test.memory_recall(
        query="银行卡密码忘记了怎么重置？",
        model_id="MEMO_21918",
        user_id=["customer01", "customer02"],
        agent_id=["ZNKF", "BKRF"],
        session_id=["customer01_001", "customer02_001"],
        query_vector=None,
        use_and_logic=False  # 使用OR逻辑，任一条件匹配即可
    )

    # 测试多用户对多场景多会话（AND逻辑）
    multi_user_multi_scene_multi_session_and_result, _ = test.memory_recall(
        query="银行卡密码忘记了怎么重置？",
        model_id="MEMO_21918",
        user_id=["customer01", "customer02"],
        agent_id=["ZNKF", "BKRF"],
        session_id=["customer01_001", "customer02_001"],
        query_vector=None,
        use_and_logic=True  # 使用AND逻辑，需同时满足所有条件
    )

    # 新增：单用户多场景
    single_user_multi_scene_result, _ = test.memory_recall(
        query="银行卡密码忘记了怎么重置？",
        model_id="MEMO_21918",
        user_id="C10001",
        agent_id=["DK", "LC", "TS", "XYK"],
        session_id=None,
        query_vector=None
    )

    # 新增：单用户单场景多会话
    single_user_single_scene_multi_session_result, _ = test.memory_recall(
        query="银行卡密码忘记了怎么重置？",
        model_id="MEMO_21918",
        user_id="C10001",
        agent_id="XYK",
        session_id=["XYK_001", "XYK_002"],
        query_vector=None
    )

    # 用 logger.info 输出每个测试用例结果，中文描述
    logger.info("单用户单场景单会话结果:")
    logger.info(single_user_single_scene_single_session_result)
    logger.info("单用户多场景结果:")
    logger.info(single_user_multi_scene_result)
    logger.info("单用户单场景多会话结果:")
    logger.info(single_user_single_scene_multi_session_result)
    logger.info("多用户多场景多会话（或逻辑）结果:")
    logger.info(multi_user_multi_scene_multi_session_or_result)
    logger.info("多用户多场景多会话（与逻辑）结果:")
    logger.info(multi_user_multi_scene_multi_session_and_result)
    
    # from common.utils.ModelUtils import ModelAPI
    # MA = ModelAPI()
        
    # 测试加入记忆后的回答

    # import pandas as pd
    # test_data = pd.read_excel("/Users/<USER>/Desktop/MemoryService/MemoryService/测试数据2.xlsx")
    # test_data.columns = ['user_id', 'agent_id', 'session_id', 'question', 'answer', 'memo']

    # ll = []
    
    # for i in range(len(test_data)):

    #     print(f" {i} / 200 ")
        
    #     temp = test_data.loc[i,].to_dict()
        
    #     # 使用列表形式传递用户、场景、会话ID
    #     results, compress_results = test.memory_recall(
    #         query = temp["question"],
    #         model_id = "MEMO_21918",
    #         user_id = [temp["user_id"]],
    #         agent_id = [temp["agent_id"]],
    #         session_id = [temp["session_id"]],
    #         ast_user_id  = None,
    #         ast_agent_id = None,
    #         ast_session_id = None,
    #         query_vector = None,
    #         use_and_logic = True  # 严格匹配条件
    #         )
        
    #     re_query = temp["question"] + results
        
    #     ans = MA.generate(re_query)
        
    #     answer = ans["answer"].replace("\n","").replace("-","")
        
    #     ll.append(answer)
        
    #     with open("/Users/<USER>/Desktop/answer.txt", "a", encoding='utf-8') as file:
    #         file.write(temp["question"] + " - " + answer + "\n")
            
                
    """
    
    # 检索参数更新
    
    import json
    
    MODEL_PARMS = dict()
    
    # 短时检索
    MODEL_PARMS['SHORT_TERM'] = True 
    MODEL_PARMS['SHORT_TERM_MODEL'] = dict() 
    # 短时检索模型参数
    SHORT_TERM_MODEL = dict()
    SHORT_TERM_MODEL['TOP_N'] = 5 # 线索结果上限
    SHORT_TERM_MODEL['RETRIEVAL_TYPE'] = 'MLT' # 检索方式 【TXT EMD MLT】
    # 短时检索模型参数 - 时间因子
    SHORT_TERM_MODEL['TIME_FACTOR'] = True
    SHORT_TERM_MODEL['TIME_FACTOR_PARMS'] = dict()
    SHORT_TERM_MODEL['TIME_FACTOR_PARMS']['ORIGIN'] = 'NOW' # 衰减起点 【NOW 7D ...】
    SHORT_TERM_MODEL['TIME_FACTOR_PARMS']['SCALE']  = '30D' # 衰减速率 【30D ...】
    SHORT_TERM_MODEL['TIME_FACTOR_PARMS']['DECAY']  = 0.5   # 衰减度
    SHORT_TERM_MODEL['TIME_FACTOR_PARMS']['FACTOR'] = 1.0   # 缩放因子
    # 短时检索模型参数 - 权重因子
    SHORT_TERM_MODEL['WEIGHT_FACTOR'] = True
    SHORT_TERM_MODEL['WEIGHT_FACTOR_PARMS'] = dict()
    SHORT_TERM_MODEL['WEIGHT_FACTOR_PARMS']['MODE']   = 'LOG1P'  # 变换函数名称，支持: LOG, LOG1P, LOG2P, LN, LN1P, LN2P, SQUARE, SQRT, RECIPROCAL
    SHORT_TERM_MODEL['WEIGHT_FACTOR_PARMS']['FACTOR'] = 2.0 # 缩放因子
    # 短时检索模型参数 - 评分因子
    SHORT_TERM_MODEL['SCORE_FACTOR'] = True
    SHORT_TERM_MODEL['SCORE_FACTOR_PARMS'] = dict()
    SHORT_TERM_MODEL['SCORE_FACTOR_PARMS']['MODE']   = 'LOG1P'  # 变换函数名称，支持: LOG, LOG1P, LOG2P, LN, LN1P, LN2P, SQUARE, SQRT, RECIPROCAL
    SHORT_TERM_MODEL['SCORE_FACTOR_PARMS']['FACTOR'] = 2.0 # 缩放因子
    MODEL_PARMS['SHORT_TERM_MODEL'] = SHORT_TERM_MODEL
    
    # 长时检索
    MODEL_PARMS['LONG_TERM'] = True 
    MODEL_PARMS['LONG_TERM_MODEL'] = dict() 
    # 长时检索模型参数
    LONG_TERM_MODEL = dict()
    LONG_TERM_MODEL['TOP_N'] = 5
    LONG_TERM_MODEL['RETRIEVAL_TYPE'] = 'MLT'
    # 长时检索模型参数 - 时间因子
    LONG_TERM_MODEL['TIME_FACTOR'] = True
    LONG_TERM_MODEL['TIME_FACTOR_PARMS'] = dict()
    LONG_TERM_MODEL['TIME_FACTOR_PARMS']['ORIGIN'] = 'NOW'
    LONG_TERM_MODEL['TIME_FACTOR_PARMS']['SCALE']  = '30D'
    LONG_TERM_MODEL['TIME_FACTOR_PARMS']['DECAY']  = 0.5
    LONG_TERM_MODEL['TIME_FACTOR_PARMS']['FACTOR'] = 1.0
    # 长时检索模型参数 - 权重因子
    LONG_TERM_MODEL['WEIGHT_FACTOR'] = True
    LONG_TERM_MODEL['WEIGHT_FACTOR_PARMS'] = dict()
    LONG_TERM_MODEL['WEIGHT_FACTOR_PARMS']['MODE']   = 'LOG1P'
    LONG_TERM_MODEL['WEIGHT_FACTOR_PARMS']['FACTOR'] = 2.0
    # 长时检索模型参数 - 评分因子
    LONG_TERM_MODEL['SCORE_FACTOR'] = True
    LONG_TERM_MODEL['SCORE_FACTOR_PARMS'] = dict()
    LONG_TERM_MODEL['SCORE_FACTOR_PARMS']['MODE']   = 'LOG1P'
    LONG_TERM_MODEL['SCORE_FACTOR_PARMS']['FACTOR'] = 2.0
    MODEL_PARMS['LONG_TERM_MODEL'] = LONG_TERM_MODEL
    
    MODEL_PARMS = json.dumps(MODEL_PARMS)
    
    
    """


    """
    
    future :

    0、数据测试 与 评估：
        1）准备大量模拟用户问答数据：
           问题根据预设场景进行设计（具有一定的逻辑性和连贯性）
           答案根据大模型结果生成，最好使用简单大模型（大模型本身不带记忆功能）
        2）评估方案：
           对比 原始答案 与 使用记忆功能后的答案 。

    1、* 关于 Milvus 集合的使用：
        方案 1）每次检索时，加载集合，检索完成释放（内存最优，实时更新）
        方案 2）服务启动时挂载集合（性能最优）
        
        * 数据更新频繁 -> 方案1
        * 检索效率优化 -> 方案2
        
        方案 3）周期性地加载集合？
        
        Milvus内存优化：
            分片、分区、优化向量维度
            内存 = 向量数量 × 维度 × 4 bit（ 100W数据 * 2048 * 4 = 7.6G ）
    
    2、* 算法优化：
        
        算法评估
        * 时间效率 
        * 效果/准确性
        
        1）召回算法：
            数据库层面的召回，如ES默认使用BM2.5，Milvus默认使用IP
            需要研究一下不同场景在最优的算法与参数
            
        2）排序算法：
            自定义公式（在应用层面实现的）
            优化参数调优（研究一下有咩有自动的调优方案）
        
        3）压缩算法：
            要对比使用纯算法与调用大模型服务进行压缩的效果（时间效率 vs 压缩效果）
            优化算法压缩逻辑    
    
    
    4、极致性能优化：
        1）存储时，异步执行 Mysql、ES、Milvus
        2）混合检索时，异步执行 ES、Milvus检索
    
    
    """

    # 辅助函数：日志输出原始 recall 结果
    def log_raw_results(results, label):
        if isinstance(results, str):
            logger.info(f"{label} 结果为字符串（prompt），不输出原始命中详情。")
            return
        logger.info(f"{label} 命中条数: {len(results) if results else 0}")
        if results:
            for i, res in enumerate(results, 1):
                logger.info(f"{label} 第{i}条: memory_id={res.get('memory_id')}, user_id={res.get('user_id')}, agent_id={res.get('agent_id')}, session_id={res.get('session_id')}, score={res.get('score')}")

    # 修正后的调用，传入 compress_results
    log_raw_results(single_user_single_scene_single_session_result if isinstance(single_user_single_scene_single_session_result, list) else None, "单用户单场景单会话")
    log_raw_results(single_user_multi_scene_result if isinstance(single_user_multi_scene_result, list) else None, "单用户多场景")
    log_raw_results(single_user_single_scene_multi_session_result if isinstance(single_user_single_scene_multi_session_result, list) else None, "单用户单场景多会话")
    log_raw_results(multi_user_multi_scene_multi_session_or_result if isinstance(multi_user_multi_scene_multi_session_or_result, list) else None, "多用户多场景多会话(或)")
    log_raw_results(multi_user_multi_scene_multi_session_and_result if isinstance(multi_user_multi_scene_multi_session_and_result, list) else None, "多用户多场景多会话(与)")














