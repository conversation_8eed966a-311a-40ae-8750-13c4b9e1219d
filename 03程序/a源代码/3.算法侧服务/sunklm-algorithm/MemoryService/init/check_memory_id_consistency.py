# -*- coding: utf-8 -*-
"""
检查 MySQL、<PERSON><PERSON><PERSON><PERSON>、Elasticsearch 是否都使用 memory_id 作为主键/ID 字段，并可一键初始化全部结构
"""
import mysql.connector
from pymilvus import Collection, utility, connections, FieldSchema, DataType, CollectionSchema
from elasticsearch import Elasticsearch
import os
import sys
# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)
from common.conf.db_config import db_config, MEMORY_DB_MAPPING, es_index_parm, field_mapping

MYSQL_TABLES = [
    MEMORY_DB_MAPPING["SHORT_TERM"]["MYSQL"]["stats_table"],
    MEMORY_DB_MAPPING["LONG_TERM"]["MYSQL"]["stats_table"]
]
MILVUS_COLLECTIONS = [
    MEMORY_DB_MAPPING["SHORT_TERM"]["MILVUS"]["collection_name"],
    MEMORY_DB_MAPPING["LONG_TERM"]["MILVUS"]["collection_name"]
]
ES_INDICES = [
    MEMORY_DB_MAPPING["SHORT_TERM"]["ES"]["index_name"],
    MEMORY_DB_MAPPING["LONG_TERM"]["ES"]["index_name"]
]

any_issue = False

def check_mysql(auto_fix=False):
    global any_issue
    print("\n[MySQL] 检查主键字段...")
    mysql_conf = db_config['MYSQL']
    conn = mysql.connector.connect(
        host=mysql_conf['host'],
        port=mysql_conf['port'],
        user=mysql_conf['user'],
        password=mysql_conf['password'],
        database=mysql_conf['db']
    )
    cursor = conn.cursor()
    for table in MYSQL_TABLES:
        cursor.execute(f"SHOW KEYS FROM {table} WHERE Key_name = 'PRIMARY'")
        pk = cursor.fetchone()
        if pk and pk[4] == 'memory_id':
            print(f"✔ {table} 主键字段为 memory_id")
        else:
            any_issue = True
            print(f"✗ {table} 主键不是 memory_id (实际: {pk[4] if pk else '无主键'})，请修正！")
            print(f"  建议修正命令: ALTER TABLE {table} CHANGE {pk[4] if pk else 'memo_id'} memory_id VARCHAR(128) NOT NULL;")
            # Only auto-fix for short-term table
            if auto_fix and table == MYSQL_TABLES[0] and pk and pk[4] == 'memo_id':
                confirm = input(f"是否自动修正 {table} 主键为 memory_id? (y/n): ").strip().lower()
                if confirm == 'y':
                    try:
                        cursor.execute(f"ALTER TABLE {table} CHANGE memo_id memory_id VARCHAR(128) NOT NULL;")
                        conn.commit()
                        print(f"已自动修正 {table} 主键为 memory_id！")
                    except Exception as e:
                        print(f"自动修正失败: {e}")
    cursor.close()
    conn.close()

def check_milvus():
    global any_issue
    print("\n[Milvus] 检查主键字段...")
    milvus_conf = db_config['MILVUS']
    connections.connect(host=milvus_conf['host'], port=milvus_conf['port'])
    for col in MILVUS_COLLECTIONS:
        if not utility.has_collection(col):
            any_issue = True
            print(f"✗ 集合 {col} 不存在！")
            continue
        collection = Collection(col)
        pk_field = None
        for field in collection.schema.fields:
            if field.is_primary:
                pk_field = field.name
                break
        if pk_field == 'memory_id':
            print(f"✔ {col} 主键字段为 memory_id")
        else:
            any_issue = True
            print(f"✗ {col} 主键不是 memory_id (实际: {pk_field})，请修正！")
            print(f"  请手动删除并重建集合 {col}，确保主键为 memory_id。")

def check_es():
    global any_issue
    print("\n[Elasticsearch] 检查 mapping 字段...")
    es_conf = db_config['ES']
    es = Elasticsearch(
        hosts=[f"http://{es_conf['host']}:{es_conf['port']}"] ,
        request_timeout=es_conf['request_timeout'],
        max_retries=es_conf['max_retries'],
        retry_on_timeout=es_conf['retry_on_timeout']
    )
    for idx in ES_INDICES:
        if not es.indices.exists(index=idx):
            any_issue = True
            print(f"✗ 索引 {idx} 不存在！")
            continue
        mapping = es.indices.get_mapping(index=idx)
        props = mapping[idx]['mappings']['properties']
        if 'memory_id' in props:
            print(f"✔ {idx} 包含 memory_id 字段")
        else:
            any_issue = True
            print(f"✗ {idx} 不包含 memory_id 字段 (实际字段: {list(props.keys())})，请修正！")
            print(f"  请手动删除并重建索引 {idx}，确保包含 memory_id 字段。")

def init_databases():
    print("\n========== 初始化 MySQL 表 ==========")
    mysql_conf = db_config['MYSQL']
    conn = mysql.connector.connect(
        host=mysql_conf['host'],
        port=mysql_conf['port'],
        user=mysql_conf['user'],
        password=mysql_conf['password'],
        database=mysql_conf['db']
    )
    cursor = conn.cursor()
    # 短期记忆统计表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS memo_storage_stas_info (
            memory_id VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '记忆ID',
            recall_stas INT(11) NULL DEFAULT NULL COMMENT '记忆召回次数',
            recall_stas_txt INT(11) NULL DEFAULT NULL COMMENT '文本召回次数',
            recall_stas_vct INT(11) NULL DEFAULT NULL COMMENT '向量召回次数',
            recall_stas_mlt INT(11) NULL DEFAULT NULL COMMENT '混合召回次数',
            create_time DATETIME NULL DEFAULT NULL,
            update_time DATETIME NULL DEFAULT NULL,
            PRIMARY KEY (memory_id) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '短期记忆检索统计表' ROW_FORMAT = DYNAMIC;
    ''')
    # 长期记忆统计表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS memo_storage_stas_info_long (
            memory_id VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '记忆ID',
            recall_stas INT(11) NULL DEFAULT NULL COMMENT '记忆召回次数',
            recall_stas_txt INT(11) NULL DEFAULT NULL COMMENT '文本召回次数',
            recall_stas_vct INT(11) NULL DEFAULT NULL COMMENT '向量召回次数',
            recall_stas_mlt INT(11) NULL DEFAULT NULL COMMENT '混合召回次数',
            create_time DATETIME NULL DEFAULT NULL,
            update_time DATETIME NULL DEFAULT NULL,
            PRIMARY KEY (memory_id) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '长期记忆检索统计表' ROW_FORMAT = DYNAMIC;
    ''')
    conn.commit()
    cursor.close()
    conn.close()
    print("MySQL 表初始化完成！")

    print("\n========== 初始化 Milvus 集合 ==========")
    milvus_conf = db_config['MILVUS']
    connections.connect(host=milvus_conf['host'], port=milvus_conf['port'])
    qa_schema = field_mapping['QA_RECORDS']
    for col in MILVUS_COLLECTIONS:
        if not utility.has_collection(col):
            print(f"创建 Milvus 集合: {col}")
            schema = CollectionSchema(fields=qa_schema['fields'], description=col, enable_dynamic_field=False)
            Collection(name=col, schema=schema, **qa_schema['collection_params'])
            print(f"Milvus 集合 {col} 创建成功！")
        else:
            print(f"Milvus 集合已存在: {col}")

    print("\n========== 初始化 Elasticsearch 索引 ==========")
    es_conf = db_config['ES']
    es = Elasticsearch(
        hosts=[f"http://{es_conf['host']}:{es_conf['port']}"] ,
        request_timeout=es_conf['request_timeout'],
        max_retries=es_conf['max_retries'],
        retry_on_timeout=es_conf['retry_on_timeout']
    )
    for idx in ES_INDICES:
        if not es.indices.exists(index=idx):
            print(f"创建 Elasticsearch 索引: {idx}")
            # 强制添加 memory_id 字段
            es_index_parm['mappings']['properties']['memory_id'] = {"type": "keyword"}
            es.indices.create(index=idx, body=es_index_parm)
            print(f"Elasticsearch 索引 {idx} 创建成功！")
        else:
            print(f"Elasticsearch 索引已存在: {idx}")
    print("Elasticsearch 索引初始化完成！")
    print("\n数据库初始化全部完成！")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='检查 memory_id 一致性或初始化数据库结构')
    parser.add_argument('--auto-fix-mysql', action='store_true', help='自动修正 MySQL 主键为 memory_id (仅短期表)')
    parser.add_argument('--init-all', action='store_true', help='初始化所有数据库结构')
    args = parser.parse_args()
    if args.init_all:
        init_databases()
        return
    check_mysql(auto_fix=args.auto_fix_mysql)
    check_milvus()
    check_es()
    if any_issue:
        print("\n检查完成，存在✗项，请根据提示修正数据库结构！")
    else:
        print("\n检查完成，所有数据库结构均已正确使用 memory_id！")

if __name__ == "__main__":
    main() 