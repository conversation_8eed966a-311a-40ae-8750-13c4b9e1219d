#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Thu Jun 19 17:29:51 2025

@author: yangzhanda
"""

from pymilvus import FieldSchema, DataType
import logging

# 配置日志
logger = logging.getLogger(__name__)

# Using local connections - update these as needed if you have different settings
MYSQL_CONFIG = {
    "host" : "**********",
    "port" : "3306",
    "user" : "SUNKLM",
    "password" : "Sunyard@6888",
    "db" : "AWEN_PRIMARY",
    "charset" : "utf8mb4"
    }

ES_CONFIG = {
    "host" : "**********",
    "port" : "9200",
    "retry_on_timeout" : True,
    "request_timeout" : 30,
    "max_retries" : 3,
    "index_name" : "memo"
    }

MILVUS_CONFIG = {
    "host" : "***********",
    "port" : "19530",
    "collection_name" : "memo",
    "db_name" : "MEMO"
    }

db_config = {
    "MYSQL"  : MYSQL_CONFIG,
    "ES"     : ES_CONFIG,
    "MILVUS" : MILVUS_CONFIG
    }

# 记忆检索模型配置说明
# 系统支持三种记忆检索模式：
# 1. MEMO_21918: 默认模型，同时启用短期和长期记忆
# 2. MEMO_SHORT_TERM: 只启用短期记忆
# 3. MEMO_LONG_TERM: 只启用长期记忆
# 
# 模型参数存储在数据库的 memo_retrieval_model_parms_info 表中
# 通过 model_id 查询对应的参数配置来决定使用哪种记忆检索模式

# 数据库映射配置
# 短期记忆和长期记忆使用不同的数据库表/集合
MEMORY_DB_MAPPING = {
    "SHORT_TERM": {
        "ES": {
            "index_name": "memo"  # 短期记忆ES索引
        },
        "MILVUS": {
            "collection_name": "memo"  # 短期记忆Milvus集合
        },
        "MYSQL": {
            "stats_table": "memo_storage_stas_info"  # 短期记忆统计表
        }
    },
    "LONG_TERM": {
        "ES": {
            "index_name": "memo_long"  # 长期记忆ES索引
        },
        "MILVUS": {
            "collection_name": "memo_long"  # 长期记忆Milvus集合
        },
        "MYSQL": {
            "stats_table": "memo_storage_stas_info_long"  # 长期记忆统计表
        }
    }
}

def get_memory_db_config(memory_type: str) -> dict:
    """
    根据记忆类型获取对应的数据库配置
    
    Args:
        memory_type: 记忆类型，"SHORT_TERM" 或 "LONG_TERM"
        
    Returns:
        包含ES、Milvus、MySQL配置的字典
    """
    if memory_type not in MEMORY_DB_MAPPING:
        logger.warning(f"记忆类型 {memory_type} 未在配置中找到，使用短期记忆配置")
        memory_type = "SHORT_TERM"
    
    memory_config = MEMORY_DB_MAPPING[memory_type]
    
    # 构建完整的配置
    config = {
        "MYSQL": {**MYSQL_CONFIG},
        "ES": {**ES_CONFIG},
        "MILVUS": {**MILVUS_CONFIG}
    }
    
    # 更新ES配置
    if "ES" in memory_config:
        config["ES"].update(memory_config["ES"])
    
    # 更新Milvus配置
    if "MILVUS" in memory_config:
        config["MILVUS"].update(memory_config["MILVUS"])
    
    # 更新MySQL配置（主要是表名）
    if "MYSQL" in memory_config:
        config["MYSQL"].update(memory_config["MYSQL"])
    
    return config

# ES 初始化
es_index_parm = {
  "mappings": {
    "properties": {
      "question": {                                                      # 用户的问题文本
        "type": "text",
        "analyzer": "standard", # 分词器默认
        "fields": {
          "keyword": { "type": "keyword" }
        }
      },
      "answer": { "type": "text", "analyzer": "standard" },               # 大模型的回答文本
      "user_id": { "type": "keyword", "eager_global_ordinals": True },    # 用户id，检索过滤条件
      "agent_id": { "type": "keyword", "eager_global_ordinals": True },   # 智能体ID，检索过滤条件
      "session_id": { "type": "keyword", "eager_global_ordinals": True }, # 会话ID，检索过滤条件
      "create_time": { "type": "long"  },             # 记忆创建日期-时间戳
      "update_time": { "type": "long"  },             # 记忆更新日期-时间戳
      "create_time_str": { "type": "date" , "format": "yyyy-MM-dd" },         # 记忆创建日期
      "update_time_str": { "type": "date" , "format": "yyyy-MM-dd"},         # 记忆更新日期
      "search_count": { "type": "integer","null_value": 0 },              # 检索计数
      "memory_score": { "type": "integer","null_value": 1 },              # 记忆评分
      "scene_keywords": { "type": "keyword" }                             # 关键词命中
    }
  },
  "settings": {
    "number_of_shards": 5,
    "number_of_replicas": 1,
    "index": {
      "sort.field": ["user_id", "agent_id", "session_id"], # 预排序字段
      "sort.order": ["asc", "asc", "asc"]
    }
  }
}


# Milvus 初始化
field_mapping = {
    "QA_RECORDS": {
        "primary_key": "memory_id",
        "fields": [
            FieldSchema(name="memory_id",    dtype=DataType.VARCHAR, max_length=128, is_primary=True, auto_id=False),   # 记忆ID 
            FieldSchema(name="user_id",      dtype=DataType.VARCHAR, max_length=128),                    # 用户ID
            FieldSchema(name="agent_id",     dtype=DataType.VARCHAR, max_length=128),                    # 智能体ID
            FieldSchema(name="session_id",   dtype=DataType.VARCHAR, max_length=128),                    # 会话ID
            FieldSchema(name="question_emb", dtype=DataType.FLOAT_VECTOR, dim=1024),                     # 问题向量
            FieldSchema(name="answer_emb",   dtype=DataType.FLOAT_VECTOR, dim=1024),                     # 回答向量
            FieldSchema(name="create_time",  dtype=DataType.INT64),                                      # 创建毫秒级时间戳
            FieldSchema(name="update_time",  dtype=DataType.INT64),  
            FieldSchema(name="create_time_str", dtype=DataType.VARCHAR, max_length=32),                  # 创建时间字符串
            FieldSchema(name="update_time_str", dtype=DataType.VARCHAR, max_length=32),
            FieldSchema(name="memory_score", dtype=DataType.INT64, default_value=1)                      # 记忆评分
        ],
        "index_fields": ["question_emb", "answer_emb"],
        "collection_params": {
            "using": 'default',
            "shards_num": 2,
            "consistency_level": "Session"  # 问答场景适合会话一致性
        }
    }
}






