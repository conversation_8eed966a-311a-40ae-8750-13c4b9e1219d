from elasticsearch import Elasticsearch
from common.utils.log_utils import logger
from common.conf.db_config import ES_CONFIG, es_index_parm


class EsClient:
    def __init__(self):
        """
        es引擎配置
        """
        self.es = Elasticsearch(
            hosts = [f"http://{ES_CONFIG['host']}:{ES_CONFIG['port']}"],
            request_timeout = ES_CONFIG['request_timeout'],
            max_retries = ES_CONFIG['max_retries'],
            retry_on_timeout = ES_CONFIG['retry_on_timeout']
        )
        if self.es.ping():
            logger.info('成功连接到 Elasticsearch')
        else:
            logger.info('无法连接到 Elasticsearch')
            raise ConnectionError("Elasticsearch 连接失败")
    
    
    def get_connect(self):
        return self.es
        
    
    # 创建索引
    def _do_create_index(self, index_name=None):
        """
        创建问答数据索引
        """
        if self.es.indices.exists(index=index_name):
            print(f"索引已存在: {index_name}")
        else:
            # 创建索引
            self.es.indices.create(index=index_name, body=es_index_parm)
            print(f"索引创建成功: {index_name}")
    









