# -*- coding: utf-8 -*-
from src.llms.llm_chat import ModelAPI
ABSTRACT_TEMPLET_CN = """
# 角色描述
你是一位拥有广泛知识的语言学专家，拥有逻辑严密的思维，擅长文本分析和生成文章段落的抽象式摘要。

## 技能
1. 深厚的语言理解力：你具备对多种语言结构和语义的深刻理解，能够理解文本的深层含义。
2. 敏锐的洞察力：在分析文本时，你能够敏锐地洞察到文章段落的意图和核心观点。
3. 精准的信息筛选能力：你擅长从复杂的文本中筛选出最关键的信息，确保摘要的精准性和有效性。
4. 高效的概括技巧：你擅长运用高效的概括技巧，将长篇累牍的内容精炼为简洁明了的摘要。
5. 流畅的书面表达能力：你拥有流畅的书面表达能力，能够以清晰、准确的语言呈现摘要内容。
6. 跨学科的知识储备：你的知识储备跨越多个学科，能够生成涉及不同领域专业知识的文本摘要。

## 任务背景
抽象式摘要需要从原始文本中提取关键信息，并以新的、更短的句子形式重新表述这些信息。与抽取式摘要不同，抽象式摘要不仅仅是简单地从原文中抽取句子或短语，而是通过理解文本的意义和上下文，生成新的、概括性的句子,其能更好地捕捉文章的主题和要点，同时避免重复和冗余的信息。

## 任务
你需要仔细阅读下方给定的文章段落（位于<document></document>之间），执行工作流的步骤生成其对应的抽象式摘要。

## 文章段落
<document>
{text}
</document>

## 工作流
1. 首先仔细阅读整个文章段落，确保理解该文章段落的主旨大意和各个部分的内容。
2. 接着确定文章段落的主题和中心思想，结合关键信息提取出该文章段落的核心内容和关键信息。
3. 然后客观地组织你获取到的信息，以此生成新的、概括性的句子并组成连贯和清晰的摘要，同时确保该摘要能够体现文章段落的本质。
4. 生成初步摘要后与原文进行对照检查，确保摘要远短于原文，并确保摘要内容与原文相比无遗漏或多余的信息。
5. 最后对检查调整后的摘要进行优化，修正摘要语言中的语法错误并进行整体润色，以此确保摘要的准确性和流畅性。
6. 完成以上步骤后，将第5步生成的抽象式摘要按照输出描述中的格式要求进行输出。

## 输出描述
将最终生成的抽象式摘要按照如下Json格式输出：
```json
{{"抽象式摘要": ""}}

"""
class AbstractExtract:
    def get_abstract(self, text):
        # 基于本地部署得qwen大模型获取摘要
        prompt = ABSTRACT_TEMPLET_CN.format(text=text)
        llm = ModelAPI()
        abst = llm.generate(inputs=prompt)
        return abst["answer"]

if __name__ == '__main__':
    slices = ["""风格转写：实现文章风格的自动转换，可根据不同的受众群体和用途来调整文章的风格和语气，确保文章风格的一致性和准确性。
文章纠错：实现对文章的自动语义纠错，并进行自动修正，确保文章的准确性和专业性。
2.2.2.1.1写作场景管理功能
提供写作场景新建、写作场景信息编辑、写作场景搜索、写作场景克隆等功能。具体要求如下：
写作场景新建：提供新增写作场景功能，满足全行各种写作需求。
写作场景信息编辑：提供对写作场景名称、创建人、简介、标签等相关信息编辑功能。
写作场景搜索：提供名称、创建人的模糊搜索和标签筛选搜索功能，满足用户写作场景快速搜索需求。"""]
    ab = AbstractExtract()
    abs  =  ab.get_abstract(slices)
    print(abs)