from pymilvus import connections, Collection, utility, CollectionSchema
from common.utils.log_utils import logger
from common.conf.db_config import MILVUS_CONFIG, field_mapping


class MilvusClient:

    def __init__(self,
                 host=MILVUS_CONFIG['host'],
                 port=MILVUS_CONFIG['port'],
                 db_name=MILVUS_CONFIG['db_name']
                 ):
        self.host = host
        self.port = port
        self.db_name = db_name
        self._connect()
    
    def _connect(self):
        connections.connect(host=self.host, port=self.port, db_name=self.db_name)
        
    def _collection_exists(self, col_name):
        """Private method to check if a collection exists."""
        return utility.has_collection(col_name)

    def _partition_exists(self, col_name, partition_name):
        return utility.has_partition(col_name, partition_name)

    def do_drop_col(self, col_name):
        """Drops a collection if it exists."""
        if self._collection_exists(col_name):
            collection = Collection(col_name)
            collection.release()
            collection.drop()
            print(f'Collection {col_name} deleted successfully.')
        else:
            print(f'Collection {col_name} does not exist.')
        
    def do_create_col(self, col_name, description, colType="QA_RECORDS"):
        """
        创建问答记录集合
        col_name: 集合名
        description: 描述信息
        colType: 集合类型（目前仅支持 QA_RECORDS）
        """

        # 检查集合类型是否支持
        if colType not in field_mapping:
            raise ValueError(f"Unsupported collection type: {colType}. Only 'QA_RECORDS' is supported.")

        # 获取配置
        config = field_mapping[colType]
        fields = config["fields"]
        index_fields = config["index_fields"]
        collection_params = config["collection_params"]

        # 创建集合模式
        schema = CollectionSchema(fields=fields,
                                  description=description,
                                  enable_dynamic_field=False)  # 关闭动态字段确保数据一致性

        # 实例化集合
        collection = Collection(name=col_name,
                                schema=schema,
                                **collection_params)

        # 创建向量索引
        index_params = {
            "metric_type": "IP",       # 内积相似度
            "index_type": "IVF_FLAT",  # 优化存储和性能
            "params": {"nlist": 128}
        }

        for index_field in index_fields:
            collection.create_index(
                field_name=index_field,
                index_params=index_params,
                index_name=f"{index_field}_index"
            )

        return collection



