#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Jun 22 22:36:04 2025

@author: yangzhanda
"""

import re
import jieba
from collections import defaultdict


def abstract_by_txt_rank(text, summary_length=80):
    """
    TextRank算法对输入文本进行简化并提取关键摘要信息
    
    Args:
        text: 待处理的原始文本
        summary_length: 目标摘要长度
    
    Returns:
        提取的文本摘要
    """

    # 1. 文本预处理：去除标点和特殊字符
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', ' ', text)
    
    # 2. 分词处理
    words = jieba.lcut(text)
    words = [word for word in words if len(word) > 1]  # 过滤单字
    
    # 3. 构建词共现图 (滑动窗口法)
    window_size = 5
    graph = defaultdict(float)
    for i in range(len(words)):
        for j in range(i+1, min(i+window_size, len(words))):
            word1, word2 = words[i], words[j]
            graph[(word1, word2)] += 1
            graph[(word2, word1)] += 1
    
    # 4. TextRank算法计算词权重
    word_weights = defaultdict(float)
    d = 0.85  # 阻尼系数
    max_iterations = 100
    min_delta = 1e-4
    
    # 初始化权重
    for word in set(words):
        word_weights[word] = 1.0
        
    """
    # 迭代计算权重
    for _ in range(max_iterations):
        new_weights = word_weights.copy()
        for word in word_weights:
            weight = 0
            for (neighbor, _) in graph:
                if neighbor == word:
                    for (_, co_occur_word) in graph:
                        if co_occur_word == word:
                            continue
                        weight += graph[(neighbor, co_occur_word)] * word_weights[co_occur_word]
            new_weights[word] = (1 - d) + d * (weight / len(word_weights) if len(word_weights) > 0 else 0)
        
        # 检查收敛性
        delta = sum(abs(new_weights[word] - word_weights[word]) for word in word_weights)
        if delta < min_delta:
            break
        
        word_weights = new_weights
    """
    
    # ** 迭代计算权重(优化逻辑)
    for _ in range(max_iterations):
        new_weights = word_weights.copy()
        # 创建graph的副本用于迭代
        graph_items = list(graph.items())
        
        for word in word_weights:
            weight = 0
            for (neighbor, _) in graph_items:
                if neighbor == word:
                    for (_, co_occur_word) in graph_items:
                        if co_occur_word == word:
                            continue
                        weight += graph.get((neighbor, co_occur_word), 0) * word_weights.get(co_occur_word, 0)
            new_weights[word] = (1 - d) + d * (weight / len(word_weights) if len(word_weights) > 0 else 0)
        
        # 检查收敛性
        delta = sum(abs(new_weights.get(word, 0) - word_weights.get(word, 0)) for word in word_weights)
        if delta < min_delta:
            break
        
        word_weights = new_weights
        
        
    # 5. 按权重排序并提取关键词
    sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, _ in sorted_words[:20]]  # 取前20个关键词
    
    # 6. 生成摘要：基于关键词匹配原文本句子
    sentences = re.split(r'[。！？；;\s]', text)
    sentences = [s for s in sentences if s]
    
    # 计算句子与关键词的匹配度
    sentence_scores = {}
    for sentence in sentences:
        sentence_words = jieba.lcut(sentence)
        score = sum(1 for word in sentence_words if word in keywords)
        sentence_scores[sentence] = score
    
    # 按分数排序并生成摘要
    sorted_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)
    
    # 拼接摘要，控制长度
    summary = ""
    for sentence, _ in sorted_sentences:
        if len(summary) + len(sentence) <= summary_length:
            summary += sentence + "。"
        else:
            break
    
    return summary


if __name__ == "__main__":
    
    # 测试代码
    
    text = """
发现账户异常交易，可以通过以下几种方式挂失：\n- **电话挂失**：立即拨打银行客服电话，
按照语音提示操作找到挂失相关服务，或者直接联系人工客服。向客服提供自己的卡号、身份证号、
姓名等身份信息，通过身份验证后即可办理临时挂失。电话挂失方便快捷，能在第一时间阻止可能
的进一步资金损失，适用于紧急情况。需注意的是，临时挂失通常有一定的有效期，一般为5天左右，
不同银行的规定可能略有不同。挂失时要记录好挂失编号，以便后续查询和处理。\n- **网上银行
或手机银行挂失**：如果已经开通了相关银行的网上银行或手机银行服务，可以登录相应平台进行
挂失。登录网上银行后，进入账户管理板块，寻找挂失服务选项，按照页面提示完成挂失流程；手机
银行的操作类似，通常在账户管理或安全中心等相关菜单中能找到挂失功能，按系统提示操作即可。
这种方式的优点是操作相对简便，且可以随时进行，不受时间和地点限制，但前提是要确保使用的网
络环境安全，防止个人信息泄露。\n- **柜台挂失**：携带本人有效身份证件，如身份证、护照等，
前往附近的银行网点。到网点后，向工作人员说明账户出现异常交易情况并要求挂失，工作人员会核
实身份信息，可能还会要求提供一些账户相关的其他信息，如预留手机号码、家庭住址等，确认无误
后会协助办理挂失手续。柜台挂失相对来说更为稳妥，能更全面地了解账户情况，同时也可以咨询工
作人员关于异常交易的处理建议，但可能需要花费一些时间前往银行网点并排队等待办理。
    
    """

    ab = abstract_by_txt_rank(text, summary_length=80)
    
    print(ab)
    
    
    
    
    

