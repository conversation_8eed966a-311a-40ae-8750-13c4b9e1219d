import uvicorn
from fastapi import FastAPI,Request
from agent.controller.AgentController import item_router
from common.conf.ServerConfig import ServerConfig

settings = ServerConfig
# 初始化应用
app = FastAPI(title="Agent API", description="智能体执行")

#包含路由控制器 - 添加限流装饰器
app.include_router(
    item_router,
    prefix="/agent",
    tags=["智能体执行"]
)


if __name__ == "__main__":
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=9081
    )

    server = uvicorn.Server(config)
    server.run()
