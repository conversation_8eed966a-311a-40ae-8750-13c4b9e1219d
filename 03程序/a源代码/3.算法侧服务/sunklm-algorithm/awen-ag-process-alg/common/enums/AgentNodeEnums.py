from enum import Enum

class AgentNode(Enum):
    KNOW_FAQ_NODE = ("FAQ检索","know_faq_node","know_faq")
    KNOW_FILE_NODE = ("FILE检索","know_file_node","know_file")
    TOOL_NODE = ("TOOL工具","tool_node","tool")
    LLM_NODE = ("大模型","llm_node","llm")

    def __init__(self, nodeName, nodeCode,nodeType):
        self.nodeName = nodeName
        self.nodeCode = nodeCode
        self.nodeType = nodeType

    def get_node_name(self):
        return self.nodeName

    def get_node_code(self):
        return self.nodeCode

    def get_node_type(self):
        return self.nodeType