import os
from enum import IntEnum
from typing import Dict

PWD = os.path.dirname(os.getcwd())

# 应用配置
class ServerConfig():
    # 服务名称
    SERVICE_NAME: str = "awen-ag-process-alg"


class ServiceApi:
    # 检索服务API
    RETRIEVAL_SERVICE_API ="http://***********:8013/RAG-alg/query_match"
    # MCP服务API
    MCP_SERVICE_API ="http://***********:9085/sse"

class MysqlConf:
    # mysql配置
    MYSQL_HOST = "**********"
    MYSQL_PORT = 3306
    MYSQL_USER = "SUNKLM"
    MYSQL_PASSWORD = "Sunyard@123"
    MYSQL_DATABASE = "AGENT_FLOW"

class ReturnCode(IntEnum):
    SUCESS = 0
    ERROR = 1

# 私有化部署的模型的标志，0 私有化部署，1 开源模型
class CompanyCode:
    PRIVATE = 0
    OPEN = 1

class ModelDataDefault:
    # 默认使用的模型
    API_KEY = ''
    COMPANY = ''
    MODEL_TYPE = "Qwen2.5-72B-Instruct"
    API_BASE = "http://**********:9997/v1/chat/completions"
    TOP_P = 0.01
    TOP_K = 1
    TEMPERATURE = 0.95
    # 文本最大输入长度
    MAX_SEQ_LEN = 100000
    # images最大输入
    MAX_IMAGES_LEN = 100000
    # 最大输出长度：
    MAX_OUT_LEN = 2048
    # 模型输出参数默认值
    QUERY = "你好"
    STREAM = False
    # 每秒允许的最大查询数，两个连续的API调用之间。默认为 1。
    QUERY_PER_SECOND = 1

    DEPLOY_MODE=0
    RERANK_MODEL_NAME = "bge-m3"
    EMBEDDING_MODEL_NAME = "bge-reranker-v2-m3"

    REWRITE = False
    POSTPROCESS = False

class ChatFormatCode:
    OPENAI_FORMAT = 'openai'
    GLM_FORMAT = 'glm'
    RAW_FORMAT = 'raw'



