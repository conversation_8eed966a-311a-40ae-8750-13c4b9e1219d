from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.sse import sse_client
import asyncio
from asyncio import Semaphore
from concurrent.futures import ThreadPoolExecutor


class MCPClientPool:
    def __init__(self, max_connections_per_url: int = 10, max_total_threads: int = 20):
        self._pool = {}  # base_url -> { server_id -> {"session": ..., "stack": ...} }
        self.semaphores = {}  # base_url -> Semaphore
        self.max_connections_per_url = max_connections_per_url
        self.thread_pool = ThreadPoolExecutor(max_workers=max_total_threads)

    def _get_or_create_semaphore(self, base_url: str) -> Semaphore:
        if base_url not in self.semaphores:
            self.semaphores[base_url] = Semaphore(self.max_connections_per_url)
        return self.semaphores[base_url]

    async def get_session(self, base_url: str, server_id: str) -> ClientSession:
        semaphore = self._get_or_create_semaphore(base_url)

        async with semaphore:
            url_map = self._pool.setdefault(base_url, {})

            # 如果已有 session 并且有效，直接返回
            if server_id in url_map:
                data = url_map[server_id]
                session = data["session"]
                if await self.is_valid(session):
                    return session
                else:
                    # session 无效，立即清理（同一协程中 close stack）
                    await self._cleanup_session(base_url, server_id)

            stack = AsyncExitStack()
            streams = await stack.enter_async_context(sse_client(url=base_url))
            session = await stack.enter_async_context(ClientSession(*streams))
            await session.initialize()

            # 绑定 session 和它的 exit stack
            url_map[server_id] = {"session": session, "stack": stack}
            return session

    async def _cleanup_session(self, base_url: str, server_id: str):
        url_map = self._pool.get(base_url, {})
        data = url_map.pop(server_id, None)
        if data:
            stack: AsyncExitStack = data["stack"]
            try:
                await stack.aclose()
            except Exception as e:
                # 如果有关闭失败也捕捉，避免抛出
                print(f"关闭 session 时出错: {e}")

    async def release_session(self, base_url: str, server_id: str):
        await self._cleanup_session(base_url, server_id)

    async def close_all(self):
        for base_url, url_map in list(self._pool.items()):
            for server_id in list(url_map.keys()):
                await self._cleanup_session(base_url, server_id)
        # 清空信号量
        self.semaphores.clear()

    async def is_valid(self, session: ClientSession) -> bool:
        try:
            await session.list_tools()
            return True
        except Exception:
            return False

    def status(self):
        return {
            "urls": list(self._pool.keys()),
            "session_counts": {
                url: len(sessions) for url, sessions in self._pool.items()
            },
            "semaphore_limits": {
                url: sem._value if hasattr(sem, "_value") else "unknown"
                for url, sem in self.semaphores.items()
            }
        }
