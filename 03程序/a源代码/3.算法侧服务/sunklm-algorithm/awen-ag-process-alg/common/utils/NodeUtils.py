from common.enums.AgentNodeEnums import AgentNode
from datetime import datetime

class NodeUtils:
    def AgentNodeInit(self,AgentNode,input):
        agentNode = {}
        agentNode['nodeCode'] = AgentNode.nodeCode
        agentNode['nodeName'] = AgentNode.nodeName
        agentNode['nodeType'] = AgentNode.nodeType

        # 获取当前时间
        now = datetime.now()
        # 格式化为 yyyy-mm-dd HH:mm:ss
        formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")
        agentNode['nodeStartTime'] =formatted_time
        agentNode['input'] = input
        return agentNode


    def AgentNodeEnd(self,nodeEntity,output,isSuccess):
        # 获取当前时间
        endTime = datetime.now()
        # 格式化为 yyyy-mm-dd HH:mm:ss
        formatted_time = endTime.strftime("%Y-%m-%d %H:%M:%S")
        nodeEntity['nodeEndTime'] = formatted_time
        nodeEntity['isSuccess'] = isSuccess
        nodeEntity['output'] = output

        # 计算时间差
        # 解析为 datetime 对象
        startTime = datetime.strptime(nodeEntity['nodeStartTime'], "%Y-%m-%d %H:%M:%S")
        # 计算时间差
        time_diff = endTime - startTime
        nodeEntity['nodeCostTime'] = time_diff.total_seconds()

        return nodeEntity