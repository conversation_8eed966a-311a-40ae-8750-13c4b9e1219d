import json
from fastapi.responses import StreamingResponse
import requests
from common.conf.ServerConfig import *
from common.utils.LogUtils import logger


model_url = "http://172.1.0.154:9080/ai/gateway/chatModel"

class ModelService:
    def __init__(self, api_key=ModelDataDefault.API_KEY,
                 company=ModelDataDefault.COMPANY,
                 model_type=ModelDataDefault.MODEL_TYPE,
                 api_base=ModelDataDefault.API_BASE,
                 top_p=ModelDataDefault.TOP_P,
                 top_k=ModelDataDefault.TOP_K,
                 max_seq_len=ModelDataDefault.MAX_SEQ_LEN,
                 max_out_len=ModelDataDefault.MAX_OUT_LEN,
                 deploy_mode=ModelDataDefault.DEPLOY_MODE,
                 temperature=ModelDataDefault.TEMPERATURE):
        self.modelParam = {
            "company":company,
            "modelType":model_type,
            "baseUrl":api_base,
            "temperature":temperature,
            "topP":top_p,
            "topK":top_k,
            "inputMaxToken":max_seq_len,
            "outputMaxToken":max_out_len,
            "deployMode":deploy_mode,
            "apiKey":api_key,
        }

    def chatModel(self,query,tools,history, stream, linkNode):
        """大模型统一调用入口"""
        try:
            self.modelParam['query'] = query
            self.modelParam['tools'] = tools
            self.modelParam['history'] = history
            self.modelParam['stream'] = stream

            inputModel = self.modelParam
            if stream:
                response = self.stream_chat(inputModel,linkNode)
            else:
                try:
                    response = self.un_stream_chat(inputModel)
                except Exception as e:
                    logger.error(f"【Service】【Model-推理】异常:{e}")
                    return None
            return response
        except Exception as e:
            return None

    # 非流式输出
    def un_stream_chat(self,inputModel):
        """
        实现具体的API调用逻辑
        """
        logger.info(f"【Service】【Model-非流式-调用】data:{inputModel}")
        res = requests.post(
            f'{model_url}',
            timeout=600,
            json=inputModel
        )
        # 获取返回结果
        response = json.loads(res.text)
        logger.info(f"【Service】【Model-非流式-模型调用】输出:{response}")
        return response

    def stream_chat(self,modelParam,linkNode):
        """
        如果需要支持流式聊天，可以实现这个方法
        """
        logger.info(f"【Service】【Model-流式-调用】inputs:{modelParam}")

        async def generate_data():
            try:
                response = requests.post(
                    f'{model_url}',
                    timeout=600,
                    json=modelParam,
                    stream=True
                )
                answerStr = ""
                if response.status_code == 200:
                    for line in response.iter_lines():
                        # 判断line为空
                        if line:
                            try:
                                line = line.decode("utf-8")
                                line = line.strip("data").strip(": ")
                                lineJson = json.loads(line)
                                answer = lineJson['retMap']['answer']
                                if answer == "Stream ended":
                                    # 发结束
                                    data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                                            "retMap": {"answer": str(answer)}}
                                    # logger.debug(f"【Service】【Model-流式-模型调用】流式消息:{data}")
                                    yield f"data:{json.dumps(data, ensure_ascii=False)}\n\n"
                                    # 发链路
                                    response = {
                                        "answer": answerStr,
                                        "nodeDetail": linkNode,
                                    }
                                    endData = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                                            "retMap": response}
                                    yield f"data:{json.dumps(endData, ensure_ascii=False)}\n\n"
                                else:
                                    answerStr += answer
                                    data = {"retCode": str(ReturnCode.SUCESS.value), "retMsg": "success",
                                            "retMap": {"answer": str(answer)}}
                                    # logger.debug(f"【Service】【Model-流式-模型调用】流式消息:{data}")
                                    yield f"data:{json.dumps(data, ensure_ascii=False)}\n\n"
                            except Exception as e:
                                print(f"发生错误: {e}")
            except Exception as e:
                # logger.error(f"【Service】【Model-流式-模型调用】异常:{e}")
                yield f"data: Error: {str(e)}\n\n"
        return StreamingResponse(generate_data(), media_type="text/event-stream")

