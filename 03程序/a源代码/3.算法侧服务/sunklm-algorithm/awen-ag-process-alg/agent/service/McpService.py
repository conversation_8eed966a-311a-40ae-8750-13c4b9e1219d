import asyncio
import sys
import os
import json
from random import choices
from typing import Optional
from contextlib import AsyncExitStack
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client
from anthropic import Anthropic
from dotenv import load_dotenv
from openai import OpenAI
from common.utils.LogUtils import logger
from agent.service.ModelService import ModelService
from common.utils.NodeUtils import NodeUtils
from common.enums.AgentNodeEnums import AgentNode
from common.conf.ServerConfig import ServiceApi


class McpService:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.messages = []

    async def connect_to_server(self, server_path: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        # await self.session.initialize()
        # 创建 SSE 客户端连接上下文管理器
        self._streams_context = sse_client(url=server_path)
        # 异步初始化 SSE 连接，获取数据流对象
        streams = await self._streams_context.__aenter__()

        # 使用数据流创建 MCP 客户端会话上下文
        self._session_context = ClientSession(*streams)
        # 初始化客户端会话对象
        self.session: ClientSession = await self._session_context.__aenter__()

        # 执行 MCP 协议初始化握手
        await self.session.initialize()
        # List available tools
        response = await self.session.list_tools()
        # tools = response.tools
        # print("\nConnected to server with tools:", [tool.name for tool in tools])

    async def process_query(self,model:ModelService, query: str,toolList,linkNode) -> str:
        """
        Process a query using Claude and available tools
        Role 可选值为 system、user、assistant、tool（functioncall 场景）。
        其中 system 角色可选，若存在则必须位于列表的最开始。user（tool）和 assistant 需交替出现（一问一答），
        以 user 提问开始，user（tool）提问结束，tool 可以连续出现多次，且 Content 不能为空。
        Role 的顺序示例为：[system（可选） user assistant user（tool tool ...） assistant user（tool tool ...） ...]
        """
        messages = self.messages

        nodeUtils = NodeUtils()

        prompt ="""
        # Role: MCP 控制智能体调度器

        ## Profile
        - language: 中文
        - description: 你是一个能够调用 MCP 平台调度多个子智能体以完成指定任务的总控智能体。
        
        ## Skills
        - 理解用户的高层任务目标并进行任务分解
        - 使用结构化格式调用 MCP 平台的接口进行代理分配和任务调度
        - 协调多个子智能体的任务顺序和依赖关系
        - 实时反馈任务进度并汇总结果输出
        
        ## OutputFormat(可选项):
        输出 JSON 格式，包含子任务ID、任务描述、所调用的子智能体类型、状态码和最终结果。
        
        ## Rules
        - 所有任务必须以结构化形式注册至 MCP 平台
        - 子任务的定义必须明确，可度量
        - 避免资源冲突和任务重复分配
        - 汇总输出需具备可读性与可验证性
        
        ## Workflows
        1. 读取用户输入的任务目标
        2. 拆分为若干子任务，定义每个任务所需能力
        3. 为每个子任务选择合适的子智能体并注册至 MCP
        4. 监控任务状态并在任务完成后收集结果
        5. 汇总结果并以所需格式输出
        
        ## Init
        你好，我是 MCP 总控智能体，请描述你希望我完成的复杂任务，我将自动调度合适的子智能体完成每个子任务，并返回最终结果。
        
        ## 用户问题
        """

        # messages.append(
        #     {
        #         "role": "user",
        #         "content": prompt+query
        #     }
        # )
        response = await self.session.list_tools()

        available_tools= []

        for tool in toolList:
            for mcpTool in response.tools:
                if  tool['toolName'] == mcpTool.name:
                    available_tools.append({
                        "type": "function",
                        "function": {
                            "name": mcpTool.name,
                            "description": mcpTool.description,
                            "parameters": mcpTool.inputSchema
                        }
                    })

        history = []
        history.append("user:"+prompt+query)
        # 首次模型询问调用工具
        llm_node = AgentNode.LLM_NODE
        llmNodeLog = nodeUtils.AgentNodeInit(llm_node, prompt+query)

        logger.info(f"【Service】【MCP工具列表】tool_list:{available_tools}")
        # todo 需要切换调用AI网关
        response= model.chatModel(prompt+query,available_tools,None,False,None)
        # 执行结束
        nodeUtils.AgentNodeEnd(llmNodeLog, response, True)
        #  链路-记录日志
        linkNode.append(llmNodeLog)

        final_text = []
        # choices = response['choices'][0]
        # message =choices['message']
        # 获取答案
        llmNodeAnswer = json.dumps(response['retMap']['answer'], ensure_ascii=False)[1:-1]
        final_text.append(llmNodeAnswer or "")
        # 拼装回复信息
        history.append(f"assistant:{llmNodeAnswer}" )
        # messages.append({
        #     "role": "assistant",
        #     "content": message['content']
        # })

        execToolList =[]
        # 处理响应并处理工具调用
        while response['retMap']['toolCalls']:
            # 处理每个工具调用
            for tool_call in response['retMap']['toolCalls']:
                tool_name = tool_call['function']['name']
                tool_args = json.loads(tool_call['function']['arguments'])


                # 初始化工具节点日志
                tool_node = AgentNode.TOOL_NODE
                mcpParam={
                    "toolName":tool_name,
                    "toolArgs":tool_args
                }
                toolNodeLog = nodeUtils.AgentNodeInit(tool_node, mcpParam)

                # 执行工具调用
                logger.info(f"【Service】【MCP调用】tool_name:{tool_name},tool_args:{tool_args}")
                result = await self.session.call_tool(tool_name, tool_args)
                logger.info(f"【Service】【MCP调用结果】result:{result}")
                final_text.append(f"[Calling tool {tool_name} with args {json.dumps(tool_args, indent=4)}]")

                # 将工具调用和结果添加到消息历史

                # messages.append({
                #     "role": "tool",
                #     "tool_calls": [
                #         {
                #             "id": tool_call['id'],
                #             "type": "function",
                #             "function": {
                #                 "name": tool_name,
                #                 "arguments": tool_args
                #             }
                #         }
                #     ],
                #     "content": json.dumps(result.content[0].text)
                # })
                # 获取已执行的工具名称
                execToolList.append(tool_name)
                toolAnswer = json.dumps(result.content[0].text, ensure_ascii=False)[1:-1]
                print(toolAnswer)
                # 记录工具调用日志
                nodeUtils.AgentNodeEnd(toolNodeLog, toolAnswer,True)
                linkNode.append(toolNodeLog)
                # history.append("assistant:" + json.dumps(result.content[0].text))
                print("中间调用结果： ", toolAnswer)
                history.append(f"tool:{tool_name}]")
                history.append(f"assistant:{toolAnswer}")
                # history.append("tool:" + f"[Calling tool {tool_name} with content {toolAnswer}]")

            # 将工具调用的结果交给 LLM
            tool_llm_node = AgentNode.LLM_NODE
            # history.append("user:历史信息是否满足用户问题")
            toolLlmNodeLog = nodeUtils.AgentNodeInit(tool_llm_node, messages)
            response = model.chatModel(prompt+query,available_tools,history,False,None )
            if  response['retMap']['toolCalls']:
                # 创建一个新的列表来存储不需要移除的 tool_call
                filtered_tool_calls = []
                for tool_call in response['retMap']['toolCalls']:
                    tool_name = tool_call['function']['name']
                    if  tool_name not in execToolList:
                        filtered_tool_calls.append(tool_call)
                # 更新 response['retMap']['toolCalls'] 为过滤后的列表
                response['retMap']['toolCalls'] = filtered_tool_calls

            # 日志记录
            nodeUtils.AgentNodeEnd(toolLlmNodeLog, None, True)
            linkNode.append(toolLlmNodeLog)
            # message = response['choices'][0]['message']
            history.append("assistant:" + response['retMap']['answer'])
            final_text.append(response['retMap']['answer'])

        # 获取最后回复结果
        messages.append(
            {
                "role": "assistant",
                "content": "\n".join(final_text)
            }
        )
        self.messages = messages

        # 返回结果
        return "\n".join(final_text)


    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

# async def main():
#     client = McpService()
#     try:
#         await client.connect_to_server("http://172.1.0.154:9085/sse")
#         await client.process_query("杭州天气")
#     finally:
#         await client.cleanup()
#
# if __name__ == "__main__":
#     asyncio.run(main())