from common.utils.LogUtils import logger
from agent.service.KnowService import KnowService
from common.utils.NodeUtils import NodeUtils
from common.enums.AgentNodeEnums import AgentNode
from agent.service.ModelService import ModelService
from agent.service.McpService import McpService
from common.conf.ServerConfig import ServiceApi
import asyncio
# 注入
knowService = KnowService()
nodeUtils = NodeUtils()

class AgentService:

    def chatAgent(self,req):
        # 获取参数
        faqInfo = req.get("faqInfo",[])
        knowledgeInfo = req.get("knowledgeInfo",[])
        query = req.get("query","")
        apiKey = req.get("apiKey","")
        company = req.get("company","")
        modelType = req.get("modelType","")
        baseUrl = req.get("baseUrl","")
        deployMode = req.get("deployMode","")

        # 初始化链路信息
        linkNode = []
        # 初始化对话信息
        messages = []

        # 初始化模型
        modelService = ModelService(api_key=apiKey,company=company,model_type=modelType,api_base=baseUrl,deploy_mode=deployMode)

        # 1. 知识库判断，查询
        if faqInfo is not None:
            faqNodeLog = self.knowFaqHandle(faqInfo,query)
            # 添加节点信息
            linkNode.append(faqNodeLog)
            messages.append({
                "role": "faq_knowledge",
                "content": str(faqNodeLog['output'])
            })

        if knowledgeInfo is not None:
            fileNodeLog = self.knowFileHandle(knowledgeInfo,query)
            # 添加节点信息
            linkNode.append(fileNodeLog)
            messages.append({
                "role": "file_knowledge",
                "content": str(fileNodeLog['output'])
            })

        # 2. 工具判断，查询，需要用mcp
        res= self.mcpRunHandle(modelService,req,linkNode)

        # 最后一次整合输出
        prompt = """
                    # 你是智能体问答专家

                    # 用户问题:
                    {query}

                    # 文件知识
                    {fileKnow}

                    # faq知识
                    {faqKnow}

                    # 工具信息
                    {tool}            
                """.format(query=query, fileKnow=str(fileNodeLog['output']), faqKnow=str(faqNodeLog['output']), tool=res)

        stream = req.get("stream", False)
        logger.info(f"【Service】【chatAgent】linkNode:{linkNode}")
        if stream:
            return modelService.chatModel(prompt, None, None, stream,linkNode)
        else:
            resp = modelService.chatModel(prompt, None, None, stream,linkNode)

            response = {
                "answer": resp['retMap']['answer'],
                "nodeDetail": linkNode,
            }
            return response

    # FAQ知识库处理
    def knowFaqHandle(self,req,query):
        logger.info(f"【Service】【FAQ-知识检索】入参:{req}")
        # 初始化节点信息
        know_faq_node = AgentNode.KNOW_FAQ_NODE
        nodeLog = nodeUtils.AgentNodeInit(know_faq_node, req)
        try:
            # 检索
            res = knowService.knowRetrieval(req, query)
            if res is None :
                # 失败结果
                nodeUtils.AgentNodeEnd(nodeLog, None, False)
            else:
                # 结果
                nodeUtils.AgentNodeEnd(nodeLog, res, True)
                logger.info(f"【Service】【FAQ-知识检索】节点结果:{nodeLog}")

        except Exception as e:
            logger.error(f"【Service】【FAQ-知识检索】异常:{e}")
            # 异常结果
            nodeUtils.AgentNodeEnd(nodeLog, None, False)
        return nodeLog


    # 文件知识库处理
    def knowFileHandle(self,req,query):
        logger.info(f"【Service】【FILE-知识检索】入参:{req}")
        # 初始化节点信息
        know_faq_node = AgentNode.KNOW_FILE_NODE
        nodeLog = nodeUtils.AgentNodeInit(know_faq_node, req)
        try:
            # 检索
            res = knowService.knowRetrieval(req, query)
            if res is None:
                # 失败结果
                nodeUtils.AgentNodeEnd(nodeLog, None, False)
            else:
                # 结果
                nodeUtils.AgentNodeEnd(nodeLog, res, True)
                logger.info(f"【Service】【FILE-知识检索】节点结果:{nodeLog}")

        except Exception as e:
            logger.error(f"【Service】【FILE-知识检索】异常:{e}")
            # 异常结果
            nodeUtils.AgentNodeEnd(nodeLog, None, False)
        return nodeLog


    def mcpRunHandle(self,model,req,linkNode):
        res = asyncio.run(self.mcpToolHandle(model,req,linkNode))
        return  res

    # MCP工具处理
    async def mcpToolHandle(self,mode,req,linkNode):
        global res
        client = McpService()
        query = req.get("query")
        toolList = req.get("toolList")
        if len(toolList) > 0:
            try:
                await client.connect_to_server(ServiceApi.MCP_SERVICE_API)
                res=  await client.process_query(mode,query,toolList,linkNode)
            except  Exception as e:
                logger.error(f"【Service】【MCP-工具处理】异常:{e}")
            finally:
                await client.cleanup()
            return res
        else:
            return ""

    # 思考过程处理
    def recRetHandle(self,req):
        pass