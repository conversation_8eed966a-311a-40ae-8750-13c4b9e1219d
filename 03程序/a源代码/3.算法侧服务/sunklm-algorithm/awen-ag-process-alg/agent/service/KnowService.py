from common.utils.LogUtils import logger
from common.conf.ServerConfig import *
import requests

"""
知识检索
"""
class KnowService:

    # 知识检索
    def knowRetrieval(self,req,query):
        global res
        logger.info(f"【Service】【知识检索】入参:{req}")
        try:
            if req is None:
                return None

            for knowInfo in req:
                knowInfo['query'] = query
                logger.info(f"【Service】【知识检索】URL:{ServiceApi.RETRIEVAL_SERVICE_API},请求参数:{knowInfo}")
                response = requests.post(ServiceApi.RETRIEVAL_SERVICE_API, json=knowInfo, headers={'Content-Type': 'application/json'} )
                res = response.json()
                logger.info(f"【Service】【知识检索】返回结果:{res}")

            if res['retCode'] == "0":
                return res['retMap']
            else:
                res = None
            return res
        except Exception as e:
            logger.error(f"【Service】【知识检索】异常:{e}")
            return None


