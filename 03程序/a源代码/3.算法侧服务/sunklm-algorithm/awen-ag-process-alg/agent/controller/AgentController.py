import json

from fastapi import APIRouter, HTTPException
from fastapi import Request
from common.utils.ResultUtis import return_dict,ReturnCode
from common.utils.LogUtils import logger
from agent.service.AgentService import AgentService
import threading
import asyncio

# 创建路由实例
item_router = APIRouter()

@item_router.api_route("/chatAgent", methods=["POST"])
async def process_task(
        request: Request):
    try:
        req = await request.json()
        logger.info(f"【Controller】【智能体接口】参数:{req}")
        # 使用 asyncio.to_thread 将阻塞任务放到线程池中执行
        result = await asyncio.to_thread(service_handle, req)
        # logger.info(f"【Controller】【智能体接口】返回结果:{result}")
        return result
    except Exception as e:
        logger.error(f"【Controller】【智能体接口】异常:{e}")
        return return_dict(str(ReturnCode.ERROR.value), None, "error")




def service_handle(req):
    """
    统一代理请求到各个服务，自动判断流式和非流式响应
    """
    print(f"当前线程名称: {threading.current_thread().name}")
    try:
        agentService = AgentService()
        res = agentService.chatAgent(req)
        return res
    except Exception as e:
        logger.error(f"【Controller】【AI网关-service处理】异常{e}")
        return return_dict(str(ReturnCode.ERROR.value), None, f"【Controller】【AI网关-service处理】异常{e}")


